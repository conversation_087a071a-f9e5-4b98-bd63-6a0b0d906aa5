import 'package:connectone/old_blocs/login/login_state.dart';

class CardState extends MyState {
  const CardState();

  @override
  List<Object> get props => [];
}

class CardInitial extends CardState {}

class StockOpenState extends CardState {
  final int openFlag;
  final String viewString;

  const StockOpenState({required this.openFlag, required this.viewString});

  @override
  List<Object> get props => [openFlag, viewString];
}

// class ViewTypeState extends CardState {
//   final viewString;
//   ViewTypeState({this.viewString});

//   @override
//   List<Object> get props => [viewString];
// }
