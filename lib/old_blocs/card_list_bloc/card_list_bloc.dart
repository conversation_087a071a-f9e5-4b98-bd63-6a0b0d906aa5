import 'package:connectone/old_blocs/card_list_bloc/card_list_event.dart';
import 'package:connectone/old_blocs/card_list_bloc/card_list_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardBloc extends Bloc<CardEvent, CardState> {
  var openFlag = -1;
  var viewTypeString = "";

  CardBloc() : super(CardInitial()) {
    on<OpenStatus>((event, emit) {
      openFlag = event.openFlag;
      emit(StockOpenState(openFlag: openFlag, viewString: ""));
    });

    on<ViewType>((event, emit) async {
      viewTypeString = event.typeRadio;
      emit(StockOpenState(openFlag: openFlag, viewString: viewTypeString));
    });
  }
}
