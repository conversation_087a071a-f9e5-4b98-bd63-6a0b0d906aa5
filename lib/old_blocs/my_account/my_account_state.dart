part of 'my_account_bloc.dart';

abstract class MyAccountState extends Equatable {
  const MyAccountState();
}

class MyAccountInitial extends MyAccountState {
  @override
  List<Object> get props => [];
}

class MyAccountLoading extends MyAccountState {
  @override
  List<Object?> get props => [];
}

class MyAccountLoaded extends MyAccountState {
  final UserProfileV2Res account;
  final SellerCategoresRes sellerData;
  final bool isEditable;
  final TextEditingController tec1;
  final TextEditingController tec2;
  final TextEditingController tec3;

  const MyAccountLoaded(
      {required this.account,
      required this.sellerData,
      this.isEditable = false,
      required this.tec1,
      required this.tec2,
      required this.tec3});

  @override
  List<Object> get props => [account, sellerData, isEditable, tec1, tec2, tec3];
}

class Message extends MyAccountState {
  final String message;

  const Message({
    required this.message,
  });

  @override
  List<Object> get props => [];
}

class MyAccountSuccess extends MyAccountState {
  const MyAccountSuccess();
  @override
  List<Object?> get props => [];
}

class MyAccountFailed extends MyAccountState {
  @override
  List<Object?> get props => [];
}
