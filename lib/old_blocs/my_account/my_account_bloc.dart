import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/seller_categories_res.dart';
import 'package:connectone/bai_models/upload_media_res.dart';
import 'package:connectone/bai_models/user_profile_v2_res.dart';
import 'package:connectone/old_models/get_account_response.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';
import '../../core/utils/safe_print.dart';
import '../../core/utils/tools.dart';
part 'my_account_event.dart';
part 'my_account_state.dart';

class MyAccountBloc extends Bloc<MyAccountEvent, MyAccountState> {
  final networkController = NetworkController();

  MyAccountBloc() : super(MyAccountInitial()) {
    on<InitializeMyAccount>((event, emit) async {
      try {
        final accountData =
            await networkController.getUserProfileV2(num.parse(getVendorId()));
        SellerCategoresRes sellerData;
        if (!isBuyer()) {
          sellerData = await networkController.getSellerCategories();
          emit(
            MyAccountLoaded(
              account: accountData,
              sellerData: sellerData,
              tec2: TextEditingController(),
              tec3: TextEditingController(),
              tec1: TextEditingController(),
            ),
          );
        } else {
          sellerData = SellerCategoresRes();
          emit(
            MyAccountLoaded(
              account: accountData,
              sellerData: sellerData,
              tec2: TextEditingController(),
              tec3: TextEditingController(),
              tec1: TextEditingController(),
            ),
          );
        }
      } catch (e) {
        safePrint(e);
        alert(sSomethingWentWrong);
      }
    });
    on<EditMyAccount>((event, emit) async {
      if (state is MyAccountLoaded) {
        final state = this.state as MyAccountLoaded;
        if (state.isEditable == true) {
          emit(MyAccountInitial());
          try {
            //edit profile
            UploadMediaRes? profilePic;
            if (event.profilePic.isNotEmpty) {
              profilePic =
                  await networkController.uploadMedia(event.profilePic);
              await networkController.insertProfileImages(req: {
                "link_primary_key_name": "vendor_id",
                "link_primary_key_value": getVendorId(),
                "url": event.profilePic,
                "title": "profile",
                "docType": "image"
              });
            }
            var response = await networkController.updateAccountWebsite(
                profilePic: profilePic?.fileUrl, website: event.website);
            if (response.status == 200) {
              final accountData =
                  await networkController.getUserProfileV2(getVendorId());
              SellerCategoresRes sellerData;
              if (!isBuyer()) {
                sellerData = await networkController.getSellerCategories();
                emit(
                  MyAccountLoaded(
                    account: accountData,
                    sellerData: sellerData,
                    tec2: TextEditingController(),
                    tec3: TextEditingController(),
                    tec1: TextEditingController(),
                  ),
                );
              } else {
                emit(
                  MyAccountLoaded(
                    account: accountData,
                    sellerData: state.sellerData,
                    tec2: TextEditingController(),
                    tec3: TextEditingController(),
                    tec1: TextEditingController(),
                  ),
                );
              }
            }
          } catch (e) {
            alert(e.toString());
            emit(MyAccountLoaded(
                account: state.account,
                sellerData: state.sellerData,
                isEditable: !state.isEditable,
                tec2: state.tec2,
                tec3: state.tec3,
                tec1: state.tec1));
          }
        } else {
          emit(MyAccountLoaded(
              account: state.account,
              sellerData: state.sellerData,
              isEditable: !state.isEditable,
              tec2: state.tec2,
              tec3: state.tec3,
              tec1: state.tec1));
        }
      }
    });
    on<DeleteMyAccount>((event, emit) async {
      try {
        var response = await networkController.deleteAccountApi();
        emit(const Message(message: 'Success'));
      } catch (e) {
        alert("Couldn't delete account, please try again!");
        emit(const Message(message: 'Failed'));
      }
    });
  }
}
