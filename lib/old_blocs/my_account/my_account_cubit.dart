import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/my_account/my_account_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MyAccountCubit extends Cubit<MyAccountState> {
  MyAccountCubit() : super(MyAccountInitial());
  var api = NetworkController();

  Future<void> uploadProfileItems(
      {String? title, List<String>? data, int? id}) async {
    emit(MyAccountLoading());
    try {
      for (int i = 0; i < (data ?? []).length; i++) {
        var item = data?[i];
        var response1 = await api.uploadMedia(item ?? "");
        var url = response1.fileUrl;
        var res = await api.insertProfileImages(req: {
          "link_primary_key_name": "vendor_id",
          "link_primary_key_value": "$id",
          "url": url,
          "title": title ?? "certificate",
          "docType": title ?? "certificate"
        });
      }
      emit(const MyAccountSuccess());
    } catch (e) {
      print(e);
      alert(e.toString());
      emit(MyAccountFailed());
    }
  }

  void reset() {
    emit(MyAccountInitial());
  }
}
