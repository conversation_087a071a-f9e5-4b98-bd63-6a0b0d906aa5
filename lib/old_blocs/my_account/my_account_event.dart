part of 'my_account_bloc.dart';

abstract class MyAccountEvent extends Equatable {
  const MyAccountEvent();
}

class InitializeMyAccount extends MyAccountEvent {
  final Account account;
  final bool isEditable;

  const InitializeMyAccount({required this.account, this.isEditable = false});

  @override
  List<Object> get props => [account, isEditable];
}

class EditMyAccount extends MyAccountEvent {
  final bool isEditable;
  final String website;
  final String profilePic;

  const EditMyAccount(
      {required this.isEditable,
      required this.website,
      required this.profilePic});

  @override
  List<Object> get props => [isEditable, website, profilePic];
}

class DeleteMyAccount extends MyAccountEvent {
  const DeleteMyAccount();

  @override
  List<Object> get props => [];
}
