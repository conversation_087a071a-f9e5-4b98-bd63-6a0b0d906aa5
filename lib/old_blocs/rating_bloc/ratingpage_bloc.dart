import 'package:bloc/bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/my_stocks_pojo.dart';
import 'package:connectone/old_models/rating_summary.dart';
import 'package:equatable/equatable.dart';

part 'ratingpage_event.dart';
part 'ratingpage_state.dart';

class RatingPageBloc extends Bloc<RatingpageEvent, RatingpageState> {
  final _networkController = NetworkController();
  var postsList = <Content>[];

  RatingPageBloc() : super(Loading()) {
    on<LoadRatingSummary>((event, emit) async {
      emit(Loading());
      try {
        RatingSummary ratingSummary =
            await _networkController.getSummary(event.stockId);
        emit(Loaded(ratingSummary));
      } catch (e) {
        print("===========================$e");
        emit(Message(e.toString()));
        emit(const Loaded(null));
      }
    });

    //   on<LoadComments>((event, emit) async {
    //     try {
    //       var response = await NetworkController().getSummary();
    //       if (response.statusCode == 200) {
    //         postsList = json.decode(response.data());
    //         //  print(postsList);
    //       }
    //       ;
    //       emit(RatingLoaded(
    //         content: postsList,
    //       ));
    //     } catch (e) {
    //       print(e);
    //     }
    //   });
  }
}
