part of 'ratingpage_bloc.dart';

abstract class RatingpageState extends Equatable {
  const RatingpageState();

  @override
  List<Object> get props => [];
}

class Loading extends RatingpageState {}

class Loaded extends RatingpageState {
  final RatingSummary? ratingSummary;

  const Loaded(this.ratingSummary);
}

class Message extends RatingpageState {
  final String message;

  const Message(this.message);
}
