part of 'ratingpage_bloc.dart';

abstract class RatingpageEvent extends Equatable {
  const RatingpageEvent();

  @override
  List<Object> get props => [];
}

class LoadRatingSummary extends RatingpageEvent {
  final String stockId;

  const LoadRatingSummary(this.stockId);
}

class LoadComments extends RatingpageEvent {
  final String stockid;

  const LoadComments({required this.stockid});
}

class CommentSearch extends RatingpageEvent {
  final String stockid;
  final String rating_star;

  const CommentSearch({required this.rating_star, required this.stockid});
}

class ShareComment extends RatingpageEvent {
  final String stockid;
  final String rating_star;

  const ShareComment({required this.stockid, required this.rating_star});
}

class FilterComment extends RatingpageEvent {
  final String stockid;
  final String rating_star;

  const FilterComment({required this.stockid, required this.rating_star});
}
