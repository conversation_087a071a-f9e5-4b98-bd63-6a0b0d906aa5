part of 'help_bloc.dart';

abstract class HelpEvent extends Equatable {
  const HelpEvent();
}

class LoadHelp extends HelpEvent {
  final bool isLoading;

  const LoadHelp({this.isLoading = true});

  @override
  List<Object> get props => [isLoading];
}

class LoadedHelp extends HelpEvent {
  final bool isLoading;

  const LoadedHelp({this.isLoading = false});

  @override
  List<Object> get props => [isLoading];
}
