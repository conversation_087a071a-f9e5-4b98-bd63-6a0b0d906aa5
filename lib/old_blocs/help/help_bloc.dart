import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'help_event.dart';
part 'help_state.dart';

class HelpBloc extends Bloc<HelpEvent, HelpState> {
  HelpBloc() : super(const HelpInitial()) {
    on<LoadedHelp>((event, emit) {
      emit(const HelpInitial(isLoading: false));
    });
    on<LoadHelp>((event, emit) {
      emit(const HelpInitial(isLoading: true));
    });
  }
}
