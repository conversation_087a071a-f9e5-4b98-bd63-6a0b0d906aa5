import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';
import '../../old_models/notifications_list_res.dart';

part 'new_home_event.dart';
part 'new_home_state.dart';

class NewHomeBloc extends Bloc<NewHomeEvent, NewHomeState> {
  NewHomeBloc() : super(NewHomeInitial()) {
    on<Load>((event, emit) async {
      final notifications = await NetworkController().getNotificationsnew();
      emit(Loaded(notifications: notifications));
    });
  }
}
