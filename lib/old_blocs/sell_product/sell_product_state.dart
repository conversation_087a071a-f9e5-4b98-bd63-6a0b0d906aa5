part of 'sell_product_bloc.dart';

abstract class SellProductState extends Equatable {
  const SellProductState();
}

class SellProductLoading extends SellProductState {
  final bool isLoading;

  const SellProductLoading(this.isLoading);

  @override
  List<Object> get props => [isLoading];
}

class SellProductLoaded extends SellProductState {
  final bool isLoading;

  const SellProductLoaded(this.isLoading);

  @override
  List<Object> get props => [isLoading];
}
