import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'sell_product_event.dart';
part 'sell_product_state.dart';

class SellProductBloc extends Bloc<SellProductEvent, SellProductState> {
  SellProductBloc() : super(const SellProductLoading(true)) {
    on<InitializeSellProductScreen>((event, emit) {
      emit(const SellProductLoading(true));
    });
    on<LoadingCompleted>((event, emit) {
      emit(const SellProductLoaded(false));
    });
  }
}
