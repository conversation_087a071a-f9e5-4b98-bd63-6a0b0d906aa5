part of 'sell_product_bloc.dart';

abstract class SellProductEvent extends Equatable {
  const SellProductEvent();
}

class InitializeSellProductScreen extends SellProductEvent {
  final bool isLoading;

  const InitializeSellProductScreen(this.isLoading);

  @override
  List<Object?> get props => [isLoading];
}

class LoadingCompleted extends SellProductEvent {
  final bool isLoading;

  const LoadingCompleted(this.isLoading);

  @override
  List<Object?> get props => [isLoading];
}
