part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();
}

class HomeLoading extends HomeState {
  const HomeLoading();

  @override
  List<Object> get props => [];
}

class HomeLoaded extends HomeState {
  final List<CategoryNew> categoryData;

  const HomeLoaded({required this.categoryData});

  @override
  List<Object> get props => [categoryData];
}

class HomeError extends HomeState {
  final bool hasError;

  const HomeError({required this.hasError});

  @override
  List<Object> get props => [hasError];
}
