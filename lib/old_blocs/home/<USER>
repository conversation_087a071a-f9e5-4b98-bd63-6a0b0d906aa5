import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';
import '../../old_models/category_new.dart';
import '../../core/utils/constants.dart';
import '../../core/utils/tools.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final NetworkController networkController = NetworkController();

  HomeBloc() : super(const HomeLoading()) {
    on<InitializeHome>((event, emit) async {
      emit(const HomeLoading());
      try {
        List<CategoryNew> categoryData =
            await networkController.getCategoryNew(additionalUrl);
        // filter out category with isAvailable false
        List<CategoryNew> categoryDataFiltered = filterCategories(categoryData);

        emit(HomeLoaded(categoryData: categoryDataFiltered));
      } catch (e) {
        alert(e.toString());
      }
    });
  }
}

// List<CategoryNew> filterCategories(List<CategoryNew> categories) {
//   List<CategoryNew> filteredCategories = [];

//   for (var category in categories) {
//     // Check if the category name contains 'admin' or 'create' and the user is a buyer
//     if ((category.name!.toLowerCase().contains('admin') || category.name!.toLowerCase().contains('create')) && !isBuyer()) {
//       continue; // Skip adding this category
//     }

//     // If the category is available
//     if (category.isAvailable == true) {
//       // Recursively filter children if they exist
//       if (category.children != null && category.children!.isNotEmpty) {
//         category.children = filterCategories(category.children!);
//       }
//       // Add the category to the filtered list
//       filteredCategories.add(category);
//     }
//   }

//   return filteredCategories;
// }

List<CategoryNew> filterCategories(List<CategoryNew> categories) {
  List<CategoryNew> filteredCategories = [];

  for (var category in categories) {
    // var designation = getDesignation().toLowerCase();
    var roleCode = getRoleLevel();

    // if (((category.name!.toLowerCase().contains('material request')) ||
    //         (category.name!.toLowerCase().contains('service request'))) &&
    //     !isBuyer()) {
    //   continue;
    // }

    if (((category.name!.toLowerCase().contains('create'))) && !isBuyer()) {
      continue;
    }

    if ((category.name!.toLowerCase().contains('site')) && !isBuyer()) {
      continue;
    }

    if ((category.name!.toLowerCase().contains('admin request')) &&
        !isBuyer()) {
      continue;
    }

    if (isBuyer() &&
        (roleCode == 15) &&
        (category.name!.toLowerCase().contains('admin'))) {
      continue;
    }

    if (roleCode == 1 &&
        (category.name!.toLowerCase().contains('my request')) &&
        isBuyer()) {
      continue;
    }

    if ((category.tileCode == 'GINFO' ||
            category.tileCode == 'RELST' ||
            category.tileCode == 'DLRP' ||
            category.tileCode == 'AICB') &&
        !isBuyer()) {
      continue;
    }

    // if (roleCode != 1 && (category.name!.toLowerCase().contains('manage'))) {
    //   continue;
    // }

    if (category.isAvailable == 1) {
      if (category.children != null && category.children!.isNotEmpty) {
        category.children = filterCategories(category.children!);
      }
      filteredCategories.add(category);
    }
  }

  return filteredCategories;
}
