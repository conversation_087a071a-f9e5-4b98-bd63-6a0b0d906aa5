import 'package:connectone/old_models/feedback_data.dart';
import 'package:equatable/equatable.dart';

abstract class StockEvent extends Equatable {
  const StockEvent();

  @override
  List<Object> get props => [];
}

class LoadStocks extends StockEvent {
  final String from;
  final String to;

  const LoadStocks({
    required this.from,
    required this.to,
  });
}

class LoadStocksNextPage extends StockEvent {
  final List<dynamic> content;
  final String from;
  final String to;

  const LoadStocksNextPage(this.content,
      {required this.from, required this.to});
}

class StocksSearch extends StockEvent {
  final String stockId;

  const StocksSearch({required this.stockId});
}

class StockSearchByLot extends StockEvent {
  final String lotNo;
  final String deliveryDate;

  const StockSearchByLot({required this.lotNo, required this.deliveryDate});
}

class ShareFeedback extends StockEvent {
  final FeedbackData data;

  const ShareFeedback({required this.data});
}

class ShareReport extends StockEvent {
  var report_comment;

  var stockId;

  ShareReport({
    required this.stockId,
    required this.report_comment,
  });
}

class SetHelpful extends StockEvent {
  var stockId;

  SetHelpful({
    required this.stockId,
  });
}

class DateSelected extends StockEvent {
  final String deliveryDate;

  const DateSelected({required this.deliveryDate});
}

class LoadQuatity extends StockEvent {}

class LoadTotalAmount extends StockEvent {}
