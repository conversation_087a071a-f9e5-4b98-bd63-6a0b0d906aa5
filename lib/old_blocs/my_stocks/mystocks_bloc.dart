import 'package:connectone/old_blocs/my_stocks/mystocks_events.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_state.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/my_stocks_pojo.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/utils/safe_print.dart';

class MyStocksBloc extends Bloc<StockEvent, MyStocksState> {
  final _networkController = NetworkController();
  var postsList = <Content>[];
  var dateSelected = "";
  var openFlag = -1;
  var viewTypeString = "";
  int _page = 1;

  MyStocksBloc() : super(MyStocksInitial()) {
    on<LoadStocks>((event, emit) async {
      // emit(MyStocksInitial());
      try {
        var sum = 0;
        var amount = 0;
        var response = await _networkController.getStocks(
          readFromStorage(customerId),
          from: event.from,
          to: event.to,
        );
        if (response.statusCode == 200) {
          postsList = MyStocks.fromJson(response.data).content;
        }
        for (var element in postsList) {
          if (element.quantity != null) {
            sum += element.quantity!.toInt();
          }
        }
        for (var element in postsList) {
          if (element.amount != null) {
            amount += element.amount!.toInt();
          }
        }
        var marginDetails = await _networkController.getMarginDetails();
        var marginHistory =
            await _networkController.getMarginHistory(getCustomerId());

        emit(
          MyStocksLoaded(
            content: postsList,
            qty: sum,
            amt: amount,
            marginDetails: marginDetails,
            marginHistory: marginHistory,
            page: 0,
          ),
        );
        _page = 1;
      } catch (e) {
        safePrint(e);
      }
    });
    on<LoadStocksNextPage>((event, emit) async {
      try {
        var sum = 0;
        var amount = 0;
        final response = await _networkController.getStocksNextPage(
            customerId: getCustomerId(), page: _page, from: '', to: '');
        if (response.statusCode == 200) {
          postsList = MyStocks.fromJson(response.data).content;
        }
        for (var element in postsList) {
          if (element.quantity != null) {
            sum += element.quantity!.toInt();
          }
        }
        for (var element in postsList) {
          if (element.amount != null) {
            amount += element.amount!.toInt();
          }
        }
        var marginDetails = await _networkController.getMarginDetails();
        var marginHistory =
            await _networkController.getMarginHistory(getCustomerId());

        emit(
          MyStocksLoaded(
            content: [...event.content, ...postsList],
            qty: sum,
            amt: amount,
            marginDetails: marginDetails,
            marginHistory: marginHistory,
            page: 0,
          ),
        );
        _page = _page + 1;
      } catch (e) {
        safePrint(e);
      }
    });
    on<StocksSearch>((event, emit) async {
      // emit(MyStocksInitial());
      postsList.clear();
      var sum = 0;
      var amount = 0;
      var response = await _networkController.getStocksSearchByStockId(
          readFromStorage(customerId), event.stockId);
      postsList = response.content;

      for (var element in postsList) {
        if (element.quantity != null) {
          sum += element.quantity!.toInt();
        }
      }
      for (var element in postsList) {
        if (element.amount != null) {
          amount += element.amount!.toInt();
        }
      }
      emit(MyStocksLoaded(content: postsList, qty: sum, amt: amount, page: 0));
    });

    on<StockSearchByLot>((event, emit) async {
      // emit(MyStocksInitial());
      postsList.clear();
      var sum = 0;
      var amount = 0;
      var response = await _networkController.getStocksSearchByLotNo(
          readFromStorage(customerId), event.lotNo, event.deliveryDate);
      postsList = response.content;

      for (var element in postsList) {
        if (element.quantity != null) {
          sum += element.quantity!.toInt();
        }
      }
      for (var element in postsList) {
        if (element.amount != null) {
          amount += element.amount!.toInt();
        }
      }
      emit(MyStocksLoaded(content: postsList, qty: sum, amt: amount, page: 0));
    });

    on<ShareFeedback>((event, emit) async {
      try {
        var response = await _networkController.postFeedback(event.data);
        alert(response);
      } catch (e) {
        alert('Feedback already given by this customer for this stock');
      }
    });

    on<DateSelected>((event, emit) async {
      dateSelected = event.deliveryDate;
    });
    on<ShareReport>((event, emit) async {
      await _networkController.putreport(
        event.report_comment,
        event.stockId,
      );
      var state = this.state as MyStocksLoaded;
      emit(state);
    });
  }
}
