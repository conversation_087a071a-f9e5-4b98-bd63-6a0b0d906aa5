import 'package:connectone/old_models/margin_details.dart';
import 'package:connectone/old_models/margin_history.dart';
import 'package:connectone/old_models/my_stocks_pojo.dart' as stockspojo;
import 'package:equatable/equatable.dart';

abstract class MyStocksState extends Equatable {
  const MyStocksState();

  @override
  List<Object> get props => [];
}

class MyStocksInitial extends MyStocksState {}

class MyStocksLoaded extends MyStocksState {
  final List<stockspojo.Content> content;

  final int page;
  final int qty;
  final int amt;
  final MarginDetails? marginDetails;
  final MarginHistory? marginHistory;

  const MyStocksLoaded(
      {required this.page,
      required this.qty,
      required this.amt,
      required this.content,
      this.marginDetails,
      this.marginHistory});

  @override
  List<Object> get props => [content, qty, amt];
}
