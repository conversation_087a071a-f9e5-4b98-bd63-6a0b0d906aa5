import 'package:bloc/bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/live_auction_utils.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/old_models/user_action_body.dart';
import 'package:equatable/equatable.dart';

import '../../old_models/autobid_response.dart';
import '../../old_models/live_auction_response.dart';
import '../../core/utils/tools.dart';

part 'live_auction_event.dart';
part 'live_auction_state.dart';

class LiveAuctionBloc extends Bloc<LiveAuctionEvent, LiveAuctionState> {
  LiveAuctionBloc() : super(LiveAuctionInitial()) {
    on<Load>((event, emit) async {
      emit(LiveAuctionInitial());
      final response = await NetworkController().getLiveAuction(
          event.roomId, int.tryParse(await LiveUtils().getStockId()) ?? 0);

      try {
        await NetworkController().saveUserAction(UserActionReq(
          action: "live_auction",
          customerId: int.tryParse(getCustomerId()) ?? 0,
          actionTime: DateTime.now().toUtc(),
          stockId: int.tryParse(await LiveUtils().getStockId()) ?? 0,
          deviceType: "mobile",
          screenName: "live_auction",
        ));
      } catch (e) {
        safePrint(e);
      }

      response.fold<dynamic>(
        (failure) {
          emit(Error(error: failure));
          emit(
            Loaded(
              list: LiveAuction(
                data: Data(
                  days: [
                    Day(
                      deliveryDate: DateTime.now(),
                      itemId: 0,
                      itemName: 'itemName',
                      noOfStocks: 0,
                      totalStocks: 0,
                      unitName: '',
                      unitShortName: '',
                    )
                  ],
                  orders: [
                    Order(
                      deliveryDate: DateTime.now(),
                      itemId: 0,
                      itemName: '',
                      orderId: 0,
                      lotNo: 0,
                      vendorName: '',
                      quantity: 0,
                      unitId: 0,
                      unitName: '',
                      unitShortName: '',
                      sellerVendorId: 0,
                      location: '',
                      latitude: '',
                      longitude: '',
                      customerId: '',
                      images: [],
                      orderSpecifications: [
                        OrderSpecification(
                          noOfBags: '',
                          grade7T8MmClean: 0,
                          rejectionsSickdsplit: 0,
                          grade7T8MmSickdsplit: 0,
                          rejectionsTotal: 0,
                          totalFruit: 0,
                          grade7T8MmTotalpercentage: 0,
                          grade17MmClean: 0,
                          grade8MmClean: 0,
                          rejectionsClean: 0,
                          totalPercentage: 0,
                          totalWeight: 0,
                          grade8MmSickdsplit: 0,
                          rejectionsFruit: 0,
                          grade7T8MmFruit: 0,
                          grade8MmFruit: 0,
                          grade17MmFruit: 0,
                          grade17MmTotalpercentage: 0,
                          moisture: '',
                          grade17MmSickdsplit: 0,
                          ltrWt: '',
                          colour: '',
                          totalSickdsplit: 0,
                          size: '',
                          grade: '',
                          grade8MmTotalpercentage: 0,
                          category: '',
                          totalClean: 0,
                        )
                      ],
                      expectedPrice: 0,
                    )
                  ],
                ),
                status: 0,
                statusDescription: '',
              ),
            ),
          );
        },
        (success) {
          emit(Loaded(list: success));
        },
      );
    });
    on<Bid>((event, emit) async {
      if (this.state is! Loaded) {
        return;
      }
      var state = this.state as Loaded;
      try {
        var response = await NetworkController().livebidNew(
          quantity: event.quantity,
          stockId: event.stockId,
          vendorId: event.vendorId,
          bidDate: event.bidDate,
          auctionId: event.auctionId,
          amount: event.amount,
        );
        if (response.status == 200 &&
            response.statusDescription.contains("success")) {
          var yourBid = event.highestBid + event.incrementValue;
          emit(Loaded(list: state.list)
              .copyWith(bidSuccess: true, yourBid: event.amount));
          emit(Loaded(list: state.list).copyWith(bidSuccess: null));
          // emit(Loaded(list: state.list, yourBid: yourBid, bidSuccess: true));
          // emit(Loaded(list: state.list, yourBid: yourBid, bidSuccess: null));
        } else {
          var yourBid = event.highestBid + event.incrementValue;
          // emit(Loaded(list: state.list)
          //     .copyWith(bidSuccess: true, yourBid: event.amount));
          emit(Loaded(list: state.list).copyWith(bidSuccess: null));
          // emit(Loaded(list: state.list, yourBid: yourBid, bidSuccess: false));
          // emit(Loaded(
          //     list: state.list, yourBid: 0, bidSuccess: null));
          if (!response.statusDescription
              .toLowerCase()
              .contains("the same amount")) {
            alert(response.statusDescription);
          }
        }
        // emit(Loaded(list: (this.state as Loaded).list));
      } catch (e) {
        alert("Cannot run auction for this stock!");
        safePrint(e);
      }
    });
  }
}
