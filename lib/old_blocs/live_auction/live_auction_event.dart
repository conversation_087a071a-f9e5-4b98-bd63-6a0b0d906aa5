part of 'live_auction_bloc.dart';

abstract class LiveAuctionEvent extends Equatable {
  const LiveAuctionEvent();
}

class Load extends LiveAuctionEvent {
  final int roomId;

  const Load({required this.roomId});

  @override
  List<Object> get props => [roomId];
}

class Bid extends LiveAuctionEvent {
  final double quantity;
  final int stockId;
  final int vendorId;
  final String bidDate;
  final int auctionId;
  final double amount;
  final double highestBid;
  final double incrementValue;
  final bool? customBid;

  const Bid({
    required this.quantity,
    required this.stockId,
    required this.vendorId,
    required this.bidDate,
    required this.auctionId,
    required this.amount,
    required this.highestBid,
    required this.incrementValue,
    this.customBid = false,
  });

  @override
  List<Object> get props => [
        quantity,
        stockId,
        vendorId,
        bidDate,
        auctionId,
        amount,
        highestBid,
        incrementValue
      ];
}
