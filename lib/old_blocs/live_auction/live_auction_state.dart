part of 'live_auction_bloc.dart';

abstract class LiveAuctionState extends Equatable {
  const LiveAuctionState();
}

class LiveAuctionInitial extends LiveAuctionState {
  @override
  List<Object> get props => [];
}

class Error extends LiveAuctionState {
  const Error({this.error});

  final CommonResponse? error;

  @override
  List<Object> get props => [];
}

class Loaded extends LiveAuctionState {
  final LiveAuction? list;
  final double yourBid;
  final bool? bidSuccess;

  const Loaded({required this.list, this.yourBid = 0, this.bidSuccess});

  Loaded copyWith({
    LiveAuction? list,
    double? yourBid,
    bool? bidSuccess,
  }) {
    return Loaded(
      list: list ?? this.list,
      yourBid: yourBid ?? this.yourBid,
      bidSuccess: bidSuccess ?? this.bidSuccess,
    );
  }

  @override
  List<Object> get props => [yourBid];
}
