import 'dart:developer' as logger;

import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/favourite_item.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';
// import '../../old_models/get_offline_stocks.dart';
import '../../core/utils/constants.dart';
import '../../core/utils/tools.dart';

part 'offline_stocks_event.dart';
part 'offline_stocks_state.dart';

class OfflineMainBloc extends Bloc<OfflineMainEvent, OfflineMainState> {
  final NetworkController networkController = NetworkController();

  OfflineMainBloc() : super(OfflineStocksInitial()) {
    var contents = <Content>[];
    var groups = <GroupedProduct>[];
    int page = 0;

    on<InitializeOfflineStocks>((event, emit) async {
      var sortBy = '';
      try {
        page = 0;
        contents.clear();
        emit(OfflineStocksInitial());
        if (event.sortBy == "NONE") {
          sortBy = '';
        } else if (event.sortBy == "Lot No") {
          sortBy = "lot_no,DESC";
        } else if (event.sortBy == "Product") {
          sortBy = "item_name,DESC";
        } else if (event.sortBy == "Seller") {
          sortBy = "name,DESC";
        } else if (event.sortBy == "Date") {
          sortBy = "delivery_dt,DESC";
        } else if (event.sortBy == "Favourite") {
          sortBy = "favourite_yn,DESC";
        } else if (event.sortBy == "Price") {
          sortBy = "expected_price,DESC";
        } else if (event.sortBy == "Leading Bid") {
          sortBy = "highest_bid_amount,DESC";
        } else if (event.sortBy == "Trailing Bid") {
          sortBy = "highest_bid_amount,ASC";
        } else if (event.sortBy == "Quality High") {
          sortBy = "grade,DESC";
        } else if (event.sortBy == "Quality Low") {
          sortBy = "grade,ASC";
        } else if (event.sortBy == "Created On") {
          sortBy = "createdAt,asc";
        } else if (event.sortBy == "Delivery On") {
          sortBy = "deliveryDate,asc";
        } else if (event.sortBy == "Quantity") {
          sortBy = "quantity,asc";
        } else if (event.sortBy == "MR ID") {
          sortBy = "orderGroupId,asc";
        }
        final favouritesResponse = await networkController.getNewFavourites();
        BaiProductsRes offlineStocks;
        var code = event.code;
        offlineStocks = await networkController.getBaiProductList(
          queryString: event.queryString,
          page: page,
          code: event.code,
          sortBy: sortBy,
        );
        contents.addAll(offlineStocks.content!);
        groups.addAll(groupByOrderId(offlineStocks.content!));
        page = 1;
        offlineStocks.content = contents;
        emit(OfflineStocksLoaded(
          offlineStocks: offlineStocks,
          favouritesResponse: favouritesResponse,
          groups: groups,
        ));
      } catch (e) {
        alert("Something went wrong.");
        logger.log(e.toString());
      }
    });
    on<InitializeOfflineStocksNext>((event, emit) async {
      var sortBy = '';
      try {
        if (event.sortBy == "NONE") {
          sortBy = '';
        } else if (event.sortBy == "Lot No") {
          sortBy = "lot_no,DESC";
        } else if (event.sortBy == "Product") {
          sortBy = "item_name,DESC";
        } else if (event.sortBy == "Seller") {
          sortBy = "name,DESC";
        } else if (event.sortBy == "Date") {
          sortBy = "delivery_dt,DESC";
        } else if (event.sortBy == "Favourite") {
          sortBy = "favourite_yn,DESC";
        } else if (event.sortBy == "Price") {
          sortBy = "expected_price,DESC";
        } else if (event.sortBy == "Leading Bid") {
          sortBy = "highest_bid_amount,DESC";
        } else if (event.sortBy == "Trailing Bid") {
          sortBy = "highest_bid_amount,ASC";
        } else if (event.sortBy == "Quality High") {
          sortBy = "grade,DESC";
        } else if (event.sortBy == "Quality Low") {
          sortBy = "grade,ASC";
        } else if (event.sortBy == "Created On") {
          sortBy = "createdAt,asc";
        } else if (event.sortBy == "Delivery On") {
          sortBy = "deliveryDate,asc";
        } else if (event.sortBy == "Quantity") {
          sortBy = "quantity,asc";
        } else if (event.sortBy == "MR ID") {
          sortBy = "orderGroupId,asc";
        }
        final favouritesResponse = await networkController.getNewFavourites();
        BaiProductsRes offlineStocks;
        var code = event.code;
        offlineStocks = await networkController.getBaiProductList(
          queryString: event.queryString,
          page: page,
          code: event.code,
          sortBy: sortBy,
        );
        contents.addAll(offlineStocks.content!);
        groups.addAll(groupByOrderId(offlineStocks.content!));
        page += 1;
        offlineStocks.content = contents;
        emit(OfflineStocksLoaded(
          offlineStocks: offlineStocks,
          favouritesResponse: favouritesResponse,
          groups: groups,
        ));
      } catch (e) {
        alert("Error occurred!");
        logger.log(e.toString());
      }
    });
    on<SortOfflineStocks>((event, emit) async {});
    on<SearchOfflineStocks>((event, emit) async {
      try {
        if (state is OfflineStocksLoaded) {
          final state = this.state as OfflineStocksLoaded;
          final offlineStocks = await networkController.searchOfflineStocks(
            deliveryDate: state.deliveryDate,
            lotNo: event.lotNo,
            stockId: event.stockId,
          );
        }
      } catch (e) {
        alert(sSomethingWentWrong);
      }
    });
    on<FavouritesClicked>((event, emit) async {
      try {
        if (state is OfflineStocksLoaded) {
          final state = this.state as OfflineStocksLoaded;
          var offlineStocks = state.offlineStocks.content;
          emit(OfflineStocksLoaded(
            offlineStocks: BaiProductsRes(),
            favouritesResponse: state.favouritesResponse,
          ));
        }
      } catch (e) {
        alert(sSomethingWentWrong);
      }
    });
    on<ItemSelected>((event, emit) async {
      var state = this.state as OfflineStocksLoaded;
      emit(OfflineStocksLoaded(
          offlineStocks: state.offlineStocks,
          favouritesResponse: state.favouritesResponse,
          selectedItem: event.selectedItem));
    });
    on<SetDeliveryDate>((event, emit) async {
      var state = this.state as OfflineStocksLoaded;
      emit(OfflineStocksLoaded(
          offlineStocks: state.offlineStocks,
          favouritesResponse: state.favouritesResponse,
          deliveryDate: event.deliveryDate));
    });
    on<MarkAsFavourite>((event, emit) async {
      try {
        await networkController.addAsFavourite(
          customerId: event.customerId,
          stockId: event.stockId,
        );
        add(
          InitializeOfflineStocks(code: event.code),
        );
      } catch (e) {
        alert("Failed to add to favourites.");
      }
    });
    on<UnMarkAsFavourite>((event, emit) async {
      try {
        await networkController.removeAsFavourite(
          customerId: event.customerId,
          stockId: event.stockId,
        );
        add(
          InitializeOfflineStocks(code: event.code),
        );
      } catch (e) {
        alert("Failed to remove from favourites.");
      }
    });
    on<RefreshOfflineStocks>((event, emit) async {
      emit(OfflineStocksRefresh());
    });
    on<ShowSplitGroupDialog>((event, emit) async {
      emit(ShowSplitGroupDialogState(event.itemId, event.categoryName));
    });
  }
}

class GroupedProduct {
  final int orderGroupId;
  final List<Content> content;

  GroupedProduct({
    required this.orderGroupId,
    required this.content,
  });
}

List<GroupedProduct> groupByOrderId(List<Content> ungroupedItems) {
  Map<int, List<Content>> groupedMap = {};

  for (var item in ungroupedItems) {
    if (item.orderGroupId != null) {
      int orderGroupIdInt = item.orderGroupId!.toInt();
      if (!groupedMap.containsKey(orderGroupIdInt)) {
        groupedMap[orderGroupIdInt] = [];
      }
      groupedMap[orderGroupIdInt]!.add(item);
    }
  }

  List<GroupedProduct> groups = [];
  groupedMap.forEach((orderGroupId, contentList) {
    groups
        .add(GroupedProduct(orderGroupId: orderGroupId, content: contentList));
  });

  return groups;
}
