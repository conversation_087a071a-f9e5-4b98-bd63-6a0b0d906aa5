import 'dart:collection';

import 'package:bloc/bloc.dart';
import 'package:connectone/old_blocs/monthwise_calendar_bloc/monthwisecalendarbloc_state.dart';
import 'package:equatable/equatable.dart';

part 'monthwisecalendarbloc_event.dart';

class MonthwiseCalendarBloc
    extends Bloc<MonthwisecalendarblocEvent, MonthwisecalendarblocState> {
  final Set<int> stocksAvailableDays = LinkedHashSet<int>();

  MonthwiseCalendarBloc() : super(MonthwisecalendarblocInitial()) {
    on<LoadMonthCalendar>((event, emit) {
      stocksAvailableDays.add(21);
      stocksAvailableDays.add(22);
      stocksAvailableDays.add(23);
      emit(MonthwiseStockLoaded(stocksAvailableDays: stocksAvailableDays));
    });
  }
}
