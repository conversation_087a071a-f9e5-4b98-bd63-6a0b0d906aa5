import 'package:equatable/equatable.dart';

abstract class MonthwisecalendarblocState extends Equatable {
  const MonthwisecalendarblocState();

  @override
  List<Object> get props => [];
}

class MonthwisecalendarblocInitial extends MonthwisecalendarblocState {}

class MonthwiseStockLoaded extends MonthwisecalendarblocState {
  final Set<int> stocksAvailableDays;

  const MonthwiseStockLoaded({required this.stocksAvailableDays});

  @override
  List<Object> get props => [stocksAvailableDays];
}
