part of 'offline_card_bloc.dart';

abstract class OfflineSubState extends Equatable {
  const OfflineSubState();
}

class OfflineCardInitial extends OfflineSubState {
  @override
  List<Object> get props => [];
}

class OfflineCardLoaded extends OfflineSubState {
  final bool isTable;
  final bool isOpen;
  final String currentItem;
  final String groupValue;
  final String buttonText;
  final FirebaseResponseOffline firebaseResponse;
  final bool showAutobid;
  final bool showMakeOffer;
  final bool showBuyNow;
  final bool isHidden;
  final bool isMoreOptionsOpen;

  const OfflineCardLoaded(
      {required this.buttonText,
      required this.currentItem,
      required this.groupValue,
      required this.isOpen,
      required this.isTable,
      required this.firebaseResponse,
      this.showAutobid = false,
      this.showBuyNow = false,
      this.showMakeOffer = false,
      this.isHidden = false,
      this.isMoreOptionsOpen = false});

  @override
  List<Object> get props => [
        buttonText,
        currentItem,
        groupValue,
        isOpen,
        isTable,
        showBuyNow,
        showMakeOffer,
        showBuyNow,
        isHidden,
        isMoreOptionsOpen
      ];
}
