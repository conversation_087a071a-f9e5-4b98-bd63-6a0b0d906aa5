part of 'offline_card_bloc.dart';

abstract class OfflineSubEvent extends Equatable {
  const OfflineSubEvent();
}

class InitializeOfflineCard extends OfflineSubEvent {
  final bool isTable;
  final bool isOpen;
  final String currentItem;
  final String groupValue;
  final String buttonText;

  const InitializeOfflineCard(this.buttonText, this.currentItem,
      this.groupValue, this.isOpen, this.isTable);

  @override
  List<Object> get props =>
      [buttonText, currentItem, groupValue, isOpen, isTable];
}

class InitializeFb extends OfflineSubEvent {
  final bool isTable;
  final bool isOpen;
  final String currentItem;
  final String groupValue;
  final String buttonText;
  final FirebaseResponseOffline firebaseResponse;

  const InitializeFb(this.buttonText, this.currentItem, this.groupValue,
      this.isOpen, this.isTable, this.firebaseResponse);

  @override
  List<Object> get props =>
      [buttonText, currentItem, groupValue, isOpen, isTable, firebaseResponse];
}

class ToggleCard extends OfflineSubEvent {
  const ToggleCard();

  @override
  List<Object?> get props => [];
}

class ToggleMoreOptions extends OfflineSubEvent {
  final bool isMoreOptionsOpen;

  const ToggleMoreOptions(this.isMoreOptionsOpen);

  @override
  List<Object?> get props => [isMoreOptionsOpen];
}

class ToggleTable extends OfflineSubEvent {
  final bool isTable;

  const ToggleTable(this.isTable);

  @override
  List<Object?> get props => [isTable];
}

class ShowAutobid extends OfflineSubEvent {
  const ShowAutobid();

  @override
  List<Object?> get props => [];
}

class SetGroupValue extends OfflineSubEvent {
  final String groupValue;

  const SetGroupValue(this.groupValue);

  @override
  List<Object?> get props => [groupValue];
}

class SetButtonText extends OfflineSubEvent {
  final String buttonText;

  const SetButtonText(this.buttonText);

  @override
  List<Object?> get props => [buttonText];
}

class SetCurrentCard extends OfflineSubEvent {
  final String currentItemId;

  const SetCurrentCard(this.currentItemId);

  @override
  List<Object?> get props => [currentItemId];
}

class Autobid extends OfflineSubEvent {
  final int quantity;
  final String increment;
  final String bidDeskNo;
  final int stockId;
  final String orderTypeCd;
  final int limit;

  const Autobid(
      {required this.limit,
      required this.orderTypeCd,
      required this.stockId,
      required this.bidDeskNo,
      required this.increment,
      required this.quantity});

  @override
  List<Object?> get props =>
      [limit, orderTypeCd, stockId, bidDeskNo, increment, quantity];
}

class MakeOffer extends OfflineSubEvent {
  final int stockId;
  final int quantity;
  final int amount;
  final String bidDeskNo;
  final String orderTypeCd;

  const MakeOffer(
      {required this.orderTypeCd,
      required this.stockId,
      required this.bidDeskNo,
      required this.quantity,
      required this.amount});

  @override
  List<Object?> get props =>
      [amount, orderTypeCd, stockId, bidDeskNo, quantity];
}

//ignore: must_be_immutable
class MakeOffline extends OfflineSubEvent {
  int quantity;
  int stockId;
  double amount;
  int vendorId;
  String bidDate;
  String orderTypeCd;
  int auctionId;
  String roomId;

  MakeOffline(
      {required this.orderTypeCd,
      required this.stockId,
      required this.vendorId,
      required this.quantity,
      required this.amount,
      required this.auctionId,
      required this.bidDate,
      required this.roomId});

  @override
  List<Object?> get props =>
      [amount, orderTypeCd, stockId, quantity, vendorId, auctionId, bidDate];
}

class BuyNow extends OfflineSubEvent {
  final int stockId;
  final int quantity;
  final int amount;
  final String bidDeskNo;
  final String orderTypeCd;

  const BuyNow(
      {required this.orderTypeCd,
      required this.stockId,
      required this.bidDeskNo,
      required this.quantity,
      required this.amount});

  @override
  List<Object?> get props =>
      [amount, orderTypeCd, stockId, bidDeskNo, quantity];
}

class RefreshAutobid extends OfflineSubEvent {
  final String stockId;

  const RefreshAutobid({required this.stockId});

  @override
  List<Object?> get props => [stockId];
}

class CancelAutobid extends OfflineSubEvent {
  final String stockId;

  const CancelAutobid({required this.stockId});

  @override
  List<Object?> get props => [stockId];
}

class Hide extends OfflineSubEvent {
  const Hide();

  @override
  List<Object?> get props => [];
}

class AddAsFavourite extends OfflineSubEvent {
  final String customerId;
  final String stockId;

  const AddAsFavourite(this.customerId, this.stockId);

  @override
  List<Object?> get props => [];
}
