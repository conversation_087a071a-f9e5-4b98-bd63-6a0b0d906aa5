import 'package:bloc/bloc.dart';
import 'package:connectone/old_models/get_comments_summary.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';

part 'get_comments_event.dart';
part 'get_comments_state.dart';

class GetCommentsBloc extends Bloc<GetCommentsEvent, GetCommentsState> {
  final _networkController = NetworkController();

  // var postsList = <Content>[];
  int _page = 1;

  GetCommentsBloc() : super(GetCommentsInitial()) {
    on<LoadComments>((event, emit) async {
      print('wwwwwwwwwwwwwwwwwwwwwwwww${event.rVal}');
      // emit(Loading());
      try {
        GetComments getComments = await _networkController.getComments(
            rVal: event.rVal, stockId: event.stockid);

        emit(Loaded(
            page: 0,
            getComments: getComments,
            rVal: event.rVal,
            stockId: event.stockid));

        _page = 1;
      } catch (e) {
        print("===========1212================$e");
        // alert("Failed to add feedback");
        // emit(Message(e.toString()));
        // emit(Loaded(null, _page, event.rVal, event.stockid));
      }
    });
    on<LoadNextComments>((event, emit) async {
      print('zzzzzzzzzzzzz');
      // emit(GetCommentsInitial());
      try {
        GetComments getComments = await _networkController.getNextComments(
            page: _page, rVal: event.rVal, stockId: event.stockid);
        // print('123123${[
        //   ...getComments.content!,
        //   ...event.getComments!.content!.map((e) => print(e)).toList()
        // ]}');
        print('-3-3---${event.getComments!.content!.length}');
        print('-3-3---${event.getComments!.content}');
        final tempList = GetComments(
            content: [...event.getComments!.content!, ...getComments.content!]);
        emit(state);
        emit(Loaded(
          page: _page,
          rVal: event.rVal,
          stockId: event.stockid,
          getComments: tempList,
        ));

        print('78787878${tempList.content}');
        print('-3-3---${getComments.content!.length}');
        print('-3-3---${getComments.content}');

        _page = _page + 1;
      } catch (e) {
        print("===========1212--================$e");
        // alert("Failed to add feedback");
        // emit(Message(e.toString()));
        // emit(Loaded(
        //     getComments: GetComments(),
        //     rVal: event.rVal,
        //     stockId: event.stockid));
      }
    });

    on<SetHelpful>((event, emit) async {
      print('**********************************************************');
      try {
        await _networkController.sethelpful(
            event.commentId.toString(), event.rVal);
        add(LoadComments(rVal: event.rVal, stockid: event.stockId));
        // emit(Loaded(SetHelpful(stockid: '292285')));
      } catch (e) {
        print("==========+++++++++++=================$e");
        // emit(Message(e.toString()));
        // emit(const Loaded(null));
      }
    });
  }
}
