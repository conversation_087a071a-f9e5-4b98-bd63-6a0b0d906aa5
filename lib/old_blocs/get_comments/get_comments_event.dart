part of 'get_comments_bloc.dart';

abstract class GetCommentsEvent extends Equatable {
  const GetCommentsEvent();

  @override
  List<Object> get props => [];
}

class LoadComments extends GetCommentsEvent {
  final String stockid;

  final String rVal;

  const LoadComments({required this.stockid, required this.rVal});
}

class LoadNextComments extends GetCommentsEvent {
  final String stockid;

  final String rVal;
  final GetComments? getComments;

  // final int page;

  const LoadNextComments(this.getComments, this.stockid, this.rVal);
}

class CommentSearch extends GetCommentsEvent {
  final String stockid;
  final String rating_star;

  const CommentSearch({required this.rating_star, required this.stockid});
}

class ShareComment extends GetCommentsEvent {
  final String stockid;
  final String rating_star;

  const ShareComment({required this.stockid, required this.rating_star});
}

class FilterComment extends GetCommentsEvent {
  final String stockid;
  final String rating_star;

  const FilterComment({required this.stockid, required this.rating_star});
}

class SetHelpful extends GetCommentsEvent {
  final int commentId;
  final String stockId;
  final String rVal;

  const SetHelpful({
    required this.rVal,
    required this.stockId,
    required this.commentId,
  });
}
