part of 'get_comments_bloc.dart';

abstract class GetCommentsState {
  const GetCommentsState();

  @override
  List<Object> get props => [];
}

class GetCommentsInitial extends GetCommentsState {}

// class Loading extends GetCommentsState {}

class Loaded extends GetCommentsState {
  final GetComments? getComments;
  final int page;
  final String rVal;
  final String stockId;

  const Loaded(
      {required this.page,
      required this.getComments,
      required this.rVal,
      required this.stockId});
}

class Message extends GetCommentsState {
  final String message;

  const Message(this.message);
}
