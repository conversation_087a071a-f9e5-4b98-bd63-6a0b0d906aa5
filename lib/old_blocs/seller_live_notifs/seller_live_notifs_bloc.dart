import 'package:bloc/bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/find_stock_by_id_response.dart';
import 'package:connectone/old_models/notifs_based_on_stock.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:equatable/equatable.dart';

import '../../old_models/bidlist_response.dart';
import '../../core/utils/tools.dart';

part 'seller_live_notifs_event.dart';
part 'seller_live_notifs_state.dart';

class SellerLiveNotifsBloc
    extends Bloc<SellerLiveNotifsEvent, SellerLiveNotifsState> {
  final NetworkController networkController = NetworkController();

  SellerLiveNotifsBloc() : super(SellerLiveNotifsInitial()) {
    on<Load>((event, emit) async {
      try {
        final stockId = event.stockId;
        final bidlist = await networkController.getBidList(stockId);
        final stock = await networkController.getFindStockById(stockId);
        final notifs = await networkController.getNotifsBasedOnStock(stockId);
        emit(Loaded(bidlist: bidlist, stock: stock, notifs: notifs));
      } catch (e) {
        safePrint(e);
        emit(Error(error: e.toString()));
      }
    });
    on<WithdrawLot>((event, emit) async {
      final stockId = event.stockId;
      final response = await networkController.withdrawLot(stockId: stockId);
      if (response.status == 200) {
        alert(response.statusDescription);
      } else {
        alert(response.statusDescription);
      }
      emit(state);
    });
    on<AcceptBid>((event, emit) async {
      final stockId = event.stockId;
      final response = await networkController.acceptBid(
          stockId: stockId, bidId: event.bidId);
      if (response.status == 200) {
        alert(response.statusDescription);
      } else {
        alert(response.statusDescription);
      }
      emit(state);
    });
    on<SubmitNegotiation>((event, emit) async {
      final stockId = event.stockId;
      final response = await networkController.submitNegotiation(
          stockId: stockId, expPrice: event.expectedPrice);
      if (response.status == 200) {
        alert(response.statusDescription);
      } else {
        alert(response.statusDescription);
      }
      emit(state);
    });
    on<RejectNegotiation>((event, emit) async {
      final stockId = event.stockId;
      final response =
          await networkController.rejectNegotiation(stockId: stockId);
      if (response.status == 200) {
        alert(response.statusDescription);
      } else {
        alert(response.statusDescription);
      }
      emit(state);
    });
    on<Lock>((event, emit) async {
      var state = this.state as Loaded;
      final isLocked = state.isLocked;
      emit(Loaded(
          bidlist: state.bidlist,
          stock: state.stock,
          notifs: state.notifs,
          isLocked: event.isLocked));
    });

    on<SellerWatchingNotifs>((event, emit) async {
      await networkController.sellerWatchingNotifs(stockId: event.stockId);
    });
  }
}
