part of 'seller_live_notifs_bloc.dart';

abstract class SellerLiveNotifsState extends Equatable {
  const SellerLiveNotifsState();
}

class SellerLiveNotifsInitial extends SellerLiveNotifsState {
  @override
  List<Object> get props => [];
}

class Error extends SellerLiveNotifsState {
  final String error;

  const Error({required this.error});

  @override
  List<Object?> get props => [];
}

class Loaded extends SellerLiveNotifsState {
  final Bidlist bidlist;
  final FindStockbyId stock;
  final NotifsBasedOnStock notifs;
  final bool isLocked;

  const Loaded(
      {required this.bidlist,
      required this.stock,
      required this.notifs,
      this.isLocked = false});

  @override
  List<Object> get props => [bidlist, stock, notifs, isLocked];
}
