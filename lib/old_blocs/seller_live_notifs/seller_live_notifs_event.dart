part of 'seller_live_notifs_bloc.dart';

abstract class SellerLiveNotifsEvent extends Equatable {
  const SellerLiveNotifsEvent();
}

class Load extends SellerLiveNotifsEvent {
  final int stockId;

  const Load({required this.stockId});

  @override
  List<Object> get props => [stockId];
}

class WithdrawLot extends SellerLiveNotifsEvent {
  // final int customerId;
  // final String url;
  final int stockId;

  const WithdrawLot({required this.stockId});

  @override
  List<Object> get props => [stockId];
}

class RejectNegotiation extends SellerLiveNotifsEvent {
  // final int customerId;
  // final String url;
  final int stockId;

  const RejectNegotiation({required this.stockId});

  @override
  List<Object> get props => [stockId];
}

class SubmitNegotiation extends SellerLiveNotifsEvent {
  // final int customerId;
  final int expectedPrice;
  final int stockId;

  const SubmitNegotiation({required this.stockId, required this.expectedPrice});

  @override
  List<Object> get props => [stockId, expectedPrice];
}

class AcceptBid extends SellerLiveNotifsEvent {
  // final int customerId;
  final int bidId;
  final int stockId;

  const AcceptBid({required this.stockId, required this.bidId});

  @override
  List<Object> get props => [stockId, bidId];
}

class Lock extends SellerLiveNotifsEvent {
  // final int customerId;
  final bool isLocked;

  const Lock(this.isLocked);

  @override
  List<Object> get props => [];
}

class SellerWatchingNotifs extends SellerLiveNotifsEvent {
  final int stockId;

  const SellerWatchingNotifs({required this.stockId});

  @override
  List<Object> get props => [stockId];
}
