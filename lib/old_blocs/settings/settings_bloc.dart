import 'package:bloc/bloc.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:equatable/equatable.dart';

import '../../core/utils/tools.dart';

part 'settings_event.dart';
part 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsBlocEvent, SettingsBlocState> {
  SettingsBloc() : super(SettingsBlocInitial()) {
    on<LoadSettings>((event, emit) {
      bool toneFF = false;
      bool vibFF = false;
      if (isToneAllowed()) {
        toneFF = true;
      } else {
        toneFF = false;
      }
      if (isVibrationAllowed()) {
        vibFF = true;
      } else {
        vibFF = false;
      }

      emit(SettingsLoaded(toneF: toneFF, vibF: vibFF));
    });

    on<UpdateToneEvent>((event, emit) {
      if (readFromStorage(toneF) == "TRUE") {
        writeToStorage(toneF, "FALSE");
      } else {
        writeToStorage(toneF, "TRUE");
      }
      emit(SettingsLoaded(
          toneF: readFromStorage(toneF) == "TRUE" ? true : false,
          vibF: readFromStorage(vibF) == "TRUE" ? true : false));
    });

    on<UpdateVibEvent>((event, emit) {
      if (readFromStorage(vibF) == "TRUE") {
        writeToStorage(vibF, "FALSE");
      } else {
        writeToStorage(vibF, "TRUE");
      }
      emit(SettingsLoaded(
          toneF: readFromStorage(toneF) == "TRUE" ? true : false,
          vibF: readFromStorage(vibF) == "TRUE" ? true : false));
    });
  }
}
