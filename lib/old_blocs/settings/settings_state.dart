part of 'settings_bloc.dart';

abstract class SettingsBlocState extends Equatable {
  const SettingsBlocState();

  @override
  List<Object> get props => [];
}

class SettingsBlocInitial extends SettingsBlocState {}

class SettingsLoaded extends SettingsBlocState {
  final bool toneF;
  final bool vibF;

  const SettingsLoaded({required this.toneF, required this.vibF});

  @override
  List<Object> get props => [toneF, vibF];
}
