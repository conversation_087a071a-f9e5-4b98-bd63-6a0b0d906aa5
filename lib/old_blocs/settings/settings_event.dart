part of 'settings_bloc.dart';

abstract class SettingsBlocEvent extends Equatable {
  const SettingsBlocEvent();

  @override
  List<Object> get props => [];
}

class LoadSettings extends SettingsBlocEvent {}

class UpdateToneEvent extends SettingsBlocEvent {
  final bool toneF;

  const UpdateToneEvent({required this.toneF});
}

class UpdateVibEvent extends SettingsBlocEvent {
  final bool vibF;

  const UpdateVibEvent({required this.vibF});
}
