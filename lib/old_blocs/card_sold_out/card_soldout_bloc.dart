import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'card_soldout_event.dart';
part 'card_soldout_state.dart';

class CardSoldBloc extends Bloc<CardSoldoutEvent, CardSoldoutState> {
  var openFlag = -1;
  var viewTypeString = "";

  CardSoldBloc() : super(CardSoldoutInitial()) {
    on<OpenSoldStatus>((event, emit) {
      openFlag = event.openFlag;
      emit(StockSoldOpenState(openFlag: openFlag, viewString: ""));
    });

    on<ViewSoldType>((event, emit) async {
      viewTypeString = event.typeRadio;
      emit(StockSoldOpenState(openFlag: openFlag, viewString: viewTypeString));
    });
  }
}
