part of 'card_soldout_bloc.dart';

abstract class CardSoldoutState extends Equatable {
  const CardSoldoutState();

  @override
  List<Object> get props => [];
}

class CardSoldoutInitial extends CardSoldoutState {}

class StockSoldOpenState extends CardSoldoutState {
  final int openFlag;
  final String viewString;

  const StockSoldOpenState({required this.openFlag, required this.viewString});

  @override
  List<Object> get props => [openFlag, viewString];
}
