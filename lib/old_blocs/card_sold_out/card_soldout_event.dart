part of 'card_soldout_bloc.dart';

abstract class CardSoldoutEvent extends Equatable {
  const CardSoldoutEvent();

  @override
  List<Object> get props => [];
}

class OpenSoldStatus extends CardSoldoutEvent {
  final int openFlag;

  const OpenSoldStatus({required this.openFlag});
}

class ViewSoldType extends CardSoldoutEvent {
  final String typeRadio;

  const ViewSoldType({required this.typeRadio});
}
