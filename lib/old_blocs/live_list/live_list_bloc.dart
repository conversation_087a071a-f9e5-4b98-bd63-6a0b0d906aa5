import 'package:bloc/bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

import '../../old_models/live_auction_list.dart';

part 'live_list_event.dart';
part 'live_list_state.dart';

class LiveListBloc extends Bloc<LiveListEvent, LiveListState> {
  LiveListBloc() : super(LiveListInitial()) {
    on<Load>((event, emit) async {
      try {
        final list = await NetworkController().getLiveAuctionList();
        var reversed = list.data.reversed.toList();
        list.data = reversed;
        emit(Loaded(list: list));
      } catch (e) {
        emit(const Loaded(list: null));
      }
    });
  }
}
