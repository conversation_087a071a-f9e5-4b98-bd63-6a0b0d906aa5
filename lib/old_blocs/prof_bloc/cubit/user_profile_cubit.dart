import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../bai_models/area_of_business_res.dart';
import '../../../bai_models/edit_area_req.dart';
import '../../../bai_models/edit_profile_req.dart';
import '../../../bai_models/pricing_res.dart';
import '../../../bai_models/seller_categories_res.dart';
import '../../../bai_models/upload_media_res.dart';
import '../../../bai_models/user_profile_v2_res.dart';
import '../../../bai_models/user_v2_res.dart';
import '../../../core/network/network_controller.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/safe_print.dart';
import '../../../core/utils/tools.dart';

part 'user_profile_state.dart';

class UserProfileCubit extends Cubit<UserprofileState> {
  UserProfileCubit() : super(UserprofileInitial());
  var networkController = NetworkController();
  Future<void> deleteAccount() async {
    emit(UserprofileInitial());

    try {
      var response = await networkController.deleteAccountApi();

      emit(AccountDelete(msg: response.statusDescription));
    } catch (e) {
      emit(Message(msg: e.toString()));
    }
  }

  Future<void> getProfile() async {
    try {
      emit(UserprofileInitial());

      final accountData =
          await networkController.getUserProfileV2(num.parse(getVendorId()));
      SellerCategoresRes sellerData;
      if (!isBuyer()) {
        sellerData = await networkController.getSellerCategories();
        var pricing = await networkController.getPricing();
        var areas = await networkController.getareaOfBusiness();
        emit(
          AccountDataFetched(
            account: accountData,
            sellerData: sellerData,
            pricing: pricing,
            areaOfBusiness: areas,
            tec2: TextEditingController(),
            tec3: TextEditingController(),
            tec1: TextEditingController(),
          ),
        );
      } else {
        sellerData = SellerCategoresRes();
        emit(
          AccountDataFetched(
            account: accountData,
            sellerData: sellerData,
            tec2: TextEditingController(),
            tec3: TextEditingController(),
            tec1: TextEditingController(),
          ),
        );
      }
    } catch (e) {
      safePrint(e);
      alert(sSomethingWentWrong);
    }
  }

  Future<void> editProfile({
    String? location,
    String? email,
    String? businessEmail,
    String? businessPhone,
    String? gstType,
    String? profilePic,
    Uint8List? profileBytes,
  }) async {
    emit(UserprofileInitial());
    try {
      UploadMediaRes? mediaUrl;

      if (kIsWeb) {
        if (profileBytes != null) {
          mediaUrl = await networkController.uploadMediaWeb(profileBytes);
        }
      } else {
        if (profilePic != null) {
          mediaUrl = await networkController.uploadMedia(profilePic);
        }
      }

      var req = EditProfileReq(
        email: email,
        businessEmail: businessEmail,
        businessPhone: businessPhone,
        url: mediaUrl?.fileUrl,
        gstType: gstType,
        address: Address(sellingAddressLine1: location),
      );

      await NetworkController().updateAccountDetails(req: req);
      emit(const UpdateSuccess());
    } catch (e) {
      alert(e.toString());
    }
  }

  // Future<void> loadUserProfile(dynamic vendorID) async {
  //   emit(UserprofileInitial());
  //   try {
  //     var res = await networkController.getUserProfileV2(vendorID);
  //     emit(ProfileLoaded(userProfileV2Res: res));
  //   } catch (e) {
  //     emit(Message(msg: e.toString()));
  //   }
  // }

  Future<void> uploadProfileItems({
    String? title,
    List<String>? data,
    int? id,
    List<Uint8List>? dataBytes,
  }) async {
    emit(UserprofileInitial());
    try {
      if (kIsWeb) {
        for (int i = 0; i < (dataBytes ?? []).length; i++) {
          var item = dataBytes?[i];
          var response1 =
              await networkController.uploadMediaWeb(item ?? Uint8List(0));
          var url = response1.fileUrl;
          await networkController.insertProfileImages(req: {
            "link_primary_key_name": "vendor_id",
            "link_primary_key_value": "$id",
            "url": url,
            "title": title ?? "certificate",
            "docType": title ?? "certificate"
          });
        }
      } else {
        for (int i = 0; i < (data ?? []).length; i++) {
          var item = data?[i];
          var response1 = await networkController.uploadMedia(item ?? "");
          var url = response1.fileUrl;
          await networkController.insertProfileImages(req: {
            "link_primary_key_name": "vendor_id",
            "link_primary_key_value": "$id",
            "url": url,
            "title": title ?? "certificate",
            "docType": title ?? "certificate"
          });
        }
      }
      emit(const UpdateSuccess());
    } catch (e) {
      alert(e.toString());
      // emit(MyAccountFailed());
    }
  }

  Future<void> editAccountProfile(EditMyAccountData account) async {
    if (state is AccountDataFetched) {
      final stateaccount = this.state as AccountDataFetched;

      final state = account;
      if (state.isEditable == true) {
        emit(UserprofileInitial());
        try {
          //edit profile
          UploadMediaRes? profilePic;
          profilePic = await networkController.uploadMedia(state.profilePic);
          await networkController.insertProfileImages(req: {
            "link_primary_key_name": "vendor_id",
            "link_primary_key_value": getVendorId(),
            "url": state.profilePic,
            "title": "profile",
            "docType": "image"
          });
          var response = await networkController.updateAccountWebsite(
            profilePic: profilePic.fileUrl,
            website: state.website,
          );
          if (response.status == 200) {
            final accountData =
                await networkController.getUserProfileV2(getVendorId());
            SellerCategoresRes sellerData;
            if (!isBuyer()) {
              sellerData = await networkController.getSellerCategories();
              emit(
                AccountDataFetched(
                  account: accountData,
                  sellerData: sellerData,
                  tec2: TextEditingController(),
                  tec3: TextEditingController(),
                  tec1: TextEditingController(),
                ),
              );
            } else {
              emit(
                AccountDataFetched(
                  account: accountData,
                  sellerData: stateaccount.sellerData,
                  tec2: TextEditingController(),
                  tec3: TextEditingController(),
                  tec1: TextEditingController(),
                ),
              );
            }
          }
        } catch (e) {
          alert(e.toString());
          emit(AccountDataFetched(
            account: stateaccount.account,
            sellerData: stateaccount.sellerData,
            isEditable: !state.isEditable,
            tec2: stateaccount.tec2,
            tec3: stateaccount.tec3,
            tec1: stateaccount.tec1,
          ));
        }
      } else {
        emit(AccountDataFetched(
          account: stateaccount.account,
          sellerData: stateaccount.sellerData,
          isEditable: !state.isEditable,
          tec2: stateaccount.tec2,
          tec3: stateaccount.tec3,
          tec1: stateaccount.tec1,
        ));
      }
    }
  }

  Future<void> updateArea(EditAreaReq req) async {
    emit(UserprofileInitial());
    try {
      var res = await NetworkController().editArea(req);
      emit(const UpdateSuccess());
    } catch (e) {
      alert("Failed to add area.");
      const UpdateSuccess();
    }
  }
}
