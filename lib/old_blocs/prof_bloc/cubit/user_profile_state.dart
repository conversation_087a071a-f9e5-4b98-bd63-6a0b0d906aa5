part of 'user_profile_cubit.dart';

class UserprofileState extends Equatable {
  const UserprofileState();

  @override
  List<Object> get props => [];
}

class UserprofileInitial extends UserprofileState {}

class AccountDelete extends UserprofileState {
  final String? msg;

  const AccountDelete({this.msg});
  @override
  List<Object> get props => [];
}

class EditMyAccountData extends UserprofileState {
  final bool isEditable;
  final String website;
  final String profilePic;

  const EditMyAccountData(
      {required this.isEditable,
      required this.website,
      required this.profilePic});

  @override
  List<Object> get props => [isEditable, website, profilePic];
}

class ProfileLoaded extends UserprofileState {
  final bool isEditable;
  final String firstName;
  final String location;
  final String phoneNumber;
  final String email;
  final String website;
  final UserV2Res? userV2Res;
  final UserProfileV2Res? userProfileV2Res;
  const ProfileLoaded({
    this.userV2Res,
    this.userProfileV2Res,
    this.isEditable = false,
    this.firstName = "",
    this.location = "",
    this.phoneNumber = "",
    this.email = "",
    this.website = "",
  });
  @override
  List<Object> get props =>
      [isEditable, firstName, location, phoneNumber, email, website];
}

class AccountDataFetched extends UserprofileState {
  final UserProfileV2Res account;
  final SellerCategoresRes sellerData;
  final AreaOfBusinessRes? areaOfBusiness;
  final bool isEditable;
  final TextEditingController tec1;
  final TextEditingController tec2;
  final TextEditingController tec3;
  final List<PricingRes>? pricing;

  const AccountDataFetched({
    required this.account,
    required this.sellerData,
    this.areaOfBusiness,
    this.isEditable = false,
    required this.tec1,
    required this.tec2,
    required this.tec3,
    this.pricing,
  });

  @override
  List<Object> get props => [];
}

class Message extends UserprofileState {
  final String? msg;

  const Message({this.msg});
  @override
  List<Object> get props => [];
}

class UpdateSuccess extends UserprofileState {
  final String? msg;

  const UpdateSuccess({this.msg});
  @override
  List<Object> get props => [];
}
