import 'package:connectone/old_blocs/sold_out/soldout_event.dart';
import 'package:connectone/old_blocs/sold_out/soldout_state.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/sold_out.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/utils/tools.dart';

class SoldOutBloc extends Bloc<SoldEvent, SoldOutState> {
  final _networkController = NetworkController();
  var postsList = <Content>[];
  var dateSelected = "";
  var openFlag = -1;
  var viewTypeString = "";
  int _page = 1;

  SoldOutBloc() : super(SoldOutsInitial()) {
    on<LoadSoldStocks>((event, emit) async {
      print('${getCustomerId()}555555555');
      emit(SoldOutsInitial());
      try {
        var sum = 0;
        var amount = 0;
        var response = await _networkController.getSoldOutStocks(
          readFromStorage(customerId),
        );
        if (response.statusCode == 200) {
          postsList = SoldOut.fromJson(response.data).content;
          //  print(postsList);
        }
        for (var element in postsList) {
          sum += element.quantity.toInt();
        }
        for (var element in postsList) {
          if (element.amount != null) {
            amount += element.amount!.toInt();
          }
        }
        // var margindetails = await _networkController.getMarginDetails();
        // var marginhistory =
        //     await _networkController.getMarginHistory(getCustomerId());
        //
        // await _networkController.getMarginHistory(getCustomerId());

        emit(SoldOutStockLoaded(
            content: postsList, qty: sum, amt: amount, page: 0));
        _page = 1;
      } catch (e) {
        print('${e}00000000000000000000000000000000000000');
      }
    });
    on<LoadNextSoldStocks>((event, emit) async {
      // emit(MystocksLoading());
      emit(SoldOutsInitial());
      try {
        var sum = 0;
        var amount = 0;
        final response = await _networkController.getSoldOutStocksNextPage(
            customerId: getCustomerId(), page: _page);
        if (response.statusCode == 200) {
          postsList = SoldOut.fromJson(response.data).content;
          print('1111${postsList}2222');
        }
        for (var element in postsList) {
          sum += element.quantity.toInt();
        }
        for (var element in postsList) {
          if (element.amount != null) {
            amount += element.amount!.toInt();
          }
        }
        // var margindetails = await _networkController.getMarginDetails();
        // var marginhistory =
        //     await _networkController.getMarginHistory(getCustomerId());

        // await _networkController.getMarginHistory(getCustomerId());
        // print('22222222222222222222222222222222222222222222');
        emit(SoldOutStockLoaded(
            content: [...event.content, ...postsList],
            qty: sum,
            amt: amount,
            page: 0));
        _page = _page + 1;
      } catch (e) {
        print(e);
      }
    });
    on<StocksSearch>((event, emit) async {
      postsList.clear();
      // print(event.stockid);
      var sum = 0;
      var amount = 0;
      var response = await _networkController.getSoldStocksSearchByStockId(
          readFromStorage(customerId), event.stockId);
      postsList = response.content;
      // print(postsList.toString());

      for (var element in postsList) {
        sum += element.quantity.toInt();
      }
      for (var element in postsList) {
        if (element.amount != null) {
          amount += element.amount!.toInt();
        }
      }
      emit(SoldOutsInitial());
      emit(SoldOutStockLoaded(
          content: postsList, qty: sum, amt: amount, page: 0));
    });

    on<StockSearchByLot>((event, emit) async {
      postsList.clear();
      // print(event.lotno);
      var sum = 0;
      var amount = 0;
      var response = await _networkController.getSoldStocksSearchByLotNo(
          readFromStorage(customerId), event.lotNo, event.deliveryDate);
      postsList = response.content;
      //  print(postsList.toString());

      for (var element in postsList) {
        sum += element.quantity.toInt();
      }
      for (var element in postsList) {
        if (element.amount != null) {
          amount += element.amount!.toInt();
        }
      }
      emit(SoldOutsInitial());
      emit(SoldOutStockLoaded(
          content: postsList, qty: sum, amt: amount, page: 0));
    });

    on<DateSelected>((event, emit) async {
      dateSelected = event.deliveryDate;
    });
  }
}
