import 'package:connectone/old_models/sold_out.dart';

abstract class SoldOutState {
  const SoldOutState();

  @override
  List<Object> get props => [];
}

class SoldOutsInitial extends SoldOutState {}

class SoldOutStockLoaded extends SoldOutState {
  final List<Content> content;
  final int qty;
  final int amt;
  final int page;

  const SoldOutStockLoaded(
      {required this.page,
      required this.qty,
      required this.amt,
      required this.content});

  @override
  List<Object> get props => [content];
}

class SoldOutNextStockLoaded extends SoldOutState {
  final List<Content> content;

  final int page;

  const SoldOutNextStockLoaded({required this.page, required this.content});

  @override
  List<Object> get props => [content];
}
