abstract class SoldEvent {
  const SoldEvent();

  @override
  List<Object> get props => [];
}

class LoadSoldStocks extends SoldEvent {
  // final int page;
}

class LoadNextSoldStocks extends SoldEvent {
  final List<dynamic> content;

  LoadNextSoldStocks(this.content);
}

class StocksSearch extends SoldEvent {
  final String stockId;

  const StocksSearch({required this.stockId});
}

class StockSearchByLot extends SoldEvent {
  final String lotNo;
  final String deliveryDate;

  const StockSearchByLot({required this.lotNo, required this.deliveryDate});
}

class DateSelected extends SoldEvent {
  final String deliveryDate;

  const DateSelected({required this.deliveryDate});
}

class LoadQuantity extends SoldEvent {}

class LoadTotalAmount extends SoldEvent {}
