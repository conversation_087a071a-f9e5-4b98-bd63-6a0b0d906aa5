part of 'bought_table_list_cubit.dart';

abstract class BoughtTableListState extends Equatable {
  const BoughtTableListState();
}

class Loading extends BoughtTableListState {
  @override
  List<Object> get props => [];
}

class Loaded extends BoughtTableListState {
  final BoughtList? boughtList;

  const Loaded({this.boughtList});

  @override
  List<Object> get props => [];
}

class Message extends BoughtTableListState {
  @override
  List<Object> get props => [];
}
