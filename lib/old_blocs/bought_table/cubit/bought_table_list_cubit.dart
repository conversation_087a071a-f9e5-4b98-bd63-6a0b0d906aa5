import 'package:bloc/bloc.dart';
import 'package:connectone/old_models/bought_model.dart';
import 'package:equatable/equatable.dart';

import '../../../core/network/network_controller.dart';

// import '../../controllers/network_controller.dart';

part 'bought_table_list_state.dart';

class BoughtTableListCubit extends Cubit<BoughtTableListState> {
  BoughtTableListCubit() : super(Loading());

  Future<void> loadBought(
      String stockId, String newFrom, String newTo, int page, int limit) async {
    emit(Loading());
    try {
      final body = await NetworkController()
          .getBoughtTable(stockId, newFrom, newTo, page, limit);
      emit(Loaded(boughtList: body));
    } catch (e) {
      emit(const Loaded(boughtList: null));
    }
  }
}
