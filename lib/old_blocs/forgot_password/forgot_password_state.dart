import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class ForgotPasswordState extends MyState {
  final bool confirmationCode;
  final bool emailEditable;
  final bool isLoading;
  final TextEditingController usernameController =
      TextEditingController(text: '<EMAIL>');
  final TextEditingController passwordController =
      TextEditingController(text: '12345678');

  ForgotPasswordState({
    this.confirmationCode = false,
    this.emailEditable = true,
    this.isLoading = false,
  });

  ForgotPasswordState copyWith(
      {bool? confirmationCode,
      bool? emailEditable,
      bool? loginSuccess,
      bool? isLoading,
      bool? remeberMe}) {
    return ForgotPasswordState(
      confirmationCode: confirmationCode ?? this.confirmationCode,
      emailEditable: emailEditable ?? this.emailEditable,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [
        confirmationCode,
        emailEditable,
        isLoading,
        usernameController,
        passwordController
      ];
}

abstract class MyState extends Equatable {
  const MyState();
}
