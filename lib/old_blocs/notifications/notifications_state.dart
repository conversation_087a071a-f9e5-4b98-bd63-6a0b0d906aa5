part of 'notifications_bloc.dart';

abstract class NotificationsState extends Equatable {
  const NotificationsState();
}

class NotificationsInitial extends NotificationsState {
  @override
  List<Object> get props => [];
}

class NotificationsLoaded extends NotificationsState {
  final NotificationsListRes notifications;

  const NotificationsLoaded(this.notifications);

  @override
  List<Object> get props => [notifications];
}
