import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';
import '../../core/utils/constants.dart';
import '../../core/utils/tools.dart';
import '../../old_models/notifications_list_res.dart';

part 'notifications_event.dart';
part 'notifications_state.dart';

class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final NetworkController networkController;

  NotificationsBloc(this.networkController) : super(NotificationsInitial()) {
    on<NotificationsEvent>((event, emit) async {
      try {
        final notifications = await networkController.getNotificationsnew();
        emit(NotificationsLoaded(notifications));
      } catch (e) {
        alert(sSomethingWentWrong);
        log(e.toString());
      }
    });
  }
}
