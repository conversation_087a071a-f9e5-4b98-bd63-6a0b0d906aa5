import 'package:connectone/bai_models/user_v2_res.dart';

class LoginState extends MyState {
  final bool isLoading;
  final bool loginSuccess;
  final UserV2Res? userV2;
  final String? orderId;

  const LoginState({
    this.isLoading = false,
    this.loginSuccess = false,
    this.userV2,
    this.orderId,
  });

  LoginState copyWith({
    bool? isLoading,
    bool? loginSuccess,
    UserV2Res? userV2,
    String? orderId,
  }) {
    return LoginState(
      isLoading: isLoading ?? this.isLoading,
      loginSuccess: loginSuccess ?? this.loginSuccess,
      userV2: userV2 ?? this.userV2,
      orderId: orderId ?? this.orderId,
    );
  }
}

abstract class MyState {
  const MyState();
}
