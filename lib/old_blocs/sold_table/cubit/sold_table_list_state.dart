part of 'sold_table_list_cubit.dart';

abstract class SoldTableListState extends Equatable {
  const SoldTableListState();

  @override
  List<Object> get props => [];
}

class Loading extends SoldTableListState {
  @override
  List<Object> get props => [];
}

class Loaded extends SoldTableListState {
  final SoldList? soldList;

  const Loaded({this.soldList});

  @override
  List<Object> get props => [];
}

class Message extends SoldTableListState {
  @override
  List<Object> get props => [];
}
