import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../core/network/network_controller.dart';
import '../../../old_models/sold_model.dart';

part 'sold_table_list_state.dart';

class SoldTableListCubit extends Cubit<SoldTableListState> {
  SoldTableListCubit() : super(Loading());

  Future<void> loadSold(
      String stockId, String newFrom, String newTo, int page, int limit) async {
    emit(Loading());
    try {
      final body = await NetworkController()
          .getSoldTable(stockId, newFrom, newTo, page, limit);
      emit(Loaded(soldList: body));
    } catch (e) {
      print('${e}111111111112222222222222233333333333333333444444444444444');
      emit(const Loaded(soldList: null));
    }
  }
}
