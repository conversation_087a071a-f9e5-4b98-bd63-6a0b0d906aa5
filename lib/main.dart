import 'dart:async';
import 'dart:developer' as logger;
// import 'dart:io';
import 'dart:math';

import 'package:app_links/app_links.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:connectone/bai_blocs/add_product/cubit/add_product_cubit.dart';
import 'package:connectone/bai_blocs/add_purchase_order/cubit/add_purchase_order_cubit.dart';
import 'package:connectone/bai_blocs/assign/cubit/assign_cubit.dart';
import 'package:connectone/bai_blocs/buyer_reg/cubit/buyer_registration_cubit.dart';
import 'package:connectone/bai_blocs/color/color_cubit.dart';
import 'package:connectone/bai_blocs/cubit/notification_count_cubit.dart';
import 'package:connectone/bai_blocs/cubit/status_dropdown_cubit.dart';
import 'package:connectone/bai_blocs/history/history_cubit.dart';
import 'package:connectone/bai_blocs/insert_stock/cubit/insert_stock_cubit.dart';
import 'package:connectone/bai_blocs/invite_status/invite_status_cubit.dart';
import 'package:connectone/bai_blocs/mr_grouping/cubit/mr_grouping_cubit.dart';
import 'package:connectone/bai_blocs/mr_summary/cubit/mr_summary_cubit.dart';
import 'package:connectone/bai_blocs/notification/notification_data_cubit.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_blocs/product_list/cubit/product_list_cubit.dart';
import 'package:connectone/bai_blocs/profile/cubit/profile_cubit.dart';
import 'package:connectone/bai_blocs/purchases_bloc/purchases_cubit.dart';
import 'package:connectone/bai_blocs/quotes_only/cubit/quotes_only_cubit.dart';
import 'package:connectone/bai_blocs/rating/rating_cubit.dart';
import 'package:connectone/bai_blocs/reviews/review_cubit.dart';
import 'package:connectone/bai_blocs/seller_reg/cubit/seller_registration_cubit.dart';
import 'package:connectone/bai_blocs/site/cubit/site_cubit.dart';
import 'package:connectone/bai_blocs/site_details/site_details_cubit.dart';
import 'package:connectone/bai_blocs/team_member/cubit/team_member_cubit.dart';
import 'package:connectone/bai_screens/single_mr_screen.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/old_blocs/bought_table/cubit/bought_table_list_cubit.dart';
import 'package:connectone/old_blocs/home/<USER>';
import 'package:connectone/old_blocs/live_auction/live_auction_bloc.dart';
import 'package:connectone/old_blocs/login/login_bloc.dart';
import 'package:connectone/old_blocs/login/login_state.dart';
import 'package:connectone/old_blocs/my_account/my_account_cubit.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_bloc.dart';
import 'package:connectone/old_blocs/prof_bloc/cubit/user_profile_cubit.dart';
import 'package:connectone/old_blocs/sold_table/cubit/sold_table_list_cubit.dart';
import 'package:connectone/old_blocs/sold_out/soldout_bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_screens/offline_filters/search_filters_bloc.dart';
import 'package:connectone/old_screens/offline_filters/offline_filters_bloc.dart';
import 'package:connectone/old_screens/splash_screen.dart';
import 'package:connectone/core/utils/app_pages.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/sound_and_vibration_utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:location/location.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

import 'old_blocs/get_comments/get_comments_bloc.dart';
import 'old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'old_blocs/rating_bloc/ratingpage_bloc.dart';
import 'core/utils/data_storage.dart';
import 'firebase_options.dart';
import 'old_models/orgaisation_model.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await GetStorage.init();
  await _initializeFirebase();

  // Initialize Mapbox access token
  MapboxOptions.setAccessToken("pk.eyJ1IjoiY29ubmVjdG9uZWNsdWIiLCJhIjoiY2txMjd3bjl6MDZicjJvczNrbXhmaWVzbSJ9.unLFJ-b5u7-OHlSplUJWrw");
  final runnableApp = _buildRunnableApp(
    isWeb: kIsWeb,
    webAppWidth: 560,
    app: const MyApp(),
  );
  runApp(runnableApp);
}

Future<void> _initializeFirebase() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    await FirebaseMessaging.instance.getToken();
  } catch (e) {
    logger.log(e.toString());
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  if (PlatformUtils.isMobile) {
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterError(
        errorDetails,
        fatal: false,
      );
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(
        error,
        stack,
        fatal: false,
      );
      return true;
    };
  }
  FirebaseAnalytics.instance.logAppOpen();
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  logger.log("Handling a background message");
  var notificationData = message.data;
  Map<String, String> notificationPayload =
      notificationData.map((key, value) => MapEntry(key, value.toString()));
  var notificationCode = notificationPayload['notification_code'];
  if (notificationCode == "OFUD" ||
      notificationCode == "DCCP" ||
      notificationCode == "NGCP" ||
      notificationCode == "LAST" ||
      notificationCode == "LASS") {
    play2();
  }
  AwesomeNotifications().createNotification(
    content: NotificationContent(
      id: Random().nextInt(1000),
      channelKey: 'connectone_channel',
      title: notificationData['subject'],
      body: notificationData['content'],
      payload: notificationPayload,
      actionType: ActionType.Default,
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late Future<bool> _authFuture;

  @override
  void initState() {
    super.initState();
    _authFuture = _startLocalAuth();
  }

  Future<bool> _startLocalAuth() async {
    // return await _authenticate();
    return true;
  }

  // Future<bool> _authenticate() async {
  //   final LocalAuthentication auth = LocalAuthentication();

  //   final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
  //   if (!canAuthenticateWithBiometrics) {
  //     return true;
  //   }

  //   final bool canAuthenticate =
  //       canAuthenticateWithBiometrics || await auth.isDeviceSupported();
  //   if (!canAuthenticate) {
  //     return true;
  //   }

  //   final List<BiometricType> availableBiometrics =
  //       await auth.getAvailableBiometrics();
  //   if (availableBiometrics.isEmpty) {
  //     return true;
  //   }

  //   try {
  //     final bool didAuthenticate = await auth.authenticate(
  //       localizedReason: 'Please authenticate to access the app.',
  //       options: const AuthenticationOptions(
  //         stickyAuth: true,
  //         sensitiveTransaction: true,
  //         biometricOnly: true,
  //       ),
  //     );
  //     return didAuthenticate;
  //   } on PlatformException catch (e) {
  //     safePrint(e);
  //     return false;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => LoginBloc(const LoginState())),
        BlocProvider(create: (_) => MyStocksBloc()),
        BlocProvider(create: (_) => SoldOutBloc()),
        BlocProvider(create: (_) => LiveAuctionBloc()),
        BlocProvider(create: (_) => OfflineMainBloc()),
        BlocProvider(create: (_) => OfflineFilterCubit()),
        BlocProvider(create: (_) => SearchFilterBloc()),
        BlocProvider(create: (_) => RatingPageBloc()),
        BlocProvider(create: (_) => GetCommentsBloc()),
        BlocProvider(create: (_) => BoughtTableListCubit()),
        BlocProvider(create: (_) => SoldTableListCubit()),
        BlocProvider(create: (_) => PurchasesCubit()),
        BlocProvider(create: (_) => InsertStockCubit()),
        BlocProvider(create: (_) => BuyerRegistrationCubit()),
        BlocProvider(create: (_) => SellerRegistrationCubit()),
        BlocProvider(create: (_) => AddEditProductCubit()),
        BlocProvider(create: (_) => HomeBloc()),
        BlocProvider(create: (_) => AssignCubit()),
        BlocProvider(create: (_) => AddPurchaseOrderCubit()),
        BlocProvider(create: (_) => ProductListCubit()),
        BlocProvider(create: (_) => ProfileCubit()),
        BlocProvider(create: (_) => HistoryCubit()),
        BlocProvider(create: (_) => ReviewCubit()),
        BlocProvider(create: (_) => OffersCubit()),
        BlocProvider(create: (_) => TeamMemberCubit()),
        BlocProvider(create: (_) => ColorCubit()),
        BlocProvider(create: (_) => SiteCubit()),
        BlocProvider(create: (_) => MyAccountCubit()),
        BlocProvider(create: (_) => NotificationDataCubit()),
        BlocProvider(create: (_) => StatusDropdownCubit()),
        BlocProvider(create: (_) => NotificationCountCubit()),
        BlocProvider(create: (_) => UserProfileCubit()),
        BlocProvider(create: (_) => MrGroupingCubit()),
        BlocProvider(create: (_) => QuotesOnlyCubit()),
        BlocProvider(create: (_) => InviteStatusCubit()),
        BlocProvider(create: (_) => RatingCubit()),
        BlocProvider(create: (_) => SiteDetailsCubit()),
        BlocProvider(create: (_) => MrSummaryCubit()),
      ],
      child: BlocListener<ColorCubit, Color>(
        listener: (context, state) {
          setState(() {
            AppColors.primaryColor = state;
          });
        },
        child: GetMaterialApp(
          debugShowCheckedModeBanner: false,
          navigatorKey: navigatorKey,
          theme: ThemeData(
            primaryColor: AppColors.primaryColor,
            scaffoldBackgroundColor: Colors.white,
            useMaterial3: false,
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.primaryColor,
              titleTextStyle: const TextStyle(
                fontFamily: 'archivo',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              centerTitle: true,
              titleSpacing: 0,
            ),
            progressIndicatorTheme:
                ProgressIndicatorThemeData(color: AppColors.primaryColor),
            fontFamily: "archivo",
          ),
          home: FutureBuilder<bool>(
            future: _authFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(child: CircularProgressIndicator()),
                );
              } else if (snapshot.hasError || !snapshot.data!) {
                return const AuthFailed();
              } else {
                if (snapshot.data == true) {
                  return const ConnectOneApp();
                } else {
                  return const AuthFailed();
                }
              }
            },
          ),
          // navigatorObservers: [FlutterSmartDialog.observer],
          builder: (context, child) {
            return MediaQuery(
              data: MediaQuery.of(context)
                  .copyWith(textScaler: const TextScaler.linear(1.05)),
              child: child!,
            );
          },
          getPages: AppPages.routes,
        ),
      ),
    );
  }
}

class AuthFailed extends StatelessWidget {
  const AuthFailed({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    FlutterNativeSplash.remove();
    return Scaffold(
      body: const Center(child: Text('Auth failed, please restart the app.')),
      appBar: AppBar(
        title: const Text("Auth Failed"),
        systemOverlayStyle:
            const SystemUiOverlayStyle(statusBarColor: Colors.transparent),
        elevation: 0,
      ),
    );
  }
}

class ConnectOneApp extends StatefulWidget {
  const ConnectOneApp({Key? key}) : super(key: key);

  @override
  State<ConnectOneApp> createState() => _ConnectOneAppState();
}

class _ConnectOneAppState extends State<ConnectOneApp> {
  StreamSubscription? _sub;
  final Connectivity _connectivity = Connectivity();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  @override
  void initState() {
    super.initState();
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: NotificationController.onActionReceivedMethod,
    );

    _initializeApp();
  }

  Future<void> _initializeApp() async {
    _getOrganisations();
    _initAwesomeNotification();
    _setupConnectivityListener();
    if (PlatformUtils.isMobile) {
      FirebaseDatabase.instance.setPersistenceEnabled(false);
    }
    FlutterNativeSplash.remove();
    initDeepLinks();
  }

  @override
  void dispose() {
    _sub?.cancel();
    _linkSubscription?.cancel();
    super.dispose();
  }

  Future<void> initDeepLinks() async {
    _appLinks = AppLinks();

    // Handle links
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      debugPrint('onAppLink: $uri');
      openAppLink(uri);
    });
  }

  void openAppLink(Uri uri) async {
    if (kIsWeb) return;
    String? id = uri.queryParameters['id'];

    // Add a 4 sec delay
    await Future.delayed(const Duration(seconds: 4));

    Get.to(
      SingleMRScreen(
        notificationData: NotificationData(
          code: "code",
          subject: "",
          event: "event",
          content: "MR - $id",
          id: id,
        ),
      ),
    );
  }

  void _setupConnectivityListener() {
    _connectivity.onConnectivityChanged.listen((event) {
      if (event == ConnectivityResult.none) {
        alert("No / unstable internet!");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return const SplashScreen();
  }

  void _getOrganisations() async {
    _getNewOrganisation();
    _getOldOrganisation();
    _getRequestTypes();
  }

  Future<void> _getRequestTypes() async {
    try {
      var networkController = NetworkController();
      await networkController.getRequestTypes();
    } catch (e) {
      DataStorage.requestTypes = [];
      safePrint(e);
    }
  }

  Future<void> _getOldOrganisation() async {
    try {
      var networkController = NetworkController();
      var res = await networkController.getOrganisation();
      var data = OrganisationModel.fromJson(res.data);
      networkController.organisationData = data.data;
      firebaseBaseUrl = data.data?.tenantId ?? "";
      safePrint(res.data);
      if (data.status == 200) {
        await _sendFirebaseToken();
        if (isLoggedIn()) {
          await _sendLocation();
        }
      }
    } catch (e) {
      logger.log(e.toString());
    }
  }

  Future<void> _getNewOrganisation() async {
    try {
      var networkController = NetworkController();
      await networkController.getNewOrganisation();

      String keyName = isBuyer() ? "buyer_colour" : "seller_colour";
      String apiColor = DataStorage.configData
              ?.firstWhere(
                (element) => element?.keyName1 == keyName,
              )
              ?.valueName1 ??
          DataStorage.configData
              ?.firstWhere(
                (element) => element?.keyName1 == "frontend_primary_colour",
              )
              ?.valueName1 ??
          "#121212";

      Color myColor =
          Color(int.parse(apiColor.substring(1), radix: 16) + 0xFF000000);
      setState(() => AppColors.primaryColor = myColor);
    } catch (e) {
      safePrint(e);
    }
  }

  Future<void> _sendLocation() async {
    try {
      var position = await determinePosition();
      if (position != null) {
        var latitude = position.latitude;
        var longitude = position.longitude;
        if (latitude != null && longitude != null) {
          await NetworkController()
              .sendLocation(latitude: latitude, longitude: longitude);
        }
      }
    } catch (e) {
      safePrint(e);
    }
  }

  Future<LocationData?> determinePosition() async {
    final Location location = Location();
    bool serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) return null;
    }

    PermissionStatus permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) return null;
    }

    return await location.getLocation();
  }

  Future<void> _sendFirebaseToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    final fcmToken = await messaging.getToken();
    if (fcmToken != null) {
      await NetworkController().sendToken(fcmToken: fcmToken);
      logger.log("Token: $fcmToken");
    }
    messaging.onTokenRefresh.listen((fcmToken) async {
      if (isLoggedIn()) {
        await NetworkController().sendToken(fcmToken: fcmToken);
      }
    }).onError((err) {
      safePrint(err);
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      logger.log('Got a message whilst in the foreground!');
      logger.log('Message data: ${message.data}');
      var notificationData = message.data;
      Map<String, String> notificationPayload =
          notificationData.map((key, value) => MapEntry(key, value.toString()));
      var notificationCode = notificationPayload['notification_code'];
      if (notificationCode == "OFUD" ||
          notificationCode == "DCCP" ||
          notificationCode == "NGCP" ||
          notificationCode == "LAST" ||
          notificationCode == "LASS") {
        play2();
      }
      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: Random().nextInt(1000),
          channelKey: 'connectone_channel',
          title: notificationData['subject'],
          body: notificationData['content'],
          payload: notificationPayload,
          actionType: ActionType.Default,
        ),
      );
    });
  }

  void _initAwesomeNotification() {
    AwesomeNotifications().initialize(
      null,
      [
        NotificationChannel(
          channelGroupKey: 'connectone_channel_group',
          channelKey: 'connectone_channel',
          channelName: 'ConnectOne Notifications',
          channelDescription: 'Notification channel for ConnectOne',
          defaultColor: AppColors.primaryColor,
          ledColor: AppColors.primaryColor,
        ),
      ],
      channelGroups: [
        NotificationChannelGroup(
          channelGroupKey: 'connectone_channel_group',
          channelGroupName: 'ConnectOne Group',
        ),
      ],
      debug: false,
    );

    AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
      if (!isAllowed) {
        AwesomeNotifications().requestPermissionToSendNotifications();
      }
    });
  }
}

Widget _buildRunnableApp({
  required bool isWeb,
  required double webAppWidth,
  required Widget app,
}) {
  if (!isWeb) {
    return app;
  }

  return Center(
    child: ClipRect(
      child: SizedBox(
        width: webAppWidth,
        child: app,
      ),
    ),
  );
}
