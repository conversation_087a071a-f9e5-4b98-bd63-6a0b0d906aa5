import 'package:intl/intl.dart';

class TimeUtils {
  String formatDate(String date) {
    var dateValue = DateFormat("yyyy-MM-dd hh:mm:ss").parseUTC(date).toLocal();
    String formattedDate = DateFormat("LLL dd hh:mm aaa").format(dateValue);
    return formattedDate;
  }

  String formatBaiDate(String date) {
    var dateValue = DateFormat("yyyy-MM-dd hh:mm:ss").parseUTC(date).toLocal();
    // String formattedDate = DateFormat("dd MMM h:mm a").format(dateValue);
    String formattedDate = DateFormat("dd MMM yyyy").format(dateValue);
    // var newTime =
    //     DateTime(dateValue.year, dateValue.month, dateValue.day, 17, 30);
    // String formattedDate = DateFormat("dd MMM hh:mm a").format(newTime);
    return formattedDate;
  }

  String formatDateFb(String date) {
    var dateValue = DateFormat("yyyy-MM-dd hh:mm:ss").parseUTC(date).toLocal();
    String formattedDate = DateFormat("LLL dd hh:mm aaa").format(dateValue);
    return formattedDate;
  }
}

String timeToNewTime(String oldTime) {
  var inputFormat = DateFormat('HH:mm');
  var inputDate = inputFormat.parse(oldTime);
  var outputFormat = DateFormat('hh:mm a');
  var outputDate = outputFormat.format(inputDate);
  return outputFormat.format(outputFormat.parse(outputDate, true).toLocal());
}

String timeToNewTimeFirebase(String oldTime) {
  var inputFormat = DateFormat('yyyy-MM-DD HH:mm:ss');
  var inputDate = inputFormat.parse(oldTime);
  var outputFormat = DateFormat('hh:mm a');
  var outputDate = outputFormat.format(inputDate);
  return outputFormat.format(outputFormat.parse(outputDate, true).toLocal());
}

String dateToNewDate(String oldTime) {
  var inputFormat = DateFormat('yyyy-MM-ddTHH:mm');
  var inputDate = inputFormat.parse(oldTime);
  // var outputFormat = DateFormat('dd-MMM-yyyy hh:mm a');
  var outputFormat = DateFormat('hh:mm a');
  var outputDate = outputFormat.format(inputDate);
  return outputDate;
}

int calculateSecondsBehind({required DateTime timestamp}) {
  DateTime now = DateTime.now();
  Duration difference = now.difference(timestamp);
  int secondsBehind = difference.inSeconds;
  return secondsBehind < 0 ? 0 : secondsBehind;
}
