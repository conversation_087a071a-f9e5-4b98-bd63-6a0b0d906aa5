import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/firebase_response.dart';
import 'package:firebase_database/firebase_database.dart';
import '../../old_models/notifications_list_res.dart';
import 'constants.dart';

class NotificationsManager {
  int getCount(List<Datum>? list) {
    var count = 0;
    for (var i in list!) {
      if (i.readYn == "N") {
        count++;
      }
    }
    return count;
  }

  void intent(Datum notificationSelected) {
    if (getCustomerRole() == "MBMR" &&
            notificationSelected.notificationCode == "OFBR" ||
        notificationSelected.notificationCode == "LASD" ||
        notificationSelected.notificationCode == "OFSD" ||
        notificationSelected.notificationCode == "OFUD" ||
        notificationSelected.notificationCode == "DCCP" ||
        notificationSelected.notificationCode == "NGCP") {
    } else if (notificationSelected.notificationCode == "LAST" ||
        notificationSelected.notificationCode == "LASS") {
    } else if (notificationSelected.notificationCode == "" ||
        notificationSelected.notificationCode == null) {}
  }

  Future<num> getMaxBidId(String stockId) async {
    final List<FirebaseResponseSellerNotifs> list = [];
    num maxBid = 0;
    num maxBidId = 0;
    final ref = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/seller_bids');
    final snapshot = await ref.child(stockId.toString()).get();
    if (snapshot.exists) {
      for (var i = 0; i < snapshot.children.length; i++) {
        list.add(FirebaseResponseSellerNotifs.fromSnapshot(
            snapshot.children.elementAt(i)));
      }
      for (int i = 0; i < list.length; i++) {
        if (list[i].price != null && list[i].price! > maxBid) {
          maxBid = list[i].price ?? 0;
          maxBidId = list[i].bid_id ?? 0;
        }
      }
    }
    return maxBidId;
  }
}
