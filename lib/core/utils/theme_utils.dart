import 'package:flutter/material.dart';

import 'colors.dart';

ThemeData getMediumTheme() {
  return ThemeData(
    primaryColor: AppColors.primaryColor,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primaryColor,
    ),
    fontFamily: 'poppins',
  );
}

ThemeData getBoldTheme() {
  return ThemeData(
    primaryColor: AppColors.primaryColor,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primaryColor,
    ),
    fontFamily: 'poppins',
  );
}

String getTitleFont() {
  return 'poppins';
}

String getAvenirBlack() {
  return 'poppins';
}

TextStyle commonTextStyle() {
  return const TextStyle(fontWeight: FontWeight.bold, fontSize: 13);
}
