import 'package:flutter/material.dart';

class DataMap {
  static final Map<String, List<String>> _dataMap = {
    'ENQRY': ['OPOE', 'CPOE', 'EPOE'],
    'PORD': ['OPOR', 'CPOR'],
    'ADMN': ['ANPE'],
  };

  DataMap();

  static List<String>? getValues(String key) {
    return _dataMap[key];
  }
}

final Map<String, String> codeMappings = {
  'OPOE': 'Open',
  'CPOE': 'Closed',
  // 'NPOE': 'New',
  'EPOE': 'Pending',
  'OPOR': 'Open',
  'CPOR': 'Closed',
  // 'EPOR': 'Pending',
  'ANPE': 'New',
};

String getFullForm(String code) {
  return codeMappings[code] ?? 'N/A';
}

var largeStyle = const TextStyle(
  fontWeight: FontWeight.bold,
  fontSize: 20,
);
