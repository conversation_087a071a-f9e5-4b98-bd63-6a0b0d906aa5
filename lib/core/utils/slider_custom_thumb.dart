import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

/// Custom thumb for the filter screen slider.
class CustomThumb extends SfThumbShape {
  CustomThumb({required this.textScaleFactor, required this.values})
      : _textSpan = const TextSpan(),
        _textPainter = TextPainter();

  final double textScaleFactor;
  final SfRangeValues values;
  final TextPainter _textPainter;
  TextSpan _textSpan;
  final double verticalSpacing = 5.0;

  @override
  Size getPreferredSize(SfSliderThemeData themeData) {
    _textSpan = TextSpan(text: values.start.toInt().toString());
    _textPainter
      ..text = _textSpan
      ..textDirection = TextDirection.ltr
      ..textScaleFactor = textScaleFactor
      ..layout();

    // Considered label height along with thumb radius to avoid the
    // label getting overlapped with adjacent widget.
    return Size(themeData.thumbRadius * 2,
        (themeData.thumbRadius + _textPainter.height + verticalSpacing) * 2);
  }

  @override
  void paint(PaintingContext context, Offset center,
      {required RenderBox parentBox,
      required RenderBox? child,
      required SfSliderThemeData themeData,
      SfRangeValues? currentValues,
      dynamic currentValue,
      required Paint? paint,
      required Animation<double> enableAnimation,
      required TextDirection textDirection,
      required SfThumb? thumb}) {
    paint ??= Paint()..color = themeData.thumbColor!;
    context.canvas.drawCircle(center, themeData.thumbRadius, paint);

    String text = currentValues!.end.toInt().toString();
    if (thumb != null) {
      text = (thumb == SfThumb.start ? currentValues.start : currentValues.end)
          .toInt()
          .toString();
    }
    _textSpan = TextSpan(
      text: text,
      style: const TextStyle(color: Colors.black),
    );
    _textPainter
      ..text = _textSpan
      ..textDirection = textDirection
      ..textScaleFactor = textScaleFactor
      ..layout()
      ..paint(
        context.canvas,
        // To show the label below the thumb, we added it with thumb radius
        // and constant vertical spacing.
        Offset(center.dx - _textPainter.width / 2,
            center.dy + verticalSpacing + themeData.thumbRadius),
      );
  }
}
