import 'package:flutter/cupertino.dart';

import 'colors.dart';

class TableContentCommon extends StatelessWidget {
  final String content;
  final bool isBold;

  const TableContentCommon(
      {Key? key, required this.content, this.isBold = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Text(
        content,
        textAlign: TextAlign.center,
        style:
            TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal),
      ),
    );
  }
}

class TableHeadingCommon extends StatelessWidget {
  final String heading;

  const TableHeadingCommon(this.heading, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Text(
        heading,
        textAlign: TextAlign.center,
        style: const TextStyle(
            color: AppColors.white, fontWeight: FontWeight.bold),
      ),
    );
  }
}
