import 'dart:io';

import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/bai_screens/bai_home.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

const appStoreUrl = 'https://apps.apple.com/app/appId';
const playStoreUrl = 'market://details?id=$androidBundleId';

versionCheck(context) async {
  if (!PlatformUtils.isMobile) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const BaiHome(),
      ),
    );
    return;
  }
  try {
    double currentVersion = Platform.isAndroid
        ? double.parse(androidAppVersion)
        : double.parse(iosAppVersion);
    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 6),
      ),
    );

    await remoteConfig.setDefaults({
      "${versionPrefix}_android_latest_version":
          double.parse(androidAppVersion),
      "${versionPrefix}_ios_latest_version": double.parse(iosAppVersion),
    });

    try {
      await remoteConfig.fetchAndActivate();
      double latestVersion = Platform.isAndroid
          ? remoteConfig.getDouble('${versionPrefix}_android_latest_version')
          : remoteConfig.getDouble('${versionPrefix}_ios_latest_version');
      if (latestVersion > currentVersion) {
        _showVersionDialog(context);
      } else {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const BaiHome(),
          ),
        );
      }
    } catch (exception) {
      safePrint(
          'Unable to fetch remote config. Cached or default values will be used');
    }
  } catch (e) {
    safePrint(e);
  }
}

_showVersionDialog(context) async {
  await showDialog<String>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      String title = "New Update Available";
      String message =
          "There is a newer version of app available, please update.";
      String btnLabel = "Update Now";
      String btnLabelCancel = "Later";
      return Platform.isIOS
          ? CupertinoAlertDialog(
              title: Text(title),
              content: Text(message),
              actions: <Widget>[
                TextButton(
                  child: Text(btnLabel),
                  onPressed: () => _launchURL(appStoreUrl),
                ),
                TextButton(
                  child: Text(btnLabelCancel),
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BaiHome(),
                      ),
                    );
                  },
                ),
              ],
            )
          : AlertDialog(
              title: Text(title),
              content: Text(message),
              actions: <Widget>[
                TextButton(
                  child: Text(btnLabel),
                  onPressed: () => _launchURL(playStoreUrl),
                ),
                TextButton(
                  child: Text(btnLabelCancel),
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BaiHome(),
                      ),
                    );
                  },
                ),
              ],
            );
    },
  );
}

_launchURL(String url) async {
  try {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  } catch (e) {
    safePrint(e);
  }
}
