import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'live_auction_utils.dart';

showSuccessDialog(BuildContext context,
    {String title = '', String description = ''}) {
  AwesomeDialog(
    context: context,
    dialogType: DialogType.success,
    title: title,
    desc: description,
    autoDismiss: true,
    //btnCancelOnPress: () {},
    btnOkOnPress: () {},
    btnOkColor: AppColors.primaryColor,
    autoHide: const Duration(milliseconds: 10000),
  ).show();
}

showSoldDialog(BuildContext context, {String title = '', String amount = ''}) {
  AwesomeDialog(
    context: context,
    dialogType: DialogType.success,
    title: "Lot Sold!",
    desc: "This lot was sold!",
    autoDismiss: true,
    //btnCancelOnPress: () {},
    btnOkOnPress: () {},
    btnOkColor: AppColors.primaryColor,
    autoHide: const Duration(milliseconds: 10000),
  ).show();
}

showUnSoldDialog(BuildContext context,
    {String title = '', String description = ''}) {
  AwesomeDialog(
    context: context,
    dialogType: DialogType.error,
    title: "Lot Unsold!",
    desc: "This lot was withdrawn from auction",
    autoDismiss: true,
    //btnCancelOnPress: () {},
    btnOkOnPress: () {},
    btnOkColor: AppColors.primaryColor,
    autoHide: const Duration(milliseconds: 10000),
  ).show();
}

void showFailureDialog(BuildContext context,
    {String title = '', String description = ''}) {
  if (!_isDialogVisible) {
    _isDialogVisible = true;
    AwesomeDialog(
      context: context,
      dialogType: DialogType.error,
      title: title,
      desc: description,
      autoDismiss: true,
      //btnCancelOnPress: () {},
      btnOkOnPress: () {
        _isDialogVisible = false;
      },
      btnOkColor: AppColors.primaryColor,
      autoHide: const Duration(milliseconds: 10000),
    ).show().then((value) => _isDialogVisible = false);
  }
}

bool _isDialogVisible = false;

Future<void> showWinningDialog(
  BuildContext context, {
  String volume = '',
  String bidAmount = '',
  String totalAmount = '',
}) async {
  if (!_isDialogVisible) {
    _isDialogVisible = true;
    AwesomeDialog(
      context: context,
      dialogType: DialogType.success,
      title: "Congratulations!",
      body: Column(
        children: [
          Text("You won the lot no ${await LiveUtils().getLotNo()}!"),
          Text("Volume - $volume"),
          Text("Bid amount - $bidAmount"),
          Text("Total amount - $totalAmount"),
        ],
      ),
      autoDismiss: true,
      //btnCancelOnPress: () {},
      btnOkOnPress: () {
        _isDialogVisible = false;
      },
      btnOkColor: AppColors.primaryColor,
      autoHide: const Duration(milliseconds: 10000),
    ).show().then((value) => _isDialogVisible = false);
  }
}
