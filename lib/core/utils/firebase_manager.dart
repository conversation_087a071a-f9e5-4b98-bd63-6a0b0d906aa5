import 'package:connectone/old_models/firebase_response.dart';
import 'package:firebase_database/firebase_database.dart';

import 'constants.dart';

class FirebaseManager {
  Future<bool> isAvailable(String id) async {
    FirebaseResponseOffline firebaseResponse = FirebaseResponseOffline(
        stockAuctionStatus: "test",
        bidUpdateTimestamp: "test",
        stockStatusDescription: "test",
        buyNowPrice: 0,
        highestBidCustomerId: 0,
        auctionEndTs: "test",
        highestBid: 0);

    final ref =
        FirebaseDatabase.instance.ref('$firebaseBaseUrl/offline_auction');
    final snapshot = await ref.child(id).get();
    firebaseResponse = FirebaseResponseOffline.fromSnapshot(snapshot);
    if (firebaseResponse.stockAuctionStatus == "BIDG") {
      return true;
    } else {
      return false;
    }
  }

  Future<num> getHighestBid(String id) async {
    final ref =
        FirebaseDatabase.instance.ref('$firebaseBaseUrl/offline_auction');
    final snapshot = await ref.child(id).get();
    final firebaseResponse = FirebaseResponseOffline.fromSnapshot(snapshot);
    return firebaseResponse.highestBid;
  }
}
