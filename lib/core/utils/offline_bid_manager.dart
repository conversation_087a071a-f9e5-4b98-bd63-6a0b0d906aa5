import 'dart:ui';

import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/firebase_response.dart';

class OfflineBidManager {
  String getStatus(FirebaseResponseOffline firebaseResponse) {
    if (firebaseResponse.stockAuctionStatus == "BIDG") {
      if (firebaseResponse.highestBidCustomerId.toString() == getCustomerId()) {
        return "Your bid is the highest bid now";
      } else {
        return "Your bid is under bid now";
      }
    } else {
      return "The bid is closed now";
    }
  }

  bool getVisibility(FirebaseResponseOffline firebaseResponse) {
    if (firebaseResponse.stockAuctionStatus == "BIDG") {
      return true;
    } else {
      return false;
    }
  }

  Color getColor(FirebaseResponseOffline firebaseResponse) {
    if (firebaseResponse.highestBidCustomerId.toString() == getCustomerId()) {
      return AppColors.green;
    } else {
      return AppColors.red;
    }
  }

  bool isAvailable(FirebaseResponseOffline firebaseResponse) {
    if (firebaseResponse.stockStatusDescription.toString() == "CLSE") {
      return false;
    } else {
      return true;
    }
  }
}
