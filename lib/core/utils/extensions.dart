import 'package:connectone/bai_models/request_types_res.dart';
import 'package:connectone/core/utils/data_storage.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension RemoveDecimalFromString on String {
  String removeDecimal() {
    final double number = double.tryParse(this) ?? 0.0;
    final int intValue = number.toInt();
    return intValue.toString();
  }
}

extension StringExtension on String {
  String tableType() => toUpperCase();

  // String removeFromLastParenthesis() {
  //   // Check if the string has at least 4 characters
  //   if (length < 4) {
  //     return this; // Return the original string if it's too short
  //   }

  //   // Get the last four characters
  //   String lastFour = substring(length - 4);

  //   // Find the index of the last '(' in the last four characters
  //   int indexInLastFour = lastFour.lastIndexOf('(');

  //   // If '(' is found, calculate the actual index in the original string
  //   if (indexInLastFour != -1) {
  //     int actualIndex = length - 4 + indexInLastFour;
  //     return substring(0, actualIndex);
  //   }

  //   // If '(' is not found, return the original string
  //   return this;
  // }

  String removeFromLastParenthesis() {
    // Check if the string has at least 4 characters
    if (length < 4) {
      return this; // Return the original string if it's too short
    }
    // remove all ()
    return replaceAll(RegExp(r'\([^)]*\)'), '');
  }
}

extension SecondsToMMSS on int {
  String toMMSS() {
    final int minutes = this ~/ 60;
    final int seconds = this % 60;

    final String minutesStr = minutes.toString().padLeft(2, '0');
    final String secondsStr = seconds.toString().padLeft(2, '0');

    return '$minutesStr:$secondsStr';
  }
}

extension StringExtensions on String {
  List<String> toCommaSeparatedList() {
    // Trim spaces around the comma and split by comma
    return split(',').map((s) => s.trim()).toList();
  }

  String addSpaceAfterCommas() {
    try {
      // Replace commas with commas followed by a space
      return replaceAll(',', ', ');
    } catch (e) {
      // If there's an error, return the original string
      return this;
    }
  }
}

extension IndianCurrencyFormatting on String {
  String toIndianCurrencyFormat() {
    // Attempt to parse the string as a double, default to 0.0 if parsing fails
    double number = double.tryParse(replaceAll(',', '')) ?? 0.0;

    // Handle negative numbers
    String sign = number < 0 ? '-' : '';
    number = number.abs();

    String numStr = number.toStringAsFixed(2);
    List<String> parts = numStr.split('.');
    String whole = parts[0];
    String fraction = parts[1];

    // Indian number format: last three digits followed by commas every two digits
    String lastThree =
        whole.length > 3 ? whole.substring(whole.length - 3) : whole;
    String otherNumbers =
        whole.length > 3 ? whole.substring(0, whole.length - 3) : '';
    otherNumbers = otherNumbers.replaceAllMapped(
        RegExp(r'\B(?=(\d{2})+(?!\d))'), (match) => ',');

    String formattedWhole =
        otherNumbers.isNotEmpty ? '$otherNumbers,$lastThree' : lastThree;

    return '$sign₹$formattedWhole.$fraction';
  }
}

extension ColorOverlayExtension on Widget {
  Widget withColorOverlay() {
    return Stack(
      children: [
        this,
        Container(
          color: Colors.black.withOpacity(0.4),
          child: ColorFiltered(
            colorFilter: ColorFilter.mode(
                Colors.black.withOpacity(0.9), BlendMode.modulate),
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ],
    );
  }
}

extension DateTimeFormatting on DateTime {
  String toCreatedOn() {
    try {
      final localDateTime = toLocal(); // Convert to local time
      final DateFormat formatter = DateFormat('dd-MMM-yyyy h:mm a');
      return formatter.format(localDateTime);
    } catch (e) {
      return 'Invalid Date'; // Handle any formatting errors
    }
  }

  String toDeliveryOn() {
    try {
      final localDateTime = toLocal(); // Convert to local time
      final DateFormat formatter = DateFormat('dd-MMM-yyyy');
      return formatter.format(localDateTime);
    } catch (e) {
      return 'Invalid Date'; // Handle any formatting errors
    }
  }

  String addFiveThirtyAndReturnInSameFormat() {
    try {
      final localDateTime = toLocal(); // Convert to local time
      final newDateTime =
          localDateTime.add(const Duration(hours: 5, minutes: 30));
      final DateFormat formatter = DateFormat('dd-MMM-yyyy h:mm a');
      return formatter.format(newDateTime);
    } catch (e) {
      return 'Invalid Date'; // Handle any formatting errors
    }
  }
}

extension StringConversion on String {
  String convertAbbreviation() {
    switch (this) {
      case 'MBMR':
        return 'Seller';
      case 'BUYR':
        return 'Buyer';
      default:
        return this; // Return the original string if no match is found
    }
  }
}

extension StatusCodeExtensions on String {
  String toStatusName() {
    final status = DataStorage.requestTypes?.firstWhere(
      (s) => s.statusCode == this,
      orElse: () => RequestTypes(),
    );
    return status?.statusName ?? this;
  }
}

extension StatusNameExtensions on String {
  String toStatusCode() {
    final status = DataStorage.requestTypes?.firstWhere(
      (s) => s.statusName == this,
      orElse: () => RequestTypes(),
    );
    return status?.statusCode ?? this;
  }
}

extension CloseableWidget on Widget {
  Widget withCloseButton(VoidCallback onClose) {
    return Stack(
      children: [
        this,
        Positioned(
          right: 0,
          top: 4,
          bottom: 8,
          child: IconButton(
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
            onPressed: onClose,
          ),
        ),
      ],
    );
  }
}

extension StringExtensionNew on String? {
  String toDisplayString() {
    if (this == 'null' || this!.isEmpty || this == '0.00') {
      return '';
    }
    return (this == null || this!.isEmpty) ? '' : this!;
  }
}

extension RupeeFormatting on String {
  String toRupeeFormat() {
    // Check if the string is empty
    if (isEmpty) {
      return '0.00';
    }

    // Try to parse the string as a double
    double? value = double.tryParse(this);
    if (value == null) {
      return '0.00';
    }

    // Format the value to two decimal places
    return value.toStringAsFixed(2);
  }
}

// an extension on string, if input is "0" return "" else return input
extension ZeroToEmpty on String {
  String toEmptyIfZero() {
    if (this == '0') {
      return '';
    }
    return this;
  }
}
