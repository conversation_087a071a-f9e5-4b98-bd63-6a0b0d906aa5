import 'package:flutter/material.dart';

class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;

  const LoadingOverlay({Key? key, required this.child, required this.isLoading})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black
                .withOpacity(0.3), // Adjust opacity for better visibility
            child: const Center(
              child:
                  CircularProgressIndicator(), // Use default circular progress indicator
            ),
          ),
      ],
    );
  }
}
