import 'package:connectone/bai_screens/profile_screen.dart';
import 'package:connectone/old_screens/live_auctions_screen.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/old_screens/marginal_page.dart';
import 'package:connectone/old_screens/my_account_screen.dart';
import 'package:connectone/old_screens/notifications_screen.dart';
import 'package:connectone/old_screens/sell_products_screen.dart';
import 'package:connectone/old_screens/settings_screen.dart';
import 'package:connectone/old_screens/sold_out.dart';
import 'package:connectone/core/utils/app_routes.dart';
import 'package:get/route_manager.dart';

import '../../old_screens/help_screen.dart';
import '../../old_screens/live_list_screen.dart';
import '../../old_screens/offline_screen.dart';
import '../../old_screens/stock_index_page.dart';

class AppPages {
  static final List<GetPage<dynamic>> routes = <GetPage<dynamic>>[
    GetPage<LoginScreen>(
      name: AppRoutes.loginScreen,
      page: () => const LoginScreen(),
      preventDuplicates: true,
    ),
    GetPage<LiveAuctions>(
      name: AppRoutes.liveAuction,
      page: () => const LiveAuctions(),
      preventDuplicates: true,
    ),
    // GetPage<MyHomePage>(
    //   name: AppRoutes.start,
    //   page: () => const MyHomePage(),
    //   preventDuplicates: true,
    // ),

    GetPage<StockIndexButton>(
      name: AppRoutes.stockindexbutton,
      page: () => const StockIndexButton(),
      transition: Transition.cupertino,
    ),
    // GetPage<LiveListScreen>(
    //   name: AppRoutes.myStocks,
    //   page: () => LiveListScreen(),
    //   transition: Transition.cupertino,
    // ),
    GetPage<MarginalDetails>(
      name: AppRoutes.marginaldetails,
      page: () => const MarginalDetails(),
      transition: Transition.cupertino,
    ),
    GetPage<SoldOutPage>(
      name: AppRoutes.outStock,
      page: () => const SoldOutPage(),
      transition: Transition.cupertino,
    ),
    GetPage<HelpScreen>(
      name: AppRoutes.helpScreen,
      page: () => HelpScreen(),
      transition: Transition.cupertino,
    ),
    GetPage<HelpScreen1>(
      name: AppRoutes.helpScreen1,
      page: () => HelpScreen1(),
      transition: Transition.cupertino,
    ),
    GetPage<LiveListScreen>(
      name: AppRoutes.liveAuctionStat,
      page: () => const LiveListScreen(),
      transition: Transition.cupertino,
    ),
    GetPage<OfflineScreen>(
      name: AppRoutes.offlineScreen,
      page: () => OfflineScreen(
        query: '',
        title: '',
        userType: '',
        categoryType: '',
        isAdmin: true,
      ),
      transition: Transition.cupertino,
    ),
    GetPage<NotificationsScreen>(
      name: AppRoutes.notificationScreen,
      page: () => const NotificationsScreen(),
      transition: Transition.cupertino,
    ),
    GetPage<SellProductsWebView>(
      name: AppRoutes.sellProductScreen,
      page: () => SellProductsWebView(),
      transition: Transition.cupertino,
    ),
    GetPage<MyAccount>(
      name: AppRoutes.myAccountScreen,
      page: () => MyAccount(),
      transition: Transition.cupertino,
    ),
    // GetPage<ScanQrCode>(
    //   name: AppRoutes.scanCode,
    //   page: () => const ScanQrCode(),
    //   transition: Transition.cupertino,
    // ),
    // GetPage<OfflineFilterScreen>(
    //   name: AppRoutes.offlineFilters,
    //   page: () => const OfflineFilterScreen(title: "Search"),
    //   transition: Transition.cupertino,
    // ),
    // GetPage<OfflineMonthwiseScreen>(
    //   name: AppRoutes.offlineMonthwise,
    //   page: () =>  OfflineMonthwiseScreen(day: ,),
    //   transition: Transition.cupertino,
    // ),
    GetPage<SettingsScreen>(
      name: AppRoutes.settingScreen,
      page: () => const SettingsScreen(),
      transition: Transition.cupertino,
    ),
    GetPage<SettingsScreen>(
      name: AppRoutes.profile,
      page: () => const ProfileScreen(),
      transition: Transition.cupertino,
    ),
    //  GetPage<CalendarPage>(
    //   name: AppRoutes.offlineMonthwise,
    //   page: () =>  CalendarPage(title: "Calendar",),
    //   transition: Transition.cupertino,
    // ),
  ];
}
