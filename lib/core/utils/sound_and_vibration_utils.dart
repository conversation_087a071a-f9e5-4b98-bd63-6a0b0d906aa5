import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';

class AppPlayer {
  static AudioPlayer? player;

  void init() {
    player = AudioPlayer();
  }

  play1() {
    play('sounds/sample1.mp3');
    vibrate();
  }
}

final player = AudioPlayer();

play1() {
  play('sounds/sample1.mp3');
  vibrate();
}

play2() async {
  play('sounds/sample2.mp3');
  vibrate();
}

play3() {
  play('sounds/sample3.mp3');
  vibrate();
}

play(String url) async {
  try {
    if (isToneAllowed()) {
      // Audio playing is abandoned due to lag issue.
    }
  } catch (e) {
    safePrint("AudioPlayer Error: $e");
  }
}

vibrate() {
  if (isVibrationAllowed()) {
    HapticFeedback.vibrate();
  }
}
