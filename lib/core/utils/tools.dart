import 'dart:math';

import 'package:connectone/bai_models/offers_res.dart' as br;
import 'package:connectone/bai_models/seller_offers_res.dart' as sr;
import 'package:connectone/core/bai_widgets/bai_popup.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/data_storage.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path/path.dart' as p;
import 'package:http/http.dart' as http;

import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

class PlatformUtils {
  static bool get isMobile {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.isIOS || Platform.isAndroid;
    }
  }
}

alert(String? message) {
  if (PlatformUtils.isMobile) Fluttertoast.cancel();
  Fluttertoast.showToast(
    msg: removeExceptionPrefix('$message'),
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.BOTTOM,
    timeInSecForIosWeb: 1,
    backgroundColor: Colors.black54,
    textColor: Colors.white,
    fontSize: 16.0,
  );
}

void properAlert(String? message) {
  showDialog(
    context: navigatorKey.currentState!.overlay!.context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text(
          'Alert',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content:
            Text(removeExceptionPrefix('$message') ?? "An error occurred!"),
        actions: <Widget>[
          TextButton(
            child: Text(
              "OK",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}

void properAlertWithOk(
  String? message,
  VoidCallback? onOk,
) {
  showDialog(
    context: navigatorKey.currentState!.overlay!.context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text(
          'Alert',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content:
            Text(removeExceptionPrefix('$message') ?? "An error occurred!"),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              "No",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
          ),
          TextButton(
            child: Text(
              "Yes",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            onPressed: () {
              onOk?.call();
            },
          ),
        ],
      );
    },
  );
}

void categoryAlert({
  required String message,
  required VoidCallback? onGoToCart,
}) {
  showDialog(
    context: navigatorKey.currentState!.overlay!.context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text(
          'Alert',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Text(removeExceptionPrefix(message)),
        actions: <Widget>[
          TextButton(
            child: Text(
              "Close",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          if (onGoToCart != null)
            TextButton(
              child: Text(
                "Go to Cart",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
              onPressed: () {
                onGoToCart.call();
              },
            ),
        ],
      );
    },
  );
}

void properPopup(String? message) {
  showDialog(
    context: navigatorKey.currentState!.overlay!.context,
    builder: (BuildContext context) {
      return BaiPopup(
        title: 'Alert',
        content: removeExceptionPrefix('$message') ?? "An error occurred!",
      );
    },
  );
}

String removeExceptionPrefix(String text) {
  return text.replaceAll('Exception:', '').trim();
}

getCustomerId() => readFromStorage(customerId);

getCustomerRole() => readFromStorage(customerRole);

getUser() => readFromStorage('user');

saveName(String name) => writeToStorage('name', name);
savePhone(String phone) => writeToStorage('phone', phone);
saveVendorPhone(String phone) => writeToStorage('vendorPhone', phone);
saveEmail(String email) => writeToStorage('email', email);
saveVendorId(String vendorId) => writeToStorage('vendorId', vendorId);
saveVendorName(String vendorName) => writeToStorage('vendorName', vendorName);
saveDesignation(String designation) =>
    writeToStorage('designation', designation);
saveDesignationId(String designationId) =>
    writeToStorage('designationId', designationId.toString());
saveV2Role(String role) => writeToStorage('v2Role', role);
saveFilter(String filter) => writeToStorage('filter', filter);
saveFilterB(String filter) => writeToStorage('filterB', filter);
saveFilterS(String filter) => writeToStorage('filterS', filter);
savePrchOrdrId(String prchOrdrId) => writeToStorage('prchOrdrId', prchOrdrId);
saveOrdrGrpNo(String ordrGrpId) => writeToStorage('ordrGrpId', ordrGrpId);
saveCategoryName(String categoryName) =>
    writeToStorage('categoryName', categoryName);

getName() => readFromStorage('name');
getPhone() => readFromStorage('phone');
getVendorPhone() => readFromStorage('vendorPhone');
getEmail() => readFromStorage('email');
getVendorId() => readFromStorage('vendorId');
getVendorName() => readFromStorage('vendorName');
getDesignation() => readFromStorage('designation');
getDesignationId() => readFromStorage('designationId');
getRoleLevel() => int.tryParse(getDesignationId() ?? "0") ?? 0;
getV2Role() => readFromStorage('v2Role');
getFilter() => readFromStorage('filter') ?? "";
getFilterB() => readFromStorage('filterB') ?? "";
getFilterS() => readFromStorage('filterS') ?? "";
getPrchOrdrId() => readFromStorage('prchOrdrId') ?? 0;
getOrdrGrpNo() => readFromStorage('ordrGrpId') ?? 0;
getCategoryName() => readFromStorage('categoryName') ?? "";

getRole() => readFromStorage(customerRole);

bool isVibrationAllowed() {
  if (readFromStorage(vibF) == "TRUE" ||
      readFromStorage(vibF).toString() == "null") {
    return true;
  } else {
    return false;
  }
}

bool isToneAllowed() {
  if (readFromStorage(toneF) == "TRUE" ||
      readFromStorage(toneF).toString() == "null") {
    return true;
  } else {
    return false;
  }
}

getPass() => readFromStorage('pass');

getUserName() => readFromStorage('username');

getAuthToken() => readFromStorage(cognitoToken);

clearAuthToken() => GetStorage().remove(cognitoToken);

bool isLoggedIn() {
  var isLogged = readFromStorage(loggedIn);
  if (isLogged == null) {
    return false;
  }
  return isLogged == "TRUE";
}

bool showActiveBuyerInfo() =>
    DataStorage.configData
        ?.firstWhere((element) => element?.keyName1 == "active_buyer_info_yn")
        ?.valueName1 ==
    "Y";

bool marginEnabled() => NetworkController().organisationData?.marginYn == "Y";

Future<void> launchInAppBrowser(String url) async {
  if (!await launchUrl(Uri.parse(url), mode: LaunchMode.inAppBrowserView)) {
    alert("Couldn't open web page!");
  }
}

String getMimeType(String path) {
  String extension = p.extension(path).toLowerCase();
  switch (extension) {
    case '.png':
      return 'image/png';
    case '.m4a':
      return 'audio/mp3';
    case '.mp4':
      return 'audio/mp4';
    case '.jpeg':
    case '.jpg':
      return 'image/jpeg';
    case '.pdf':
      return 'application/pdf';
    case '.doc':
      return 'application/msword';
    case '.docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    default:
      throw Exception("Unsupported file extension: $extension");
  }
}

bool isSupportedMimeType(String mimeType) {
  const supportedMimeTypes = [
    'image/png',
    'audio/mp3',
    'audio/mp4',
    'image/jpeg',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];
  return supportedMimeTypes.contains(mimeType);
}

String generateRandomFilename(String extension) {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  final random = Random();
  final randomString = List.generate(
      10, (index) => characters[random.nextInt(characters.length)]).join();
  return '$randomString.$extension';
}

bool isBuyer() {
  return getCustomerRole() != "MBMR";
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

Color? getCategoryColor(String code) {
  var requests = DataStorage.requestTypes ?? [];
  for (var request in requests) {
    if (code == request.statusCode) {
      String colourCode = (request.colourCode ?? "").replaceAll('#', '');
      try {
        return Color(int.parse("0xFF$colourCode"));
      } catch (e) {
        return null;
      }
    }
  }
  return null;
}

br.Offer convertAlreadySubmittedToOffer(sr.AlreadySubmittedQuote quote) {
  return br.Offer(
    id: quote.id,
    mvtItemId: quote.mvtItemId,
    mvtItemName: quote.mvtItemName,
    optionGroupId: quote.optionGroupId,
    optionGroupName: quote.optionGroupName,
    optionName: quote.optionName,
    optionId: quote.optionId,
    offerPrice: quote.offerPrice,
    customerId: quote.customerId,
    vendorId: quote.vendorId,
    prchOrdrId: quote.prchOrdrId,
    statusCd: quote.statusCd,
    statusName: quote.statusName,
    vendorName: quote.vendorName,
    vendorCity: quote.vendorCity,
    variant1OptionId: quote.variant1OptionId,
    variant1OptionName: quote.variant1OptionName,
    variant2OptionId: quote.variant2OptionId,
    variant2OptionName: quote.variant2OptionName,
    variant3OptionId: quote.variant3OptionId,
    variant3OptionName: quote.variant3OptionName,
    variant1OptionGroupId: quote.variant1OptionGroupId,
    variant1OptionGroupName: quote.variant1OptionGroupName,
    variant2OptionGroupId: quote.variant2OptionGroupId,
    variant2OptionGroupName: quote.variant2OptionGroupName,
    variant3OptionGroupId: quote.variant3OptionGroupId,
    variant3OptionGroupName: quote.variant3OptionGroupName,
    vendorPhone: quote.vendorPhone,
    customerName: quote.customerName,
    customerPhone: quote.customerPhone,
    remarks: quote.remarks,
    quantity: quote.quantity,
    createdAt: quote.createdAt,
    baiMember: quote.baiMember,
    typeOfGstFiling: quote.typeOfGstFiling,
    rating: quote.rating,
    medias: quote.medias?.map((e) => br.Media.fromJson(e.toJson())).toList(),
    negotiationHistory: quote.negotiationHistory,
    negotiationStatus: quote.negotiationStatus,
    negotiationStatusName: quote.negotiationStatusName,
  );
}

String formatToIndianRupee(String input) {
  try {
    double value = double.parse(input);
    final formatter = NumberFormat('#,##,##0.##', 'en_IN');
    return formatter.format(value);
  } catch (e) {
    return input;
  }
}

bool isAdmin() {
  var roleCode = getRoleLevel();
  return roleCode == 1;
}

Color getMediumColor(int index) {
  List<Color> mediumColors = [
    Colors.blue.shade800,
    Colors.green.shade800,
    Colors.red.shade800,
    Colors.orange.shade800,
    Colors.purple.shade800,
    Colors.teal.shade800,
    Colors.amber.shade800,
    Colors.cyan.shade800,
    Colors.indigo.shade800,
    Colors.lime.shade800,
  ];
  return mediumColors[index % mediumColors.length];
}

Future<Uint8List> blobToBytes(String url) async {
  try {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      Uint8List bytes = response.bodyBytes;
      return bytes;
    } else {
      throw Exception('Failed to load image at $url');
    }
  } catch (e) {
    // rethrow;
    // print("object");
    return Uint8List(0);
  }
}

// Check if a route is in the stack using Navigator
bool checkIfRouteInStack(BuildContext context, String routeName) {
  bool routeExists = false;
  Navigator.of(context).popUntil((route) {
    if (route.settings.name == routeName) {
      routeExists = true;
    }
    // By returning true, we stop popUntil from popping any routes.
    return true;
  });
  return routeExists;
}
