import 'package:connectone/core/utils/app_routes.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:get/get.dart';

void navigateSafe(String toRoute, {dynamic arguments}) {
  final loginStatus = isLoggedIn();

  final route = loginStatus ? toRoute : AppRoutes.loginScreen;

  if (loginStatus) {
    Get.toNamed(route, arguments: arguments);
  } else {
    Get.offAllNamed(route, arguments: arguments);
  }
}
