import 'package:connectone/bai_models/favourite_item.dart';

class OfflineUtils {
  bool isFavourite(List<FavouriteItem>? favouritesResponse, String stockId) {
    bool result = false;
    for (int i = 0; i < (favouritesResponse?.length ?? 0); i++) {
      if ((favouritesResponse?[i].id.toString()) == stockId) {
        result = true;
        break;
      }
    }
    return result;
  }

  // Future<Datum1> getAutobidData(String stockId) async {
  //   Datum1 result = Datum1(
  //       optionStatus: OptionStatus.none, stockId: 0, autoBidCreatedAt: DateTime.now(), autoBidAmount: 0, buyBidPrice: 0, autoBidStatus: AutoBidStatus.iact);
  //   var favouritesResponse = await NetworkController().getNewFavourites();
  //   for (int i = 0; i < favouritesResponse.length; i++) {
  //     if ((favouritesResponse[i].id.toString()) == stockId) {
  //       result = favouritesResponse[i];
  //       break;
  //     }
  //   }
  //   return result;
  // }
}
