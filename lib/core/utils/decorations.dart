import 'package:flutter/material.dart';

import 'custom_border.dart';
import 'colors.dart'; // Assuming AppColors is defined here

final _defaultBorder = SelectedInputBorderWithShadow(
  borderRadius: BorderRadius.circular(12),
  borderSide:
      const BorderSide(color: Color.fromARGB(255, 180, 178, 178), width: .1),
);
const _defaultPadding = EdgeInsets.all(15);

InputDecoration getTextInputDecoration(String hint) {
  return InputDecoration(
    hintText: hint,
    focusedBorder: _defaultBorder,
    enabledBorder: _defaultBorder,
    border: _defaultBorder,
    contentPadding: _defaultPadding,
  );
}

InputDecoration getLoginInputDecoration(
    String hint, IconData icon, bool isEmail) {
  return InputDecoration(
    hintText: hint,
    prefixIcon: isEmail
        ? Icon(
            icon,
            color: AppColors.primaryColor,
            size: 18,
          )
        : const Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  '+91',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                ),
              )
            ],
          ),
    focusedBorder: _defaultBorder,
    enabledBorder: _defaultBorder,
    border: _defaultBorder,
    contentPadding: _defaultPadding,
  );
}
