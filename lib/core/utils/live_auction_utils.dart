import 'package:firebase_database/firebase_database.dart';

import 'constants.dart';

class LiveUtils {
  Future<String> getStockId() async {
    final ref =
        FirebaseDatabase.instance.ref('$firebaseBaseUrl/live_auction/1');
    final snapshot = await ref.get();
    if (snapshot.exists) {
      if (snapshot.child('highest_bids').children.isEmpty) {
        return "";
      }
      return snapshot.child('highest_bids').children.first.key ?? "";
    } else {
      return '';
    }
  }

  Future<int> getLotNo() async {
    try {
      final ref =
          FirebaseDatabase.instance.ref('$firebaseBaseUrl/live_auction/1');
      final snapshot = await ref.get();
      if (snapshot.exists) {
        Map<dynamic, dynamic> map = snapshot.value as Map;
        return map['livebidding_stock_lot_no'] ?? 0;
      } else {
        return 0;
      }
    } catch (e) {
      return 0;
    }
  }

  Future<double> getHighestBid() async {
    final ref =
        FirebaseDatabase.instance.ref('$firebaseBaseUrl/live_auction/1');
    final snapshot = await ref.get();
    if (snapshot.exists) {
      return double.parse(snapshot
          .child('highest_bids')
          .children
          .first
          .child('highest_bid')
          .value
          .toString());
    } else {
      return 0;
    }
  }

  Future<bool> bidsReceived(String stockId) async {
    final ref = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/seller_bids');
    final snapshot = await ref.get();
    if (snapshot.exists) {
      return snapshot.child(stockId).children.isNotEmpty;
    } else {
      return false;
    }
  }
}
