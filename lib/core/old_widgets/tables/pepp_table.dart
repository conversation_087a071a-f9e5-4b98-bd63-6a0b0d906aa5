import 'package:connectone/core/network/network_controller.dart';
import 'package:flutter/material.dart';

import '../../../old_models/pepp_res.dart';
import '../../utils/colors.dart';
import '../../utils/safe_print.dart';
import '../../utils/table_utils.dart';

class PeppTable extends StatefulWidget {
  const PeppTable({required this.stockId, Key? key}) : super(key: key);
  final String stockId;

  @override
  State<PeppTable> createState() => _PeppTableState();
}

class _PeppTableState extends State<PeppTable> {
  PeppRes? res;
  bool isLoading = false;
  bool isError = false;

  @override
  void initState() {
    super.initState();
    fetchTable();
  }

  void fetchTable() async {
    try {
      setState(() {
        isLoading = true;
      });
      res = await NetworkController().getPepperTable(widget.stockId);
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
        isError = true;
      });
      safePrint(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return isError
        ? const SizedBox(
            height: 160,
            child: Center(
                child:
                    Text("Couldn't load the table. Please try again later.")))
        : Column(
            children: [
              _buildTableWithTitle("Test", [
                "Moisture",
                "Light berries",
                "Extraneous matter",
                "Bulk density"
              ], [
                res?.data?.moisture,
                res?.data?.lightBerries,
                res?.data?.extraneousMatter,
                res?.data?.bulkDensity
              ]),
              const SizedBox(height: 20),
              _buildTableWithTitle("Bold percentage of the lot", [
                "5 mm",
                "4.25 mm",
                "4.75 mm"
              ], [
                res?.data?.bold5mm,
                res?.data?.bold425mm,
                res?.data?.bold475mm
              ]),
              const SizedBox(height: 20),
              _buildTableWithTitle(
                  "Chemical Examination",
                  ["Mineral Oil", "Mould"],
                  [res?.data?.mineralOil, res?.data?.mould]),
            ],
          );
  }

  Widget _buildTableWithTitle(
      String title, List<String> headings, List<String?> values) {
    return Table(
      border: TableBorder.all(
          borderRadius: BorderRadius.circular(12.0), color: AppColors.green),
      children: [
        TableRow(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.0),
              topRight: Radius.circular(12.0),
            ),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon(title),
            const TableHeadingCommon(""),
          ],
        ),
        for (int i = 0; i < headings.length; i++)
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  headings[i],
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  values[i] ?? "",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 15,
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }
}
