import 'dart:math';

import 'package:connectone/core/old_widgets/tables/pepp_table.dart';
import 'package:flutter/material.dart';

import 'card_table.dart';

class AppTable extends StatefulWidget {
  const AppTable({
    required this.stockId,
    Key? key,
    this.tableType,
  }) : super(key: key);

  final String stockId;
  final String? tableType;

  @override
  State<AppTable> createState() => _AppTableState();
}

class _AppTableState extends State<AppTable> {
  Widget getTable() {
    switch (widget.tableType) {
      case "PEPPER":
        return PeppTable(
          stockId: widget.stockId,
          key: Key("${Random().nextInt(1000)}"),
        );
      case "CARDAMOM":
        return CardTable(
          stockId: widget.stockId,
          key: Key("${Random().nextInt(1000)}"),
        );
      default:
        return const SizedBox(); // Return an empty widget if no tableType matches
    }
  }

  @override
  Widget build(BuildContext context) {
    return getTable();
  }
}
