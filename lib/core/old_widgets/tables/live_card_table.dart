import 'package:connectone/old_models/card_res.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../../utils/colors.dart';
import '../../utils/table_utils.dart';

class LiveCardTable extends StatelessWidget {
  final CardRes? res;

  const LiveCardTable({Key? key, required this.res}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return res == null
        ? const SizedBox(
            height: 160,
            child: Center(
                child:
                    Text("Couldn't load the table. Please try again later.")))
        : Column(
            children: [
              _buildInfoTable(),
              const SizedBox(height: 16.0),
              _buildGradeTable(),
            ],
          );
  }

  Widget _buildInfoTable() {
    return Table(
      border: TableBorder.all(
        borderRadius: BorderRadius.circular(12.0),
        color: AppColors.green,
      ),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.0),
              topRight: Radius.circular(12.0),
            ),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Size"),
            TableHeadingCommon("Ltr.Wt."),
            TableHeadingCommon("Moisture"),
            TableHeadingCommon("Colour"),
            TableHeadingCommon("Seller"),
          ],
        ),
        TableRow(
          children: [
            TableContentCommon(
              content: res?.data?.size ?? '',
              isBold: true,
            ),
            TableContentCommon(
              content: res?.data?.ltrWt?.toStringAsFixed(0) ?? '',
              isBold: true,
            ),
            TableContentCommon(
              content: res?.data?.moisture?.toStringAsFixed(0) ?? '',
              isBold: true,
            ),
            TableContentCommon(
              content: res?.data?.colour ?? '',
              isBold: true,
            ),
            const TableContentCommon(content: '-', isBold: true),
          ],
        ),
      ],
    );
  }

  Widget _buildGradeTable() {
    return Table(
      border: TableBorder.all(
          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
          color: AppColors.green),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0)),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Grade"),
            TableHeadingCommon("Clean"),
            TableHeadingCommon("Sick/Split"),
            TableHeadingCommon("Total"),
          ],
        ),
        _buildGradeRow(
            "8+ mm",
            res?.data?.grade8mmClean as double?,
            res?.data?.grade8mmSickdsplit as double?,
            res?.data?.grade8mmTotalpercentage as double?),
        _buildGradeRow(
            "7-8 mm",
            res?.data?.grade7t8mmClean as double?,
            res?.data?.grade7t8mmSickdsplit as double?,
            res?.data?.grade7t8mmTotalpercentage as double?),
        _buildGradeRow(
            "6-7 mm",
            res?.data?.grade17mmClean as double?,
            res?.data?.grade17mmSickdsplit as double?,
            res?.data?.grade17mmTotalpercentage as double?),
        _buildGradeRow(
            "<6 mm",
            double.tryParse(res?.data?.rejectionsClean ?? ""),
            double.tryParse(res?.data?.rejectionsSickdsplit ?? ""),
            double.tryParse(res?.data?.rejectionsTotalpercentage ?? "")),
        _buildTotalRow(),
      ],
    );
  }

  TableRow _buildGradeRow(
      String grade, double? clean, double? sickSplit, double? totalPercentage) {
    return TableRow(
      children: [
        TableContentCommon(content: grade, isBold: true),
        TableContentCommon(content: clean?.toStringAsFixed(0) ?? ""),
        TableContentCommon(content: sickSplit?.toStringAsFixed(0) ?? ""),
        TableContentCommon(
            content: "${totalPercentage?.toStringAsFixed(0) ?? ""}%",
            isBold: true),
      ],
    );
  }

  TableRow _buildTotalRow() {
    return TableRow(
      children: [
        const TableContentCommon(content: "Total", isBold: true),
        TableContentCommon(
            content:
                "${res?.data?.totalClean.toString().removeDecimal()}%" ?? "",
            isBold: true),
        TableContentCommon(
            content:
                "${res?.data?.totalSickdsplit.toString().removeDecimal()}%" ??
                    "",
            isBold: true),
        const TableContentCommon(content: "100%", isBold: true),
      ],
    );
  }
}
