import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';

import '../../network/network_controller.dart';
import '../../../old_models/card_res.dart';
import '../../utils/colors.dart';
import '../../utils/safe_print.dart';
import '../../utils/table_utils.dart';

class CardTable extends StatefulWidget {
  const CardTable({required this.stockId, Key? key}) : super(key: key);
  final String stockId;

  @override
  State<CardTable> createState() => _CardTableState();
}

class _CardTableState extends State<CardTable> {
  CardRes? res;
  bool isLoading = false;
  bool isError = false;

  @override
  void initState() {
    super.initState();
    fetchTable();
  }

  Future<void> fetchTable() async {
    try {
      setState(() {
        isLoading = true;
      });
      res = await NetworkController().getCardamomTable(widget.stockId);
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
          isError = true;
        });
      }
      safePrint(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    safePrint(res?.data?.toString());
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (isError) {
      return const SizedBox(
        height: 160,
        child: Center(
            child: Text("Couldn't load the table. Please try again later.")),
      );
    }
    return Column(
      children: [
        buildInfoTable(),
        const SizedBox(height: 16.0),
        buildGradeTable(),
      ],
    );
  }

  Widget buildInfoTable() {
    return Table(
      border: TableBorder.all(
          borderRadius: BorderRadius.circular(12.0), color: AppColors.green),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0)),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Size"),
            TableHeadingCommon("Ltr.Wt."),
            TableHeadingCommon("Moisture"),
            TableHeadingCommon("Colour"),
            TableHeadingCommon("Seller"),
          ],
        ),
        TableRow(
          children: [
            TableContentCommon(content: res?.data?.size ?? '', isBold: true),
            TableContentCommon(
                content: res?.data?.ltrWt?.toStringAsFixed(0) ?? '',
                isBold: true),
            TableContentCommon(
                content: res?.data?.moisture?.toStringAsFixed(0) ?? '',
                isBold: true),
            TableContentCommon(content: res?.data?.colour ?? '', isBold: true),
            const TableContentCommon(content: '-', isBold: true),
          ],
        ),
      ],
    );
  }

  Widget buildGradeTable() {
    return Table(
      border: TableBorder.all(
          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
          color: AppColors.green),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0)),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Grade"),
            TableHeadingCommon("Clean"),
            TableHeadingCommon("Sick/Split"),
            TableHeadingCommon("Total"),
          ],
        ),
        buildGradeTableRow(
            "8+ mm",
            res?.data?.grade8mmClean?.toDouble(),
            res?.data?.grade8mmSickdsplit?.toDouble(),
            res?.data?.grade8mmTotalpercentage?.toDouble()),
        buildGradeTableRow(
            "7-8 mm",
            res?.data?.grade7t8mmClean?.toDouble(),
            res?.data?.grade7t8mmSickdsplit?.toDouble(),
            res?.data?.grade7t8mmTotalpercentage?.toDouble()),
        buildGradeTableRow(
            "6-7 mm",
            res?.data?.grade17mmClean?.toDouble(),
            res?.data?.grade17mmSickdsplit?.toDouble(),
            res?.data?.grade17mmTotalpercentage?.toDouble()),
        buildGradeTableRow(
          "<6 mm",
          double.tryParse(res?.data?.rejectionsClean ?? ""),
          double.tryParse(res?.data?.rejectionsSickdsplit ?? ""),
          double.tryParse(res?.data?.rejectionsTotalpercentage ?? ""),
        ),
        buildTotalRow(),
      ],
    );
  }

  TableRow buildGradeTableRow(
      String grade, double? clean, double? sickSplit, double? totalPercentage) {
    return TableRow(
      children: [
        TableContentCommon(content: grade, isBold: true),
        TableContentCommon(content: clean?.toStringAsFixed(0) ?? ""),
        TableContentCommon(content: sickSplit?.toStringAsFixed(0) ?? ""),
        TableContentCommon(
            content: "${totalPercentage?.toStringAsFixed(0) ?? ""}%",
            isBold: true),
      ],
    );
  }

  TableRow buildTotalRow() {
    return TableRow(
      children: [
        const TableContentCommon(content: "Total", isBold: true),
        TableContentCommon(
            content:
                "${res?.data?.totalClean.toString().removeDecimal()}%" ?? "",
            isBold: true),
        TableContentCommon(
            content:
                "${res?.data?.totalSickdsplit.toString().removeDecimal()}%" ??
                    "",
            isBold: true),
        const TableContentCommon(content: "100%", isBold: true),
      ],
    );
  }
}
