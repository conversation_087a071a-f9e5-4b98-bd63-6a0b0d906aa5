// import 'package:connectone/bai_models/bai_products_res.dart';
// import 'package:connectone/old_models/firebase_response.dart';
// // import 'package:connectone/old_models/get_offline_stocks.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/const_dimens.dart';
// import 'package:connectone/core/utils/decorations.dart';
// import 'package:fbroadcast/fbroadcast.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../old_blocs/offline_card/offline_card_bloc.dart';
// import '../../utils/tools.dart';

// //ignore: must_be_immutable
// class MakeAnOfferDialog extends StatelessWidget {
//   final String title;
//   final Content currentItem;
//   final FirebaseResponseOffline firebaseResponse;

//   MakeAnOfferDialog({Key? key, required this.title, required this.currentItem, required this.firebaseResponse}) : super(key: key);
//   final TextEditingController makeOfferAmountController = TextEditingController();

//   @override
//   Widget build(BuildContext context) {
//     return Theme(
//       data: ThemeData(fontFamily: 'poppins'),
//       child: BlocBuilder<OfflineSubBloc, OfflineSubState>(
//         builder: (context, state) {
//           return Dialog(
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(DialogConstants.padding),
//             ),
//             backgroundColor: Colors.transparent,
//             child: SingleChildScrollView(
//               physics: const ClampingScrollPhysics(),
//               child: Stack(
//                 children: <Widget>[
//                   Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Container(
//                         height: 320,
//                         padding: const EdgeInsets.only(
//                           left: DialogConstants.padding,
//                           top: DialogConstants.avatarRadius + DialogConstants.padding,
//                           right: DialogConstants.padding,
//                           bottom: DialogConstants.padding,
//                         ),
//                         margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
//                         decoration: BoxDecoration(
//                           shape: BoxShape.rectangle,
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(DialogConstants.padding),
//                           boxShadow: const [BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10)],
//                         ),
//                         child: Column(
//                           mainAxisSize: MainAxisSize.min,
//                           children: <Widget>[
//                             Text(
//                               title,
//                               textAlign: TextAlign.center,
//                               style: const TextStyle(color: AppColors.green, fontSize: 20, fontWeight: FontWeight.bold),
//                             ),
//                             const Spacer(),
//                             const Text(
//                               "Please enter your offer amount",
//                               textAlign: TextAlign.center,
//                               style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
//                             ),
//                             const Spacer(),
//                             Container(
//                               width: double.infinity,
//                               decoration: BoxDecoration(
//                                 border: Border.all(color: Colors.black, width: 1.0),
//                                 borderRadius: BorderRadius.circular(8.0),
//                               ),
//                               child: TextField(
//                                 keyboardType: TextInputType.number,
//                                 controller: makeOfferAmountController,
//                                 style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                                 textAlign: TextAlign.center,
//                                 decoration: getTextInputDecoration("Offer amount"),
//                               ),
//                             ),
//                             const Spacer(),
//                             Row(
//                               children: [
//                                 Expanded(
//                                   child: SizedBox(
//                                     height: 48,
//                                     child: ElevatedButton(
//                                       onPressed: () {
//                                         if (int.parse(makeOfferAmountController.text) > firebaseResponse.highestBid) {
//                                           context.read<OfflineSubBloc>().add(MakeOffer(
//                                                 orderTypeCd: "BIDD",
//                                                 stockId: currentItem.id!.toInt(),
//                                                 bidDeskNo: "Mobile",
//                                                 amount: int.parse(makeOfferAmountController.text),
//                                                 quantity: currentItem.quantity!.toInt(),
//                                               ));
//                                           Navigator.pop(context);
//                                           FBroadcast.instance().broadcast(
//                                             "CLOSE_MO",
//                                             value: "CLOSE_MO",
//                                           );
//                                         } else {
//                                           alert("Entered value is less than highest bid!");
//                                         }
//                                       },
//                                       style: ButtonStyle(
//                                         backgroundColor: WidgetStateProperty.all<Color>(AppColors.green),
//                                         shape: WidgetStateProperty.all<RoundedRectangleBorder>(
//                                           RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                       child: const Text("Submit"),
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                   Positioned(
//                     left: DialogConstants.padding,
//                     right: DialogConstants.padding,
//                     child: CircleAvatar(
//                       radius: 48,
//                       backgroundColor: Colors.grey.shade200,
//                       child: const CircleAvatar(
//                         backgroundColor: Colors.white,
//                         radius: 44,
//                         child: Icon(
//                           Icons.sell,
//                           color: Colors.black,
//                           size: 44,
//                         ),
//                       ),
//                     ),
//                   ),
//                   Positioned(
//                     top: 0,
//                     right: 0,
//                     child: GestureDetector(
//                       onTap: () {
//                         Navigator.pop(context);
//                       },
//                       child: const Icon(
//                         Icons.cancel,
//                         color: AppColors.white,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
