import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/old_blocs/get_comments/get_comments_bloc.dart';
import 'package:connectone/old_blocs/get_comments/get_comments_bloc.dart'
    as comm;
import 'package:connectone/old_blocs/card_list_bloc/card_list_bloc.dart';
import 'package:connectone/old_blocs/card_list_bloc/card_list_event.dart';
import 'package:connectone/old_blocs/card_list_bloc/card_list_state.dart';
import 'package:connectone/old_blocs/rating_bloc/ratingpage_bloc.dart' as rated;
import 'package:connectone/core/old_widgets/common/sell_info.dart';
import 'package:connectone/core/old_widgets/common/show_feedback_dialog.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';
import '../../../old_models/my_stocks_pojo.dart';
import '../tables/app_table.dart';
import '../../utils/circular_progress.dart';
import '../../utils/colors.dart';

class CardListItemMyStocks extends StatefulWidget {
  final Content item;
  final String sellerName;
  final String buyerName;
  final String stockId;
  final int index;
  final String gradeText;
  final String locationText;
  final String qtyText;
  final String date;
  final String noBidText;
  final String highestBid;
  final String orderId;
  final String lotNo;
  final double rating;
  final String? currentMargin;
  final String? currentLimit;
  final String? imageUrl;
  final bool animate;
  final bool isSold;
  final bool isBought;
  final String sellerId;
  final Function editExpectedPrice;
  final bool fromSold;

  const CardListItemMyStocks({
    Key? key,
    required this.sellerId,
    required this.index,
    required this.sellerName,
    required this.buyerName,
    required this.stockId,
    required this.gradeText,
    required this.locationText,
    required this.qtyText,
    required this.date,
    required this.noBidText,
    required this.highestBid,
    required this.orderId,
    required this.lotNo,
    required this.rating,
    this.animate = false,
    required this.currentMargin,
    required this.currentLimit,
    required this.imageUrl,
    required this.isSold,
    required this.isBought,
    required this.item,
    required this.editExpectedPrice,
    required this.fromSold,
  }) : super(key: key);

  @override
  State<CardListItemMyStocks> createState() => _CardListItemMyStocksState();
}

class _CardListItemMyStocksState extends State<CardListItemMyStocks> {
  late List<charts.Series<dynamic, String>> seriesList;
  bool isOpen = false;
  String groupValue = "Table";
  final TextEditingController controller = TextEditingController();
  final DateFormat formatter = DateFormat('yyyy-MM-dd');

  @override
  Widget build(BuildContext context) {
    seriesList = _createSampleData();
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) =>
                CardBloc()..add(const OpenStatus(openFlag: -1))),
      ],
      child: BlocBuilder<CardBloc, CardState>(
        builder: (context, state) {
          return Card(
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0)),
            elevation: 3.0,
            margin: const EdgeInsets.all(8.0),
            child: Container(
              foregroundDecoration: RotatedCornerDecoration.withColor(
                color: widget.isSold
                    ? Colors.green
                    : widget.isBought
                        ? AppColors.maroon
                        : Colors.transparent,
                badgePosition: BadgePosition.topStart,
                textSpan: null,
                badgeSize: const Size(40, 40),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    _buildMainInfo(context),
                    _buildActionsRow(context, state),
                    if (state is StockOpenState)
                      _buildExpandableContent(context),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMainInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildImageSection(context),
        const SizedBox(width: 20),
        _buildDetailsSection(),
      ],
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            if (widget.imageUrl != null) {
              showImageDialog(context, widget.imageUrl);
            }
          },
          child: Container(
            height: 100,
            width: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              image: widget.imageUrl != null
                  ? DecorationImage(
                      image: NetworkImage(widget.imageUrl!), fit: BoxFit.cover)
                  : const DecorationImage(
                      image:
                          AssetImage('assets/images/no_image_available.jpeg'),
                      fit: BoxFit.cover),
              boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 5.0)],
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(widget.stockId,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13)),
        const SizedBox(height: 5),
        Text("Lot No: ${widget.lotNo}",
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13)),
        const SizedBox(height: 5),
        GestureDetector(
          onTap: () {
            showDialog(
                context: context,
                builder: (BuildContext context) =>
                    CustomerRating(context, sellerId: widget.sellerId));
          },
          child: RatingBarIndicator(
            rating: widget.rating,
            itemBuilder: (context, index) =>
                const Icon(Icons.star, color: Colors.amber),
            itemCount: 5,
            itemSize: 15.0,
            unratedColor: Colors.grey,
            direction: Axis.horizontal,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow("Seller", widget.sellerName, AppColors.green, () {
          showDialog(
              context: context,
              builder: (BuildContext context) =>
                  SellerInfoDialog(sellerId: widget.sellerId));
        }),
        _buildInfoRow(grade, widget.gradeText),
        _buildInfoRow(dateTime, formatter.format(DateTime.parse(widget.date))),
        _buildInfoRow(location, widget.locationText, AppColors.green),
        _buildInfoRow(qty, widget.qtyText),
        _buildInfoRow(noBid, "0"),
        _buildInfoRow(highest, roundString(widget.highestBid), AppColors.green),
        if (widget.fromSold) _buildEditablePriceRow(),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value,
      [Color? valueColor, VoidCallback? onTap]) {
    return Row(
      children: [
        SizedBox(
            width: 90,
            child: Text(label,
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold))),
        const Text(":"),
        const SizedBox(width: 5),
        GestureDetector(
          onTap: onTap,
          child: Text(
            value,
            style: TextStyle(
                fontWeight: FontWeight.bold, color: valueColor ?? Colors.black),
          ),
        ),
      ],
    );
  }

  Widget _buildEditablePriceRow() {
    return Row(
      children: [
        const SizedBox(
            width: 90,
            child: Text("Exp. Price",
                style: TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold))),
        const Text(":"),
        const SizedBox(width: 5),
        Text(widget.item.expectedPrice?.toStringAsFixed(2) ?? "",
            style: const TextStyle(
                color: AppColors.green, fontWeight: FontWeight.bold)),
        const SizedBox(width: 4),
        IconButton(
          onPressed: () async {
            var price = await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  title: const Text('Edit Reserved Price (Expected Price)'),
                  content: TextField(
                    controller: controller,
                    decoration: const InputDecoration(
                        hintText: "Enter new reserved price"),
                    keyboardType: TextInputType.number,
                  ),
                  actions: <Widget>[
                    ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor:
                              WidgetStateProperty.all(AppColors.red)),
                      onPressed: () => Navigator.pop(context),
                      child: const Text("Cancel"),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context, controller.text);
                        controller.clear();
                      },
                      style: ButtonStyle(
                          backgroundColor:
                              WidgetStateProperty.all(AppColors.primaryColor)),
                      child: const Text('Update'),
                    ),
                  ],
                );
              },
            );
            widget.editExpectedPrice(price);
          },
          constraints: const BoxConstraints(),
          padding: EdgeInsets.zero,
          icon: const Center(child: Icon(Icons.edit, size: 16)),
        ),
      ],
    );
  }

  Widget _buildActionsRow(BuildContext context, CardState state) {
    return Row(
      children: [
        const SizedBox(width: 10),
        Expanded(
          child: MaterialButton(
            onPressed: () {
              showDialog(
                  context: context,
                  builder: (BuildContext context) => ShowFeedbackDialogue(
                      title: "Thank you", stockId: widget.stockId));
            },
            height: 30,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            color: AppColors.primaryColor,
            textColor: AppColors.white,
            child: const Text(feedbackButton),
          ),
        ),
        const SizedBox(width: 10),
        if (widget.item.product?.tableType() == "CARDAMOM")
          Expanded(
            child: MaterialButton(
              onPressed: () {
                setState(() {
                  isOpen = !isOpen;
                });
              },
              height: 30,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              color: AppColors.primaryColor,
              textColor: AppColors.white,
              child: Text(isOpen ? "Close" : "Open"),
            ),
          ),
        const SizedBox(width: 10),
      ],
    );
  }

  Widget _buildExpandableContent(BuildContext context) {
    return Visibility(
      visible: isOpen,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildRadioOption("Table"),
              _buildRadioOption("Graph"),
            ],
          ),
          Visibility(
            visible: groupValue == "Table",
            child: AppTable(
                stockId: widget.stockId,
                tableType: widget.item.product?.tableType()),
          ),
          Visibility(
            visible: groupValue == "Graph",
            child: SizedBox(
              height: 200,
              child: Container(
                color: Colors.black,
                child: charts.BarChart(
                  seriesList,
                  animate: widget.animate,
                  barGroupingType: charts.BarGroupingType.grouped,
                  behaviors: [charts.SeriesLegend()],
                  domainAxis: charts.OrdinalAxisSpec(
                    renderSpec: charts.SmallTickRendererSpec(
                      labelStyle: charts.TextStyleSpec(
                        fontSize: 12,
                        color: charts.ColorUtil.fromDartColor(Colors.white),
                      ),
                    ),
                  ),
                  primaryMeasureAxis: charts.NumericAxisSpec(
                    renderSpec: charts.SmallTickRendererSpec(
                      labelStyle: charts.TextStyleSpec(
                        fontSize: 12,
                        color: charts.ColorUtil.fromDartColor(Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioOption(String label) {
    return Row(
      children: [
        Radio(
          value: label,
          groupValue: groupValue,
          onChanged: (value) {
            setState(() {
              groupValue = value as String;
            });
          },
          activeColor: AppColors.mainColor,
          fillColor: WidgetStateProperty.all(AppColors.mainColor),
        ),
        Text(label,
            style: const TextStyle(
                color: Colors.black, fontWeight: FontWeight.bold)),
      ],
    );
  }

  List<charts.Series<StockTable, String>> _createSampleData() {
    final cleanData = [
      StockTable(
          '>8mm',
          widget.item.grade8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      StockTable(
          '>7-8mm',
          widget.item.grade7T8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      StockTable(
          '<7mm',
          widget.item.grade17MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      StockTable(
          '<6mm',
          widget.item.rejectionsClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
    ];

    final splitData = [
      StockTable('>8mm', widget.item.grade8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      StockTable('>7-8mm', widget.item.grade7T8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      StockTable('<7mm', widget.item.grade17MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      StockTable('<6mm', widget.item.rejectionsSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
    ];

    final otherData = [
      StockTable(
          '>8mm',
          widget.item.grade8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      StockTable(
          '>7-8mm',
          widget.item.grade7T8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      StockTable(
          '<7mm',
          widget.item.grade17MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      StockTable(
          '<6mm',
          widget.item.rejectionsFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
    ];

    return [
      charts.Series<StockTable, String>(
        id: 'Clean',
        domainFn: (StockTable sales, _) => sales.year,
        measureFn: (StockTable sales, _) => sales.sales,
        data: cleanData,
        colorFn: (StockTable sales, _) => sales.barColor,
      ),
      charts.Series<StockTable, String>(
        id: 'Sick/Split',
        domainFn: (StockTable sales, _) => sales.year,
        measureFn: (StockTable sales, _) => sales.sales,
        data: splitData,
        colorFn: (StockTable sales, _) => sales.barColor,
      ),
      charts.Series<StockTable, String>(
        id: 'Other',
        domainFn: (StockTable sales, _) => sales.year,
        measureFn: (StockTable sales, _) => sales.sales,
        data: otherData,
        colorFn: (StockTable sales, _) => sales.barColor,
      ),
    ];
  }

  String roundString(String? numberString, {int decimalPlaces = 2}) {
    if (numberString == null || numberString.isEmpty) {
      return numberString ?? "";
    }

    try {
      double number = double.parse(numberString);
      return number.toStringAsFixed(decimalPlaces);
    } catch (e) {
      // Return the original string if parsing fails
      return numberString;
    }
  }
}

class CustomerRating extends StatefulWidget {
  final BuildContext context;
  final String sellerId;

  const CustomerRating(this.context, {Key? key, required this.sellerId})
      : super(key: key);

  @override
  State<CustomerRating> createState() => _CustomerRatingState();
}

class _CustomerRatingState extends State<CustomerRating> {
  @override
  void initState() {
    super.initState();
    context
        .read<rated.RatingPageBloc>()
        .add(rated.LoadRatingSummary(widget.sellerId));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(10),
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: BlocBuilder<rated.RatingPageBloc, rated.RatingpageState>(
        builder: (context, state) {
          if (state is rated.Loaded) {
            if (state.ratingSummary!.ratingPercentage!.isEmpty) {
              return _buildEmptyRating();
            }
            return _buildRatingContent(state);
          }
          return Center(child: progressIndicator);
        },
      ),
    );
  }

  Widget _buildEmptyRating() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.0)),
      margin: const EdgeInsets.only(top: 50),
      width: double.infinity,
      height: 300,
      child: const Center(
        child: Text('Nothing to show here!',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
      ),
    );
  }

  Widget _buildRatingContent(rated.Loaded state) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.0)),
      margin: const EdgeInsets.only(top: 50),
      width: double.infinity,
      height: 300,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text("Customer Reviews",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
          _buildRatingRow(state),
          Text(' ${state.ratingSummary!.totalFeedbacks} Global ratings ',
              style:
                  const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
          _buildRatingBars(state),
        ]),
      ),
    );
  }

  Widget _buildRatingRow(rated.Loaded state) {
    return Row(
      children: [
        RatingBarIndicator(
          rating: state.ratingSummary!.avgRating!.toDouble(),
          itemBuilder: (context, index) =>
              const Icon(Icons.star, color: Colors.amberAccent),
          itemCount: 5,
          itemSize: 15.0,
          unratedColor: Colors.grey,
          direction: Axis.horizontal,
        ),
        Text(' ${state.ratingSummary!.avgRating ?? 0} out of 5 ',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
        const Spacer(),
        GestureDetector(
          onTap: () {
            showDialog(
                context: context,
                builder: (BuildContext context) => ShowFeedbackDialogue(
                    title: "Put your Comments Here", stockId: widget.sellerId));
          },
          child: const Text('Write a review',
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.blue)),
        ),
      ],
    );
  }

  Widget _buildRatingBars(rated.Loaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 8),
        for (int i = 5; i > 0; i--)
          chartRow(context, '$i', comp(state, i) ?? 0, widget.sellerId),
        const SizedBox(height: 8),
      ],
    );
  }
}

Widget chartRow(
    BuildContext context, String label, double pct, String stockId) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Text(label, style: TextStyle(color: Colors.blue[800])),
      const SizedBox(width: 8),
      Text('Star',
          style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.blue[800])),
      Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(8, 5, 5, 8),
        child: GestureDetector(
          onTap: () {
            Navigator.pop(context);
            Get.to(() => RatingPage(context, k: label, stockId: stockId));
          },
          child: Stack(
            children: [
              Container(
                  width: kIsWeb ? 400 : MediaQuery.of(context).size.width * 0.6,
                  height: 20,
                  decoration: BoxDecoration(color: Colors.grey[400]),
                  child: const Text('')),
              Container(
                  width: MediaQuery.of(context).size.width * (pct / 100) * 0.5,
                  height: 20,
                  decoration: const BoxDecoration(color: Colors.orange),
                  child: const Text('')),
            ],
          ),
        ),
      ),
      Text('$pct%', style: TextStyle(color: Colors.blue[800])),
    ],
  );
}

double? comp(rated.Loaded state, int index) {
  return state.ratingSummary!.ratingPercentage!
      .firstWhereOrNull((element) => element.rating == index)
      ?.percentage;
}

class RatingPage extends StatefulWidget {
  final String k;
  final String stockId;

  const RatingPage(
    BuildContext context, {
    Key? key,
    required this.k,
    required this.stockId,
  }) : super(key: key);

  @override
  State<RatingPage> createState() => _RatingPageState();
}

class _RatingPageState extends State<RatingPage> {
  late ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    context
        .read<comm.GetCommentsBloc>()
        .add(comm.LoadComments(stockid: widget.stockId, rVal: widget.k));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        title: const Text('Edpark Bid Management'),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
      ),
      body: BlocBuilder<comm.GetCommentsBloc, comm.GetCommentsState>(
        builder: (context, state) {
          if (state is comm.Loaded) {
            return _buildCommentsList(state);
          }
          return Center(child: progressIndicator);
        },
      ),
    );
  }

  Widget _buildCommentsList(comm.Loaded state) {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollEndNotification &&
              scrollController.position.extentAfter == 0) {
            context.read<comm.GetCommentsBloc>().add(
                LoadNextComments(state.getComments, widget.stockId, widget.k));
          }
          return false;
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('FILTERED BY',
                        style: TextStyle(
                            fontSize: 15, fontWeight: FontWeight.bold)),
                    Row(
                      children: [
                        Text('${widget.k} star',
                            style:
                                const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(width: 30),
                      ],
                    ),
                    Text('${state.getComments?.totalElements} reviews'),
                    const SizedBox(height: 15),
                    const Divider(),
                    const Text('From India',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 10),
                    _buildComments(context, state),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildComments(BuildContext context, comm.Loaded state) {
    return ListView.builder(
      controller: scrollController,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) {
        return _buildCommentItem(context, state, index);
      },
      itemCount: state.getComments!.content!.length,
      shrinkWrap: true,
    );
  }

  Widget _buildCommentItem(BuildContext context, comm.Loaded state, int index) {
    return SizedBox(
      height: MediaQuery.of(context).size.height / 5,
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Row(
            children: [
              const CircleAvatar(
                backgroundImage: NetworkImage(
                    'https://images.unsplash.com/photo-1670272499232-d6c55af87386?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxlZGl0b3JpYWwtZmVlZHwxMHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=60'),
                radius: 15,
              ),
              const SizedBox(width: 15),
              Text(state.getComments!.content![index].customerName.toString() ??
                  'cshh'),
            ],
          ),
          Row(
            children: [
              RatingBarIndicator(
                rating: double.parse(widget.k),
                itemBuilder: (context, index) =>
                    const Icon(Icons.star, color: Colors.amber),
                itemCount: 5,
                itemSize: 15.0,
                unratedColor: Colors.grey,
                direction: Axis.horizontal,
              ),
              const SizedBox(width: 10),
              if (state.getComments!.content![index].verifiedPurchase ?? false)
                const Text('Verified Purchase',
                    style: TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                        fontSize: 12)),
            ],
          ),
          Text(state.getComments!.content![index].comment ?? 'cshh',
              style:
                  const TextStyle(fontSize: 15, fontWeight: FontWeight.bold)),
          Text(
              '${state.getComments!.content![index].helpfulCount} people found this helpful',
              style: const TextStyle(fontSize: 13, color: Colors.grey)),
          _buildCommentActions(context, state, index),
        ],
      ),
    );
  }

  Widget _buildCommentActions(
      BuildContext context, comm.Loaded state, int index) {
    return Row(
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.white),
          onPressed: () {
            context.read<comm.GetCommentsBloc>().add(comm.SetHelpful(
                rVal: widget.k,
                commentId: state.getComments!.content![index].id!.toInt(),
                stockId: widget.stockId));
          },
          child: const Text('Helpful', style: TextStyle(color: Colors.black)),
        ),
        TextButton(
          onPressed: () {
            showDialog(
                context: context,
                builder: (BuildContext context) => WriteReportDialogue(
                    title: "Put your Report Here",
                    stockId:
                        state.getComments!.content![index].id!.toString()));
          },
          child: const Text('Report'),
        ),
      ],
    );
  }
}

class StockTable {
  final String year;
  final int sales;
  final charts.Color barColor;

  StockTable(this.year, this.sales, this.barColor);
}
