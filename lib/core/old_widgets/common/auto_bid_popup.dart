// import 'package:connectone/bai_models/bai_products_res.dart';
// import 'package:connectone/old_blocs/offline_card/offline_card_bloc.dart';
// import 'package:connectone/old_models/firebase_response.dart';
// // import 'package:connectone/old_models/get_offline_stocks.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/const_dimens.dart';
// import 'package:connectone/core/utils/decorations.dart';
// import 'package:connectone/core/utils/theme_utils.dart';
// import 'package:fbroadcast/fbroadcast.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../old_models/favourites_response.dart';

// class AutoBidDialog extends StatelessWidget {
//   final String title;
//   final Content currentItem;
//   final FirebaseResponseOffline firebaseResponse;
//   final Datum1 autobidResponse;

//   AutoBidDialog({
//     Key? key,
//     required this.title,
//     required this.currentItem,
//     required this.firebaseResponse,
//     required this.autobidResponse,
//   }) : super(key: key);

//   final TextEditingController autoBidAmountController = TextEditingController();

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(DialogConstants.padding),
//       ),
//       elevation: 0,
//       backgroundColor: Colors.transparent,
//       child: Theme(
//         data: getBoldTheme(),
//         child: BlocBuilder<OfflineSubBloc, OfflineSubState>(
//           builder: (context, state) {
//             return SingleChildScrollView(
//               physics: const ClampingScrollPhysics(),
//               child: Stack(
//                 children: <Widget>[
//                   _buildDialogContent(context),
//                   _buildAvatar(),
//                   _buildCloseButton(context),
//                 ],
//               ),
//             );
//           },
//         ),
//       ),
//     );
//   }

//   Widget _buildDialogContent(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Container(
//           height: 320,
//           padding: const EdgeInsets.only(
//               left: DialogConstants.padding,
//               top: DialogConstants.avatarRadius + DialogConstants.padding,
//               right: DialogConstants.padding,
//               bottom: DialogConstants.padding),
//           margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
//           decoration: BoxDecoration(
//             shape: BoxShape.rectangle,
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(DialogConstants.padding),
//             boxShadow: const [
//               BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
//             ],
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: <Widget>[
//               Text(
//                 title,
//                 textAlign: TextAlign.center,
//                 style: const TextStyle(
//                   color: AppColors.green,
//                   fontSize: 20,
//                   fontFamily: 'poppins',
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 16),
//               Expanded(
//                 child: autobidResponse.autoBidStatus == AutoBidStatus.iact || autobidResponse.autoBidStatus == AutoBidStatus.cncl
//                     ? Text(
//                         "Bids will be automatically increased by +${currentItem.bidIncrementValue} till the maximum amount",
//                         textAlign: TextAlign.center,
//                         style: const TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.bold,
//                           fontSize: 14,
//                           fontFamily: 'poppins',
//                         ),
//                       )
//                     : const Text(
//                         "Please cancel your \nautobid",
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                           color: Colors.black,
//                           fontSize: 20,
//                           fontFamily: 'poppins',
//                         ),
//                       ),
//               ),
//               _buildAutoBidAmountTextField(),
//               const SizedBox(height: 8.0),
//               _buildActionButtons(context),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildAutoBidAmountTextField() {
//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         border: Border.all(color: Colors.black, width: 1.0),
//         borderRadius: BorderRadius.circular(8.0),
//       ),
//       child: TextField(
//         enabled: autobidResponse.autoBidStatus == AutoBidStatus.iact || autobidResponse.autoBidStatus == AutoBidStatus.cncl,
//         keyboardType: TextInputType.number,
//         controller: autoBidAmountController,
//         style: const TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
//         textAlign: TextAlign.center,
//         decoration: getTextInputDecoration(
//           autobidResponse.autoBidStatus == AutoBidStatus.iact || autobidResponse.autoBidStatus == AutoBidStatus.cncl
//               ? "Max bid amount"
//               : autobidResponse.autoBidAmount.toString(),
//         ),
//       ),
//     );
//   }

//   Widget _buildActionButtons(BuildContext context) {
//     return Visibility(
//       visible: autobidResponse.autoBidStatus == AutoBidStatus.iact || autobidResponse.autoBidStatus == AutoBidStatus.cncl,
//       replacement: Expanded(
//         child: Row(
//           children: [
//             Expanded(
//               child: SizedBox(
//                 height: 48,
//                 child: ElevatedButton(
//                   onPressed: () {
//                     Navigator.pop(context);
//                   },
//                   style: ButtonStyle(
//                     backgroundColor: WidgetStateProperty.all<Color>(AppColors.green),
//                     shape: WidgetStateProperty.all<RoundedRectangleBorder>(
//                       RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(5.0),
//                       ),
//                     ),
//                   ),
//                   child: const Text("Go Back"),
//                 ),
//               ),
//             ),
//             const SizedBox(width: 5),
//             Expanded(
//               child: SizedBox(
//                 height: 48,
//                 child: ElevatedButton(
//                   onPressed: () {
//                     context.read<OfflineSubBloc>().add(CancelAutobid(stockId: currentItem.id.toString()));
//                     Navigator.pop(context);
//                     FBroadcast.instance().broadcast("CLOSE_MO", value: "CLOSE_MO");
//                   },
//                   style: ButtonStyle(
//                     backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
//                     shape: WidgetStateProperty.all<RoundedRectangleBorder>(
//                       RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(5.0),
//                       ),
//                     ),
//                   ),
//                   child: const Text("Cancel"),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//       child: Expanded(
//         child: Row(
//           children: [
//             Expanded(
//               child: SizedBox(
//                 height: 48,
//                 child: ElevatedButton(
//                   onPressed: () {
//                     if (autobidResponse.autoBidStatus == AutoBidStatus.iact || autobidResponse.autoBidStatus == AutoBidStatus.cncl) {
//                       context.read<OfflineSubBloc>().add(
//                             Autobid(
//                               limit: int.parse(autoBidAmountController.text),
//                               orderTypeCd: "BIDD",
//                               stockId: currentItem.id!.toInt(),
//                               bidDeskNo: "Mobile",
//                               increment: currentItem.bidIncrementValue.toString(),
//                               quantity: currentItem.quantity!.toInt(),
//                             ),
//                           );
//                       Navigator.pop(context);
//                       FBroadcast.instance().broadcast("CLOSE_MO", value: "CLOSE_MO");
//                     }
//                   },
//                   style: ButtonStyle(
//                     backgroundColor: WidgetStateProperty.all<Color>(AppColors.green),
//                     shape: WidgetStateProperty.all<RoundedRectangleBorder>(
//                       RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(5.0),
//                       ),
//                     ),
//                   ),
//                   child: const Text("Ok"),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildAvatar() {
//     return Positioned(
//       left: DialogConstants.padding,
//       right: DialogConstants.padding,
//       child: CircleAvatar(
//         radius: 48,
//         backgroundColor: Colors.grey.shade200,
//         child: const CircleAvatar(
//           backgroundColor: Colors.white,
//           radius: 44,
//           child: Icon(
//             Icons.flight,
//             color: Colors.black,
//             size: 44,
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildCloseButton(BuildContext context) {
//     return Positioned(
//       top: 0,
//       right: 0,
//       child: GestureDetector(
//         onTap: () {
//           Navigator.pop(context);
//         },
//         child: const Icon(
//           Icons.cancel,
//           color: AppColors.white,
//         ),
//       ),
//     );
//   }
// }
