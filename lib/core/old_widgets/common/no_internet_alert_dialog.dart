import 'package:flutter/material.dart';

class NoInternetAlertDialog extends StatelessWidget {
  const NoInternetAlertDialog({
    Key? key,
    required this.onClick,
  }) : super(key: key);

  final VoidCallback onClick;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('No internet!'),
      content: const Text(
          'Make sure that WiFi or mobile data is turned on & try again.'),
      actions: [
        TextButton(
          style: TextButton.styleFrom(foregroundColor: const Color(0xFF6200EE)),
          onPressed: onClick,
          child: const Text('ACCEPT'),
        ),
      ],
    );
  }
}
