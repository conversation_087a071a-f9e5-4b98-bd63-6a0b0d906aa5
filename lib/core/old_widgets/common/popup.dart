import 'package:connectone/core/utils/const_dimens.dart';
import 'package:flutter/material.dart';
import '../../utils/colors.dart';

class Popup extends StatelessWidget {
  final String content;

  const Popup({Key? key, required this.content}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(fontFamily: "poppins"),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Stack(
            children: <Widget>[
              Container(
                height: 160,
                width: double.infinity,
                padding: const EdgeInsets.only(
                  left: DialogConstants.padding,
                  top: DialogConstants.avatarRadius + DialogConstants.padding,
                  right: DialogConstants.padding,
                  bottom: DialogConstants.padding,
                ),
                margin:
                    const EdgeInsets.only(top: DialogConstants.avatarRadius),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(DialogConstants.padding),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black,
                        offset: Offset(0, 10),
                        blurRadius: 10),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(
                      content,
                      style: const TextStyle(
                          fontSize: 20, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
              Positioned(
                left: DialogConstants.padding,
                right: DialogConstants.padding,
                child: CircleAvatar(
                  radius: 48,
                  backgroundColor: Colors.grey.shade200,
                  child: const CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 44,
                    child: Icon(
                      Icons.settings,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.cancel,
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
