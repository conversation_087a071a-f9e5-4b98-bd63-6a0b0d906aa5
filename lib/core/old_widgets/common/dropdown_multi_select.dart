import 'package:flutter/material.dart';
import 'package:multi_select_flutter/chip_display/multi_select_chip_display.dart';
import 'package:multi_select_flutter/dialog/multi_select_dialog_field.dart';
import 'package:multi_select_flutter/util/multi_select_item.dart';
import 'package:multi_select_flutter/util/multi_select_list_type.dart';
import 'package:connectone/core/utils/colors.dart';
import '../../../old_models/offline_filters_model.dart';
import '../../../old_models/search_model.dart';

class MultiSelectDropDown extends StatelessWidget {
  final List<Product> products;
  final String label;
  final Function(List<Product>) onPress;
  final List selectedEntities;

  const MultiSelectDropDown({
    Key? key,
    required this.products,
    required this.label,
    required this.onPress,
    required this.selectedEntities,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: MultiSelectDialogField<Product>(
        dialogHeight: 240,
        decoration: BoxDecoration(
          color: selectedEntities.isNotEmpty ? AppColors.primaryColor : null,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(32),
        ),
        initialValue: products
            .where((element) => selectedEntities.contains(element.name))
            .toList(),
        items: products
            .map((e) => MultiSelectItem(e, "${e.name}  (${e.stockCount})"))
            .toList(),
        listType: MultiSelectListType.LIST,
        chipDisplay: MultiSelectChipDisplay.none(),
        buttonText: Text(
          label,
          style: TextStyle(
              color: selectedEntities.isNotEmpty ? AppColors.white : null),
        ),
        buttonIcon: const Icon(Icons.arrow_drop_down),
        onConfirm: (values) => onPress(values),
      ),
    );
  }
}

class DropDownSavedSearches extends StatelessWidget {
  final List<SearchModel> products;
  final String label;
  final Function(List<SearchModel>) onPress;
  final List selectedEntities;

  const DropDownSavedSearches({
    Key? key,
    required this.products,
    required this.label,
    required this.onPress,
    required this.selectedEntities,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: MultiSelectDialogField<SearchModel>(
        dialogHeight: 240,
        decoration: BoxDecoration(
          color: selectedEntities.isNotEmpty ? AppColors.primaryColor : null,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(32),
        ),
        initialValue: products
            .where((element) => selectedEntities.contains(element.searchName))
            .toList(),
        items: products
            .map((e) => MultiSelectItem(e, e.searchName ?? ""))
            .toList(),
        listType: MultiSelectListType.LIST,
        chipDisplay: MultiSelectChipDisplay.none(),
        buttonText: Text(
          label,
          style: TextStyle(
              color: selectedEntities.isNotEmpty ? AppColors.white : null),
        ),
        buttonIcon: const Icon(Icons.arrow_drop_down),
        onConfirm: (values) => onPress(values),
      ),
    );
  }
}
