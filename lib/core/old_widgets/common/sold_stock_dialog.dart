import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'package:connectone/old_blocs/sold_out/soldout_bloc.dart';
import 'package:connectone/old_blocs/sold_out/soldout_event.dart';
import 'package:connectone/old_blocs/sold_out/soldout_state.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/custom_border.dart';

const double _kDateTimePickerHeight = 180;

class CustomSoldOutDialogBox extends StatelessWidget {
  final String title;

  CustomSoldOutDialogBox({
    Key? key,
    required this.title,
  }) : super(key: key);

  final DateTime dateTime = DateTime.now();
  final TextEditingController lotNumberController = TextEditingController();
  final TextEditingController stockIdController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SoldOutBloc(),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: BlocBuilder<SoldOutBloc, SoldOutState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Stack(
                children: <Widget>[
                  Container(
                    padding: const EdgeInsets.only(
                      left: DialogConstants.padding,
                      top: DialogConstants.avatarRadius +
                          DialogConstants.padding,
                      right: DialogConstants.padding,
                      bottom: DialogConstants.padding,
                    ),
                    margin: const EdgeInsets.only(
                        top: DialogConstants.avatarRadius),
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.circular(DialogConstants.padding),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(0, 10),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Text(
                          title,
                          style: TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 22,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: lotNumberController,
                          decoration:
                              getTextInputDecoration("ENTER LOT NUMBER"),
                        ),
                        const SizedBox(height: 10),
                        TextField(
                          controller: stockIdController,
                          decoration: getTextInputDecoration("ENTER STOCK ID"),
                        ),
                        const SizedBox(height: 10),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Select Delivery Date",
                            style: TextStyle(
                              color: AppColors.primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: _kDateTimePickerHeight,
                          child: CupertinoDatePicker(
                            mode: CupertinoDatePickerMode.date,
                            initialDateTime: dateTime,
                            onDateTimeChanged: (newDateTime) {
                              final DateFormat formatter =
                                  DateFormat('yyyy-MM-dd');
                              final String formattedDate =
                                  formatter.format(newDateTime);
                              context.read<SoldOutBloc>().add(
                                  DateSelected(deliveryDate: formattedDate));
                            },
                          ),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () => Navigator.pop(context),
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColors.red),
                                  shape: WidgetStateProperty.all<
                                      RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.0),
                                    ),
                                  ),
                                ),
                                child: const Text("Back"),
                              ),
                            ),
                            const SizedBox(width: 5),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  if (stockIdController.text.isEmpty &&
                                      lotNumberController.text.isNotEmpty) {
                                    context.read<SoldOutBloc>().add(
                                          StockSearchByLot(
                                            lotNo: lotNumberController.text,
                                            deliveryDate: context
                                                .read<SoldOutBloc>()
                                                .dateSelected,
                                          ),
                                        );
                                  } else if (stockIdController
                                      .text.isNotEmpty) {
                                    context.read<SoldOutBloc>().add(
                                          StocksSearch(
                                            stockId: stockIdController.text,
                                          ),
                                        );
                                  }
                                },
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                          AppColors.primaryColor),
                                  shape: WidgetStateProperty.all<
                                      RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5.0),
                                    ),
                                  ),
                                ),
                                child: const Text("Search"),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    left: DialogConstants.padding,
                    right: DialogConstants.padding,
                    child: CircleAvatar(
                      radius: 48,
                      backgroundColor: Colors.grey.shade200,
                      child: const CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 44,
                        child: Icon(
                          Icons.search,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: const Icon(
                        Icons.cancel,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  InputDecoration getTextInputDecoration(String hintText) {
    return InputDecoration(
      hintText: hintText,
      focusedBorder: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      enabledBorder: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      border: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      contentPadding: const EdgeInsets.all(15),
    );
  }
}
