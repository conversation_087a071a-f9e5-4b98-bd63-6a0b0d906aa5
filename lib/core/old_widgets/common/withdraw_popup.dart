import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';

//ignore: must_be_immutable
class Withdraw extends StatelessWidget {
  final String title;
  final Loaded state;

  Withdraw({
    Key? key,
    required this.title,
    required this.state,
  }) : super(key: key);

  final TextEditingController maxBidAmountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
      builder: (context, state) {
        if (state is Loaded) {
          return Theme(
            data: getMediumTheme(),
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(DialogConstants.padding),
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Stack(
                  children: <Widget>[
                    Column(
                      children: [
                        Container(
                          height: 240,
                          padding:
                              const EdgeInsets.all(DialogConstants.padding),
                          margin: const EdgeInsets.only(
                              top: DialogConstants.avatarRadius),
                          decoration: BoxDecoration(
                            shape: BoxShape.rectangle,
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(DialogConstants.padding),
                            boxShadow: const [
                              BoxShadow(
                                color: Colors.black,
                                offset: Offset(0, 10),
                                blurRadius: 10,
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                title,
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 20,
                                  // fontFamily: 'avenir_black',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              const Text(
                                "Are you sure you want to withdraw the lot?",
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const Spacer(),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => Navigator.pop(context),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppColors.red,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                        ),
                                      ),
                                      child: const Text("No"),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () {
                                        context
                                            .read<SellerLiveNotifsBloc>()
                                            .add(WithdrawLot(
                                                stockId: state.stock.data.id));
                                        Navigator.pop(context);
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppColors.primaryColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0),
                                        ),
                                      ),
                                      child: const Text("Yes"),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Positioned(
                      left: DialogConstants.padding,
                      right: DialogConstants.padding,
                      child: CircleAvatar(
                        radius: 48,
                        backgroundColor: Colors.grey.shade200,
                        child: CircleAvatar(
                          backgroundColor: Colors.white,
                          radius: 44,
                          child: Icon(
                            Icons.cancel,
                            size: 44,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(
                          Icons.cancel,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          return const Center(
            child: Text("Error occurred!"),
          );
        }
      },
    );
  }
}
