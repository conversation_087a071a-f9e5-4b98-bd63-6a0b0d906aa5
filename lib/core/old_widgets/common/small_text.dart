import 'package:flutter/material.dart';
import '../../utils/colors.dart';

class SmallText extends StatelessWidget {
  final Color color;
  final String text;
  final double size;
  final double height;

  const SmallText({
    Key? key,
    this.color = AppColors.primaryColorOld,
    required this.text,
    this.size = 14.0,
    this.height = 1.2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        color: color,
        fontFamily: 'Roboto',
        height: height,
        fontSize: size,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
