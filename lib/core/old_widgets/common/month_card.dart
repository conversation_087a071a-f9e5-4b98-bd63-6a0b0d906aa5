// import 'package:connectone/bai_models/bai_filter_res.dart';
// import 'package:flutter/material.dart';
// import '../../utils/colors.dart';

// String getMonthName(int month) {
//   const List<String> monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
//   return monthNames[month - 1];
// }

// class MonthCardBai extends StatelessWidget {
//   const MonthCardBai({
//     Key? key,
//     required this.summary,
//     required this.year,
//     required this.month,
//     required this.onCardClick,
//   }) : super(key: key);

//   final List<Summary> summary;
//   final String year;
//   final int month;
//   final VoidCallback onCardClick;

//   @override
//   Widget build(BuildContext context) {
//     var totalStockCount = getStockCount();
//     var totalStockWeight = getQuantity();
//     var style = const TextStyle(fontSize: 12);
//     var monthName = getMonthName(month);

//     return GestureDetector(
//       onTap: onCardClick,
//       child: Card(
//         clipBehavior: Clip.hardEdge,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(10),
//         ),
//         elevation: 2,
//         child: Column(
//           children: [
//             Expanded(
//               child: Container(
//                 color: totalStockCount > 0 ? AppColors.primaryColor : Colors.grey,
//                 width: double.maxFinite,
//                 child: Center(
//                   child: Padding(
//                     padding: const EdgeInsets.all(4.0),
//                     child: Text(
//                       monthName,
//                       style: const TextStyle(color: Colors.white),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//             const SizedBox(height: 4),
//             Text(
//               "$totalStockCount",
//               style: const TextStyle(
//                 fontWeight: FontWeight.bold,
//                 fontSize: 12,
//               ),
//             ),
//             const SizedBox(height: 2),
//             Text(
//               "PO Count",
//               style: style,
//             ),
//             const SizedBox(height: 4),
//             Text(
//               "${totalStockWeight.toStringAsFixed(2)} Kg",
//               style: const TextStyle(
//                 fontWeight: FontWeight.bold,
//                 fontSize: 12,
//               ),
//             ),
//             const SizedBox(height: 4),
//             Text(
//               "Quantity",
//               style: style,
//             ),
//             const SizedBox(height: 8),
//           ],
//         ),
//       ),
//     );
//   }

//   int getStockCount() {
//     int data = 0;
//     for (var summaryItem in summary) {
//       if (summaryItem.deliveryDate != null && summaryItem.deliveryDate!.year.toString() == year && summaryItem.deliveryDate!.month == month) {
//         data += summaryItem.stockCount?.toInt() ?? 0;
//       }
//     }
//     return data;
//   }

//   double getQuantity() {
//     double data = 0.0;
//     for (var summaryItem in summary) {
//       if (summaryItem.deliveryDate != null && summaryItem.deliveryDate!.year.toString() == year && summaryItem.deliveryDate!.month == month) {
//         data += summaryItem.stockCount?.toInt() ?? 0;
//       }
//     }
//     return data;
//   }
// }
