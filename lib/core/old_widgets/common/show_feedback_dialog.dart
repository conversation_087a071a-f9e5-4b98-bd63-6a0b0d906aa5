import 'package:connectone/old_blocs/my_stocks/mystocks_bloc.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_events.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_state.dart';
import 'package:connectone/old_models/feedback_data.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/custom_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

import '../../utils/tools.dart';

class ShowFeedbackDialogue extends StatefulWidget {
  final String title;
  final String stockId;

  const ShowFeedbackDialogue(
      {Key? key, required this.title, required this.stockId})
      : super(key: key);

  @override
  State<ShowFeedbackDialogue> createState() => _ShowFeedbackDialogueState();
}

class _ShowFeedbackDialogueState extends State<ShowFeedbackDialogue> {
  final TextEditingController commentController = TextEditingController();
  late double ratingVal = 0;

  @override
  Widget build(BuildContext context) {
    return _buildDialog(
      context,
      widget.title,
      [
        const Text("How do you rate your experience with us?",
            style: TextStyle(fontSize: 16), textAlign: TextAlign.center),
        const SizedBox(height: 10),
        RatingBar.builder(
          initialRating: 0,
          minRating: 1,
          unratedColor: Colors.grey.withOpacity(0.3),
          direction: Axis.horizontal,
          allowHalfRating: true,
          itemCount: 5,
          itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
          itemBuilder: (context, _) =>
              const Icon(Icons.star, color: Colors.amber),
          onRatingUpdate: (rating) => setState(() => ratingVal = rating),
        ),
        const SizedBox(height: 16),
        const Text(
          "We would like to get your feedback.",
          style: TextStyle(fontSize: 14),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        _buildCommentField(commentController),
        const SizedBox(height: 10),
        _buildActionButtons(context, () {
          if (commentController.text.isNotEmpty) {
            context.read<MyStocksBloc>().add(ShareFeedback(
                  data: FeedbackData(
                    customerId: int.tryParse(getCustomerId()) ?? 00000,
                    rating: ratingVal,
                    stockId: int.tryParse(widget.stockId) ?? 00000,
                    comment: commentController.text,
                  ),
                ));
            Navigator.pop(context);
          } else {
            alert("Please provide feedback!");
          }
        }),
      ],
    );
  }
}

class WriteReportDialogue extends StatefulWidget {
  final String title;
  final String stockId;

  const WriteReportDialogue(
      {Key? key, required this.title, required this.stockId})
      : super(key: key);

  @override
  State<WriteReportDialogue> createState() => _WriteReportDialogueState();
}

class _WriteReportDialogueState extends State<WriteReportDialogue> {
  final TextEditingController commentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return _buildDialog(
      context,
      widget.title,
      [
        const Text("We would like to get your feedback",
            style: TextStyle(fontSize: 16)),
        const SizedBox(height: 10),
        _buildCommentField(commentController),
        const SizedBox(height: 10),
        _buildActionButtons(context, () {
          if (commentController.text.isNotEmpty) {
            context.read<MyStocksBloc>().add(ShareReport(
                  stockId: widget.stockId,
                  report_comment: commentController.text,
                ));
            Navigator.pop(context);
          } else {
            alert("Please provide feedback!");
          }
        }),
      ],
    );
  }
}

Widget _buildDialog(BuildContext context, String title, List<Widget> children) {
  return Dialog(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(DialogConstants.padding),
    ),
    elevation: 0,
    backgroundColor: Colors.transparent,
    child: BlocBuilder<MyStocksBloc, MyStocksState>(
      builder: (context, state) {
        return SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Stack(
            children: <Widget>[
              Container(
                padding: const EdgeInsets.only(
                  left: DialogConstants.padding,
                  top: DialogConstants.avatarRadius + DialogConstants.padding,
                  right: DialogConstants.padding,
                  bottom: DialogConstants.padding,
                ),
                margin:
                    const EdgeInsets.only(top: DialogConstants.avatarRadius),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(DialogConstants.padding),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black,
                        offset: Offset(0, 10),
                        blurRadius: 10),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Text(title,
                        style: TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700)),
                    const SizedBox(height: 10),
                    ...children,
                  ],
                ),
              ),
              Positioned(
                left: DialogConstants.padding,
                right: DialogConstants.padding,
                child: CircleAvatar(
                  radius: 48,
                  backgroundColor: Colors.grey.shade200,
                  child: const CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 44,
                    child: Icon(Icons.check_box, color: Colors.black),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.cancel, color: AppColors.white),
                ),
              ),
            ],
          ),
        );
      },
    ),
  );
}

Widget _buildCommentField(TextEditingController controller) {
  return TextField(
    maxLines: 5,
    controller: controller,
    decoration: InputDecoration(
      hintText: "Write here...",
      focusedBorder: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      enabledBorder: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      border: SelectedInputBorderWithShadow(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(
            color: Color.fromARGB(255, 180, 178, 178), width: .1),
      ),
      contentPadding: const EdgeInsets.all(15),
    ),
  );
}

Widget _buildActionButtons(BuildContext context, VoidCallback onSubmit) {
  var style = const TextStyle(fontWeight: FontWeight.bold);
  return Row(
    children: [
      Expanded(
        child: ElevatedButton(
          onPressed: () => Navigator.pop(context),
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            ),
          ),
          child: Text(
            "Cancel",
            style: style,
          ),
        ),
      ),
      const SizedBox(width: 12),
      Expanded(
        child: ElevatedButton(
          onPressed: onSubmit,
          style: ButtonStyle(
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColors.primaryColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            ),
          ),
          child: Text(
            "Submit",
            style: style,
          ),
        ),
      ),
    ],
  );
}
