import 'dart:async';

import 'package:connectone/old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/old_models/highest_bid_after_close.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/live_auction_utils.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class AuctionClosed extends StatefulWidget {
  final String title;
  final Loaded state;
  final int countdown;

  const AuctionClosed({
    Key? key,
    required this.title,
    required this.state,
    required this.countdown,
  }) : super(key: key);

  @override
  State<AuctionClosed> createState() => _AuctionClosedState();
}

class _AuctionClosedState extends State<AuctionClosed> {
  Timer? timer;
  late int remaining = widget.countdown;
  String status = "";
  HighestBidAfterClose data = HighestBidAfterClose(
    data: Data(
      bidId: 0,
      stockId: 0,
      highestBidAmount: 0,
      startingPrice: 0,
      expectedPrice: 0,
    ),
    status: 0,
    statusDescription: '',
  );

  bool bidsReceived = true;

  @override
  void initState() {
    super.initState();
    startTimer();
    isAvailable();
    getHighestBidAfterClose();
  }

  void startTimer() {
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (remaining == 0) {
          setState(() {
            timer.cancel();
            status = "Time Exceeded";
          });
        } else {
          setState(() {
            remaining--;
            status = "Countdown: ${remaining.toMMSS()}";
          });
        }
      },
    );
  }

  Future<void> getHighestBidAfterClose() async {
    try {
      final result = await NetworkController()
          .getHighestBidAfterClose(widget.state.stock.data.id);
      setState(() {
        data = result;
      });
    } catch (e) {
      safePrint(e);
    }
  }

  Future<void> isAvailable() async {
    try {
      bidsReceived =
          await LiveUtils().bidsReceived(widget.state.stock.data.id.toString());
      setState(() {});
    } catch (e) {
      safePrint(e);
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: getMediumTheme(),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
          builder: (context, state) {
            if (state is Loaded) {
              if (remaining == 0) {
                context.read<SellerLiveNotifsBloc>().add(const Lock(true));
              } else {
                context.read<SellerLiveNotifsBloc>().add(const Lock(false));
              }
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Stack(
                  children: <Widget>[
                    _buildDialogContent(state),
                    _buildAvatar(),
                    _buildCloseButton(context),
                  ],
                ),
              );
            } else {
              return const Center(
                child: Text("Error occurred!"),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildDialogContent(Loaded state) {
    return Column(
      children: [
        Container(
          height: 360,
          padding: const EdgeInsets.only(
              left: DialogConstants.padding,
              top: DialogConstants.avatarRadius + DialogConstants.padding,
              right: DialogConstants.padding,
              bottom: DialogConstants.padding),
          margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Colors.white,
            borderRadius: BorderRadius.circular(DialogConstants.padding),
            boxShadow: const [
              BoxShadow(
                color: Colors.black,
                offset: Offset(0, 10),
                blurRadius: 10,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                "Lot No. ${state.stock.data.lotNo}",
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                status,
                style: const TextStyle(
                  color: AppColors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (!bidsReceived)
                Expanded(
                  child: Text(
                    "Lot ${state.stock.data.lotNo} did not get any bids.",
                    style: const TextStyle(
                      color: AppColors.red,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(height: 4),
              Text(
                "Starting Price - ${state.stock.data.startingPrice}",
                style: const TextStyle(
                  color: AppColors.green,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Expanded(
                child: Text(
                  "Control Price - ${state.stock.data.expectedPrice!.toStringAsFixed(2)}",
                  style: const TextStyle(
                    color: AppColors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              if (bidsReceived)
                Text(
                  "Highest Bid - ${data.data.highestBidAmount}",
                  style: const TextStyle(
                    color: AppColors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              const SizedBox(height: 4),
              if (bidsReceived)
                _buildActionButton(
                  context,
                  "Sell",
                  AppColors.green,
                  () async {
                    if (remaining > 0 && !state.isLocked) {
                      context.read<SellerLiveNotifsBloc>().add(AcceptBid(
                          stockId: state.stock.data.id,
                          bidId: data.data.bidId));
                      Get.back();
                    }
                  },
                ),
              const SizedBox(height: 8),
              _buildActionButton(
                context,
                "Withdraw",
                AppColors.red,
                () {
                  if (remaining > 0 && !state.isLocked) {
                    context
                        .read<SellerLiveNotifsBloc>()
                        .add(WithdrawLot(stockId: state.stock.data.id));
                    Get.back();
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.lock,
            size: 44,
            color: AppColors.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }

  Widget _buildActionButton(
      BuildContext context, String label, Color color, VoidCallback onPressed) {
    return Expanded(
      child: SizedBox(
        height: 40,
        width: double.infinity,
        child: ElevatedButton(
          onPressed: onPressed,
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all<Color>(color),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            ),
          ),
          child:
              Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
    );
  }
}
