import 'package:connectone/core/utils/circular_progress.dart';
import 'package:flutter/material.dart';

class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;

  const LoadingOverlay({Key? key, required this.child, required this.isLoading})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child, // Display the child widget
        if (isLoading)
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            right: 0,
            child: Container(
              color: Colors.white
                  .withOpacity(0.5), // Semi-transparent black background
              child: Center(
                child: progressIndicator, // Loading indicator
              ),
            ),
          ),
      ],
    );
  }
}
