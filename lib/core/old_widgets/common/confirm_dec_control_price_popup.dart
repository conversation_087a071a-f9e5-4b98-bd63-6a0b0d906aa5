import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../old_blocs/offline_stocks/offline_stocks_bloc.dart';
import '../../../old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';

class ConfirmDecControlPrice extends StatelessWidget {
  final String title;
  final Loaded state;

  ConfirmDecControlPrice({Key? key, required this.title, required this.state})
      : super(key: key);

  final TextEditingController maxBidAmountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DialogConstants.padding),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Theme(
            data: getMediumTheme(),
            child: BlocBuilder<OfflineMainBloc, OfflineMainState>(
              builder: (context, state) {
                return SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Stack(
                    children: <Widget>[
                      buildDialogContent(context),
                      buildCircleAvatar(),
                      buildCloseButton(context),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget buildDialogContent(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 320,
          padding: const EdgeInsets.only(
            left: DialogConstants.padding,
            top: DialogConstants.avatarRadius + DialogConstants.padding,
            right: DialogConstants.padding,
            bottom: DialogConstants.padding,
          ),
          margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Colors.white,
            borderRadius: BorderRadius.circular(DialogConstants.padding),
            boxShadow: const [
              BoxShadow(
                  color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                title,
                style: const TextStyle(
                    color: Colors.black,
                    fontSize: 22,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Spacer(),
              const Text(
                "Are you sure you want to decrease Control Price?",
                style: TextStyle(
                    color: Colors.black, fontSize: 16, fontFamily: 'poppins'),
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              const SizedBox(height: 24),
              buildButtons(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {},
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("No"),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: ElevatedButton(
            onPressed: () {},
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColors.primaryColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("Yes"),
          ),
        ),
      ],
    );
  }

  Widget buildCircleAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: const CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.settings,
            size: 44,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }
}
