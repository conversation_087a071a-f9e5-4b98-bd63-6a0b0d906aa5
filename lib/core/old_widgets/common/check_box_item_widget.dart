import 'package:flutter/material.dart';

class CheckBoxItemWidget extends StatelessWidget {
  final bool isChecked;
  final String label;
  final ValueChanged<bool?>? onChanged;

  const CheckBoxItemWidget({
    Key? key,
    required this.isChecked,
    required this.label,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          checkColor: Colors.white,
          value: isChecked,
          onChanged: onChanged,
        ),
        Text(label),
      ],
    );
  }
}
