import 'dart:async';

import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../../old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';

class SellOrWithdraw extends StatefulWidget {
  final String title;
  final Loaded state;
  final int countDown;

  const SellOrWithdraw({
    Key? key,
    required this.title,
    required this.state,
    required this.countDown,
  }) : super(key: key);

  @override
  _SellOrWithdrawState createState() => _SellOrWithdrawState();
}

class _SellOrWithdrawState extends State<SellOrWithdraw> {
  late Timer timer;
  late int remaining = widget.countDown;
  String status = "";

  void startTimer() {
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        setState(() {
          if (remaining == 0) {
            timer.cancel();
            status = "Time Exceeded";
          } else {
            remaining--;
            status = "Countdown: ${remaining.toMMSS()}";
          }
        });
      },
    );
  }

  @override
  void initState() {
    startTimer();
    super.initState();
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(fontFamily: 'poppins'),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
          builder: (context, state) {
            if (state is Loaded) {
              context.read<SellerLiveNotifsBloc>().add(Lock(remaining == 0));
              return _buildContent(context, state);
            } else {
              return const Center(child: Text("Error occurred!"));
            }
          },
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, Loaded state) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Stack(
        children: <Widget>[
          Column(
            children: [
              _buildDialogContainer(context, state),
            ],
          ),
          _buildTopIcon(),
          _buildCloseButton(context),
        ],
      ),
    );
  }

  Widget _buildDialogContainer(BuildContext context, Loaded state) {
    return Container(
      height: 360,
      padding: const EdgeInsets.only(
        left: DialogConstants.padding,
        top: DialogConstants.avatarRadius + DialogConstants.padding,
        right: DialogConstants.padding,
        bottom: DialogConstants.padding,
      ),
      margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: Colors.white,
        borderRadius: BorderRadius.circular(DialogConstants.padding),
        boxShadow: const [
          BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            widget.title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            "Lot No. ${state.stock.data.lotNo}",
            style: const TextStyle(
                color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Text(
            status,
            style: const TextStyle(
                color: AppColors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Text(
            "Lot ${state.stock.data.lotNo} did not get any bids.",
            style: const TextStyle(
                color: AppColors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Text(
            "Starting Price - ${state.stock.data.startingPrice}",
            style: const TextStyle(
                color: AppColors.green,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Text(
            "Control Price - ${state.stock.data.expectedPrice?.toStringAsFixed(2) ?? "0"}",
            style: const TextStyle(
                color: AppColors.green,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          _buildWithdrawButton(context, state),
        ],
      ),
    );
  }

  Widget _buildWithdrawButton(BuildContext context, Loaded state) {
    return SizedBox(
      height: 40,
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          if (!state.isLocked) {
            context
                .read<SellerLiveNotifsBloc>()
                .add(WithdrawLot(stockId: state.stock.data.id));
            Get.back();
          }
        },
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
        child: const Text("Withdraw",
            style: TextStyle(fontWeight: FontWeight.bold)),
      ),
    );
  }

  Widget _buildTopIcon() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.sell_outlined,
            color: AppColors.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(Icons.cancel, color: AppColors.white),
      ),
    );
  }
}
