// import 'package:connectone/bai_models/bai_products_res.dart';
// import 'package:connectone/core/utils/data_storage.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/const_dimens.dart';
// import 'package:connectone/core/utils/firebase_manager.dart';
// import 'package:connectone/core/utils/offline_utils.dart';
// import 'package:connectone/core/utils/theme_utils.dart';
// import 'package:connectone/old_models/firebase_response.dart';
// // import 'package:connectone/old_models/get_offline_stocks.dart';
// import 'package:connectone/core/old_widgets/common/auto_bid_popup.dart';
// import 'package:connectone/core/old_widgets/common/buy_now.dart';
// import 'package:connectone/core/old_widgets/common/make_an_offer_popup.dart';
// import 'package:fbroadcast/fbroadcast.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// import '../../../old_blocs/offline_card/offline_card_bloc.dart';
// import '../../utils/tools.dart';

// //ignore: must_be_immutable
// class MoreOptionsPopup extends StatelessWidget {
//   String title;
//   Content currentItem;
//   FirebaseResponseOffline firebaseResponse;
//   BuildContext parentContext;

//   MoreOptionsPopup({
//     Key? key,
//     required this.title,
//     required this.currentItem,
//     required this.firebaseResponse,
//     required this.parentContext,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(DialogConstants.padding),
//       ),
//       elevation: 0,
//       backgroundColor: Colors.transparent,
//       child: Theme(
//         data: getBoldTheme(),
//         child: BlocBuilder<OfflineSubBloc, OfflineSubState>(
//           builder: (context, state) {
//             FBroadcast.instance().register("CLOSE_MO", (value, callback) {
//               Navigator.pop(context);
//               FBroadcast.instance().unregister(this);
//             });
//             return SingleChildScrollView(
//               physics: const ClampingScrollPhysics(),
//               child: Stack(
//                 children: <Widget>[
//                   Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Container(
//                         height: 320,
//                         padding: const EdgeInsets.only(
//                           left: DialogConstants.padding,
//                           top: DialogConstants.avatarRadius + DialogConstants.padding,
//                           right: DialogConstants.padding,
//                           bottom: DialogConstants.padding,
//                         ),
//                         margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
//                         decoration: BoxDecoration(
//                           shape: BoxShape.rectangle,
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(DialogConstants.padding),
//                           boxShadow: const [
//                             BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
//                           ],
//                         ),
//                         child: Column(
//                           mainAxisSize: MainAxisSize.max,
//                           children: <Widget>[
//                             Text(
//                               title,
//                               textAlign: TextAlign.center,
//                               style: const TextStyle(
//                                 color: AppColors.green,
//                                 fontSize: 20,
//                                 // fontFamily: 'avenir_black',
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             const SizedBox(height: 16),
//                             Expanded(
//                               child: Container(
//                                 height: 40,
//                                 width: double.infinity,
//                                 decoration: BoxDecoration(
//                                   border: Border.all(color: AppColors.green, width: 2.0),
//                                   borderRadius: BorderRadius.circular(8.0),
//                                 ),
//                                 child: TextButton(
//                                   onPressed: () async {
//                                     if (await FirebaseManager().isAvailable(currentItem.id.toString())) {
//                                       var autobidResponse = await OfflineUtils().getAutobidData(currentItem.id.toString());
//                                       final cardContext = context.read<OfflineSubBloc>();
//                                       showDialog(
//                                         context: parentContext,
//                                         builder: (BuildContext context) {
//                                           return BlocProvider<OfflineSubBloc>.value(
//                                             value: cardContext,
//                                             child: AutoBidDialog(
//                                               title: "Autobid - Lot No ${currentItem.lotNo}",
//                                               currentItem: currentItem,
//                                               firebaseResponse: firebaseResponse,
//                                               autobidResponse: autobidResponse,
//                                             ),
//                                           );
//                                         },
//                                       );
//                                     } else {
//                                       alert("Already sold!");
//                                     }
//                                   },
//                                   child: const Text(
//                                     "Auto Bid",
//                                     style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             const SizedBox(
//                               height: 8.0,
//                             ),
//                             Expanded(
//                               child: Container(
//                                 height: 40,
//                                 width: double.infinity,
//                                 decoration: BoxDecoration(
//                                   border: Border.all(color: Colors.blue.shade900, width: 2.0),
//                                   borderRadius: BorderRadius.circular(8.0),
//                                 ),
//                                 child: TextButton(
//                                   onPressed: () async {
//                                     if (await FirebaseManager().isAvailable(currentItem.id.toString())) {
//                                       final offlineCardBloc = context.read<OfflineSubBloc>();
//                                       showDialog(
//                                         context: parentContext,
//                                         builder: (BuildContext context) {
//                                           return BlocProvider<OfflineSubBloc>.value(
//                                             value: offlineCardBloc,
//                                             child: MakeAnOfferDialog(
//                                               title: "Make An Offer - Lot No ${currentItem.lotNo}",
//                                               currentItem: currentItem,
//                                               firebaseResponse: firebaseResponse,
//                                             ),
//                                           );
//                                         },
//                                       );
//                                     } else {
//                                       alert("Already sold!");
//                                     }
//                                   },
//                                   child: const Text(
//                                     "Make An Offer",
//                                     style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             const SizedBox(height: 8.0),
//                             Expanded(
//                               child: (DataStorage.configData?.firstWhere((element) => element?.keyName1 == "buy_now_enabled_yn")?.valueName1 == "Y") &&
//                                       firebaseResponse.buyNowPrice.toDouble() != 0
//                                   ? Container(
//                                       height: 40,
//                                       width: double.infinity,
//                                       decoration: BoxDecoration(
//                                         border: Border.all(color: Colors.black54, width: 2.0),
//                                         borderRadius: BorderRadius.circular(8.0),
//                                       ),
//                                       child: TextButton(
//                                         onPressed: () async {
//                                           if (await FirebaseManager().isAvailable(currentItem.id.toString())) {
//                                             final cardContext = context.read<OfflineSubBloc>();
//                                             showDialog(
//                                               context: parentContext,
//                                               builder: (BuildContext context) {
//                                                 return BlocProvider<OfflineSubBloc>.value(
//                                                   value: cardContext,
//                                                   child: BuyNowDialog(
//                                                     title: "Buy Now - Lot No ${currentItem.lotNo}",
//                                                     currentItem: currentItem,
//                                                     firebaseResponse: firebaseResponse,
//                                                   ),
//                                                 );
//                                               },
//                                             );
//                                           } else {
//                                             alert("Already sold!");
//                                           }
//                                         },
//                                         child: Text(
//                                           "Buy Now - ${firebaseResponse.buyNowPrice.toStringAsFixed(0)}",
//                                           style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//                                         ),
//                                       ),
//                                     )
//                                   : const SizedBox(height: 40),
//                             ),
//                             const SizedBox(height: 8.0),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                   Positioned(
//                     left: DialogConstants.padding,
//                     right: DialogConstants.padding,
//                     child: CircleAvatar(
//                       radius: 48,
//                       backgroundColor: Colors.grey.shade200,
//                       child: CircleAvatar(
//                         backgroundColor: Colors.white,
//                         radius: 44,
//                         child: Icon(
//                           Icons.settings,
//                           color: AppColors.primaryColor,
//                           size: 44,
//                         ),
//                       ),
//                     ),
//                   ),
//                   Positioned(
//                     top: 0,
//                     right: 0,
//                     child: GestureDetector(
//                       onTap: () {
//                         Navigator.pop(context);
//                       },
//                       child: const Icon(
//                         Icons.cancel,
//                         color: AppColors.white,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             );
//           },
//         ),
//       ),
//     );
//   }
// }
