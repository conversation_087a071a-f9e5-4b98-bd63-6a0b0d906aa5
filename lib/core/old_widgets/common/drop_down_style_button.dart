import 'package:flutter/material.dart';
import 'package:connectone/core/utils/colors.dart';

class FilterDropDown extends StatelessWidget {
  final String label;
  final bool isActive;
  final VoidCallback onPress;
  final bool? isSelected;

  const FilterDropDown({
    Key? key,
    required this.label,
    required this.onPress,
    required this.isActive,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onPress,
        child: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            border: Border.all(
                color: isSelected! ? Colors.tealAccent : Colors.grey,
                width: isSelected! ? 2 : 1),
            color: isActive ? AppColors.primaryColor : Colors.white,
            borderRadius: BorderRadius.circular(32),
          ),
          child: Row(
            children: [
              Text(
                label,
                style: TextStyle(color: isActive ? Colors.white : Colors.black),
              ),
              const Spacer(),
              Icon(
                Icons.arrow_drop_down,
                color: isActive ? Colors.white : Colors.black,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
