import 'package:flutter/material.dart';

import '../../utils/colors.dart';
import '../../utils/const_dimens.dart';
import '../../utils/theme_utils.dart';

class IncreaseBidAmountPopup extends StatefulWidget {
  final String title;
  final BuildContext parentContext;
  final double currentBid;

  const IncreaseBidAmountPopup({
    Key? key,
    required this.title,
    required this.parentContext,
    required this.currentBid,
  }) : super(key: key);

  @override
  State<IncreaseBidAmountPopup> createState() => _IncreaseBidAmountPopupState();
}

class _IncreaseBidAmountPopupState extends State<IncreaseBidAmountPopup> {
  var yourBid = "";

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DialogConstants.padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Theme(
        data: getBoldTheme(),
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: <PERSON><PERSON>(
            children: <Widget>[
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                      left: DialogConstants.padding,
                      top: 40,
                      right: DialogConstants.padding,
                      bottom: DialogConstants.padding,
                    ),
                    width: double.infinity,
                    margin: const EdgeInsets.only(
                      top: DialogConstants.avatarRadius,
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        DialogConstants.padding,
                      ),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(0, 10),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Text(
                          widget.title,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: AppColors.green,
                            fontSize: 20,
                            // fontFamily: 'avenir_black',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              "Current Bid - ₹${widget.currentBid}",
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: AppColors.green,
                                fontSize: 20,
                                // fontFamily: 'avenir_black',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              "Increase Bid Amount by",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                                // fontFamily: 'avenir_black',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              height: 216,
                              child: CustomGrid(
                                onPressed: (val) {
                                  if (val == 11) {
                                    yourBid = "";
                                    Navigator.pop(context, null);
                                  } else {
                                    setState(() {
                                      yourBid += val.toString();
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              width: double.infinity,
                              height: 40,
                              child: Center(
                                child: Text(
                                  "Your Bid - ₹${widget.currentBid + (int.tryParse(yourBid) ?? 0)}",
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    // fontFamily: 'avenir_black',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.green,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              width: double.infinity,
                              height: 40,
                              clipBehavior: Clip.hardEdge,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context, [
                                    widget.currentBid +
                                        (int.tryParse(yourBid) ?? 0)
                                  ]);
                                },
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.all(AppColors.green),
                                ),
                                child: const Text(
                                  "Submit",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    // fontFamily: 'avenir_black',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Positioned(
                left: DialogConstants.padding,
                right: DialogConstants.padding,
                child: CircleAvatar(
                  radius: 48,
                  backgroundColor: Colors.grey.shade200,
                  child: CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 44,
                    child: Icon(
                      Icons.check_box,
                      color: AppColors.primaryColor,
                      size: 44,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () {
                    Navigator.pop(context, null);
                  },
                  child: const Icon(
                    Icons.cancel,
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomGrid extends StatelessWidget {
  final Function(int) onPressed;

  const CustomGrid({Key? key, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // First row with three buttons
        Row(
          children: [
            buildButton(1),
            buildButton(2),
            buildButton(3),
          ],
        ),
        // Second row with three buttons
        Row(
          children: [
            buildButton(4),
            buildButton(5),
            buildButton(6),
          ],
        ),
        // Third row with three buttons
        Row(
          children: [
            buildButton(7),
            buildButton(8),
            buildButton(9),
          ],
        ),
        // Fourth row with one normal-sized button and one double-width button
        Row(
          children: [
            buildButton(0),
            buildDoubleWidthButton(11),
          ],
        ),
      ],
    );
  }

  Widget buildButton(int index) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            onPressed(index);
          },
          borderRadius: BorderRadius.circular(8.0),
          child: Container(
            height: 36,
            margin: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey),
            ),
            child: Center(
              child: Text(
                '$index',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  // fontFamily: 'avenir_black',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildDoubleWidthButton(int index) {
    return Expanded(
      flex: 2,
      child: InkWell(
        onTap: () {
          onPressed(index);
        },
        borderRadius: BorderRadius.circular(8.0),
        child: Container(
          height: 36,
          margin: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Colors.grey),
          ),
          child: Center(
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppColors.primaryColor,
                // fontFamily: 'avenir_black',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
