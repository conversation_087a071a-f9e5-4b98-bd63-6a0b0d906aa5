import 'package:flutter/material.dart';
import '../../utils/colors.dart';
import '../../utils/table_utils.dart';

class CardamomTable extends StatelessWidget {
  final double? grade8MmClean;
  final double? grade8MmSickdsplit;
  final double? grade8MmFruit;
  final double? grade8MmTotalPercentage;
  final double? grade7T8MmClean;
  final double? grade7T8MmSickdsplit;
  final double? grade7T8MmFruit;
  final double? grade7T8MmTotalPercentage;
  final double? grade17MmClean;
  final double? grade17MmSickdsplit;
  final double? grade17MmFruit;
  final double? grade17MmTotalPercentage;
  final double? totalSickdsplit;
  final double? totalFruit;
  final double? totalPercentage;
  final String? size;
  final double? quantity;
  final String? literWeight;
  final String? moisture;
  final String? colour;
  final double? rejectionsClean;
  final double? totalClean;
  final double? rejectionsSickdsplit;
  final double? rejectionsFruit;
  final double? rejectionsTotalpercentage;

  const CardamomTable({
    Key? key,
    required this.grade8MmClean,
    required this.grade8MmSickdsplit,
    required this.grade8MmFruit,
    this.grade8MmTotalPercentage = 0,
    required this.grade7T8MmClean,
    required this.grade7T8MmSickdsplit,
    required this.grade7T8MmFruit,
    this.grade7T8MmTotalPercentage = 0,
    required this.grade17MmClean,
    required this.grade17MmSickdsplit,
    required this.grade17MmFruit,
    this.grade17MmTotalPercentage = 0,
    this.totalSickdsplit = 0,
    this.totalFruit = 0,
    this.totalPercentage = 0,
    required this.size,
    required this.quantity,
    required this.literWeight,
    required this.moisture,
    required this.colour,
    required this.totalClean,
    required this.rejectionsClean,
    required this.rejectionsSickdsplit,
    required this.rejectionsFruit,
    this.rejectionsTotalpercentage = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: true,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTable1(),
            const SizedBox(height: 16.0),
            _buildTable2(),
          ],
        ),
      ),
    );
  }

  Widget _buildTable1() {
    return Table(
      border: TableBorder.all(
          borderRadius: BorderRadius.circular(12.0), color: AppColors.green),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0)),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Size"),
            TableHeadingCommon("Bags"),
            TableHeadingCommon("Ltr.Wt."),
            TableHeadingCommon("Moisture"),
            TableHeadingCommon("Colour"),
          ],
        ),
        TableRow(
          children: [
            TableContentCommon(content: size ?? '', isBold: true),
            TableContentCommon(
                content: quantity?.toString() ?? '', isBold: true),
            TableContentCommon(content: literWeight ?? '', isBold: true),
            TableContentCommon(content: moisture ?? '', isBold: true),
            TableContentCommon(content: colour ?? '', isBold: true),
          ],
        ),
      ],
    );
  }

  Widget _buildTable2() {
    return Table(
      border: TableBorder.all(
          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
          color: AppColors.green),
      children: [
        const TableRow(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0)),
            color: AppColors.green,
          ),
          children: [
            TableHeadingCommon("Grade"),
            TableHeadingCommon("Clean"),
            TableHeadingCommon("Sick/Split"),
            TableHeadingCommon("Total"),
          ],
        ),
        _buildTableRow("8+ mm", grade8MmClean, grade8MmSickdsplit,
            grade8MmTotalPercentage),
        _buildTableRow("7-8 mm", grade7T8MmClean, grade7T8MmSickdsplit,
            grade7T8MmTotalPercentage),
        _buildTableRow("6-7 mm", grade17MmClean, grade17MmSickdsplit,
            grade17MmTotalPercentage),
        _buildTableRow("<6 mm", rejectionsClean, rejectionsSickdsplit,
            rejectionsTotalpercentage),
        TableRow(
          children: [
            const TableContentCommon(content: "Total", isBold: true),
            TableContentCommon(
                content: totalClean?.toString() ?? '', isBold: true),
            TableContentCommon(
                content: totalSickdsplit?.toString() ?? '', isBold: true),
            const TableContentCommon(content: "100", isBold: true),
          ],
        ),
      ],
    );
  }

  TableRow _buildTableRow(
      String label, double? clean, double? sickSplit, double? totalPercentage) {
    return TableRow(
      children: [
        TableContentCommon(content: label, isBold: true),
        TableContentCommon(content: clean?.toString() ?? ''),
        TableContentCommon(content: sickSplit?.toString() ?? ''),
        TableContentCommon(content: totalPercentage?.toString() ?? ''),
      ],
    );
  }
}
