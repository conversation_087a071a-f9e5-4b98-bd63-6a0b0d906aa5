import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../../old_screens/offline_filters/offline_filters_bloc.dart';
import '../../utils/custom_border.dart';
import '../../utils/tools.dart';

class SaveSearchDialog extends StatefulWidget {
  final String title;
  final String product;
  final String location;
  final String grade;
  final String quantityStart;
  final String priceStart;
  final String buyBidPriceStart;
  final String quantityEnd;
  final String priceEnd;
  final String buyBidPriceEnd;

  const SaveSearchDialog({
    Key? key,
    required this.title,
    required this.product,
    required this.location,
    required this.grade,
    required this.quantityStart,
    required this.priceStart,
    required this.buyBidPriceStart,
    required this.quantityEnd,
    required this.priceEnd,
    required this.buyBidPriceEnd,
  }) : super(key: key);

  @override
  _SaveSearchDialogState createState() => _SaveSearchDialogState();
}

class _SaveSearchDialogState extends State<SaveSearchDialog> {
  final TextEditingController searchNameController = TextEditingController();
  final TextEditingController minQuantityController = TextEditingController();
  final TextEditingController maxQuantityController = TextEditingController();
  final TextEditingController minBidController = TextEditingController();
  final TextEditingController maxBidController = TextEditingController();
  final TextEditingController minBuyController = TextEditingController();
  final TextEditingController maxBuyController = TextEditingController();

  bool daily = false;
  bool weekly = false;
  bool biweekly = false;
  bool monthly = false;

  bool sun = false;
  bool mon = false;
  bool tue = false;
  bool wed = false;
  bool thu = false;
  bool fri = false;
  bool sat = false;

  void setRecurrent(String key) {
    setState(() {
      // daily = weekly = biweekly = monthly = false;
      switch (key) {
        case "daily":
          daily = !daily;
          break;
        case "weekly":
          weekly = !weekly;
          break;
        case "biweekly":
          biweekly = !biweekly;
          break;
        case "monthly":
          monthly = !monthly;
          break;
      }
    });
  }

  @override
  void initState() {
    super.initState();
    setValues();
  }

  void setValues() {
    minQuantityController.text = widget.quantityStart;
    maxQuantityController.text = widget.quantityEnd;
    minBidController.text = widget.priceStart;
    maxBidController.text = widget.priceEnd;
    minBuyController.text = widget.buyBidPriceStart;
    maxBuyController.text = widget.buyBidPriceEnd;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: getMediumTheme(),
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: BlocBuilder<OfflineFilterCubit, OfflineFilterState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Stack(
                children: <Widget>[
                  Container(
                    height: 656,
                    padding: const EdgeInsets.only(
                      left: DialogConstants.padding,
                      top: DialogConstants.avatarRadius +
                          DialogConstants.padding,
                      right: DialogConstants.padding,
                      bottom: DialogConstants.padding,
                    ),
                    margin: const EdgeInsets.only(
                        top: DialogConstants.avatarRadius),
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.circular(DialogConstants.padding),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(0, 10),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Text(
                          widget.title,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text("Do you want to save the search?"),
                        const SizedBox(height: 8),
                        _buildTextField(
                            searchNameController, "Enter Search Name"),
                        const SizedBox(height: 16),
                        _buildTable(),
                        const SizedBox(height: 16),
                        _buildSelectDays(),
                        _buildRecurrentOptions(),
                        const SizedBox(height: 16),
                        _buildButtons(context),
                      ],
                    ),
                  ),
                  _buildAvatar(),
                  _buildCloseButton(context),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller, String hintText) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primaryColor, width: 1.0),
        borderRadius: BorderRadius.circular(32.0),
      ),
      child: TextField(
        controller: controller,
        style: TextStyle(
            color: AppColors.primaryColor, fontStyle: FontStyle.italic),
        decoration: InputDecoration(
          hintText: hintText,
          isDense: true,
          hintStyle: TextStyle(
              color: AppColors.primaryColor, fontStyle: FontStyle.italic),
          contentPadding: const EdgeInsets.all(8),
          focusedBorder: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(32),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
          enabledBorder: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(32),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
          border: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(32),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
        ),
      ),
    );
  }

  Widget _buildTable() {
    return Table(
      border: TableBorder.all(),
      children: [
        _buildTableRow(["Price", "Min", "Max"]),
        _buildTableRowWithTextFields(
            "Quantity", minQuantityController, maxQuantityController),
        _buildTableRowWithTextFields(
            "Bid Price", minBidController, maxBidController),
        _buildTableRowWithTextFields(
            "Buy Now Price", minBuyController, maxBuyController),
      ],
    );
  }

  TableRow _buildTableRow(List<String> cells) {
    return TableRow(
      children: cells
          .map((cell) => Text(
                cell,
                textAlign: TextAlign.center,
              ))
          .toList(),
    );
  }

  TableRow _buildTableRowWithTextFields(
      String label,
      TextEditingController minController,
      TextEditingController maxController) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Text(
            label,
            textAlign: TextAlign.center,
          ),
        ),
        _buildTextFieldInTable(minController),
        _buildTextFieldInTable(maxController),
      ],
    );
  }

  Widget _buildTextFieldInTable(TextEditingController controller) {
    return TextField(
      controller: controller,
      keyboardType: TextInputType.number,
      decoration: const InputDecoration(hintText: "..."),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSelectDays() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Select Days"),
        const SizedBox(height: 8),
        Row(
          children: _buildDayLabels(),
        ),
        Row(
          children: _buildDayCheckboxes(),
        ),
      ],
    );
  }

  List<Widget> _buildDayLabels() {
    return [
      "Sun",
      "Mon",
      "Tue",
      "Wed",
      "Thu",
      "Fri",
      "Sat",
    ]
        .map((day) => Expanded(
            child: Text(day,
                textAlign: TextAlign.center,
                style: TextStyle(color: AppColors.primaryColor))))
        .toList();
  }

  List<Widget> _buildDayCheckboxes() {
    return [
      sun,
      mon,
      tue,
      wed,
      thu,
      fri,
      sat,
    ].asMap().entries.map((entry) {
      int idx = entry.key;
      bool value = entry.value;
      return Expanded(
        child: Checkbox(
          value: value,
          onChanged: (bool? newValue) {
            setState(() {
              switch (idx) {
                case 0:
                  sun = !sun;
                  break;
                case 1:
                  mon = !mon;
                  break;
                case 2:
                  tue = !tue;
                  break;
                case 3:
                  wed = !wed;
                  break;
                case 4:
                  thu = !thu;
                  break;
                case 5:
                  fri = !fri;
                  break;
                case 6:
                  sat = !sat;
                  break;
              }
            });
          },
          activeColor: AppColors.primaryColor,
        ),
      );
    }).toList();
  }

  Widget _buildRecurrentOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Recurrence"),
        const SizedBox(height: 8),
        Row(
          children: _buildRecurrentLabels(),
        ),
        Row(
          children: _buildRecurrentCheckboxes(),
        ),
      ],
    );
  }

  List<Widget> _buildRecurrentLabels() {
    return [
      "Daily",
      "Weekly",
      "Biweekly",
      "Monthly",
    ]
        .map((label) => Expanded(
            child: Text(label,
                textAlign: TextAlign.center,
                style: TextStyle(color: AppColors.primaryColor))))
        .toList();
  }

  List<Widget> _buildRecurrentCheckboxes() {
    return [
      daily,
      weekly,
      biweekly,
      monthly,
    ].asMap().entries.map((entry) {
      int idx = entry.key;
      bool value = entry.value;
      return Expanded(
        child: Checkbox(
          value: value,
          onChanged: (bool? newValue) {
            setState(() {
              switch (idx) {
                case 0:
                  setRecurrent("daily");
                  break;
                case 1:
                  setRecurrent("weekly");
                  break;
                case 2:
                  setRecurrent("biweekly");
                  break;
                case 3:
                  setRecurrent("monthly");
                  break;
              }
            });
          },
          activeColor: AppColors.primaryColor,
        ),
      );
    }).toList();
  }

  Widget _buildButtons(BuildContext context) {
    return Expanded(
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: ButtonStyle(
                  backgroundColor:
                      WidgetStateProperty.all<Color>(AppColors.red),
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                  ),
                ),
                child: const Text("Back"),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: SizedBox(
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  List<String> days = [];
                  if (sun) days.add("sunday");
                  if (mon) days.add("monday");
                  if (tue) days.add("tuesday");
                  if (wed) days.add("wednesday");
                  if (thu) days.add("thursday");
                  if (fri) days.add("friday");
                  if (sat) days.add("saturday");

                  if (searchNameController.text.isEmpty) {
                    alert("Please fill in the search name!");
                  } else {
                    final searchName = searchNameController.text;
                    print('searchName: $searchName');

                    final buyBidPriceFrom = minBidController.text.isNotEmpty
                        ? _parseNumber(minBidController.text)
                        : null;
                    print('buyBidPriceFrom: $buyBidPriceFrom');

                    final buyBidPriceTo = maxBidController.text.isNotEmpty
                        ? _parseNumber(maxBidController.text)
                        : null;
                    print('buyBidPriceTo: $buyBidPriceTo');

                    final priceFrom = minBuyController.text.isNotEmpty
                        ? _parseNumber(minBuyController.text)
                        : null;
                    print('priceFrom: $priceFrom');

                    final priceTo = maxBuyController.text.isNotEmpty
                        ? _parseNumber(maxBuyController.text)
                        : null;
                    print('priceTo: $priceTo');

                    final quantityFrom = minQuantityController.text.isNotEmpty
                        ? _parseNumber(minQuantityController.text)
                        : null;
                    print('quantityFrom: $quantityFrom');

                    final quantityTo = maxQuantityController.text.isNotEmpty
                        ? _parseNumber(maxQuantityController.text)
                        : null;
                    print('quantityTo: $quantityTo');

                    if ((buyBidPriceFrom == 0 && buyBidPriceTo == 0) &&
                        (quantityFrom == 0 && quantityTo == 0) &&
                        (priceFrom == 0 && priceTo == 0)) {
                      alert("Please fill in at least one field!");
                    } else if (buyBidPriceFrom == null ||
                        buyBidPriceTo == null) {
                      alert(
                          "Please fill in both 'Buy Bid Price Min' and 'Buy Bid Price Max' fields!");
                    } else if (priceFrom == null || priceTo == null) {
                      alert(
                          "Please fill in both 'Price Mix' and 'Price Max' fields!");
                    } else if (quantityFrom == null || quantityTo == null) {
                      alert(
                          "Please fill in both 'Quantity Min' and 'Quantity Max' fields!");
                    } else if (buyBidPriceFrom > buyBidPriceTo) {
                      alert(
                          "'Buy Bid Price Min' cannot be greater than 'Buy Bid Price Max'!");
                    } else if (priceFrom > priceTo) {
                      alert("'Price Min' cannot be greater than 'Price Max'!");
                    } else if (quantityFrom > quantityTo) {
                      alert(
                          "'Quantity Min' cannot be greater than 'Quantity Max'!");
                    } else {
                      final days1 = days;
                      final recurrence = daily
                          ? "daily"
                          : weekly
                              ? "weekly"
                              : biweekly
                                  ? "biweekly"
                                  : monthly
                                      ? "monthly"
                                      : "";
                      final location = widget.location;
                      final product = widget.product;
                      context.read<OfflineFilterCubit>().saveSearch();
                      Get.back();
                    }
                  }
                },
                style: ButtonStyle(
                  backgroundColor:
                      WidgetStateProperty.all<Color>(AppColors.primaryColor),
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                  ),
                ),
                child: const Text("Save"),
              ),
            ),
          ),
        ],
      ),
    );
  }

  int? _parseNumber(String text) {
    try {
      return double.parse(text).toInt();
    } catch (e) {
      return null;
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style:
          TextStyle(fontWeight: FontWeight.bold, color: AppColors.primaryColor),
      textAlign: TextAlign.start,
    );
  }

  Widget _buildAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.save,
            size: 44,
            color: AppColors.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }
}
