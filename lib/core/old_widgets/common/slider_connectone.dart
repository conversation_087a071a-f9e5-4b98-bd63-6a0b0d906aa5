import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

import '../../utils/colors.dart';
import '../../utils/slider_custom_thumb.dart';

class SliderConnectOne extends StatefulWidget {
  final SfRangeValues values;
  double min;
  double max;
  final Function(SfRangeValues) callback;
  final Function(SfRangeValues) completedCallback;

  SliderConnectOne({
    Key? key,
    required this.min,
    required this.max,
    required this.values,
    required this.callback,
    required this.completedCallback,
  }) : super(key: key);

  @override
  State<SliderConnectOne> createState() => _SliderConnectOneState();
}

class _SliderConnectOneState extends State<SliderConnectOne> {
  late SfRangeValues _values;

  @override
  void initState() {
    super.initState();
    _values = widget.values;
  }

  @override
  Widget build(BuildContext context) {
    _validateValues();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: SfRangeSlider(
        min: widget.min,
        max: widget.max,
        activeColor: AppColors.primaryColor,
        values: _values,
        interval: widget.max - widget.min,
        stepSize: 0.1,
        showTicks: false,
        showLabels: false,
        enableTooltip: true,
        minorTicksPerInterval: 0,
        thumbShape: CustomThumb(
          textScaleFactor: MediaQuery.of(context).textScaleFactor,
          values: _values,
        ),
        onChangeEnd: (SfRangeValues values) {
          widget.completedCallback(values);
        },
        onChanged: (SfRangeValues values) {
          setState(() {
            _values = values;
          });
          widget.callback(values);
        },
      ),
    );
  }

  void _validateValues() {
    if (_values.start < widget.min) {
      _values = SfRangeValues(widget.min, _values.end);
    }
    if (_values.end > widget.max) {
      _values = SfRangeValues(_values.start, widget.max);
    }
    if (_values.start > _values.end) {
      _values = SfRangeValues(_values.start, _values.start);
    }
    if (widget.min < 0) {
      widget.min = 0;
    }
    if (widget.max < widget.min) {
      double temp = widget.min;
      widget.min = widget.max;
      widget.max = temp;
    }
  }
}
