import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../../../old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';
import '../../utils/custom_border.dart';

class DecControlPrice extends StatelessWidget {
  final String title;
  final Loaded state;
  final TextEditingController amountController = TextEditingController();

  DecControlPrice({Key? key, required this.title, required this.state})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
      builder: (context, state) {
        return Theme(
          data: getMediumTheme(),
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(DialogConstants.padding),
            ),
            elevation: 0,
            backgroundColor: Colors.transparent,
            child: BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
              builder: (context, state) {
                if (state is Loaded) {
                  return SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Stack(
                      children: <Widget>[
                        _buildDialogContainer(context),
                        _buildCircleAvatar(),
                        _buildCloseButton(context),
                      ],
                    ),
                  );
                } else {
                  return const Center(
                    child: Text("Error occurred!"),
                  );
                }
              },
            ),
          ),
        );
      },
    );
  }

  // Method to build the main dialog container
  Widget _buildDialogContainer(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 360,
          padding: const EdgeInsets.only(
            left: DialogConstants.padding,
            top: DialogConstants.avatarRadius + DialogConstants.padding,
            right: DialogConstants.padding,
            bottom: DialogConstants.padding,
          ),
          margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Colors.white,
            borderRadius: BorderRadius.circular(DialogConstants.padding),
            boxShadow: const [
              BoxShadow(
                  color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              _buildTitle(),
              const Spacer(),
              _buildUnsoldMessage(),
              const Spacer(),
              _buildAmountInput(),
              const Spacer(),
              _buildActionButtons(context),
            ],
          ),
        ),
      ],
    );
  }

  // Method to build the title of the dialog
  Widget _buildTitle() {
    return Text(
      title,
      style: const TextStyle(
          color: AppColors.green,
          fontSize: 20,
          fontFamily: 'poppins',
          fontWeight: FontWeight.bold),
      textAlign: TextAlign.center,
    );
  }

  // Method to build the unsold message
  Widget _buildUnsoldMessage() {
    return Text(
      "Sorry! Your lot no. ${state.stock.data.lotNo} was unsold",
      style: const TextStyle(
          color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold),
      textAlign: TextAlign.center,
    );
  }

  // Method to build the amount input field
  Widget _buildAmountInput() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 1.0),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: TextField(
        style: const TextStyle(
            color: AppColors.green, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
        enabled: true,
        controller: amountController
          ..text = state.stock.data.expectedPrice!.toStringAsFixed(2),
        decoration: InputDecoration(
          hintText: state.stock.data.expectedPrice.toString(),
          focusedBorder: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
          enabledBorder: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
          border: SelectedInputBorderWithShadow(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
                color: Color.fromARGB(255, 180, 178, 178), width: .1),
          ),
          contentPadding: const EdgeInsets.all(15),
        ),
      ),
    );
  }

  // Method to build the action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 40,
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              context.read<SellerLiveNotifsBloc>().add(
                    SubmitNegotiation(
                      stockId: state.stock.data.id,
                      expectedPrice:
                          double.parse(amountController.text).round(),
                    ),
                  );
              Get.back();
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all<Color>(AppColors.green),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("Submit"),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 40,
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              context.read<SellerLiveNotifsBloc>().add(
                    RejectNegotiation(stockId: state.stock.data.id),
                  );
              Get.back();
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("Reject Negotiation"),
          ),
        ),
      ],
    );
  }

  // Method to build the circle avatar at the top
  Widget _buildCircleAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.arrow_circle_down,
            size: 44,
            color: AppColors.primaryColor,
          ),
        ),
      ),
    );
  }

  // Method to build the close button at the top right
  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }
}
