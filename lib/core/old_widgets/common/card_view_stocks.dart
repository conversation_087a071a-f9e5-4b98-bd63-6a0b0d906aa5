import 'package:connectone/core/old_widgets/common/big_text.dart';
import 'package:connectone/core/old_widgets/common/small_text.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';

class CardViewMyStocksSoldOut extends StatelessWidget {
  final String number;
  final String label;
  final String suffix;

  const CardViewMyStocksSoldOut({
    Key? key,
    required this.number,
    required this.label,
    required this.suffix,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: getMediumTheme(),
      child: Expanded(
        flex: 1,
        child: Card(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
          child: Padding(
            padding: const EdgeInsets.all(5.0),
            child: SizedBox(
              height: 64,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  const SizedBox(height: 8),
                  Expanded(
                    child: BigText(
                      key: key,
                      text: "$number $suffix",
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: SmallText(
                      key: key,
                      text: label,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
