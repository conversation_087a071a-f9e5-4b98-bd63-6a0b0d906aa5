import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:connectone/old_blocs/card_sold_out/card_soldout_bloc.dart';
import 'package:connectone/core/old_widgets/common/card_list_item.dart';
import 'package:connectone/core/old_widgets/common/sell_info.dart';
import 'package:connectone/core/old_widgets/common/show_feedback_dialog.dart';
import 'package:connectone/old_models/sold_out.dart';
import 'package:connectone/core/old_widgets/tables/app_table.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:intl/intl.dart';
import '../../utils/colors.dart';

class CardListItemSoldOut extends StatefulWidget {
  final Content item;
  final String sellerName;
  final String stockid;
  final int index;
  final String gradeText;
  final String locationText;
  final String qtyText;
  final String date;
  final String noBidText;
  final String highestBid;
  final String orderId;
  final String lotNo;
  final double rating;
  final bool animate;
  final String sellerId;

  const CardListItemSoldOut({
    Key? key,
    required this.sellerId,
    required this.index,
    required this.sellerName,
    required this.stockid,
    required this.gradeText,
    required this.locationText,
    required this.qtyText,
    required this.date,
    required this.noBidText,
    required this.highestBid,
    required this.orderId,
    required this.lotNo,
    required this.rating,
    this.animate = false,
    required this.item,
  }) : super(key: key);

  @override
  State<CardListItemSoldOut> createState() => _CardListItemSoldOutState();
}

class _CardListItemSoldOutState extends State<CardListItemSoldOut> {
  late List<charts.Series<GraphData, String>> seriesList;

  @override
  Widget build(BuildContext context) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    seriesList = _createSampleData();
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) =>
                CardSoldBloc()..add(const OpenSoldStatus(openFlag: -1))),
      ],
      child: BlocBuilder<CardSoldBloc, CardSoldoutState>(
        builder: (context, state) {
          return Theme(
            data: getMediumTheme(),
            child: Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0)),
              elevation: 3.0,
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    _buildTopSection(context, formatter),
                    _buildActionButtons(context, state),
                    if (state is StockSoldOpenState)
                      _buildExpandableContent(context, state),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopSection(BuildContext context, DateFormat formatter) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildImageSection(context),
        const SizedBox(width: 20),
        _buildDetailsSection(formatter),
      ],
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 100,
          width: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            image: DecorationImage(
              image: widget.item.image1 != null
                  ? NetworkImage(widget.item.image1!)
                  : const AssetImage('assets/images/no_image_available.jpeg')
                      as ImageProvider,
              fit: BoxFit.cover,
            ),
            boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 5.0)],
          ),
        ),
        const SizedBox(height: 5),
        Text(widget.stockid,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13)),
        const SizedBox(height: 5),
        Text("Lot No :${widget.lotNo}",
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13)),
        const SizedBox(height: 5),
        _buildSellerInfo(context),
        const SizedBox(height: 5),
        _buildRatingSection(context),
      ],
    );
  }

  Widget _buildSellerInfo(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return SellerInfoDialog(sellerId: widget.sellerId);
          },
        );
      },
      child: SizedBox(
        width: 100,
        child: Text(
          widget.sellerName,
          textAlign: TextAlign.center,
          maxLines: 1,
          style: const TextStyle(
              overflow: TextOverflow.ellipsis,
              color: AppColors.green,
              fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildRatingSection(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CustomerRating(context, sellerId: widget.sellerId);
          },
        );
      },
      child: RatingBarIndicator(
        rating: widget.rating,
        itemBuilder: (context, index) =>
            const Icon(Icons.star, color: Colors.black),
        itemCount: 5,
        itemSize: 15.0,
        unratedColor: Colors.grey,
        direction: Axis.horizontal,
      ),
    );
  }

  Widget _buildDetailsSection(DateFormat formatter) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow("Seller", widget.sellerName),
        _buildDetailRow("Grade", widget.gradeText),
        _buildDetailRow("Date", formatter.format(DateTime.parse(widget.date))),
        _buildDetailRow("Location", widget.locationText,
            textColor: AppColors.green),
        _buildDetailRow("Qty", widget.qtyText),
        _buildDetailRow("No Bid", "0"),
        _buildDetailRow("Highest", widget.highestBid),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value,
      {Color textColor = Colors.black}) {
    return Row(
      children: [
        SizedBox(
            width: 90,
            child: Text(label,
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold))),
        const Text(":"),
        const SizedBox(width: 5),
        Text(value,
            style: TextStyle(fontWeight: FontWeight.bold, color: textColor)),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, CardSoldoutState state) {
    return Row(
      children: [
        const SizedBox(width: 10),
        _buildFeedbackButton(context),
        const SizedBox(width: 10),
        if (widget.item.product?.tableType() == "CARDAMOM")
          _buildOpenButton(context, state),
        const SizedBox(width: 10),
      ],
    );
  }

  Widget _buildFeedbackButton(BuildContext context) {
    return Expanded(
      child: MaterialButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return ShowFeedbackDialogue(
                  title: "Thank you", stockId: widget.stockid);
            },
          );
        },
        height: 24,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: AppColors.primaryColor,
        textColor: AppColors.white,
        child: const Text(feedbackButton),
      ),
    );
  }

  Widget _buildOpenButton(BuildContext context, CardSoldoutState state) {
    return Expanded(
      child: MaterialButton(
        onPressed: () {
          context
              .read<CardSoldBloc>()
              .add(OpenSoldStatus(openFlag: widget.index));
        },
        height: 24,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: AppColors.primaryColor,
        textColor: AppColors.white,
        child: const Text(openButton),
      ),
    );
  }

  Widget _buildExpandableContent(
      BuildContext context, StockSoldOpenState state) {
    return widget.item.product?.tableType() == "CARDAMOM"
        ? Visibility(
            visible: state.openFlag == widget.index,
            child: Column(
              children: [
                _buildViewTypeToggle(context),
                if (context.read<CardSoldBloc>().viewTypeString ==
                    "Table${widget.index}")
                  _buildTableView(),
                if (context.read<CardSoldBloc>().viewTypeString ==
                    "Graph${widget.index}")
                  _buildGraphView(),
              ],
            ),
          )
        : const SizedBox();
  }

  Widget _buildViewTypeToggle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildViewTypeRadio(context, "Table"),
        _buildViewTypeRadio(context, "Graph"),
      ],
    );
  }

  Widget _buildViewTypeRadio(BuildContext context, String label) {
    return Row(
      children: [
        Radio(
          value: "$label${widget.index}",
          groupValue: context.read<CardSoldBloc>().viewTypeString,
          onChanged: (value) {
            context
                .read<CardSoldBloc>()
                .add(ViewSoldType(typeRadio: value.toString()));
          },
          activeColor: AppColors.mainColor,
          fillColor: WidgetStateProperty.all(AppColors.mainColor),
        ),
        Text(label),
      ],
    );
  }

  Widget _buildTableView() {
    return AppTable(
      stockId: widget.stockid,
      tableType: widget.item.product?.tableType(),
    );
  }

  Widget _buildGraphView() {
    return SizedBox(
      height: 200,
      child: Container(
        color: Colors.black,
        child: charts.BarChart(
          seriesList,
          animate: widget.animate,
          barGroupingType: charts.BarGroupingType.grouped,
          behaviors: [charts.SeriesLegend()],
          domainAxis: charts.OrdinalAxisSpec(
            renderSpec: charts.SmallTickRendererSpec(
              labelStyle: charts.TextStyleSpec(
                  fontSize: 12,
                  color: charts.ColorUtil.fromDartColor(Colors.white)),
            ),
          ),
          primaryMeasureAxis: charts.NumericAxisSpec(
            renderSpec: charts.SmallTickRendererSpec(
              labelStyle: charts.TextStyleSpec(
                  fontSize: 12,
                  color: charts.ColorUtil.fromDartColor(Colors.white)),
            ),
          ),
        ),
      ),
    );
  }

  List<charts.Series<GraphData, String>> _createSampleData() {
    final cleanData = [
      GraphData(
          '>8mm',
          widget.item.grade8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      GraphData(
          '>7-8mm',
          widget.item.grade7T8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      GraphData(
          '<7mm',
          widget.item.grade17MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      GraphData(
          '<6mm',
          widget.item.rejectionsClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
    ];

    final splitData = [
      GraphData('>8mm', widget.item.grade8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      GraphData('>7-8mm', widget.item.grade7T8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      GraphData('<7mm', widget.item.grade17MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      GraphData('<6mm', widget.item.rejectionsSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
    ];

    final otherData = [
      GraphData(
          '>8mm',
          widget.item.grade8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      GraphData(
          '>7-8mm',
          widget.item.grade7T8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      GraphData(
          '<7mm',
          widget.item.grade17MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      GraphData(
          '<6mm',
          widget.item.rejectionsFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
    ];

    return [
      charts.Series<GraphData, String>(
        id: 'Clean',
        domainFn: (GraphData sales, _) => sales.year,
        measureFn: (GraphData sales, _) => sales.sales,
        data: cleanData,
        colorFn: (GraphData sales, _) => sales.barColor,
      ),
      charts.Series<GraphData, String>(
        id: 'Sick/Split',
        domainFn: (GraphData sales, _) => sales.year,
        measureFn: (GraphData sales, _) => sales.sales,
        data: splitData,
        colorFn: (GraphData sales, _) => sales.barColor,
      ),
      charts.Series<GraphData, String>(
        id: 'Other',
        domainFn: (GraphData sales, _) => sales.year,
        measureFn: (GraphData sales, _) => sales.sales,
        data: otherData,
        colorFn: (GraphData sales, _) => sales.barColor,
      ),
    ];
  }
}

class GraphData {
  final String year;
  final int sales;
  final charts.Color barColor;

  GraphData(this.year, this.sales, this.barColor);
}
