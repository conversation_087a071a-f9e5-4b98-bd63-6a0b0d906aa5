import 'package:flutter/material.dart';

import '../../utils/colors.dart';

class NewHomeCard extends StatelessWidget {
  const NewHomeCard({
    Key? key,
    required this.text,
    required this.onTap,
    required this.imageUrl,
  }) : super(key: key);

  final String text;
  final String imageUrl;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.hardEdge,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.0))),
        elevation: 4.0,
        margin: const EdgeInsets.all(8.0),
        child: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              height: 184,
              child: Image.network(
                imageUrl.isEmpty
                    ? "https://images.unsplash.com/photo-1567554747701-66e30bac4cf3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80"
                    : imageUrl,
                color: Colors.grey.withOpacity(0.95),
                colorBlendMode: BlendMode.modulate,
                fit: BoxFit.cover,
                frameBuilder: (context, child, frame, _) {
                  if (frame == null) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  return child;
                },
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                width: double.infinity,
                color: AppColors.primaryColor.withOpacity(0.8),
                child: Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
