import 'package:connectone/old_screens/offline_filters/offline_filters_bloc.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

class DeleteSearchDialog extends StatelessWidget {
  final String title;
  final int id;
  final String name;

  const DeleteSearchDialog({
    Key? key,
    required this.title,
    required this.id,
    required this.name,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: getMediumTheme(),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DialogConstants.padding),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: BlocBuilder<OfflineFilterCubit, OfflineFilterState>(
          builder: (context, state) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Stack(
                children: <Widget>[
                  _buildDialogContainer(context),
                  _buildCircleAvatar(),
                  _buildCloseButton(context),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Method to build the main dialog container
  Widget _buildDialogContainer(BuildContext context) {
    return Container(
      height: 360,
      padding: const EdgeInsets.only(
        left: DialogConstants.padding,
        top: DialogConstants.avatarRadius + DialogConstants.padding,
        right: DialogConstants.padding,
        bottom: DialogConstants.padding,
      ),
      margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: Colors.white,
        borderRadius: BorderRadius.circular(DialogConstants.padding),
        boxShadow: const [
          BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildTitle(),
          const Spacer(),
          _buildMessage(),
          const Spacer(flex: 2),
          _buildActionButtons(context),
        ],
      ),
    );
  }

  // Method to build the title of the dialog
  Widget _buildTitle() {
    return Text(
      title,
      style: const TextStyle(
          color: Colors.black, fontSize: 22, fontWeight: FontWeight.bold),
    );
  }

  // Method to build the confirmation message
  Widget _buildMessage() {
    return Text(
      "Do you want to delete the search named - $name?",
      textAlign: TextAlign.center,
      style: const TextStyle(
        color: Colors.black,
        fontSize: 20,
      ),
    );
  }

  // Method to build the action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 40,
            child: ElevatedButton(
              onPressed: () {
                Get.back();
              },
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
              ),
              child: const Text("No"),
            ),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: SizedBox(
            height: 40,
            child: ElevatedButton(
              onPressed: () {
                context.read<OfflineFilterCubit>().deleteSearch();
                Get.back();
              },
              style: ButtonStyle(
                backgroundColor:
                    WidgetStateProperty.all<Color>(AppColors.primaryColor),
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                ),
              ),
              child: const Text("Yes"),
            ),
          ),
        ),
      ],
    );
  }

  // Method to build the circle avatar at the top
  Widget _buildCircleAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.delete,
            size: 44,
            color: AppColors.primaryColor,
          ),
        ),
      ),
    );
  }

  // Method to build the close button at the top right
  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }
}
