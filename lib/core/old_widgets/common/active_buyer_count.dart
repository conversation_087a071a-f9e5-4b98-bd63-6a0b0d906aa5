import 'dart:async';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';

import '../../network/network_controller.dart';
import '../../../old_models/active_buyers.dart';
import '../../utils/colors.dart';
import '../../utils/constants.dart';
import '../../utils/safe_print.dart';

enum FromScreen { live, offline }

class ActiveBuyerCount extends StatefulWidget {
  const ActiveBuyerCount({
    required this.stockId,
    required this.screenType,
    Key? key,
  }) : super(key: key);

  final String stockId;
  final FromScreen screenType;

  @override
  State<ActiveBuyerCount> createState() => _ActiveBuyerCountState();
}

class _ActiveBuyerCountState extends State<ActiveBuyerCount> {
  String count = "0";
  StreamSubscription? liveSubscription;
  StreamSubscription? offlineSubscription;

  @override
  void initState() {
    super.initState();
    if (widget.screenType == FromScreen.live) {
      liveFirebaseListener();
    } else {
      offlineFirebaseListener();
    }
  }

  /// Sets up a Firebase listener for live auctions
  void liveFirebaseListener() {
    DatabaseReference dbReference =
        FirebaseDatabase.instance.ref('$firebaseBaseUrl/live_auction/1');
    liveSubscription = dbReference.onValue.listen((DatabaseEvent event) {
      if (event.snapshot.exists) {
        fetchCount();
      }
    });
  }

  /// Sets up a Firebase listener for offline auctions
  void offlineFirebaseListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/offline_auction')
        .child(widget.stockId);
    offlineSubscription = dbReference.onValue.listen((DatabaseEvent event) {
      if (event.snapshot.exists) {
        fetchCount();
      }
    });
  }

  /// Fetches the count of active buyers from the network
  Future<void> fetchCount() async {
    try {
      ActiveBuyers activeBuyers =
          await NetworkController().getActiveBuyers(widget.stockId);
      if (mounted) {
        setState(() {
          count = activeBuyers.data?.noOfBuyers ?? "0";
        });
      }
    } catch (e) {
      safePrint(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      "No of Active Buyers: $count",
      style: const TextStyle(
        color: AppColors.white,
        fontFamily: 'poppins',
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  @override
  void dispose() {
    liveSubscription?.cancel();
    offlineSubscription?.cancel();
    super.dispose();
  }
}
