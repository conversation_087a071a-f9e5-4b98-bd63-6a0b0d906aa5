import 'package:connectone/old_blocs/my_stocks/mystocks_bloc.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_events.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_state.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/custom_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

const double _kDateTimePickerHeight = 180;

class CustomDialogBox extends StatelessWidget {
  final String title;

  CustomDialogBox({Key? key, required this.title}) : super(key: key);

  final DateTime dateTime = DateTime.now();
  final TextEditingController lotNumberController = TextEditingController();
  final TextEditingController stockIdController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DialogConstants.padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: BlocBuilder<MyStocksBloc, MyStocksState>(
        builder: (context, state) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Stack(
              children: <Widget>[
                _buildDialogContainer(context),
                _buildCircleAvatar(),
                _buildCloseButton(context),
              ],
            ),
          );
        },
      ),
    );
  }

  // Method to build the main dialog container
  Widget _buildDialogContainer(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        left: DialogConstants.padding,
        top: DialogConstants.avatarRadius + DialogConstants.padding,
        right: DialogConstants.padding,
        bottom: DialogConstants.padding,
      ),
      margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: Colors.white,
        borderRadius: BorderRadius.circular(DialogConstants.padding),
        boxShadow: const [
          BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildTitle(),
          const SizedBox(height: 10),
          _buildLotNumberTextField(),
          const SizedBox(height: 10),
          _buildStockIdTextField(),
          const SizedBox(height: 10),
          _buildDeliveryDateSection(context),
          const SizedBox(height: 10),
          _buildActionButtons(context),
        ],
      ),
    );
  }

  // Method to build the title of the dialog
  Widget _buildTitle() {
    return Text(
      title,
      style: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 22,
          fontWeight: FontWeight.w600),
    );
  }

  // Method to build the lot number text field
  Widget _buildLotNumberTextField() {
    return TextField(
      keyboardType: TextInputType.number,
      controller: lotNumberController,
      decoration: InputDecoration(
        hintText: "ENTER LOT NUMBER",
        focusedBorder: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        enabledBorder: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        border: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        contentPadding: const EdgeInsets.all(15),
      ),
    );
  }

  // Method to build the stock ID text field
  Widget _buildStockIdTextField() {
    return TextField(
      keyboardType: TextInputType.number,
      controller: stockIdController,
      decoration: InputDecoration(
        hintText: "ENTER STOCK ID",
        focusedBorder: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        enabledBorder: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        border: SelectedInputBorderWithShadow(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
              color: Color.fromARGB(255, 180, 178, 178), width: .1),
        ),
        contentPadding: const EdgeInsets.all(15),
      ),
    );
  }

  // Method to build the delivery date section
  Widget _buildDeliveryDateSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Select Delivery Date",
          style: TextStyle(
              color: AppColors.primaryColor,
              fontSize: 16,
              fontWeight: FontWeight.w600),
        ),
        SizedBox(
          height: _kDateTimePickerHeight,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.date,
            initialDateTime: dateTime,
            onDateTimeChanged: (newDateTime) {
              final DateFormat formatter = DateFormat('yyyy-MM-dd');
              final String formattedDate = formatter.format(newDateTime);
              context
                  .read<MyStocksBloc>()
                  .add(DateSelected(deliveryDate: formattedDate));
            },
          ),
        ),
      ],
    );
  }

  // Method to build the action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all<Color>(AppColors.red),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("Back"),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              if (stockIdController.text.isEmpty) {
                if (lotNumberController.text.isNotEmpty) {
                  context.read<MyStocksBloc>().add(
                        StockSearchByLot(
                          lotNo: lotNumberController.text,
                          deliveryDate:
                              context.read<MyStocksBloc>().dateSelected,
                        ),
                      );
                }
              } else {
                context
                    .read<MyStocksBloc>()
                    .add(StocksSearch(stockId: stockIdController.text));
              }
              Navigator.pop(context);
            },
            style: ButtonStyle(
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColors.primaryColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
            ),
            child: const Text("Search"),
          ),
        ),
      ],
    );
  }

  // Method to build the circle avatar at the top
  Widget _buildCircleAvatar() {
    return Positioned(
      left: DialogConstants.padding,
      right: DialogConstants.padding,
      child: CircleAvatar(
        radius: 48,
        backgroundColor: Colors.grey.shade200,
        child: const CircleAvatar(
          backgroundColor: Colors.white,
          radius: 44,
          child: Icon(
            Icons.search,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  // Method to build the close button at the top right
  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: const Icon(
          Icons.cancel,
          color: AppColors.white,
        ),
      ),
    );
  }
}
