// import 'package:connectone/bai_models/bai_products_res.dart';
// import 'package:connectone/old_blocs/offline_card/offline_card_bloc.dart';
// import 'package:connectone/old_models/firebase_response.dart';
// // import 'package:connectone/old_models/get_offline_stocks.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/const_dimens.dart';
// import 'package:connectone/core/utils/decorations.dart';
// import 'package:connectone/core/utils/theme_utils.dart';
// import 'package:fbroadcast/fbroadcast.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// class BuyNowDialog extends StatelessWidget {
//   final String title;
//   final Content currentItem;
//   final FirebaseResponseOffline firebaseResponse;
//   final TextEditingController buyAmountController = TextEditingController();

//   BuyNowDialog({
//     Key? key,
//     required this.title,
//     required this.currentItem,
//     required this.firebaseResponse,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Theme(
//       data: getBoldTheme(),
//       child: BlocBuilder<OfflineSubBloc, OfflineSubState>(
//         builder: (context, state) {
//           return Dialog(
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(DialogConstants.padding),
//             ),
//             elevation: 0,
//             backgroundColor: Colors.transparent,
//             child: SingleChildScrollView(
//               physics: const ClampingScrollPhysics(),
//               child: Stack(
//                 children: <Widget>[
//                   _buildDialogContent(context),
//                   _buildAvatar(),
//                   _buildCloseButton(context),
//                 ],
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildDialogContent(BuildContext context) {
//     buyAmountController.text = firebaseResponse.buyNowPrice.toStringAsFixed(0);
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Container(
//           height: 320,
//           padding: const EdgeInsets.only(
//             left: DialogConstants.padding,
//             top: DialogConstants.avatarRadius + DialogConstants.padding,
//             right: DialogConstants.padding,
//             bottom: DialogConstants.padding,
//           ),
//           margin: const EdgeInsets.only(top: DialogConstants.avatarRadius),
//           decoration: BoxDecoration(
//             shape: BoxShape.rectangle,
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(DialogConstants.padding),
//             boxShadow: const [
//               BoxShadow(color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
//             ],
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.max,
//             children: <Widget>[
//               Expanded(
//                 child: Text(
//                   title,
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(color: AppColors.green, fontSize: 20, fontWeight: FontWeight.bold),
//                 ),
//               ),
//               const Text(
//                 "Please confirm your purchase",
//                 textAlign: TextAlign.center,
//                 style: TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 16),
//               Expanded(
//                 child: _buildAmountTextField(),
//               ),
//               const SizedBox(height: 8),
//               Expanded(
//                 child: _buildConfirmButton(context),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildAmountTextField() {
//     return Container(
//       height: 40,
//       width: double.infinity,
//       decoration: BoxDecoration(
//         border: Border.all(color: Colors.black, width: 1.0),
//         borderRadius: BorderRadius.circular(8.0),
//       ),
//       child: TextField(
//         style: const TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
//         textAlign: TextAlign.center,
//         enabled: false,
//         controller: buyAmountController,
//         decoration: getTextInputDecoration(''),
//       ),
//     );
//   }

//   Widget _buildConfirmButton(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(
//           child: SizedBox(
//             height: 48,
//             child: ElevatedButton(
//               onPressed: () {
//                 context.read<OfflineSubBloc>().add(
//                       BuyNow(
//                         orderTypeCd: firebaseResponse.stockAuctionStatus,
//                         stockId: currentItem.id!.toInt(),
//                         bidDeskNo: "Mobile",
//                         amount: int.parse(buyAmountController.text),
//                         quantity: currentItem.quantity!.toInt(),
//                       ),
//                     );
//                 Navigator.pop(context);
//                 FBroadcast.instance().broadcast("CLOSE_MO", value: "CLOSE_MO");
//               },
//               style: ButtonStyle(
//                 backgroundColor: WidgetStateProperty.all<Color>(AppColors.green),
//                 shape: WidgetStateProperty.all<RoundedRectangleBorder>(
//                   RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(5.0),
//                   ),
//                 ),
//               ),
//               child: const Text("Confirm"),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildAvatar() {
//     return Positioned(
//       left: DialogConstants.padding,
//       right: DialogConstants.padding,
//       child: CircleAvatar(
//         radius: 48,
//         backgroundColor: Colors.grey.shade200,
//         child: CircleAvatar(
//           backgroundColor: Colors.white,
//           radius: 44,
//           child: Icon(
//             Icons.price_change,
//             color: AppColors.primaryColor,
//             size: 44,
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildCloseButton(BuildContext context) {
//     return Positioned(
//       top: 0,
//       right: 0,
//       child: GestureDetector(
//         onTap: () {
//           Navigator.pop(context);
//         },
//         child: const Icon(
//           Icons.cancel,
//           color: AppColors.white,
//         ),
//       ),
//     );
//   }
// }
