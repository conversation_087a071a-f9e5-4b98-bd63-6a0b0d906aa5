// import 'dart:developer';

// import 'package:connectivity_plus/connectivity_plus.dart';
// import 'package:connectone/core/utils/constants.dart';
// import 'package:connectone/core/utils/safe_print.dart';
// import 'package:dio/dio.dart';
// import 'package:dio_smart_retry/dio_smart_retry.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';

// import '../../main.dart';

// class NetworkClient {
//   static Dio? _instance;
//   final connectivity = Connectivity();

//   Dio getInstance() {
//     if (_instance != null) {
//       return _instance!;
//     } else {
//       _instance = _getDio();
//       return _instance!;
//     }
//   }

//   Dio _getDio() {
//     final dio = Dio();
//     dio.options = BaseOptions(
//       baseUrl: "$baseAppUrl/apis/",
//       connectTimeout: const Duration(seconds: 20),
//       receiveTimeout: const Duration(seconds: 20),
//     );

//     dio.interceptors.clear();

//     // if (kDebugMode) {
//     //   dio.interceptors.add(PrettyDioLogger(requestBody: true));
//     // }

//     dio.interceptors.add(
//       RetryInterceptor(
//         dio: dio,
//         logPrint: safePrint,
//         retries: 1,
//         retryDelays: const [
//           Duration(seconds: 1),
//         ],
//       ),
//     );

//     // dio.interceptors.add(ConnectivityInterceptor(connectivity));

//     dio.interceptors.add(
//       QueuedInterceptorsWrapper(
//         onRequest: (options, handler) async {
//           // Set the Authorization header with the access token
//           options.headers['Authorization'] = "Bearer ${await AppToken.getAuthToken()}";
//           return handler.next(options);
//         },
//         onResponse: (response, handler) {
//           return handler.next(response);
//         },
//         onError: (DioException e, handler) async {
//           if (e.response != null && e.response?.statusCode == 401) {
//             // Token refresh logic
//             try {
//               var refToken = await AppToken.getRefreshToken();
//               var refreshResult = await refreshToken(refToken) as RefreshTokenRes;
//               await AppToken.saveAuthToken(refreshResult.data?.authToken ?? "");
//               // Retry the original request after token refresh
//               var response = await dio.request(
//                 e.requestOptions.path,
//                 options: Options(method: e.requestOptions.method),
//                 cancelToken: e.requestOptions.cancelToken,
//                 onReceiveProgress: e.requestOptions.onReceiveProgress,
//                 data: e.requestOptions.data,
//                 queryParameters: e.requestOptions.queryParameters,
//               );
//               handler.resolve(response);
//             } catch (e) {
//               // Handle token refresh errors
//               safePrint(e);
//               navigatorKey.currentState?.pushNamedAndRemoveUntil(
//                 LoginPage.routeName,
//                 (Route<dynamic> route) => false,
//               );
//             }
//           } else {
//             handler.next(e);
//           }
//         },
//       ),
//     );
//     return dio;
//   }

//   /// refresh token
//   Future<dynamic> refreshToken(String currentRefreshToken) async {
//     try {
//       var dio = serviceLocator<Dio>();
//       final response = await dio.post(
//         "$baseUrl/refresh-token",
//         data: RefreshTokenReq(refreshToken: currentRefreshToken).toJson(),
//         options: Options(
//           validateStatus: (status) {
//             return true;
//           },
//         ),
//       );
//       final result = response.data;
//       log("yayyyyy $result");
//       return RefreshTokenRes.fromJson(result);
//     } catch (e) {
//       safePrint(e);
//       print("crashhhhhhh ${e.toString()}");
//       navigatorKey.currentState?.pushNamedAndRemoveUntil(
//         LoginPage.routeName,
//         (Route<dynamic> route) => false,
//       );
//     }
//   }
// }
