import 'package:audioplayers/audioplayers.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_screens/edit_assign_approve.dart';
// import 'package:connectone/bai_screens/quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/user_profile_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:intl/intl.dart';

class NegotiationCard extends StatefulWidget {
  final Offer offerDetails;
  final Function onAccept;
  final Function onNegotiate;
  final bool showQuantity;

  const NegotiationCard({
    Key? key,
    required this.offerDetails,
    required this.onAccept,
    required this.onNegotiate,
    required this.showQuantity,
  }) : super(key: key);

  @override
  State<NegotiationCard> createState() => _NegotiationCardState();
}

class _NegotiationCardState extends State<NegotiationCard> {
  final FlutterSoundPlayer _mPlayer = FlutterSoundPlayer();
  final bool _mPlayerIsInited = false;
  final bool _mplaybackReady = false;
  bool playing = false;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _mPlayer.openPlayer();
    _setArrays(widget.offerDetails);
  }

  void _setArrays(Offer offer) {
    final List<Media> items = offer.medias ?? [];

    for (var media in items) {
      final String? url = media.url;
      if (url == null) {
        continue;
      }
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady) {
      return null;
    }
    return _mPlayer.isStopped ? play() : stopPlayer();
  }

  void play() {
    assert(_mPlayerIsInited && _mplaybackReady && _mPlayer.isStopped);
    _mPlayer.startPlayer(whenFinished: () {
      setState(() {});
    }).then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  @override

  /// Builds the negotiation card widget.
  ///
  /// This widget displays detailed information about an offer,
  /// including the vendor's name, rating, contact information,
  /// and various offer details such as variants, BAI membership,
  /// GST type, submission date, and negotiation status.
  ///
  /// It also provides functionality to view the vendor's profile
  /// and make a phone call to the vendor. The widget style uses
  /// [AppColors] for theming and displays the card with rounded
  /// corners and a shadow effect when approved.
  Widget build(BuildContext context) {
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 15,
    );
    var gstType = widget.offerDetails.typeOfGstFiling;
    var isBaiMember = widget.offerDetails.baiMember ?? false;
    var rating = widget.offerDetails.rating;

    var isApproved = widget.offerDetails.statusCd == "APPR";

    var gst = widget.offerDetails.gst ?? 0;
    var offerPrice = widget.offerDetails.negotiatedPrice ??
        widget.offerDetails.offerPrice ??
        0;

    // Calculate GST-related values (only if buyer)
    var gstAmount = (offerPrice * gst) / 100;
    var quoteWithoutGst = offerPrice;
    var quoteWithGst = offerPrice + gstAmount;

    return Card(
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: isApproved ? Colors.green.shade100 : Colors.white,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withOpacity(0.2),
              offset: const Offset(0, 2),
              blurRadius: 6,
              spreadRadius: 2,
            ),
          ],
        ),
        margin: const EdgeInsets.all(2),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return UserProfileDialog(
                              vendorId: widget.offerDetails.vendorId,
                              prchOrdrId: widget.offerDetails.prchOrdrId,
                            );
                          },
                        );
                      },
                      child: Text(
                        (widget.offerDetails.vendorName ?? 'N/A'),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                      ),
                    ),
                  ),
                  Row(children: [
                    Icon(
                      Icons.star_rate_rounded,
                      color: AppColors.primaryColor,
                      size: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Text(
                        rating != null ? rating.toStringAsFixed(1) : "N/A",
                        style: style,
                      ),
                    ),
                  ]),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    margin: const EdgeInsets.only(top: 2),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.offerDetails.id.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: () {
                  if ((widget.offerDetails.vendorPhone ?? "").isNotEmpty) {
                    makePhoneCall(widget.offerDetails.vendorPhone ?? "");
                  }
                },
                child: Row(
                  children: [
                    const Expanded(
                      flex: 2,
                      child: Text(
                        "Phone: ",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.vendorPhone ?? 'N/A',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColorOld,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    )
                  ],
                ),
              ),
              if (widget.offerDetails.variant1OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant1OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${widget.offerDetails.variant1OptionGroupName ?? "Variant 1"}:',
                        style: style,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant1OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              if (widget.offerDetails.variant2OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant2OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${widget.offerDetails.variant2OptionGroupName ?? "Variant 2"}: ',
                        style: style,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant2OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              if (widget.offerDetails.variant3OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant3OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                          '${widget.offerDetails.variant3OptionGroupName ?? "Variant 3"}:',
                          style: style),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant3OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      "BAI Member: ",
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      isBaiMember ? "Yes" : "No",
                      style: style,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      "GST Type:",
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      gstType ?? "N/A",
                      style: style,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Submitted At:',
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      widget.offerDetails.createdAt?.toCreatedOn() ?? "N/A",
                      style: style,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Status:',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      widget.offerDetails.negotiationStatusName ?? "N/A",
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              // Show GST details only for buyers
              if (isBuyer() || true) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'GST %:',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '${gst.toStringAsFixed(2)}%',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'GST Amount:',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '₹${NumberFormat('#,##0.00').format(gstAmount)}',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Quote (w/o GST):',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '₹${NumberFormat('#,##0.00').format(quoteWithoutGst)}',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Quote (with GST):',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '₹${NumberFormat('#,##0.00').format(quoteWithGst)}',
                        style:
                            style.copyWith(backgroundColor: Colors.greenAccent),
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 12),
              generateHistoryTable(context, widget.offerDetails)
            ],
          ),
        ),
      ),
    );
  }

  String getPricePerQuantity() {
    if (widget.offerDetails.quantity != null) {
      var price =
          widget.offerDetails.negotiatedPrice ?? widget.offerDetails.offerPrice;
      if (price != null) {
        double pricePerQuantity = price / widget.offerDetails.quantity!;
        return '₹${NumberFormat('#,##0.00').format(pricePerQuantity)}';
      }
    }
    return 'N/A';
  }
}
