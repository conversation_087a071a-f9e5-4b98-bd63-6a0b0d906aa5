import 'package:connectone/bai_models/report_req.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';

class ReportButton extends StatefulWidget {
  const ReportButton({
    Key? key,
    required this.prchOrdrId,
  }) : super(key: key);

  final String prchOrdrId;

  @override
  State<ReportButton> createState() => _ReportButtonState();
}

class _ReportButtonState extends State<ReportButton> {
  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(
        Icons.report_problem_outlined,
        color: AppColors.yellowColor,
      ),
      constraints: const BoxConstraints(),
      padding: EdgeInsets.zero,
      onPressed: () {
        showReportDialog(context, widget.prchOrdrId);
      },
    );
  }
}

void showReportDialog(BuildContext context, String prchOrdrId) {
  final descriptionController = TextEditingController();

  void reportProblem() async {
    final customerId = int.tryParse(getCustomerId());
    final purchaseOrderId = int.tryParse(prchOrdrId);

    if (customerId == null || purchaseOrderId == null) {
      alert("Invalid ID format. Please contact support.");
      return;
    }
    try {
      var networkController = NetworkController();
      var req = ReportReq(
        prchOrdrId: purchaseOrderId,
        customerId: customerId,
        description: descriptionController.text,
      );
      await networkController.reportProblem(req);
      alert("Report submitted successfully.");
      Navigator.pop(context);
    } catch (e) {
      safePrint(e);
      alert("Failed to submit report. Please try again.");
    } finally {
      descriptionController.clear();
    }
  }

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        insetPadding: const EdgeInsets.all(8),
        title: const Text(
          'Report a Problem',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontSize: 16,
          ),
        ),
        titlePadding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              style: const TextStyle(
                // fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Enter your issue',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              if (descriptionController.text.isEmpty) {
                alert("Please enter your issue.");
                return;
              }
              reportProblem();
            },
            child: const Text(
              'Report',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.darkRed,
                fontSize: 14,
              ),
            ),
          ),
        ],
      );
    },
  );
}
