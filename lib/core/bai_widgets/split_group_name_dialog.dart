import 'package:connectone/bai_models/split_group_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import '../utils/colors.dart';

/// A dialog for entering a split group name and date.
///
/// This dialog displays a form with a text field for entering a group name,
/// a date picker for selecting a date, and a confirm button to submit the information.
class SplitGroupNameDialog extends StatefulWidget {
  /// Creates a split group name dialog.
  ///
  /// The [fixedPrefix] parameter is a suggested prefix for the text field.
  /// The [prchOrdrId] parameter is the purchase order ID to be used in the API call.
  const SplitGroupNameDialog({
    Key? key,
    this.fixedPrefix,
    required this.prchOrdrId,
  }) : super(key: key);

  /// The suggested prefix for the text field.
  /// For example, "Cement - " in "Cement - High quality"
  final String? fixedPrefix;

  /// The purchase order ID to be used in the API call
  final int prchOrdrId;

  @override
  State<SplitGroupNameDialog> createState() => _SplitGroupNameDialogState();
}

class _SplitGroupNameDialogState extends State<SplitGroupNameDialog> {
  late TextEditingController _nameController;
  final FocusNode _nameFocusNode = FocusNode();
  bool _isLoading = false;

  // Date picker state
  DateTime _selectedDate = DateTime.now();
  final TextEditingController _dateController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Set up the name controller with the suggested prefix
    _nameController = TextEditingController(text: widget.fixedPrefix ?? '');

    // Initialize date controller with formatted current date
    _dateController.text = _formatDate(_selectedDate);

    // Automatically focus on the name field when the dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_nameFocusNode);
    });
  }

  // Format date to display in the text field
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Show date picker dialog
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(_selectedDate);
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dateController.dispose();
    _nameFocusNode.dispose();
    super.dispose();
  }

  /// Creates a split group by calling the API and returns the ID
  Future<void> _createSplitGroup() async {
    // Get the name
    final name = _nameController.text.trim();

    // Validate the input before closing the dialog
    if (name.isEmpty) {
      // Show error message if name is empty
      alert('Please enter a split group name');
      return;
    }

    // Update state to show loading
    setState(() {
      _isLoading = true;
    });

    try {
      // Make API call to create split group
      final networkController = NetworkController();

      final response = await networkController.dio.post(
        '$baseAppUrl/apis/bidding/prch-ordr-split/create',
        data: {
          'prch_ordr_id': widget.prchOrdrId,
          'name': name,
          'delivery_date':
              '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')} 00:00:00',
        },
      );

      // Check if widget is still mounted
      if (!mounted) return;

      // Update loading state
      setState(() {
        _isLoading = false;
      });

      if (response.statusCode == 200) {
        final splitGroupRes = SplitGroupRes.fromJson(response.data);
        // Close the dialog and return the split group ID, name, and date
        Navigator.of(context).pop({
          'id': splitGroupRes.data.id,
          'name': splitGroupRes.data.name,
          'date': _selectedDate,
        });
      } else {
        // Show error message
        alert('Failed to create split group. Please try again.');
      }
    } catch (e) {
      // Check if widget is still mounted
      if (!mounted) return;

      // Update loading state
      setState(() {
        _isLoading = false;
      });

      // Show error message
      alert('Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      insetPadding: const EdgeInsets.all(16),
      elevation: 8.0,
      child: Container(
        padding: const EdgeInsets.all(0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with app's primary color
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 18),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16.0),
                  topRight: Radius.circular(16.0),
                ),
              ),
              child: const Text(
                'Split Group Name',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ).withCloseButton(() => Navigator.pop(context)),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
              child: Column(
                children: [
                  const SizedBox(height: 24),

                  // Single name text field
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(
                        color: AppColors.green,
                        width: 1.5,
                      ),
                      color: Colors.grey.shade50,
                    ),
                    child: TextField(
                      controller: _nameController,
                      focusNode: _nameFocusNode,
                      style: const TextStyle(
                        fontSize: 16, // Reduced font size
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      decoration: InputDecoration(
                        labelText: 'Split Group Name',
                        labelStyle: TextStyle(
                          fontSize: 14, // Reduced font size
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                        hintText: 'Enter split group name',
                        hintStyle: TextStyle(
                          fontSize: 14, // Reduced font size
                          color: Colors.grey.shade400,
                          fontWeight: FontWeight.normal,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 16, horizontal: 16), // Reduced padding
                        border: InputBorder.none,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Date picker field
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(
                        color: AppColors.green,
                        width: 1.5,
                      ),
                      color: Colors.grey.shade50,
                    ),
                    child: InkWell(
                      onTap: () => _selectDate(context),
                      child: TextField(
                        controller: _dateController,
                        enabled: false, // Disable direct editing
                        style: const TextStyle(
                          fontSize: 16, // Reduced font size
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        decoration: InputDecoration(
                          labelText: 'Delivery Date',
                          labelStyle: TextStyle(
                            fontSize: 14, // Reduced font size
                            color: AppColors.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 16), // Reduced padding
                          border: InputBorder.none,
                          suffixIcon: Icon(
                            Icons.calendar_today,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 36),

                  // Confirm button with improved styling
                  _isLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : ElevatedButton(
                          onPressed: _createSplitGroup,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.green,
                            elevation: 0,
                            minimumSize: const Size(double.infinity, 48),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            "OK",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
