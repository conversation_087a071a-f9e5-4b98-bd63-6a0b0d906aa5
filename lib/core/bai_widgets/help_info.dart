// import 'package:connectone/core/utils/tools.dart';
// import 'package:flutter/material.dart';
// import 'package:url_launcher/url_launcher.dart';
// import '../../old_screens/help_screen_new.dart';

// class InfoHelp extends StatelessWidget {
//   final String? url;
//   final String? name;

//   const InfoHelp({
//     Key? key,
//     this.url,
//     this.name,
//   }) : super(key: key);

//   static const String _iconPath = "assets/images/icon_info_home_screen.png";
//   static const String _defaultYouTubeLink =
//       "https://www.youtube.com/shorts/AjrZJqC6MIY";

//   @override

//   /// A [TextButton] that displays a column with a help icon and the text "HELP".
//   ///
//   /// When pressed, it will launch the [url] in the default application associated
//   /// with the URL, or launch the default YouTube video if [url] is null or empty.
//   ///
//   /// The [url] can be a YouTube link or a link to a webpage. If [url] is null or
//   /// empty, it will launch the default YouTube video. If [url] is a YouTube link,
//   /// it will launch the YouTube app with the video. If [url] is a link to a webpage,
//   /// it will launch a [HelpScreenNew] with the [url] and [name] as arguments.
//   Widget build(BuildContext context) {
//     return TextButton(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Image(
//             image: AssetImage(_iconPath),
//             width: 20,
//             height: 20,
//           ),
//           const SizedBox(height: 4),
//           Text(
//             "HELP",
//             style: TextStyle(
//               fontSize: 12,
//               color: Colors.cyanAccent.shade200,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//         ],
//       ),
//       onPressed: () async {
//         if (url != null && url!.isNotEmpty) {
//           if (url!.contains('youtube.com') || url!.contains('youtu.be')) {
//             if (await canLaunchUrl(Uri.parse(url!))) {
//               await launchUrl(
//                 Uri.parse(url!),
//                 mode: LaunchMode.externalApplication,
//               );
//             } else {
//               alert('Could not launch $url');
//             }
//           } else {
//             Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => HelpScreenNew(
//                   url: url!,
//                   name: name ?? '',
//                 ),
//               ),
//             );
//           }
//         } else {
//           if (await canLaunchUrl(Uri.parse(_defaultYouTubeLink))) {
//             await launchUrl(
//               Uri.parse(_defaultYouTubeLink),
//               mode: LaunchMode.externalApplication,
//             );
//           } else {
//             alert('Could not launch $_defaultYouTubeLink');
//           }
//         }
//       },
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../old_screens/help_screen_new.dart';
import '../utils/tools.dart';

class InfoHelp extends StatelessWidget {
  final String? url;
  final String? name;
  final VoidCallback? onTap;

  const InfoHelp({
    Key? key,
    this.url,
    this.name,
    this.onTap,
  }) : super(key: key);

  static const String _iconPath = "assets/images/icon_info_home_screen.png";
  static const String _defaultYouTubeLink =
      "https://www.youtube.com/shorts/AjrZJqC6MIY";

  @override

  /// A [TextButton] that displays a column with a help icon and the text "HELP".
  ///
  /// When pressed, it will launch the [url] in the default application associated
  /// with the URL, or launch the default YouTube video if [url] is null or empty.
  ///
  /// The [url] can be a YouTube link or a link to a webpage. If [url] is null or
  /// empty, it will launch the default YouTube video. If [url] is a YouTube link,
  /// it will launch the YouTube app with the video. If [url] is a link to a webpage,
  /// it will launch a [HelpScreenNew] with the [url] and [name] as arguments.
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: url == null || url!.isEmpty
          ? () {
              onTap!();
            }
          : () async {
              if (await canLaunchUrl(Uri.parse(url!))) {
                await launchUrl(
                  Uri.parse(url!),
                  mode: LaunchMode.externalApplication,
                );
              } else {
                alert('Could not launch $url');
              }
            },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Image(
            image: AssetImage(_iconPath),
            width: 20,
            height: 20,
          ),
          const SizedBox(height: 4),
          Text(
            "HELP",
            style: TextStyle(
              fontSize: 12,
              color: Colors.cyanAccent.shade200,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
