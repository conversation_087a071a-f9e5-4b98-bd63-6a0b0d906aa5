import 'package:connectone/bai_models/team_member.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class MemberCard extends StatelessWidget {
  const MemberCard({
    Key? key,
    required this.teamMember,
    required this.onApprove,
    required this.onReject,
    required this.onDelete,
    required this.onInactive,
  }) : super(key: key);

  final TeamMember teamMember;
  final Function onApprove;
  final Function onReject;
  final Function onDelete;
  final Function onInactive;

  @override

  /// Builds the UI for a team member card.
  ///
  /// The card displays the team member's name, phone, registration date,
  /// designation, and approval status. It also includes action buttons
  /// to approve, reject, or delete the team member.
  ///
  /// - The `InfoRow` widget is used to display the name, date of registration,
  ///   designation, and status.
  /// - A phone row is included using `_buildPhoneRow()`.
  /// - Action buttons are added using `_buildActionButtons()` and `_buildActionButtonsNew()`.
  ///
  /// The card is styled with a margin, elevation, and rounded corners.
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InfoRow(label: 'Name', value: teamMember.uname ?? "N/A"),
            const SizedBox(height: 8),
            _buildPhoneRow(),
            const SizedBox(height: 8),
            InfoRow(
                label: 'Date of Reg.',
                value: teamMember.createdAt?.toCreatedOn() ?? "N/A"),
            const SizedBox(height: 8),
            InfoRow(
                label: 'Designation',
                value: teamMember.designation?.trim().isNotEmpty == true
                    ? teamMember.designation ?? "N/A"
                    : "N/A"),
            const SizedBox(height: 8),
            InfoRow(
                label: 'Status',
                value: '${teamMember.customerApprovalStatusCd}'),
            if (!((teamMember.designation ?? "")
                .toLowerCase()
                .contains("admin")))
              const SizedBox(height: 12),
            _buildActionButtons(),
            // const SizedBox(height: 12),
            // _buildActionButtonsNew(),
          ],
        ),
      ),
    );
  }

  Row _buildPhoneRow() {
    return Row(
      children: [
        const SizedBox(
          width: 120,
          child: Text('Phone:', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () async {
              final Uri launchUri = Uri.parse(
                  'tel:${teamMember.countryCode}${teamMember.uphone}');
              if (await canLaunchUrl(launchUri)) {
                await launchUrl(launchUri);
              } else {
                alert('Could not launch the phone dialer.');
              }
            },
            child: Text(
              '${teamMember.countryCode} ${teamMember.uphone}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                decoration: TextDecoration.underline,
                color: Colors.blue.shade800,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Row _buildActionButtons() {
    final status = teamMember.customerApprovalStatusCd;
    if (status == "PEND") {
      return Row(
        children: [
          Expanded(
            child: BaiButton(
              onTap: onReject,
              text: "REJECT",
              backgoundColor: Colors.red.shade800,
              height: 40,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: BaiButton(
              onTap: onApprove,
              text: "APPROVE",
              backgoundColor: Colors.green.shade800,
              height: 40,
            ),
          ),
        ],
      );
    }
    if (status == "APPR") {
      return Row(
        children: [
          Expanded(
            child: BaiButton(
              onTap: onDelete,
              text: "DELETE",
              backgoundColor: Colors.red.shade800,
              height: 40,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: BaiButton(
              onTap: onInactive,
              text: "DISABLE",
              backgoundColor: Colors.amber.shade800,
              height: 40,
            ),
          ),
        ],
      );
    }
    return Row(
      children: [
        if (status == "PEND" ||
            (status == "APPR" &&
                !((teamMember.designation ?? "")
                    .toLowerCase()
                    .contains("admin"))))
          Expanded(
            child: BaiButton(
              onTap: onReject,
              text: "REJECT",
              backgoundColor: Colors.red.shade800,
              height: 40,
            ),
          ),
        if (status == "PEND" || (status == "RJCT")) const SizedBox(width: 16),
        if (status == "PEND" || (status == "RJCT"))
          Expanded(
            child: BaiButton(
              onTap: onApprove,
              text: "APPROVE",
              backgoundColor: Colors.green.shade800,
              height: 40,
            ),
          ),
      ],
    );
  }
}

class InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const InfoRow({Key? key, required this.label, required this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text('$label :',
              style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        Expanded(
          child:
              Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
      ],
    );
  }
}
