import 'package:connectone/bai_blocs/invite_status/invite_status_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/invite_status_res.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';

class InviteStatusDialog extends StatefulWidget {
  final Content content;

  const InviteStatusDialog({
    Key? key,
    required this.content,
  }) : super(key: key);

  @override
  State<InviteStatusDialog> createState() => _QuotesDialogState();
}

class _QuotesDialogState extends State<InviteStatusDialog> {
  InviteStatusRes? inviteStatusRes;

  @override
  void initState() {
    super.initState();
    context.read<InviteStatusCubit>().getInviteStatus(
          widget.content.prchOrdrId?.toString() ?? "",
        );
  }

  @override
  Widget build(BuildContext context) {
    var style = const TextStyle(fontWeight: FontWeight.bold);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: AppLoader(
        child: BlocConsumer<InviteStatusCubit, InviteStatusState>(
            listener: (context, state) {
          if (state is InviteStatusLoaded) {
            setState(() {
              inviteStatusRes = state.inviteList;
            });
          }
          if (state is RemindSuccess) {
            properAlert("Reminder sent successfully.");
          }
          if (state is InviteStatusError) {
            alert(state.error);
          }
        }, builder: (context, state) {
          (state is InviteStatusLoading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          return Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 52,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Center(
                      child: Text(
                        'Invite Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ).withCloseButton(() => Navigator.pop(context)),
                  const SizedBox(height: 20),
                  Text(
                    "Total Invites: ${inviteStatusRes?.totalInvite}",
                    style: style,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "Total Quotes Received: ${inviteStatusRes?.totalQuoteReceived}",
                    style: style,
                  ),
                  const SizedBox(height: 20),
                  InvitationTable(
                    invite: inviteStatusRes,
                    onRemind: (p0) {
                      context.read<InviteStatusCubit>().remind(
                            widget.content.prchOrdrId?.toInt() ?? 0,
                            p0.vendorId?.toInt() ?? 0,
                          );
                    },
                  ),
                ],
              ),
            ),
          );
          // } else {
          //   return Container();
          // }
        }),
      ),
    );
  }
}
