import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:flutter/material.dart';

class CartItem extends StatelessWidget {
  final String productName;
  final String unit;
  final int quantity;
  final String instructions;
  final int index;
  final StockItem item;
  final VoidCallback onAdd;
  final VoidCallback onSubtract;
  final VoidCallback onDelete;

  const CartItem({
    Key? key,
    required this.productName,
    required this.unit,
    required this.quantity,
    required this.instructions,
    required this.index,
    required this.item,
    required this.onAdd,
    required this.onSubtract,
    required this.onDelete,
  }) : super(key: key);

  @override

  /// Builds a row that displays a cart item.
  ///
  /// The row displays the product name, a row of icons if the product has
  /// images, files, or audio recordings, the quantity of the product, and
  /// buttons to add or subtract one unit of the product from the cart.
  ///
  /// The product name is displayed with a bold font. If the product has
  /// instructions, they are displayed below the product name with a normal
  /// font.
  ///
  /// If the product has variants, they are displayed below the instructions
  /// with a normal font. The variants are displayed in a column with the
  /// option group name followed by a comma-separated list of option names.
  ///
  /// The quantity is displayed with a bold font. The add and subtract buttons
  /// are displayed with a normal font. The delete button is displayed with a
  /// normal font and a smaller size.
  Widget build(BuildContext context) {
    var isSteelReinforcement =
        productName.toLowerCase().contains("reinforcement");
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    productName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      if (item.images!.isNotEmpty)
                        const Icon(
                          Icons.image,
                          color: Colors.indigo,
                        ),
                      if (item.files!.isNotEmpty) const SizedBox(width: 6),
                      if (item.files!.isNotEmpty)
                        const Icon(
                          Icons.file_copy_outlined,
                          color: Colors.blue,
                          size: 20,
                        ),
                      if (item.audios!.isNotEmpty) const SizedBox(width: 6),
                      if (item.audios!.isNotEmpty)
                        const Icon(
                          Icons.play_circle,
                          color: Colors.deepPurple,
                        ),
                    ],
                  ),
                  if (instructions.isNotEmpty) const SizedBox(height: 6),
                  if (instructions.isNotEmpty)
                    Text(
                      "Instructions: $instructions",
                      style: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  // item.poStockItem?.variants?.isNotEmpty ?? false ? const SizedBox(height: 2) : const SizedBox.shrink(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: (item.poStockItem?.variants ?? [])
                        .fold<Map<String, List<String>>>({}, (map, variant) {
                          map
                              .putIfAbsent(
                                  variant.optionGroupName ?? "", () => [])
                              .add(variant.optionName ?? "");
                          return map;
                        })
                        .entries
                        .map<Widget>((entry) {
                          return Text(
                            "${entry.key}: ${entry.value.join(', ')}",
                            style: const TextStyle(
                              fontWeight: FontWeight.normal,
                              fontSize: 14,
                            ),
                          );
                        })
                        .toList(),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                if (!isSteelReinforcement)
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: onSubtract,
                          constraints: const BoxConstraints(),
                          padding: const EdgeInsets.all(4),
                          icon: const Icon(Icons.remove),
                        ),
                        Container(
                          constraints: const BoxConstraints(minWidth: 20),
                          child: Text(
                            quantity.toString(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        IconButton(
                          onPressed: onAdd,
                          constraints: const BoxConstraints(),
                          padding: const EdgeInsets.all(4),
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(width: 6),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: IconButton(
                    onPressed: onDelete,
                    constraints: const BoxConstraints(),
                    padding: const EdgeInsets.all(4),
                    icon: const Icon(
                      Icons.delete_outline,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
