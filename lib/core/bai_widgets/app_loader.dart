import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';

class AppLoader extends StatelessWidget {
  final Widget child;
  final Color? color;
  const AppLoader({Key? key, required this.child, this.color})
      : super(key: key);

  @override

  /// Builds a widget that wraps the given [child] with a
  /// [LoaderOverlay], which is a widget that can be used to display
  /// a loading indicator overlay on top of the wrapped widget.
  ///
  /// The [overlayColor] parameter can be used to specify the color of
  /// the overlay. If not provided, [Colors.white70] will be used.
  ///
  /// The [disableBackButton] parameter is passed directly to the
  /// [LoaderOverlay] constructor and defaults to [false].
  ///
  /// The [child] parameter is the widget to be wrapped with the
  /// [LoaderOverlay].

  Widget build(BuildContext context) {
    return LoaderOverlay(
      overlayColor: color ?? Colors.white70,
      disableBackButton: false,
      child: child,
    );
  }
}
