import 'package:audioplayers/audioplayers.dart';
import 'package:connectone/bai_blocs/history/history_cubit.dart';
import 'package:connectone/bai_models/history_res.dart';
import 'package:connectone/bai_screens/single_mr_screen.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../bai_models/summary_res.dart';
import 'buyer_quotes_dialog.dart';
import 'seller_quotes_dialog.dart';

class HistoryDialog extends StatefulWidget {
  final String? prchOrdrId;
  final int? quantity;
  const HistoryDialog({
    Key? key,
    this.prchOrdrId,
    this.quantity,
  }) : super(key: key);

  @override
  State<HistoryDialog> createState() => _HistoryDialogState();
}

class _HistoryDialogState extends State<HistoryDialog> {
  @override
  void initState() {
    super.initState();
    context.read<HistoryCubit>().loadHistory(widget.prchOrdrId!);
  }

  @override

  /// Builds the UI for the History Dialog widget.
  ///
  /// This method is the root of the widget tree for the History Dialog widget.
  /// It uses the BlocConsumer widget to consume the HistoryCubit and build the
  /// UI based on the state of the cubit.
  ///
  /// The UI is built in the following way:
  /// - If the state is HistoryLoading, it displays a progress indicator.
  /// - If the state is HistoryLoaded, it displays a list of history items.
  ///   If the list of history items is empty, it displays a message indicating
  ///   that no data is available.
  /// - If the state is HistoryError, it displays a message with the error
  ///   message.
  ///
  /// The `prchOrdrId` parameter is used to get the history data from the server.
  /// The `HistoryItem` widget is used to display each item in the list of
  /// history items. The `isLast` parameter is used to determine whether the
  /// history item is the last one in the list. The `TextButton` widget is used to
  /// display a close button at the bottom of the dialog.
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: AppLoader(
        child: BlocConsumer<HistoryCubit, HistoryState>(
            listener: (context, state) {
          if (state is HistoryLoaded) {
            safePrint("History Loaded");
          }
          if (state is HistoryError) {
            properAlert(state.message);

            // safePrint("History Loaded");
          }
        }, builder: (context, state) {
          (state is HistoryLoading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          if (state is HistoryLoaded) {
            var data = state.historyRes;
            return Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 52,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Center(
                        child: Text(
                          'History',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ).withCloseButton(() => Navigator.of(context).pop()),
                    const SizedBox(height: 16),
                    if (data.isEmpty)
                      const Text(
                        "No Data Available",
                        style: TextStyle(
                          color: Colors.black54,
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        separatorBuilder: (context, index) {
                          return const SizedBox(height: 16);
                        },
                        itemCount: data.length,
                        itemBuilder: (context, index) {
                          return HistoryItem(
                            historyData: data[index],
                            isLast: data.length - 1 == index,
                            onOpenNego: () {
                              if (isBuyer()) {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return BuyerQuotesDialog(
                                        prchOrdrId: int.tryParse(
                                                widget.prchOrdrId ?? "0") ??
                                            0,
                                        index: 1,
                                        brand: "",
                                      );
                                    });
                              } else {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return SellerQuotesDialog(
                                        statusCd: "QUOT",
                                        
                                        prchOrdrId: int.tryParse(
                                                widget.prchOrdrId ?? "0") ??
                                            0,
                                        index: 1,
                                        itemDetails:
                                            Detail(quantity: widget.quantity),
                                      );
                                    });
                              }
                            },
                            onOpenQuotes: () {
                              if (isBuyer()) {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return BuyerQuotesDialog(
                                        prchOrdrId: int.tryParse(
                                                widget.prchOrdrId ?? "0") ??
                                            0,
                                        brand: "",
                                      );
                                    });
                              } else {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return SellerQuotesDialog(
                                          statusCd: "QUOT",
                                        prchOrdrId: int.tryParse(
                                                widget.prchOrdrId ?? "0") ??
                                            0,
                                        itemDetails:
                                            Detail(quantity: widget.quantity),
                                      );
                                    });
                              }
                            },
                            onViewOld: () {
                              var id =
                                  data[index].mvtPrchOrdrHistoryId.toString();
                              Get.to(
                                SingleMRScreen(
                                  notificationData: NotificationData(
                                    code: '',
                                    subject: '',
                                    event: '',
                                    content: "History",
                                    id: '',
                                  ),
                                  mvtPrchOrdrHistoryId: id,
                                ),
                              );
                            },
                            onViewSplit: () {
                              var id = data[index].prchOrdrId.toString();
                              Get.to(
                                SingleMRScreen(
                                  notificationData: NotificationData(
                                    code: '',
                                    subject: '',
                                    event: '',
                                    content: "Split",
                                    id: id,
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        'X Close',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Container();
          }
        }),
      ),
    );
  }

  Widget getIcon(String title) {
    switch (title.toLowerCase()) {
      case "submitted enquiry":
        return Image.asset(
          "assets/images/enquiry_submmitted.png",
          height: 24,
          width: 24,
        );
      case "approved":
        return Image.asset(
          "assets/images/approved.png",
          height: 24,
          width: 24,
        );
      case "received offers":
        return Image.asset(
          "assets/images/received_offer.png",
          height: 24,
          width: 24,
        );
      case "negotiate price":
        return Image.asset(
          "assets/images/negotiate_price.png",
          height: 24,
          width: 24,
        );
      case "accept offer":
        return Image.asset(
          "assets/images/accepted_offer.png",
          height: 24,
          width: 24,
        );
      default:
        return const Icon(Icons.article, color: AppColors.primaryColorOld);
    }
  }
}

class HistoryItem extends StatefulWidget {
  final HistoryRes historyData;
  final bool isLast;
  final VoidCallback? onViewOld;
  final VoidCallback? onViewSplit;
  final VoidCallback? onOpenQuotes;
  final VoidCallback? onOpenNego;

  const HistoryItem({
    Key? key,
    required this.historyData,
    required this.isLast,
    this.onViewOld,
    this.onViewSplit,
    this.onOpenQuotes,
    this.onOpenNego,
  }) : super(key: key);

  @override
  State<HistoryItem> createState() => _HistoryItemState();
}

class _HistoryItemState extends State<HistoryItem> {
  final FlutterSoundPlayer _mPlayer = FlutterSoundPlayer();
  // var theSource = AudioSource.microphone;
  final bool _mPlayerIsInited = false;
  final bool _mplaybackReady = false;
  bool playing = false;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _mPlayer.openPlayer();
    _setArrays(widget.historyData);
  }

  void _setArrays(HistoryRes historyRes) {
    final List<Media> items = historyRes.medias ?? [];

    for (var media in items) {
      final String? url = media.url;
      if (url == null) {
        continue;
      }
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady) {
      return null;
    }
    return _mPlayer.isStopped ? play() : stopPlayer();
  }

  void play() {
    assert(_mPlayerIsInited && _mplaybackReady && _mPlayer.isStopped);
    _mPlayer.startPlayer(whenFinished: () {
      setState(() {});
    }).then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    var isEditable = widget.historyData.statusCd == "EDIT";
    var isSplit = widget.historyData.statusCd == "SPLT";
    var openQuotes = widget.historyData.openQuoteScreen == true;
    var openNego = widget.historyData.openNegoScreen == true;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            const Icon(Icons.article, color: AppColors.primaryColorOld),
            const SizedBox(height: 16),
            if (!widget.isLast)
              Container(
                width: 1,
                height: calculateIntrinsicHeight(),
                color: Colors.grey,
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.historyData.title ?? "N/A",
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 7),
                  Text(
                    widget.historyData.createdAt
                            ?.addFiveThirtyAndReturnInSameFormat() ??
                        "N/A",
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 7),

                  Text(
                    '${widget.historyData.eventCreatedCustomerName ?? "Name N/A"}, ${widget.historyData.eventCreatedCustomerPhone ?? "Phone N/A"}',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 7),
                  RichText(
                    text: TextSpan(
                      children: [
                        const TextSpan(
                          text: 'Comment: ',
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontFamily: "Poppins",
                          ),
                        ),
                        TextSpan(
                          text: widget.historyData.description ?? "N/A",
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                            fontFamily: "Poppins",
                          ),
                        ),
                      ],
                    ),
                  ),

                  if (images.isNotEmpty) const SizedBox(height: 16),
                  if (images.isNotEmpty)
                    SizedBox(
                      height: 74,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: images.length,
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(2),
                        itemBuilder: (context, index) {
                          return Material(
                            elevation: 2,
                            borderRadius: BorderRadius.circular(8.0),
                            clipBehavior: Clip.hardEdge,
                            child: GestureDetector(
                              onTap: () {
                                showImageDialog(context, images[index]);
                              },
                              child: Container(
                                margin: const EdgeInsets.all(0.25),
                                child: Image.network(
                                  images[index],
                                  width: 72.0,
                                  height: 72.0,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            // Image.network(images[index], fit: BoxFit.fill),
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return const SizedBox(width: 8);
                        },
                      ),
                    ),

                  if (files.isNotEmpty)
                    SizedBox(
                      height: 74,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: files.length,
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(2),
                        itemBuilder: (context, index) {
                          var url = Uri.parse(files[index]);
                          return GestureDetector(
                            onTap: () async {
                              if (await canLaunchUrl(url)) {
                                await launchUrl(url);
                              } else {
                                throw 'Could not launch $url';
                              }
                            },
                            child: Material(
                              elevation: 2,
                              borderRadius: BorderRadius.circular(8.0),
                              clipBehavior: Clip.hardEdge,
                              child: Container(
                                color: Colors.grey.shade200,
                                height: 72,
                                width: 72,
                                margin: const EdgeInsets.all(0.25),
                                child: Center(
                                    child: Icon(Icons.file_copy_outlined,
                                        size: 32.0,
                                        color: AppColors.primaryColor)),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return const SizedBox(width: 8);
                        },
                      ),
                    ),
                  // : const Text("No documents available"),

                  if (audios.isNotEmpty) const SizedBox(height: 16),
                  if (audios.isNotEmpty)
                    Row(
                      children: [
                        GestureDetector(
                          onTap: _togglePlayback,
                          child: Icon(
                            playing ? Icons.pause : Icons.play_circle_fill,
                            size: 40.0,
                            color: AppColors.primaryColorOld,
                          ),
                        ),
                        const SizedBox(width: 8.0),
                        Container(
                          width: 200.0,
                          height: 40.0,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Image.asset("assets/images/waveform.png"),
                        ),
                      ],
                    ),
                  if (widget.historyData.locationLat != null)
                    const SizedBox(height: 16),
                  if (widget.historyData.locationLat != null)
                    Row(
                      children: [
                        const SizedBox(width: 3),
                        GestureDetector(
                          onTap: () async {
                            try {
                              if (await MapLauncher.isMapAvailable(
                                      MapType.google) ??
                                  false) {
                                await MapLauncher.showMarker(
                                  mapType: MapType.google,
                                  coords: Coords(
                                    double.parse(
                                        widget.historyData.locationLat ?? "0"),
                                    double.parse(
                                        widget.historyData.locationLong ?? "0"),
                                  ),
                                  title: "Location",
                                  description:
                                      "${widget.historyData.locationLat}, ${widget.historyData.locationLong}",
                                );
                              }
                            } catch (e) {
                              safePrint(e);
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.location_on_sharp,
                              color: AppColors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              if (isEditable && widget.onViewOld != null)
                TextButton(
                  onPressed: widget.onViewOld,
                  child: Text(
                    'View old',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (isSplit && widget.onViewSplit != null)
                TextButton(
                  onPressed: widget.onViewSplit,
                  child: Text(
                    'View',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (openQuotes)
                TextButton(
                  onPressed: widget.onOpenQuotes,
                  child: Text(
                    'Open',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (openNego)
                TextButton(
                  onPressed: widget.onOpenNego,
                  child: Text(
                    'Open',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  double calculateIntrinsicHeight() {
    double totalHeight = 0;

    // Icon height
    totalHeight += 12;

    // First SizedBox height
    totalHeight += 12;

    // Text heights
    totalHeight += 12;
    totalHeight += 12;
    totalHeight += 12;

    // ListView height (if applicable)
    if (images.isNotEmpty) {
      totalHeight += 74;
    }

    // Audio Row height (if applicable)
    if (audios.isNotEmpty) {
      totalHeight += 40;
    }

    return totalHeight;
  }
}
