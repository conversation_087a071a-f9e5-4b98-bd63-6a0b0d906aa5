import 'package:connectone/bai_blocs/profile/cubit/profile_cubit.dart';
import 'package:connectone/bai_models/user_profile_v2_res.dart';
import 'package:connectone/bai_screens/edit_assign_approve.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:loader_overlay/loader_overlay.dart';

class UserProfileDialog extends StatefulWidget {
  const UserProfileDialog({
    Key? key,
    this.stockId,
    this.vendorId,
    this.prchOrdrId,
  }) : super(key: key);

  final num? vendorId;
  final num? prchOrdrId;

  final String? stockId;

  @override
  State<UserProfileDialog> createState() => _UserProfileDialogState();
}

class _UserProfileDialogState extends State<UserProfileDialog> {
  @override
  void initState() {
    context.read<ProfileCubit>().loadUserProfile(widget.vendorId);
    super.initState();
  }

  @override

  /// Builds the widget tree for [UserProfileDialog].
  ///
  /// Displays a [Dialog] with a title, rating bar, and the vendor's information
  /// such as location, phone number, email, website, and BAI membership.
  ///
  /// The dialog also displays the number of orders and total amount for the
  /// vendor.
  ///
  /// The dialog uses a [BlocConsumer] to listen to the [ProfileCubit] and
  /// display the information when the state is [ProfileLoaded].
  ///
  /// The dialog also displays a [TextButton] to close the dialog.
  ///
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(20),
      child: AppLoader(
        child: BlocConsumer<ProfileCubit, ProfileState>(
            listener: (context, state) {},
            builder: (context, state) {
              (state is ProfileLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              if (state is ProfileLoaded) {
                var data = state.userProfileV2Res?.data;
                return Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          height: 52,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Center(
                            child: Text(
                              'Company Profile',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ).withCloseButton(() => Navigator.pop(context)),
                        const SizedBox(height: 30),
                        Text(
                          data?.name ?? "",
                          style: const TextStyle(
                            fontWeight: FontWeight.w900,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 20),
                        RatingBar.builder(
                          initialRating: data?.averageRating != null
                              ? double.parse(data!.averageRating!.toString())
                              : 4.0,
                          minRating: 1,
                          updateOnDrag: false,
                          direction: Axis.horizontal,
                          allowHalfRating: true,
                          ignoreGestures: true,
                          itemCount: 5,
                          unratedColor: Colors.grey.withOpacity(0.3),
                          itemPadding:
                              const EdgeInsets.symmetric(horizontal: 4.0),
                          itemBuilder: (context, _) => Icon(
                            Icons.star,
                            color: AppColors.primaryColor,
                          ),
                          onRatingUpdate: (rating) {},
                        ),
                        const SizedBox(height: 10),
                        Text(
                          data?.totalReviews != null && data!.totalReviews! > 0
                              ? 'Avg. ${data.averageRating ?? 'N/A'} based on ${data.totalReviews} reviews'
                              : 'No reviews yet',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 30),
                        buildInfoRow(
                          'Location',
                          "${data?.addressLine1 ?? "N/A"} ${data?.addressLine2 ?? ""}",
                        ),
                        const SizedBox(height: 18),
                        InkWell(
                            onTap: () {
                              makePhoneCall(data?.primaryPhone ?? "");
                            },
                            child: buildInfoRow(
                              'Phone',
                              data?.primaryPhone ?? "",
                            )),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'Email',
                          data?.email ?? "",
                        ),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'Website',
                          data?.website ?? "N/A",
                        ),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'BAI Member',
                          data?.baiMember ?? false ? "Yes" : "No",
                        ),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'GST Type',
                          data?.typeOfGstFiling ?? "N/A",
                        ),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'No. of Orders',
                          data?.totalOrder.toString() ?? 'N/A',
                          isHighlighted: true,
                        ),
                        const SizedBox(height: 18),
                        buildInfoRow(
                          'Total Amount',
                          "₹ ${data?.totalAmount ?? 'N/A'}",
                          isHighlighted: true,
                        ),
                        const SizedBox(height: 18),
                        buildCertificationRow(data?.medias),
                        const SizedBox(height: 10),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text(
                            'X Close',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                return Container();
              }
            }),
      ),
    );
  }

  Widget buildCertificationRow(List<Medias>? medias) {
    // Filter the list based on the docType
    List<Medias>? certificateMedias =
        medias?.where((media) => media.docType == 'certificate').toList();
    List<Medias>? photoMedias =
        medias?.where((media) => media.docType == 'photos').toList();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if ((certificateMedias ?? []).isNotEmpty) ...{
          buildSectionTitle('Certifications'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: certificateMedias?.length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return InkWell(
                    onTap: () {},
                    child: SizedBox(
                      height: 100,
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.picture_as_pdf,
                              color: Colors.red,
                              size: 45,
                            ),
                            const SizedBox(width: 15),
                            Text(certificateMedias?[index].title ?? ""),
                            // IconButton(
                            //   icon: const Icon(Icons.close, color: Colors.red),
                            //   onPressed: () {
                            //     // Handle remove certification button press
                            //   },
                            // ),
                          ],
                        ),
                      ),
                    ));
              },
            ),
          ),
          const SizedBox(height: 19),
        },
        if ((certificateMedias ?? []).isNotEmpty) ...{
          buildSectionTitle('Photos'),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: photoMedias?.length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () =>
                      showImageDialog(context, photoMedias?[index].url ?? ""),
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SizedBox(
                      height: 100,
                      child: Image.network(photoMedias?[index].url ?? ""),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 19),
        }
      ],
    );
  }

  Widget buildInfoRow(String label, String value,
      {bool isHighlighted = false}) {
    if (value.trim().isEmpty) {
      value = "N/A";
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isHighlighted ? Colors.red : Colors.black,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w900,
              color: isHighlighted ? Colors.red : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildSectionTitle(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w900,
          fontSize: 16,
        ),
      ),
    );
  }

  void showImageDialog(BuildContext context, String? url) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                height: MediaQuery.of(context).size.height * 0.75,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.network(
                    url ?? 'https://via.placeholder.com/600',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ),
              Positioned(
                top: -10,
                right: -10,
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.all(2),
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildPhotoRow() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: const DecorationImage(
                image: NetworkImage('https://via.placeholder.com/150'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: const DecorationImage(
                image: NetworkImage('https://via.placeholder.com/150'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: const DecorationImage(
                image: NetworkImage('https://via.placeholder.com/150'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
