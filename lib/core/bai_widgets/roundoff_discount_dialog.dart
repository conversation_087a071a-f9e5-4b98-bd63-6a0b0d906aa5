import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';

class RoundoffDiscountDialog extends StatefulWidget {
  const RoundoffDiscountDialog({
    Key? key,
    required this.orderGroupId,
    required this.currentTotalAmount,
    required this.roundoffDiscount,
    this.offerId,
  }) : super(key: key);

  final int orderGroupId;
  final double currentTotalAmount;
  final double roundoffDiscount;
  final List<int>? offerId;

  @override
  State<RoundoffDiscountDialog> createState() => _RoundoffDiscountDialogState();
}

class _RoundoffDiscountDialogState extends State<RoundoffDiscountDialog> {
  final TextEditingController _roundoffDiscountController =
      TextEditingController();
  bool _isLoading = false;
  double _newTotalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _newTotalAmount = widget.currentTotalAmount;
    _roundoffDiscountController.text = widget.roundoffDiscount.toString();
  }

  void _calculateNewTotal() {
    double roundoffDiscount =
        double.tryParse(_roundoffDiscountController.text) ?? 0.0;
    setState(() {
      _newTotalAmount = widget.currentTotalAmount - roundoffDiscount;
    });
  }

  Future<void> _submitRoundoffDiscount() async {
    if (_roundoffDiscountController.text.isEmpty) {
      alert("Please enter roundoff discount value.");
      return;
    }

    double? roundoffDiscount =
        double.tryParse(_roundoffDiscountController.text);
    if (roundoffDiscount == null || roundoffDiscount < 0) {
      alert("Please enter a valid roundoff discount value.");
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final networkController = NetworkController();
      final response = await networkController.createRoundOffDiscount(
        orderGroupId: widget.orderGroupId.toString(),
        discountRoundOff: roundoffDiscount,
        offerId: widget.offerId ?? [],
      );
      if (response.status == 200) {
        // alert(response.statusDescription);
        Navigator.pop(context, true);
      } else {
        alert(response.statusDescription);
      }
    } catch (e) {
      alert("Error: ${e.toString()}");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? Container(
                padding: const EdgeInsets.all(20),
                height: 80,
                width: 64,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: CircularProgressIndicator()),
              )
            : SingleChildScrollView(
                child: Column(
                  children: [
                    // Header Section
                    Container(
                      height: 52,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColorOld,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Roundoff Discount',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ).withCloseButton(() => Navigator.pop(context)),
                    const SizedBox(height: 24),

                    // Current Total Amount (read-only box)
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Current Total Amount',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey.shade400,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(4),
                        color: Colors.grey.shade50,
                      ),
                      child: Text(
                        "₹${widget.currentTotalAmount.toStringAsFixed(2)}",
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Roundoff Discount field
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Roundoff Discount',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      style: const TextStyle(color: Colors.black),
                      controller: _roundoffDiscountController,
                      keyboardType: TextInputType.number,
                      autofocus: true,
                      decoration: InputDecoration(
                        hintText: 'Enter roundoff discount amount',
                        filled: false,
                        isDense: true,
                        fillColor: Colors.grey.shade300,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      onChanged: (value) {
                        _calculateNewTotal();
                      },
                    ),
                    const SizedBox(height: 24),

                    // New Total Amount (calculated, read-only box - distinct)
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'New Total Amount',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.green,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.green.withOpacity(0.1),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.green.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Icon(
                            Icons.calculate,
                            color: AppColors.green,
                            size: 24,
                          ),
                          Text(
                            "₹${_newTotalAmount.toStringAsFixed(2)}",
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: AppColors.green,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Submit and Close Buttons
                    SizedBox(
                      height: 40,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: BaiButton(
                              onTap: () {
                                Navigator.pop(context, false);
                              },
                              text: "CLOSE",
                              backgoundColor: AppColors.red,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: BaiButton(
                              onTap: _submitRoundoffDiscount,
                              text: "SUBMIT",
                              backgoundColor: AppColors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  @override
  void dispose() {
    _roundoffDiscountController.dispose();
    super.dispose();
  }
}
