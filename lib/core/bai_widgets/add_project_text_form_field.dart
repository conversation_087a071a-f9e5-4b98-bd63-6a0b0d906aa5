import 'package:flutter/material.dart';

class AddProjectTextFormField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final int? maxLength;
  final ValueChanged<String>? onChanged;

  const AddProjectTextFormField({
    Key? key,
    required this.labelText,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.maxLength,
    this.onChanged,
  }) : super(key: key);

  @override

  /// Returns a [TextFormField] widget with the given properties.
  ///
  /// The returned [TextFormField] has a black border, black label text, and
  /// black text. The border is 0.75px wide. The label text is not dense.
  ///
  /// The [onChanged] callback is set to the [onChanged] parameter if it is not
  /// null. If it is null, the callback is not set.
  ///
  /// The [onSaved] callback is set to save the current value of the [controller]
  /// to the [controller].
  ///
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      maxLength: maxLength,
      style: const TextStyle(color: Colors.black),
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: const TextStyle(color: Colors.black),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        isDense: true,
      ),
      onSaved: (value) {
        controller.text = value ?? '';
      },
      onChanged: onChanged,
    );
  }
}
