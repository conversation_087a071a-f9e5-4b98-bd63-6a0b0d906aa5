import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';

class BaiButton extends StatelessWidget {
  const BaiButton({
    Key? key,
    required this.onTap,
    required this.text,
    this.backgoundColor,
    this.textColor,
    this.height,
    this.elevation,
    this.borderRadius,
  }) : super(key: key);

  final Function onTap;
  final String text;
  final Color? backgoundColor;
  final Color? textColor;
  final double? height;
  final double? elevation;
  final double? borderRadius;

  @override

  /// Build a button with the provided text and onTap callback, with an option to provide the background color, text color, and border radius.
  ///
  /// The button will be a SizedBox with a height of [height] (defaulting to 48), and will have a width of double.infinity.
  /// The child of the SizedBox is an ElevatedButton, with the provided [onTap] callback, and the style of the button will be set to
  /// [ElevatedButton.styleFrom] with the provided [backgoundColor] (defaulting to [AppColors.primaryColor]), with a shape of
  /// [RoundedRectangleBorder] with a border radius of the provided [borderRadius] (defaulting to 4).
  ///
  /// The child of the ElevatedButton is a Text, with the provided [text] and [textColor] (defaulting to [Colors.white]), with a font weight
  /// of [FontWeight.bold] and a text alignment of [TextAlign.center].
  Widget build(BuildContext context) {
    return SizedBox(
      height: height ?? 48,
      // padding: const EdgeInsets.symmetric(vertical: 8),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          onTap();
        },
        style: ElevatedButton.styleFrom(
          elevation: elevation,
          backgroundColor: backgoundColor ?? AppColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 4),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
              fontWeight: FontWeight.bold, color: textColor ?? Colors.white),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
