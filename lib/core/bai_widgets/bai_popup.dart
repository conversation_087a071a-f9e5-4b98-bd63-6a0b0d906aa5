import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:flutter/material.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/const_dimens.dart';
import 'package:connectone/core/utils/theme_utils.dart';

class BaiPopup extends StatelessWidget {
  final String title;
  final String content;

  const BaiPopup({
    Key? key,
    required this.title,
    required this.content,
  }) : super(key: key);

  @override

  /// Build a dialog with a title and content and a close button.
  ///
  /// The title is displayed in a large font size and the content is
  /// displayed in a smaller font size.
  ///
  /// The close button is displayed in the top right corner of the dialog.
  ///
  /// The dialog is displayed with a rounded rectangle shape and a shadow.
  /// The background color of the dialog is transparent.
  ///
  /// The dialog is scrollable if the content is too long to fit in the
  /// available space.
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DialogConstants.padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Theme(
        data: getBoldTheme(),
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Stack(
            children: <Widget>[
              Container(
                padding: const EdgeInsets.all(DialogConstants.padding),
                margin:
                    const EdgeInsets.only(top: DialogConstants.avatarRadius),
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(DialogConstants.padding),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black,
                      offset: Offset(0, 10),
                      blurRadius: 10,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    // const SizedBox(height: 40),
                    // Text(
                    //   title,
                    //   textAlign: TextAlign.center,
                    //   style: const TextStyle(
                    //     color: AppColors.green,
                    //     fontSize: 20,
                    //     fontWeight: FontWeight.bold,
                    //   ),
                    // ),
                    const SizedBox(height: 16),
                    Text(
                      content,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: AppColors.green,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    BaiButton(
                      onTap: () => Navigator.pop(context),
                      text: "OK",
                      height: 48,
                    ),
                  ],
                ),
              ),
              Positioned(
                left: DialogConstants.padding,
                right: DialogConstants.padding,
                child: CircleAvatar(
                  radius: 48,
                  backgroundColor: Colors.grey.shade200,
                  child: CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 44,
                    child: Icon(
                      Icons.info,
                      color: AppColors.primaryColor,
                      size: 44,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.close,
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
