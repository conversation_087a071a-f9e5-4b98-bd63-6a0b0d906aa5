import 'package:connectone/bai_blocs/team_member/cubit/team_member_cubit.dart';
import 'package:connectone/bai_models/member_list_res.dart';
import 'package:connectone/bai_models/team_member.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssignToNewMemberDialog extends StatefulWidget {
  final TeamMember member;

  const AssignToNewMemberDialog({
    Key? key,
    required this.member,
  }) : super(key: key);

  @override
  State<AssignToNewMemberDialog> createState() =>
      _AssignToNewMemberDialogState();
}

class _AssignToNewMemberDialogState extends State<AssignToNewMemberDialog> {
  MemberListItem? selectedMember;

  @override
  void initState() {
    super.initState();
    context.read<TeamMemberCubit>().getMemberList();
  }

  @override

  /// A dialog to assign the <Delete user's> data to a new team member.
  ///
  /// This dialog contains a title, a description, a dropdown to select the
  /// new team member, and two buttons: "CLOSE" and "SUBMIT".
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      insetPadding: const EdgeInsets.all(16),
      child: BlocConsumer<TeamMemberCubit, TeamMemberState>(
        listener: (context, state) {
          if (state is TeamMemberError) {
            alert("Something went wrong. Please try again.");
            context.read<TeamMemberCubit>().getMemberList();
          }
          if (state is NewTeamMemberAssigned) {
            context.read<TeamMemberCubit>().getMemberList();
          }
          if (state is TeamMemberApproved) {
            context.read<TeamMemberCubit>().getMemberList();
          }
          if (state is TeamMemberRejected) {
            context.read<TeamMemberCubit>().getMemberList();
          }
        },
        builder: (context, state) {
          if (state is TeamMemberLoaded) {
            var teamMembers = state.memberList;
            return Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 56,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColorOld,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.handshake, color: Colors.white, size: 28),
                        SizedBox(width: 8),
                        Text(
                          'Assign to New Member',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      "Assign the ${widget.member.uname}'s data to new team member",
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  DropdownButtonFormField<MemberListItem>(
                    value: selectedMember,
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: const InputDecoration(
                      isDense: true,
                      border: OutlineInputBorder(),
                      labelText: 'New Team Member',
                    ),
                    items: teamMembers?.map((MemberListItem member) {
                      return DropdownMenuItem<MemberListItem>(
                        value: member,
                        child: Text(member.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedMember = value;
                      });
                    },
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: BaiButton(
                          height: 44,
                          onTap: () {
                            Navigator.pop(context);
                          },
                          text: "CLOSE",
                          backgoundColor: AppColors.darkRed,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: BaiButton(
                          height: 44,
                          onTap: () {
                            if (selectedMember == null) {
                              alert("Please select a team member.");
                              return;
                            }
                            context.read<TeamMemberCubit>().assignToNewMember(
                                  widget.member.id?.toString() ?? "",
                                  selectedMember!.id,
                                );
                          },
                          text: "SUBMIT",
                          backgoundColor: AppColors.primaryColorOld,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          } else if (state is TeamMemberLoading) {
            return const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            );
          }
          return Container();
        },
      ),
    );
  }
}
