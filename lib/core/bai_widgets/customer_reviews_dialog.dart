import 'package:connectone/bai_blocs/reviews/review_cubit.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:connectone/bai_models/bai_products_res.dart' as bpr;

class CustomerReviewsDialog extends StatefulWidget {
  final String? id;
  final bpr.Content? content;
  const CustomerReviewsDialog({Key? key, this.id, this.content})
      : super(key: key);

  @override
  State<CustomerReviewsDialog> createState() => _CustomerReviewsDialogState();
}

class _CustomerReviewsDialogState extends State<CustomerReviewsDialog> {
  @override
  void initState() {
    context.read<ReviewCubit>().loadReview(widget.id ?? "");
    super.initState();
  }

  @override

  /// Builds the Customer Reviews Dialog widget.
  ///
  /// This widget displays customer reviews for a specific product within a dialog
  /// interface. It uses a BlocConsumer to handle the review states:
  /// - While loading, a loading overlay is shown.
  /// - If reviews are successfully loaded, it displays a list of reviews with
  ///   star ratings, customer names, and comments.
  /// - If an error occurs or no reviews are available, a message is shown
  ///   indicating "No Reviews Available".
  ///
  /// The dialog includes a title, the customer's name, their average rating,
  /// and the number of global ratings. It also provides a close button to
  /// dismiss the dialog.
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: AppLoader(
        child: BlocConsumer<ReviewCubit, ReviewState>(
            listener: (context, state) {},
            builder: (context, state) {
              (state is ReviewLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              if (state is ReviewError) {
                return const Padding(
                  padding: EdgeInsets.all(30.0),
                  child: Text(
                    'No Reviews Available',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }
              if (state is ReviewLoaded) {
                var data = state.reviewsRes?.content;

                return Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: 52,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Center(
                            child: Text(
                              'Customer reviews',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ).withCloseButton(() => Navigator.pop(context)),
                        const SizedBox(height: 16),
                        Text(
                          widget.content?.customerName ?? "",
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 18),
                        ),
                        const SizedBox(height: 8),
                        RatingBar.builder(
                          initialRating: 3.5,
                          minRating: 1,
                          direction: Axis.horizontal,
                          allowHalfRating: true,
                          itemCount: 5,
                          itemPadding:
                              const EdgeInsets.symmetric(horizontal: 4.0),
                          itemBuilder: (context, _) => const Icon(
                            Icons.star,
                            color: Colors.amber,
                          ),
                          onRatingUpdate: (rating) {
                            print(rating);
                          },
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${widget.content?.averageRating} out of 5',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${data?.length} Global Rating',
                          style: const TextStyle(
                            color: Colors.black54,
                          ),
                        ),
                        const Divider(height: 32, color: Colors.grey),
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Comments',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ListView.separated(
                          itemBuilder: (context, index) {
                            return buildComment(
                              name: data?[index].customerName ?? "",
                              date: data?[index].createdAt ?? "",
                              rating: data?[index].rating != null
                                  ? double.parse(
                                      data?[index].rating.toString() ?? "")
                                  : 0.0,
                              comment: data?[index].comment ?? "",
                              helpfulCount: data?[index].helpfulCount ?? 0,
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 16);
                          },
                          itemCount: data?.length ?? 0,
                        ),

                        // buildComment(
                        //   name: 'Alice',
                        //   date: '12 June 2024',
                        //   rating: 4.0,
                        //   comment:
                        //       'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
                        //   helpfulCount: 4,
                        // ),
                        // const SizedBox(height: 16),
                        // buildComment(
                        //   name: 'Sam',
                        //   date: '12 June 2024',
                        //   rating: 4.0,
                        //   comment:
                        //       'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
                        //   helpfulCount: 1,
                        // ),
                        // const SizedBox(height: 16),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text(
                            'X Close',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                return const Center(
                  child: Text(
                    'No Reviews Available',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                );
              }
            }),
      ),
    );
  }

  Widget buildComment({
    required String name,
    required String date,
    required double rating,
    required String comment,
    required int helpfulCount,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.grey.shade300,
              child: Icon(
                Icons.person,
                size: 24,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Text(
              date,
              style: const TextStyle(
                color: Colors.black54,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        RatingBar.builder(
          initialRating: rating,
          minRating: 1,
          direction: Axis.horizontal,
          allowHalfRating: true,
          itemCount: 5,
          itemSize: 20,
          itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
          itemBuilder: (context, _) => const Icon(
            Icons.star,
            color: Colors.blue,
          ),
          onRatingUpdate: (rating) {},
        ),
        const SizedBox(height: 8),
        Text(
          comment,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
          style: TextStyle(
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 8),
        if (helpfulCount > 0)
          Text(
            '$helpfulCount people found this helpful',
            style: const TextStyle(
              color: Colors.black54,
            ),
          ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                side: const BorderSide(color: AppColors.primaryColorOld),
              ),
              child: const Text(
                'Helpful',
                style: TextStyle(color: AppColors.primaryColorOld),
              ),
            ),
            const SizedBox(width: 8),
            TextButton(
              onPressed: () {},
              child: const Text('Report'),
            ),
          ],
        ),
      ],
    );
  }
}
