import 'package:audio_session/audio_session.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/seller_card.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';

import '../utils/safe_print.dart';
import '../utils/tools.dart';

class NegotiateDialog extends StatefulWidget {
  const NegotiateDialog({
    Key? key,
    required this.offer,
    required this.unit,
    this.history,
  }) : super(key: key);

  final Offer offer;
  final String unit;
  final NegotiationHistory? history;

  @override
  State<NegotiateDialog> createState() => _NegotiateDialogState();
}

class _NegotiateDialogState extends State<NegotiateDialog> {
  final TextEditingController _pricePerUnitController = TextEditingController();
  final TextEditingController _totalPriceController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();

  Codec _codec = Codec.aacMP4;
  final String _mPath = 'audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
  FlutterSoundPlayer? _mPlayer = FlutterSoundPlayer();
  FlutterSoundRecorder? _mRecorder = FlutterSoundRecorder();
  var theSource = AudioSource.microphone;
  bool _mPlayerIsInited = false;
  bool _mRecorderIsInited = false;
  bool _mplaybackReady = false;

  final List<String> _audios = [];

  final ImagePicker picker = ImagePicker();
  List<String> images = [];
  List<String> files = [];

  final List<Uint8List> _imageBytes = [];
  final List<Uint8List> _fileBytes = [];

  bool hasAttachmentsMaxed() {
    return images.length + files.length >= 5; // Including audio files in count
  }

  Future<void> getImage({bool fromCamera = true}) async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files. Please remove some to add new ones.");
    } else {
      try {
        final XFile? image = await picker.pickImage(
          source: fromCamera ? ImageSource.camera : ImageSource.gallery,
          requestFullMetadata: false,
          imageQuality: 25,
        );

        if (image != null) {
          final Uint8List imageBytes = await image.readAsBytes();

          if (mounted) {
            setState(() {
              _imageBytes.add(imageBytes);
              images.add(image.path);
            });
          }
        }
      } catch (e) {
        safePrint(e);
      }
    }
  }

  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Select Image Source",
              style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                GestureDetector(
                  child: const Text("Take using Camera"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: true);
                  },
                ),
                const SizedBox(height: 16),
                GestureDetector(
                  child: const Text("Pick from Gallery"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: false);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> pickFiles() async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files. Please remove some to add new ones.");
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;

        if (kIsWeb) {
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              files.add(file.name);
            });
          }
        } else {
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              files.add(file.path ?? file.name);
            });
          } else if (file.path != null) {
            File selectedFile = File(file.path!);
            setState(() {
              files.add(file.path!);
              selectedFile.readAsBytes().then((value) {
                _fileBytes.add(value);
              });
            });
          }
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _mPlayer!.openPlayer().then((value) {
      setState(() {
        _mPlayerIsInited = true;
      });
    });

    openTheRecorder().then((value) {
      setState(() {
        _mRecorderIsInited = true;
      });
    });

    _pricePerUnitController.addListener(_updateTotalPrice);
    _quantityController.text = widget.offer.quantity.toString();
  }

  Future<void> openTheRecorder() async {
    if (!kIsWeb) {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException(
            'Microphone permission not granted.');
      }
    }
    await _mRecorder!.openRecorder();
    if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
      _codec = Codec.opusWebM;
      if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
        _mRecorderIsInited = true;
        return;
      }
    }
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.voiceCommunication,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));
    _mRecorderIsInited = true;
  }

  void _updateTotalPrice() {
    var quantity = widget.offer.quantity;
    String pricePerUnitText = _pricePerUnitController.text;
    double pricePerUnit =
        double.tryParse(cleanRupeeString(pricePerUnitText)) ?? 0.0;
    double totalPrice = pricePerUnit * (quantity ?? 1);
    _totalPriceController.text = convertToINR(totalPrice.toString());
  }

  var _isEditingQuantity = false;
  final _quantityController = TextEditingController();

  @override

  /// Builds the negotiate dialog widget.
  ///
  /// This widget provides a dialog interface for negotiating offers.
  /// It uses a [BlocConsumer] to listen to [OffersCubit] state changes.
  /// Displays a loading overlay when the state is [BuyerOffersLoading].
  ///
  /// The dialog contains the following components:
  /// - Current Offer Price and Quantity with editable fields.
  /// - Requested Price per unit and total, with input fields.
  /// - Notes and Voice Note sections for additional information.
  /// - Buttons to record/play voice notes, and to submit or close the dialog.
  ///
  /// The dialog updates the quantity and total price based on user input.
  /// Submits negotiation details including any voice note and notes provided.
  Widget build(BuildContext context) {
    return BlocConsumer<OffersCubit, OffersState>(
      listener: (context, state) {},
      builder: (context, state) {
        (state is BuyerOffersLoading)
            ? context.loaderOverlay.show()
            : context.loaderOverlay.hide();
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          insetPadding: const EdgeInsets.all(8),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    height: 52,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColorOld,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.handshake, color: Colors.white, size: 28),
                        SizedBox(width: 8),
                        Text(
                          'Negotiate',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ).withCloseButton(() => Navigator.pop(context)),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        children: [
                          const Align(
                            alignment: Alignment.center,
                            child: Text(
                              'Current Offer',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Align(
                            alignment: Alignment.center,
                            child: Text(
                              convertToINR(widget.history == null
                                  ? widget.offer.offerPrice.toString()
                                  : widget.history?.offerPrice.toString() ??
                                      "0.0"),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: 40,
                        width: 1,
                        color: Colors.black,
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                      ),
                      Column(
                        children: [
                          const Align(
                            alignment: Alignment.center,
                            child: Text(
                              'Quantity',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Align(
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _isEditingQuantity
                                    ? SizedBox(
                                        width: 80, // Adjust width as needed
                                        child: TextField(
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold),
                                          controller: _quantityController,
                                          keyboardType: TextInputType.number,
                                          decoration: const InputDecoration(
                                            border: OutlineInputBorder(),
                                            isDense: true,
                                          ),
                                          onSubmitted: (value) {
                                            setState(() {
                                              _isEditingQuantity = false;
                                              // You can update the offer quantity here if needed
                                              widget.offer.quantity =
                                                  int.tryParse(value) ??
                                                      widget.offer.quantity;
                                            });
                                          },
                                        ),
                                      )
                                    : Text(
                                        _quantityController.text,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 24,
                                        ),
                                      ),
                                IconButton(
                                  constraints: const BoxConstraints(),
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                  icon: Icon(
                                    _isEditingQuantity
                                        ? Icons.check
                                        : Icons.edit,
                                    size: 20,
                                    color: Colors.black,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isEditingQuantity = !_isEditingQuantity;
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Column(
                    children: [
                      const Align(
                        alignment: Alignment.center,
                        child: Text(
                          'Current Price / Unit',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Align(
                        alignment: Alignment.center,
                        child: Text(
                          convertToINR((((widget.history == null
                                          ? widget.offer.offerPrice
                                          : widget.history?.offerPrice ??
                                              0.0) ??
                                      0) /
                                  (widget.offer.quantity ?? 1))
                              .toString()),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Requested Price / ${widget.unit}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _pricePerUnitController,
                    keyboardType: TextInputType.number,
                    // inputFormatters: [IndianRupeeFormatter()],
                    decoration: InputDecoration(
                      hintText: '',
                      filled: false,
                      isDense: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Requested Price (Total)',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    style: const TextStyle(color: Colors.black),
                    controller: _totalPriceController,
                    keyboardType: TextInputType.number,
                    // inputFormatters: [IndianRupeeFormatter()],
                    enabled: false,
                    decoration: InputDecoration(
                      hintText: '',
                      filled: false,
                      isDense: true,
                      fillColor: Colors.grey.shade300,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Upload / Attach Photo",
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              if (images.isNotEmpty)
                                Flexible(
                                  child: SizedBox(
                                    height: 76,
                                    child: ListView.separated(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: images.length,
                                      padding: const EdgeInsets.all(6),
                                      itemBuilder: (context, index) {
                                        return Stack(
                                          clipBehavior: Clip.none,
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              clipBehavior: Clip.hardEdge,
                                              child: kIsWeb
                                                  ? Image.network(
                                                      images[index],
                                                      width: 64,
                                                      height: 64,
                                                      fit: BoxFit.cover,
                                                    )
                                                  : Image.file(
                                                      File(images[index]),
                                                      width: 64,
                                                      height: 64,
                                                      fit: BoxFit.cover,
                                                    ),
                                            ),
                                            Positioned(
                                              top: -8,
                                              right: -8,
                                              child: InkWell(
                                                onTap: () {
                                                  setState(() {
                                                    images.removeAt(index);
                                                    _imageBytes.removeAt(index);
                                                  });
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                        color: Colors.red),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    color: Colors.transparent,
                                                  ),
                                                  padding:
                                                      const EdgeInsets.all(2),
                                                  child: const Icon(
                                                    Icons.close,
                                                    size: 16,
                                                    color: Colors.red,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return const SizedBox(width: 12);
                                      },
                                    ),
                                  ),
                                ),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade200,
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.photo),
                                  onPressed: () {
                                    showImageSourceDialog(context);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Upload Additional Specifications / Docs",
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              if (files.isNotEmpty)
                                Flexible(
                                  child: SizedBox(
                                    height: 76,
                                    child: ListView.separated(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: files.length,
                                      padding: const EdgeInsets.all(6),
                                      itemBuilder: (context, index) {
                                        return Stack(
                                          clipBehavior: Clip.none,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                color: Colors.grey.shade200,
                                              ),
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.file_copy_outlined,
                                                  color: AppColors.primaryColor,
                                                ),
                                                onPressed: () {
                                                  setState(() {
                                                    files.removeAt(index);
                                                    _fileBytes.removeAt(index);
                                                  });
                                                },
                                              ),
                                            ),
                                            Positioned(
                                              top: -8,
                                              right: -8,
                                              child: GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    files.removeAt(index);
                                                    _fileBytes.removeAt(index);
                                                  });
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                        color: Colors.red),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                  ),
                                                  padding:
                                                      const EdgeInsets.all(2),
                                                  child: const Icon(
                                                    Icons.close,
                                                    size: 16,
                                                    color: Colors.red,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return const SizedBox(width: 12);
                                      },
                                    ),
                                  ),
                                ),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade200,
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.file_upload_outlined),
                                  onPressed: () {
                                    pickFiles();
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Add Voice Note',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                  child: Text(_audios.isNotEmpty
                                      ? "Voice Note Added"
                                      : "Add Voice Note")),
                              const Icon(Icons.mic),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(children: [
                            SizedBox(
                              width: kIsWeb
                                  ? 100
                                  : MediaQuery.of(context).size.width * 0.25,
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primaryColor),
                                onPressed: () {
                                  getRecorderFn();
                                },
                                child: Text(_mRecorder!.isRecording
                                    ? 'Stop'
                                    : 'Record'),
                              ),
                            ),
                            const SizedBox(
                              width: 20,
                            ),
                            Text(_mRecorder!.isRecording
                                ? 'Recording in progress'
                                : 'Recorder is stopped'),
                          ]),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (_audios.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      height: 64,
                      width: double.infinity,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.black),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(children: [
                        SizedBox(
                          width: kIsWeb
                              ? 100
                              : MediaQuery.of(context).size.width * 0.25,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primaryColor),
                            onPressed: () {
                              getPlaybackFn();
                            },
                            child: Text(_mPlayer!.isPlaying ? 'Stop' : 'Play'),
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        Text(_mPlayer!.isPlaying
                            ? 'Playback in progress'
                            : 'Player is stopped'),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _audios.clear();
                            });
                          },
                          icon: const Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                        ),
                      ]),
                    ),
                  if (_audios.isNotEmpty) const SizedBox(height: 16),
                  const SizedBox(height: 12),
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Add Notes',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _noteController,
                    keyboardType: TextInputType.text,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: '',
                      filled: false,
                      isDense: true,
                      fillColor: Colors.grey.shade300,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 40,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: BaiButton(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            text: "CANCEL",
                            backgoundColor: AppColors.red,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: BaiButton(
                            onTap: () {
                              context.read<OffersCubit>().negotiateOffer(
                                    widget.offer.id.toString(),
                                    double.parse(cleanRupeeString(
                                        _totalPriceController.text.trim())),
                                    audio: _audios.isNotEmpty
                                        ? _audios.first
                                        : null,
                                    paths: [...files, ...images, ..._audios],
                                    bytes: [..._fileBytes, ..._imageBytes],
                                    notes: _noteController.text.trim(),
                                    quantity: double.tryParse(
                                            _quantityController.text) ??
                                        0.0,
                                  );
                              Navigator.pop(context);
                            },
                            text: "ADD",
                            backgoundColor: AppColors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void record() {
    _mRecorder!
        .startRecorder(
      toFile: _mPath,
      codec: _codec,
      audioSource: theSource,
    )
        .then((value) {
      setState(() {});
    });
  }

  void stopRecorder() async {
    await _mRecorder!.stopRecorder().then((value) {
      setState(() {
        _audios.clear();
        if (value != null) {
          _audios.add(value);
        }
        _mplaybackReady = true;
      });
    });
  }

  void play() {
    assert(_mPlayerIsInited &&
        _mplaybackReady &&
        _mRecorder!.isStopped &&
        _mPlayer!.isStopped);
    _mPlayer!
        .startPlayer(
            fromURI: _mPath,
            whenFinished: () {
              setState(() {});
            })
        .then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer!.stopPlayer().then((value) {
      setState(() {});
    });
  }

  getRecorderFn() {
    if (!_mRecorderIsInited || !_mPlayer!.isStopped) {
      return null;
    }
    return _mRecorder!.isStopped ? record() : stopRecorder();
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady || !_mRecorder!.isStopped) {
      return null;
    }
    return _mPlayer!.isStopped ? play() : stopPlayer();
  }

  @override
  void dispose() {
    _pricePerUnitController.removeListener(_updateTotalPrice);
    _mPlayer!.closePlayer();
    _mPlayer = null;
    _mRecorder!.closeRecorder();
    _mRecorder = null;
    super.dispose();
  }
}

String convertToINR(String amount) {
  try {
    double value = double.parse(amount);
    final NumberFormat formatter = NumberFormat.simpleCurrency(name: 'INR');
    return formatter.format(value);
  } catch (e) {
    return amount;
  }
}
