import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';

class CategoryCard extends StatelessWidget {
  final String category;
  final Function onTap;
  final String imageUrl;
  final String title;

  const CategoryCard({
    Key? key,
    required this.category,
    required this.onTap,
    required this.imageUrl,
    required this.title,
  }) : super(key: key);

  @override

  /// Returns a [Card] widget with a rounded corner and shadow.
  ///
  /// The card contains an [InkWell] widget which is a rectangular area of a
  /// [Material] that responds to touch.
  ///
  /// The [InkWell] widget contains a [Stack] which has a [BaiImage] widget and
  /// a [Positioned] widget.
  ///
  /// The [BaiImage] widget displays the image for the category.
  ///
  /// The [Positioned] widget has a [Container] which is positioned at the
  /// bottom of the card. The container contains a [Text] widget with the title
  /// of the category.
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.hardEdge,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12.0)),
      ),
      elevation: 4.0,
      margin: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: () => onTap(),
        splashColor: AppColors.primaryColor.withOpacity(0.5),
        highlightColor: AppColors.primaryColor.withOpacity(0.2),
        child: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              // height: 200,
              child: BaiImage(url: imageUrl),
            ).withColorOverlay(),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                width: double.infinity,
                constraints: const BoxConstraints(minHeight: 40),
                color: AppColors.primaryColor.withOpacity(0.8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.0,
                    ),
                    maxLines: 2,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
