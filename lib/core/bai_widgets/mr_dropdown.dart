import 'package:connectone/bai_models/search_categories_res.dart';
import 'package:flutter/material.dart';

class MRDropdown extends StatelessWidget {
  final String labelText;
  final List<Content> items;
  final ValueChanged<Content?>? onChanged;
  final Content? selectedValue;

  const MRDropdown({
    Key? key,
    required this.labelText,
    required this.items,
    this.onChanged,
    this.selectedValue,
  }) : super(key: key);

  @override

  /// Returns a [DropdownButtonFormField] widget with the given properties.
  ///
  /// The returned [DropdownButtonFormField] has a white background color,
  /// black label text, and black text. The border is 0.75px wide. The label
  /// text is not dense.
  ///
  /// The [onChanged] callback is set to the [onChanged] parameter if it is
  /// not null. If it is null, the callback is not set.
  ///
  /// The [items] are mapped into [DropdownMenuItem] widgets with white
  /// background color, black text, and a border radius of 4. The width of the
  /// [DropdownMenuItem] is set to the width of the screen minus 88.
  ///
  /// The [selectedValue] is set to the selected value of the
  /// [DropdownButtonFormField].
  Widget build(BuildContext context) {
    return DropdownButtonFormField<Content>(
      dropdownColor: Colors.white,
      value: selectedValue,
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: const TextStyle(color: Colors.black),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.black,
            width: 0.75,
          ),
        ),
        isDense: true,
      ),
      style: const TextStyle(
        color: Colors.black,
        fontFamily: "poppins",
      ),
      items: items.map((Content value) {
        return DropdownMenuItem<Content>(
          value: value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            width: MediaQuery.of(context).size.width - 88,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              // color: value == selectedValue ? AppColors.primaryColor : Colors.white,
            ),
            child: Text(
              value.name.toString(),
              // style: TextStyle(color: value == selectedValue ? Colors.white : Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }).toList(),
      onChanged: onChanged,
      // validator: (value) {
      //   if (value == null || value.isEmpty) {
      //     return 'This field is required';
      //   }
      //   return null;
      // },
    );
  }
}
