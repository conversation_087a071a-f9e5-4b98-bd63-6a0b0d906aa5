import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
// import 'package:connectone/bai_screens/quotes_dialog.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';

class FilterPopup extends StatefulWidget {
  final OffersFilterRes data;
  final ViewOfferReq viewOfferReq;
  final Function(ViewOfferReq) onApply;

  const FilterPopup({
    Key? key,
    required this.data,
    required this.viewOfferReq,
    required this.onApply,
  }) : super(key: key);

  @override
  State<FilterPopup> createState() => _FilterPopupState();
}

class _FilterPopupState extends State<FilterPopup> {
  Map<String, List<String>> selectedValues = {};

  var style = const TextStyle(fontWeight: FontWeight.bold);

  @override
  void initState() {
    super.initState();
    // Initialize the selection state based on the viewOfferReq data
    selectedValues['variant1OptionGroupName'] =
        List.from(widget.viewOfferReq.variant1OptionGroupName);
    selectedValues['variant1OptionNames'] =
        List.from(widget.viewOfferReq.variant1OptionNames);
    selectedValues['variant2OptionGroupName'] =
        List.from(widget.viewOfferReq.variant2OptionGroupName);
    selectedValues['variant2OptionNames'] =
        List.from(widget.viewOfferReq.variant2OptionNames);
    selectedValues['variant3OptionGroupName'] =
        List.from(widget.viewOfferReq.variant3OptionGroupName);
    selectedValues['variant3OptionNames'] =
        List.from(widget.viewOfferReq.variant3OptionNames);
  }

  @override

  /// Builds the filter popup for the quotes screen.
  ///
  /// The filter popup allows the user to select options for variant 1, variant 2 and variant 3.
  /// The selected values are stored in the [selectedValues] map.
  /// The [onApply] callback is called when the user presses the 'Apply' button.
  ///
  /// The popup displays the variant options as a list of checkboxes. The user can select multiple options.
  /// The selected options are displayed at the top of the list.
  ///
  /// The popup also has a 'Close' button which closes the popup without applying the selected values.
  /// The popup also has an 'Apply' button which applies the selected values and closes the popup.
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.all(16.0),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Display variant 1 options
            if (widget.data.variant1OptionGroupName != null &&
                widget.data.variant1OptionGroupName!.isNotEmpty)
              Text(
                widget.data.variant1OptionGroupName?.first ?? "N/A",
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            if (widget.data.variant1OptionGroupName != null &&
                widget.data.variant1OptionGroupName!.isNotEmpty)
              ...widget.data.variant1OptionNames!.map((value) {
                return CheckboxListTile(
                  title: Text(value),
                  dense: false,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  value: selectedValues['variant1OptionNames']!.contains(value),
                  onChanged: (bool? newValue) {
                    setState(() {
                      if (newValue == true) {
                        selectedValues['variant1OptionNames']!.add(value);
                      } else {
                        selectedValues['variant1OptionNames']!.remove(value);
                      }
                    });
                  },
                );
              }).toList(),
            // Display variant 2 options
            if (widget.data.variant2OptionGroupName != null &&
                widget.data.variant2OptionGroupName!.isNotEmpty)
              Text(
                widget.data.variant2OptionGroupName?.first ?? "N/A",
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            if (widget.data.variant2OptionGroupName != null &&
                widget.data.variant2OptionGroupName!.isNotEmpty)
              ...widget.data.variant2OptionNames!.map((value) {
                return CheckboxListTile(
                  title: Text(value),
                  dense: false,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  value: selectedValues['variant2OptionNames']!.contains(value),
                  onChanged: (bool? newValue) {
                    setState(() {
                      if (newValue == true) {
                        selectedValues['variant2OptionNames']!.add(value);
                      } else {
                        selectedValues['variant2OptionNames']!.remove(value);
                      }
                    });
                  },
                );
              }).toList(),
            // Display variant 3 options
            if (widget.data.variant3OptionGroupName != null &&
                widget.data.variant3OptionGroupName!.isNotEmpty)
              Text(
                widget.data.variant3OptionGroupName?.first ?? "N/A",
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            if (widget.data.variant3OptionGroupName != null &&
                widget.data.variant3OptionGroupName!.isNotEmpty)
              ...widget.data.variant3OptionNames!.map((value) {
                return CheckboxListTile(
                  title: Text(value),
                  dense: false,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  value: selectedValues['variant3OptionNames']!.contains(value),
                  onChanged: (bool? newValue) {
                    setState(() {
                      if (newValue == true) {
                        selectedValues['variant3OptionNames']!.add(value);
                      } else {
                        selectedValues['variant3OptionNames']!.remove(value);
                      }
                    });
                  },
                );
              }).toList(),
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          style: ButtonStyle(
              foregroundColor: WidgetStatePropertyAll(AppColors.primaryColor)),
          child: Text(
            'Close',
            style: style,
          ),
        ),
        ElevatedButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColors.primaryColor),
          ),
          onPressed: () {
            var req = ViewOfferReq(
              variant1OptionGroupName:
                  widget.data.variant1OptionGroupName ?? [],
              variant1OptionNames: selectedValues['variant1OptionNames'] ?? [],
              variant2OptionGroupName:
                  widget.data.variant2OptionGroupName ?? [],
              variant2OptionNames: selectedValues['variant2OptionNames'] ?? [],
              variant3OptionGroupName:
                  widget.data.variant3OptionGroupName ?? [],
              variant3OptionNames: selectedValues['variant3OptionNames'] ?? [],
            );
            widget.onApply(req);
            Navigator.of(context).pop();
          },
          child: Text(
            'Apply',
            style: style,
          ),
        ),
      ],
    );
  }
}
