import 'package:audioplayers/audioplayers.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_screens/edit_assign_approve.dart';
// import 'package:connectone/bai_screens/quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/user_profile_dialog.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class BuyerCard extends StatefulWidget {
  final Offer offerDetails;
  final Function onAccept;
  final Function onNegotiate;
  final bool showQuantity;

  const BuyerCard({
    Key? key,
    required this.offerDetails,
    required this.onAccept,
    required this.onNegotiate,
    required this.showQuantity,
  }) : super(key: key);

  @override
  State<BuyerCard> createState() => _BuyerCardState();
}

class _BuyerCardState extends State<BuyerCard> {
  final FlutterSoundPlayer _mPlayer = FlutterSoundPlayer();
  final bool _mPlayerIsInited = false;
  final bool _mplaybackReady = false;
  bool playing = false;

  List<String> urls = [];
  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  final player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _mPlayer.openPlayer();
    _setArrays(widget.offerDetails);
  }

  void _setArrays(Offer offer) {
    final List<Media> items = offer.medias ?? [];

    for (var media in items) {
      final String? url = media.url;
      if (url == null) {
        continue;
      }
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady) {
      return null;
    }
    return _mPlayer.isStopped ? play() : stopPlayer();
  }

  void play() {
    assert(_mPlayerIsInited && _mplaybackReady && _mPlayer.isStopped);
    _mPlayer.startPlayer(whenFinished: () {
      setState(() {});
    }).then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  @override

  /// Builds a buyer card widget.
  ///
  /// This widget is used to display buyer information
  /// in the buyer list screen.
  ///
  /// It displays the buyer's name, phone, BAI member status,
  /// GST type, variant information, submitted at date,
  /// quantity, price/quantity, status, total price,
  /// remarks, and attached images and files.
  ///
  /// It also provides a button to negotiate with the buyer
  /// and a button to accept the buyer's offer.
  ///
  /// The style of the widget is based on the [AppColors] class.
  ///
  Widget build(BuildContext context) {
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 15,
    );
    var gstType = widget.offerDetails.typeOfGstFiling;
    var isBaiMember = widget.offerDetails.baiMember ?? false;
    var rating = widget.offerDetails.rating;

    var isApproved = widget.offerDetails.statusCd == "APPR";

    var gst = widget.offerDetails.gst ?? 0;
    var offerPrice = widget.offerDetails.offerPrice ?? 0;

    // Calculate GST-related values
    var gstAmount = (offerPrice * gst) / 100;
    var quoteWithoutGst = offerPrice;
    var quoteWithGst = offerPrice + gstAmount;

    return Card(
      elevation: 8,
      clipBehavior: Clip.hardEdge,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: isApproved ? Colors.green.shade100 : Colors.white,
          boxShadow: const [
            // BoxShadow(
            //   color: AppColors.primaryColor.withOpacity(0.2),
            //   offset: const Offset(0, 2),
            //   blurRadius: 6,
            //   spreadRadius: 2,
            // ),
          ],
        ),
        margin: const EdgeInsets.all(0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return UserProfileDialog(
                              vendorId: widget.offerDetails.vendorId,
                              prchOrdrId: widget.offerDetails.prchOrdrId,
                            );
                          },
                        );
                      },
                      child: Text(
                        (widget.offerDetails.vendorName ?? 'N/A'),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                      ),
                    ),
                  ),
                  // Row(
                  //   children: [
                  //     if (gstType == "Compound")
                  //       Image.asset(
                  //         "assets/images/gst.png",
                  //         height: 24,
                  //         width: 24,
                  //       ),
                  //     if (isBaiMember) const SizedBox(width: 4),
                  //     if (isBaiMember)
                  //       Image.asset(
                  //         "assets/images/bai_member.png",
                  //         height: 24,
                  //         width: 24,
                  //       ),
                  //     const SizedBox(width: 4),
                  //   ],
                  // ),
                  Row(children: [
                    Icon(
                      Icons.star_rate_rounded,
                      color: AppColors.primaryColor,
                      size: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Text(
                        rating != null ? rating.toStringAsFixed(1) : "N/A",
                        style: style,
                      ),
                    ),
                  ]),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    margin: const EdgeInsets.only(top: 2),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.offerDetails.id.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: () {
                  if ((widget.offerDetails.vendorPhone ?? "").isNotEmpty) {
                    makePhoneCall(widget.offerDetails.vendorPhone ?? "");
                  }
                },
                child: Row(
                  children: [
                    const Expanded(
                      flex: 2,
                      child: Text(
                        "Phone: ",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.vendorPhone ?? 'N/A',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      "BAI Member: ",
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      isBaiMember ? "Yes" : "No",
                      style: style,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      "GST Type:",
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      "$gstType",
                      style: style,
                    ),
                  ),
                ],
              ),
              if (widget.offerDetails.variant1OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant1OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${widget.offerDetails.variant1OptionGroupName ?? "Variant 1"}:',
                        style: style,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant1OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              if (widget.offerDetails.variant2OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant2OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${widget.offerDetails.variant2OptionGroupName ?? "Variant 2"}: ',
                        style: style,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant2OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              if (widget.offerDetails.variant3OptionName != null)
                const SizedBox(height: 8),
              if (widget.offerDetails.variant3OptionName != null)
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                          '${widget.offerDetails.variant3OptionGroupName ?? "Variant 3"}:',
                          style: style),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        widget.offerDetails.variant3OptionName ?? "N/A",
                        style: style,
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Submitted At:',
                      style: style,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      widget.offerDetails.createdAt?.toCreatedOn() ?? "N/A",
                      style: style,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Quantity:${(widget.offerDetails.quantityEdit ?? false) ? " * " : ""}',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      widget.offerDetails.quantity?.toString() ?? "N/A",
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Price / Quantity:',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      getPricePerQuantity(),
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              // GST %, GST amount, Quote (w/o GST), Quote (with GST)
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'GST %:',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      '${gst.toStringAsFixed(2)}%',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'GST Amount:',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      '₹${NumberFormat('#,##0.00').format(gstAmount)}',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Quote (w/o GST):',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      '₹${NumberFormat('#,##0.00').format(quoteWithoutGst)}',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Quote (with GST):',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      '₹${NumberFormat('#,##0.00').format(quoteWithGst)}',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      'Status:',
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      widget.offerDetails.statusName ?? "N/A",
                      style:
                          style.copyWith(backgroundColor: Colors.greenAccent),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.blue[900],
                ),
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
                child: Row(
                  children: [
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Total Price (with GST):',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        '₹${NumberFormat('#,##0.00').format(quoteWithGst)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.offerDetails.remarks != null &&
                  widget.offerDetails.remarks!.isNotEmpty)
                const SizedBox(height: 12),
              if (widget.offerDetails.remarks != null &&
                  widget.offerDetails.remarks!.isNotEmpty)
                Text(
                  "Remarks: ${widget.offerDetails.remarks}",
                  style: style,
                ),
              const SizedBox(height: 16),
              generateHistoryTable(context, widget.offerDetails),
              if (images.isNotEmpty) const SizedBox(height: 16),
              if (images.isNotEmpty)
                SizedBox(
                  height: 74,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: images.length,
                    shrinkWrap: true,
                    padding: const EdgeInsets.all(2),
                    itemBuilder: (context, index) {
                      return Material(
                        elevation: 2,
                        borderRadius: BorderRadius.circular(8.0),
                        clipBehavior: Clip.hardEdge,
                        child: GestureDetector(
                            onTap: () async {
                              showImageDialog(context, images[index]);
                            },
                            child: Image.network(
                              images[index],
                              fit: BoxFit.fill,
                            )),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(width: 8);
                    },
                  ),
                ),
              if (files.isNotEmpty) const SizedBox(height: 16),
              if (files.isNotEmpty)
                SizedBox(
                  height: 74,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: files.length,
                    shrinkWrap: true,
                    padding: const EdgeInsets.all(2),
                    itemBuilder: (context, index) {
                      var url = Uri.parse(files[index]);
                      return GestureDetector(
                        onTap: () async {
                          if (await canLaunchUrl(url)) {
                            await launchUrl(url);
                          } else {
                            throw 'Could not launch $url';
                          }
                        },
                        child: Material(
                          elevation: 2,
                          borderRadius: BorderRadius.circular(8.0),
                          clipBehavior: Clip.hardEdge,
                          child: Container(
                            color: Colors.grey.shade200,
                            height: 72,
                            width: 72,
                            margin: const EdgeInsets.all(0.25),
                            child: Center(
                              child: Icon(
                                Icons.file_copy_outlined,
                                size: 32.0,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const SizedBox(width: 8);
                    },
                  ),
                ),
              if (audios.isNotEmpty) const SizedBox(height: 16),
              if (audios.isNotEmpty)
                Row(
                  children: [
                    GestureDetector(
                      onTap: _togglePlayback,
                      child: Icon(
                        playing ? Icons.pause : Icons.play_circle_fill,
                        size: 40.0,
                        color: AppColors.primaryColorOld,
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Container(
                      width: 200.0,
                      height: 40.0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 4),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Image.asset("assets/images/waveform.png"),
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              if (widget.offerDetails.statusCd == "OPEN")
                const SizedBox(height: 12),
              if (widget.offerDetails.statusCd == "OPEN")
                Row(
                  children: [
                    Expanded(
                      child: BaiButton(
                        onTap: () {
                          widget.onNegotiate(widget.offerDetails);
                        },
                        text: "NEGOTIATE",
                        height: 40,
                        backgoundColor: Colors.red.shade800,
                      ),
                    ),
                    // const SizedBox(width: 12),
                    // Expanded(
                    //   child: BaiButton(
                    //     onTap: () {
                    //       widget.onAccept(widget.offerDetails);
                    //     },
                    //     text: "ACCEPT",
                    //     height: 40,
                    //     backgoundColor: Colors.green.shade800,
                    //   ),
                    // ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  String getPricePerQuantity() {
    if (widget.offerDetails.quantity != null &&
        widget.offerDetails.offerPrice != null) {
      double pricePerQuantity =
          widget.offerDetails.offerPrice! / widget.offerDetails.quantity!;
      return '₹${NumberFormat('#,##0.00').format(pricePerQuantity)}';
    }
    return 'N/A';
  }
}
