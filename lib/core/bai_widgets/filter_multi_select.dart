// import 'package:connectone/bai_models/item_offering_res.dart';
// import 'package:connectone/core/utils/extensions.dart';
// import 'package:connectone/core/utils/tools.dart';
// import 'package:flutter/material.dart';

// class FilterMultiSelectDropdown extends StatefulWidget {
//   final String labelText;
//   final List<String> items;
//   final List<String> selectedValues;
//   final ValueChanged<List<String>> onChanged;

//   const FilterMultiSelectDropdown({
//     Key? key,
//     required this.labelText,
//     required this.items,
//     required this.selectedValues,
//     required this.onChanged,
//   }) : super(key: key);

//   @override
//   State<FilterMultiSelectDropdown> createState() =>
//       _FilterMultiSelectDropdownState();
// }

// class _FilterMultiSelectDropdownState extends State<FilterMultiSelectDropdown> {
//   bool isExpanded = false;

//   @override
//   void initState() {
//     super.initState();
//     isExpanded = widget.selectedValues.isEmpty ? false : true;
//   }

//   @override

//   /// Builds the dropdown UI widget with a tap gesture detector.
//   ///
//   /// When tapped, it toggles the expansion state of the dropdown.
//   /// Displays a container with a customizable border, background color,
//   /// and padding. The container includes a column with a row for the label
//   /// text and an expansion icon, and a visibility widget for the list of
//   /// selectable items.
//   ///
//   /// The dropdown items are displayed using [FilterChip] widgets. The selection
//   /// state of each item is managed, and the [onChanged] callback is triggered
//   /// with the updated list of selected values.
//   ///
//   /// Returns a [GestureDetector] wrapping a [Container] widget.
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           isExpanded = !isExpanded;
//         });
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           border: Border.all(color: Colors.grey),
//           borderRadius: BorderRadius.circular(24),
//           color: (widget.selectedValues.isEmpty)
//               ? Colors.white
//               : !isExpanded
//                   ? Colors.greenAccent.withOpacity(0.6)
//                   : Colors.white,
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   widget.labelText,
//                   style: const TextStyle(
//                       fontSize: 14, fontWeight: FontWeight.bold),
//                 ),
//                 Icon(isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down),
//               ],
//             ),
//             SizedBox(height: isExpanded ? 16 : 0),
//             Visibility(
//               visible: isExpanded,
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 32,
//                 child: Wrap(
//                   spacing: 6,
//                   runSpacing: 8,
//                   children: widget.items.map((String item) {
//                     var itemNew = item.replaceAll(RegExp(r'\s*\(\d+\)'), '');
//                     bool isSelected = widget.selectedValues.contains(itemNew);
//                     return FilterChip(
//                       label: Text(
//                         item,
//                         style: TextStyle(
//                             fontWeight: isSelected
//                                 ? FontWeight.bold
//                                 : FontWeight.normal),
//                         maxLines: 2,
//                       ),
//                       showCheckmark: false,
//                       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                       selected: isSelected,
//                       onSelected: (bool selected) {
//                         setState(() {
//                           if (selected) {
//                             widget.selectedValues.add(itemNew);
//                           } else {
//                             widget.selectedValues.remove(itemNew);
//                           }
//                           widget.onChanged(widget.selectedValues);
//                         });
//                       },
//                       selectedColor: Colors.cyan.withOpacity(0.4),
//                       backgroundColor: Colors.grey.shade200,
//                     );
//                   }).toList(),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class FilterMultiSelectRequestsDropdown extends StatefulWidget {
//   final String labelText;
//   final List<String> items;
//   final List<String> selectedValues;
//   final ValueChanged<List<String>> onChanged;

//   const FilterMultiSelectRequestsDropdown({
//     Key? key,
//     required this.labelText,
//     required this.items,
//     required this.selectedValues,
//     required this.onChanged,
//   }) : super(key: key);

//   @override
//   State<FilterMultiSelectRequestsDropdown> createState() =>
//       _FilterMultiSelectRequestsDropdownState();
// }

// class _FilterMultiSelectRequestsDropdownState
//     extends State<FilterMultiSelectRequestsDropdown> {
//   bool isExpanded = false;

//   @override
//   void initState() {
//     super.initState();
//     isExpanded = widget.selectedValues.isEmpty ? false : true;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           isExpanded = !isExpanded;
//         });
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           border: Border.all(color: Colors.grey),
//           borderRadius: BorderRadius.circular(24),
//           color: (widget.selectedValues.isEmpty)
//               ? Colors.white
//               : !isExpanded
//                   ? Colors.greenAccent.withOpacity(0.6)
//                   : Colors.white,
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   widget.labelText,
//                   style: const TextStyle(
//                     fontSize: 14,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 Icon(isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down),
//               ],
//             ),
//             SizedBox(height: isExpanded ? 16 : 0),
//             Visibility(
//               visible: isExpanded,
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 32,
//                 child: Wrap(
//                   spacing: 6,
//                   runSpacing: 8,
//                   children: widget.items.map((String item) {
//                     var itemNew = item
//                         .replaceAll(RegExp(r'\s*\(\d+\)'), '')
//                         .toStatusCode();
//                     bool isSelected = widget.selectedValues.contains(itemNew);
//                     return FilterChip(
//                       label: Text(
//                         item,
//                         style: TextStyle(
//                             fontWeight: isSelected
//                                 ? FontWeight.bold
//                                 : FontWeight.normal),
//                         maxLines: 2,
//                       ),
//                       showCheckmark: false,
//                       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                       selected: isSelected,
//                       onSelected: (bool selected) {
//                         setState(() {
//                           if (selected) {
//                             widget.selectedValues.add(itemNew);
//                           } else {
//                             widget.selectedValues.remove(itemNew);
//                           }
//                           widget.onChanged(widget.selectedValues);
//                         });
//                       },
//                       selectedColor: Colors.cyan.withOpacity(0.4),
//                       backgroundColor: Colors.grey.shade200,
//                     );
//                   }).toList(),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class FilterMultiSelectNew extends StatefulWidget {
//   final String labelText;
//   final List<String> items;
//   final List<String> selectedValues;
//   final ValueChanged<List<String>> onChanged;

//   const FilterMultiSelectNew({
//     Key? key,
//     required this.labelText,
//     required this.items,
//     required this.selectedValues,
//     required this.onChanged,
//   }) : super(key: key);

//   @override
//   State<FilterMultiSelectNew> createState() => _FilterMultiSelectNewState();
// }

// class _FilterMultiSelectNewState extends State<FilterMultiSelectNew> {
//   bool isExpanded = true;

//   void _showTextInputDialog(String item) {
//     final TextEditingController textController = TextEditingController();
//     var style = const TextStyle(
//       fontWeight: FontWeight.bold,
//       color: Colors.black,
//     );
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text(
//             'Specify',
//             style: TextStyle(
//               fontWeight: FontWeight.bold,
//               fontSize: 16,
//             ),
//           ),
//           content: TextField(
//             controller: textController,
//             decoration: const InputDecoration(hintText: 'Enter details'),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text(
//                 'Cancel',
//                 style: style,
//               ),
//             ),
//             TextButton(
//               onPressed: () {
//                 String enteredText = textController.text;
//                 if (enteredText.isNotEmpty) {
//                   setState(() {
//                     String newValue = '$item - $enteredText';
//                     widget.selectedValues
//                         .removeWhere((selected) => selected.startsWith(item));
//                     widget.selectedValues.add(newValue);
//                     widget.onChanged(widget.selectedValues);
//                   });
//                 }
//                 Navigator.of(context).pop();
//               },
//               child: Text(
//                 'OK',
//                 style: style,
//               ),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           isExpanded = !isExpanded;
//         });
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           border: Border.all(color: Colors.black),
//           borderRadius: BorderRadius.circular(6),
//           color: Colors.white,
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   widget.labelText,
//                   style: const TextStyle(
//                       fontSize: 14, fontWeight: FontWeight.bold),
//                 ),
//                 Icon(
//                   isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
//                   color: Colors.black,
//                 ),
//               ],
//             ),
//             SizedBox(height: isExpanded ? 16 : 0),
//             Visibility(
//               visible: isExpanded,
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 32,
//                 child: Wrap(
//                   spacing: 6,
//                   runSpacing: 8,
//                   children: widget.items.map((String item) {
//                     // bool isSelected = widget.selectedValues.contains(item) ||
//                     //     widget.selectedValues
//                     //         .any((selected) => selected.startsWith(item));

//                     bool isSelected = widget.selectedValues
//                         .any((selected) => selected.startsWith(item));
//                     String displayValue = isSelected
//                         ? widget.selectedValues
//                             .firstWhere((selected) => selected.startsWith(item))
//                         : item;

//                     return FilterChip(
//                       label: Text(
//                         displayValue,
//                         style: TextStyle(
//                             fontWeight: isSelected
//                                 ? FontWeight.bold
//                                 : FontWeight.normal),
//                       ),
//                       showCheckmark: false,
//                       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                       selected: isSelected,
//                       onSelected: (bool selected) {
//                         setState(() {
//                           if (selected) {
//                             if (item.contains("specify")) {
//                               _showTextInputDialog(item);
//                             } else {
//                               widget.selectedValues.add(item);
//                             }
//                           } else {
//                             widget.selectedValues.removeWhere(
//                                 (selected) => selected.startsWith(item));
//                           }
//                           widget.onChanged(widget.selectedValues);
//                         });
//                       },
//                       selectedColor: Colors.cyan.withOpacity(0.4),
//                       backgroundColor: Colors.grey.shade200,
//                     );
//                   }).toList(),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class FilterMultiSelectNewSteel extends StatefulWidget {
//   final String labelText;
//   final List<String> items;
//   final List<String> selectedValues;
//   final ValueChanged<List<String>> onChanged;
//   final OptionGroup? optionGroup;

//   const FilterMultiSelectNewSteel({
//     Key? key,
//     required this.labelText,
//     required this.items,
//     required this.selectedValues,
//     required this.onChanged,
//     required this.optionGroup,
//   }) : super(key: key);

//   @override
//   State<FilterMultiSelectNewSteel> createState() =>
//       _FilterMultiSelectNewSteelState();
// }

// class _FilterMultiSelectNewSteelState extends State<FilterMultiSelectNewSteel> {
//   bool isExpanded = true;
//   String? selectedUnit;

//   void _showQuantityDialog(String item) {
//     selectedUnit = null;
//     final TextEditingController quantityController = TextEditingController();

//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         var style =
//             const TextStyle(fontWeight: FontWeight.bold, color: Colors.black);
//         return AlertDialog(
//           title: Text(
//             'Enter Quantity for $item',
//             style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
//           ),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               TextField(
//                 controller: quantityController,
//                 keyboardType: TextInputType.number,
//                 decoration: const InputDecoration(hintText: 'Quantity'),
//               ),
//               const SizedBox(height: 16),
//               DropdownButtonFormField<String>(
//                 hint: const Text("Select Unit"),
//                 value: selectedUnit,
//                 onChanged: (String? newValue) {
//                   setState(() {
//                     selectedUnit = newValue;
//                   });
//                 },
//                 items: widget.optionGroup?.options
//                     ?.map<DropdownMenuItem<String>>((unit) {
//                   return DropdownMenuItem<String>(
//                     value: unit.name,
//                     child: Text(unit.name ?? ""),
//                   );
//                 }).toList(),
//               ),
//             ],
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text('Cancel', style: style),
//             ),
//             TextButton(
//               onPressed: () {
//                 if (selectedUnit == null) {
//                   alert("Please select unit");
//                   return;
//                 }
//                 String quantity = quantityController.text;
//                 if (quantity.isNotEmpty && selectedUnit != null) {
//                   setState(() {
//                     String newValue = '$item - $quantity $selectedUnit';
//                     if (!widget.selectedValues
//                         .any((selected) => selected.startsWith(item))) {
//                       widget.selectedValues.add(newValue);
//                     } else {
//                       widget.selectedValues
//                           .removeWhere((selected) => selected.startsWith(item));
//                       widget.selectedValues.add(newValue);
//                     }
//                     widget.onChanged(widget.selectedValues);
//                   });
//                 }
//                 Navigator.of(context).pop();
//               },
//               child: Text('OK', style: style),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           isExpanded = !isExpanded;
//         });
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           border: Border.all(color: Colors.black),
//           borderRadius: BorderRadius.circular(6),
//           color: (widget.selectedValues.isEmpty) ? Colors.white : Colors.white,
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   widget.labelText,
//                   style: const TextStyle(
//                       fontSize: 14, fontWeight: FontWeight.bold),
//                 ),
//                 Icon(
//                   isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
//                   color: Colors.black,
//                 ),
//               ],
//             ),
//             SizedBox(height: isExpanded ? 16 : 0),
//             Visibility(
//               visible: isExpanded,
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 32,
//                 child: Wrap(
//                   spacing: 6,
//                   runSpacing: 8,
//                   children: widget.items.map((String item) {
//                     bool isSelected = widget.selectedValues
//                         .any((selected) => selected.startsWith(item));
//                     String displayValue = isSelected
//                         ? widget.selectedValues
//                             .firstWhere((selected) => selected.startsWith(item))
//                         : item;

//                     return FilterChip(
//                       label: Text(
//                         displayValue,
//                         style: TextStyle(
//                             fontWeight: isSelected
//                                 ? FontWeight.bold
//                                 : FontWeight.normal),
//                       ),
//                       showCheckmark: false,
//                       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                       selected: isSelected,
//                       onSelected: (bool selected) {
//                         if (selected) {
//                           _showQuantityDialog(item);
//                         } else {
//                           setState(() {
//                             widget.selectedValues.removeWhere(
//                                 (selected) => selected.startsWith(item));
//                             widget.onChanged(widget.selectedValues);
//                           });
//                         }
//                       },
//                       selectedColor: Colors.cyan.withOpacity(0.4),
//                       backgroundColor: Colors.grey.shade200,
//                     );
//                   }).toList(),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class FilterMultiSelectAssign extends StatefulWidget {
//   final String labelText;
//   final List<String> items;
//   final List<String> selectedValues;
//   final ValueChanged<List<String>> onChanged;

//   const FilterMultiSelectAssign({
//     Key? key,
//     required this.labelText,
//     required this.items,
//     required this.selectedValues,
//     required this.onChanged,
//   }) : super(key: key);

//   @override
//   State<FilterMultiSelectAssign> createState() =>
//       _FilterMultiSelectAssignState();
// }

// class _FilterMultiSelectAssignState extends State<FilterMultiSelectAssign> {
//   bool isExpanded = false;
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           isExpanded = !isExpanded;
//         });
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           border: Border.all(color: Colors.grey),
//           borderRadius: BorderRadius.circular(6),
//           color: (widget.selectedValues.isEmpty)
//               ? Colors.white
//               : !isExpanded
//                   ? Colors.white
//                   : Colors.white,
//         ),
//         padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Visibility(
//               // visible: isExpanded,
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 32,
//                 child: Wrap(
//                   spacing: 6,
//                   runSpacing: 8,
//                   children: widget.items.map((String item) {
//                     // var itemNew = item.replaceAll(RegExp(r'\s*\(\d+\)'), '');
//                     bool isSelected = widget.selectedValues.contains(item);
//                     return FilterChip(
//                       label: Text(
//                         item,
//                         style: TextStyle(
//                             fontWeight: isSelected
//                                 ? FontWeight.bold
//                                 : FontWeight.normal),
//                         maxLines: 2,
//                       ),
//                       showCheckmark: false,
//                       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//                       selected: isSelected,
//                       onSelected: (bool selected) {
//                         setState(() {
//                           if (selected) {
//                             widget.selectedValues.add(item);
//                           } else {
//                             widget.selectedValues.remove(item);
//                           }
//                           widget.onChanged(widget.selectedValues);
//                         });
//                       },
//                       selectedColor: Colors.cyan.withOpacity(0.4),
//                       backgroundColor: Colors.grey.shade200,
//                     );
//                   }).toList(),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class FilterValue {
//   final String value;
//   final String label;

//   FilterValue({
//     required this.value,
//     required this.label,
//   });
// }

import 'package:connectone/bai_models/item_offering_res.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';

class FilterMultiSelectDropdown extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;

  const FilterMultiSelectDropdown({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<FilterMultiSelectDropdown> createState() =>
      _FilterMultiSelectDropdownState();
}

class _FilterMultiSelectDropdownState extends State<FilterMultiSelectDropdown> {
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    isExpanded = widget.selectedValues.isEmpty ? false : true;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(24),
          color: (widget.selectedValues.isEmpty)
              ? Colors.white
              : !isExpanded
                  ? Colors.greenAccent.withOpacity(0.6)
                  : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.labelText,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Icon(isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down),
              ],
            ),
            SizedBox(height: isExpanded ? 16 : 0),
            Visibility(
              visible: isExpanded,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 32,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 8,
                  children: widget.items.map((String item) {
                    var itemNew = item.replaceAll(RegExp(r'\s*\(\d+\)'), '');
                    bool isSelected = widget.selectedValues.contains(itemNew);
                    return FilterChip(
                      label: Text(
                        item,
                        style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                        maxLines: 2,
                      ),
                      showCheckmark: false,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      onSelected: (bool selected) {
                        setState(() {
                          if (selected) {
                            widget.selectedValues.add(itemNew);
                          } else {
                            widget.selectedValues.remove(itemNew);
                          }
                          widget.onChanged(widget.selectedValues);
                        });
                      },
                      selectedColor: Colors.cyan.withOpacity(0.4),
                      backgroundColor: Colors.grey.shade200,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterMultiSelectRequestsDropdown extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;

  const FilterMultiSelectRequestsDropdown({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<FilterMultiSelectRequestsDropdown> createState() =>
      _FilterMultiSelectRequestsDropdownState();
}

class _FilterMultiSelectRequestsDropdownState
    extends State<FilterMultiSelectRequestsDropdown> {
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    isExpanded = widget.selectedValues.isEmpty ? false : true;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(24),
          color: (widget.selectedValues.isEmpty)
              ? Colors.white
              : !isExpanded
                  ? Colors.greenAccent.withOpacity(0.6)
                  : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.labelText,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Icon(isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down),
              ],
            ),
            SizedBox(height: isExpanded ? 16 : 0),
            Visibility(
              visible: isExpanded,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 32,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 8,
                  children: widget.items.map((String item) {
                    var itemNew = item
                        .replaceAll(RegExp(r'\s*\(\d+\)'), '')
                        .toStatusCode();
                    bool isSelected = widget.selectedValues.contains(itemNew);
                    return FilterChip(
                      label: Text(
                        item,
                        style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                        maxLines: 2,
                      ),
                      showCheckmark: false,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      onSelected: (bool selected) {
                        setState(() {
                          if (selected) {
                            widget.selectedValues.add(itemNew);
                          } else {
                            widget.selectedValues.remove(itemNew);
                          }
                          widget.onChanged(widget.selectedValues);
                        });
                      },
                      selectedColor: Colors.cyan.withOpacity(0.4),
                      backgroundColor: Colors.grey.shade200,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterMultiSelectNew extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;

  const FilterMultiSelectNew({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<FilterMultiSelectNew> createState() => _FilterMultiSelectNewState();
}

class _FilterMultiSelectNewState extends State<FilterMultiSelectNew> {
  bool isExpanded = true;

  void _showTextInputDialog(String item) {
    final TextEditingController textController = TextEditingController();
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      color: Colors.black,
    );
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Specify',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          content: TextField(
            controller: textController,
            decoration: const InputDecoration(hintText: 'Enter details'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: style,
              ),
            ),
            TextButton(
              onPressed: () {
                String enteredText = textController.text;
                if (enteredText.isNotEmpty) {
                  setState(() {
                    String newValue = '$item - $enteredText';
                    widget.selectedValues
                        .removeWhere((selected) => selected.startsWith(item));
                    widget.selectedValues.add(newValue);
                    widget.onChanged(widget.selectedValues);
                  });
                }
                Navigator.of(context).pop();
              },
              child: Text(
                'OK',
                style: style,
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(6),
          color: Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.labelText,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Icon(
                  isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                  color: Colors.black,
                ),
              ],
            ),
            SizedBox(height: isExpanded ? 16 : 0),
            Visibility(
              visible: isExpanded,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 32,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 8,
                  children: widget.items.map((String item) {
                    bool isSelected = widget.selectedValues
                        .any((selected) => selected.startsWith(item));
                    String displayValue = isSelected
                        ? widget.selectedValues
                            .firstWhere((selected) => selected.startsWith(item))
                        : item;

                    return FilterChip(
                      label: Text(
                        displayValue,
                        style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                      ),
                      showCheckmark: false,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      onSelected: (bool selected) {
                        setState(() {
                          if (selected) {
                            if (item.toLowerCase().contains("specify")) {
                              _showTextInputDialog(item);
                            } else {
                              widget.selectedValues.add(item);
                            }
                          } else {
                            widget.selectedValues.removeWhere(
                                (selected) => selected.startsWith(item));
                          }
                          widget.onChanged(widget.selectedValues);
                        });
                      },
                      selectedColor: Colors.cyan.withOpacity(0.4),
                      backgroundColor: Colors.grey.shade200,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterMultiSelectNewSteel extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;
  final OptionGroup? optionGroup;

  const FilterMultiSelectNewSteel({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
    required this.optionGroup,
  }) : super(key: key);

  @override
  State<FilterMultiSelectNewSteel> createState() =>
      _FilterMultiSelectNewSteelState();
}

class _FilterMultiSelectNewSteelState extends State<FilterMultiSelectNewSteel> {
  bool isExpanded = true;
  String? selectedUnit;

  void _showTextInputDialog(String item) {
    final TextEditingController textController = TextEditingController();
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      color: Colors.black,
    );
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Specify',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          content: TextField(
            controller: textController,
            decoration: const InputDecoration(hintText: 'Enter details'),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: style,
              ),
            ),
            TextButton(
              onPressed: () {
                String enteredText = textController.text;
                if (enteredText.isNotEmpty) {
                  setState(() {
                    String newValue = '$item - $enteredText';
                    widget.selectedValues
                        .removeWhere((selected) => selected.startsWith(item));
                    widget.selectedValues.add(newValue);
                    widget.onChanged(widget.selectedValues);
                  });
                }
                Navigator.of(context).pop();
              },
              child: Text(
                'OK',
                style: style,
              ),
            ),
          ],
        );
      },
    );
  }

  void _showQuantityDialog(String item) {
    selectedUnit = null;
    final TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        var style =
            const TextStyle(fontWeight: FontWeight.bold, color: Colors.black);
        return AlertDialog(
          title: Text(
            'Enter Quantity for $item',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(hintText: 'Quantity'),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                hint: const Text("Select Unit"),
                value: selectedUnit,
                onChanged: (String? newValue) {
                  setState(() {
                    selectedUnit = newValue;
                  });
                },
                items: widget.optionGroup?.options
                    ?.map<DropdownMenuItem<String>>((unit) {
                  return DropdownMenuItem<String>(
                    value: unit.name,
                    child: Text(unit.name ?? ""),
                  );
                }).toList(),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Cancel', style: style),
            ),
            TextButton(
              onPressed: () {
                if (selectedUnit == null) {
                  alert("Please select unit");
                  return;
                }
                String quantity = quantityController.text;
                if (quantity.isNotEmpty && selectedUnit != null) {
                  setState(() {
                    String newValue = '$item - $quantity $selectedUnit';
                    if (!widget.selectedValues
                        .any((selected) => selected.startsWith(item))) {
                      widget.selectedValues.add(newValue);
                    } else {
                      widget.selectedValues
                          .removeWhere((selected) => selected.startsWith(item));
                      widget.selectedValues.add(newValue);
                    }
                    widget.onChanged(widget.selectedValues);
                  });
                }
                Navigator.of(context).pop();
              },
              child: Text('OK', style: style),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(6),
          color: (widget.selectedValues.isEmpty) ? Colors.white : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.labelText,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                Icon(
                  isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                  color: Colors.black,
                ),
              ],
            ),
            SizedBox(height: isExpanded ? 16 : 0),
            Visibility(
              visible: isExpanded,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 32,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 8,
                  children: widget.items.map((String item) {
                    bool isSelected = widget.selectedValues
                        .any((selected) => selected.startsWith(item));
                    String displayValue = isSelected
                        ? widget.selectedValues
                            .firstWhere((selected) => selected.startsWith(item))
                        : item;

                    return FilterChip(
                      label: Text(
                        displayValue,
                        style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                      ),
                      showCheckmark: false,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      onSelected: (bool selected) {
                        if (selected) {
                          if (item.toLowerCase().contains("specify")) {
                            _showTextInputDialog(item);
                          } else {
                            _showQuantityDialog(item);
                          }
                        } else {
                          setState(() {
                            widget.selectedValues.removeWhere(
                                (selected) => selected.startsWith(item));
                            widget.onChanged(widget.selectedValues);
                          });
                        }
                      },
                      selectedColor: Colors.cyan.withOpacity(0.4),
                      backgroundColor: Colors.grey.shade200,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterMultiSelectAssign extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;

  const FilterMultiSelectAssign({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<FilterMultiSelectAssign> createState() =>
      _FilterMultiSelectAssignState();
}

class _FilterMultiSelectAssignState extends State<FilterMultiSelectAssign> {
  bool isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(6),
          color: (widget.selectedValues.isEmpty)
              ? Colors.white
              : !isExpanded
                  ? Colors.white
                  : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
              // visible: isExpanded,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 32,
                child: Wrap(
                  spacing: 6,
                  runSpacing: 8,
                  children: widget.items.map((String item) {
                    // var itemNew = item.replaceAll(RegExp(r'\s*\(\d+\)'), '');
                    bool isSelected = widget.selectedValues.contains(item);
                    return FilterChip(
                      label: Text(
                        item,
                        style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal),
                        maxLines: 2,
                      ),
                      showCheckmark: false,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      selected: isSelected,
                      onSelected: (bool selected) {
                        setState(() {
                          if (selected) {
                            widget.selectedValues.add(item);
                          } else {
                            widget.selectedValues.remove(item);
                          }
                          widget.onChanged(widget.selectedValues);
                        });
                      },
                      selectedColor: Colors.cyan.withOpacity(0.4),
                      backgroundColor: Colors.grey.shade200,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterValue {
  final String value;
  final String label;

  FilterValue({
    required this.value,
    required this.label,
  });
}
