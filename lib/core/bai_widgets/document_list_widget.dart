import 'package:connectone/old_screens/my_account_screen.dart';
import 'package:flutter/material.dart';
import 'package:connectone/core/utils/colors.dart';

class DocumentListWidgetSeller extends StatefulWidget {
  final Function(bool) onAllChecked;

  const DocumentListWidgetSeller({Key? key, required this.onAllChecked})
      : super(key: key);

  @override
  State<DocumentListWidgetSeller> createState() =>
      _DocumentListWidgetSellerState();
}

class _DocumentListWidgetSellerState extends State<DocumentListWidgetSeller> {
  Map<String, bool> checkboxValues = {
    'Privacy Policy': false,
    'Terms and Conditions': false,
    'Refund & Cancellation Policies': false,
  };

  bool _allChecked = false;

  bool areAllCheckboxesChecked() {
    return checkboxValues.values.every((value) => value);
  }

  void _handleAllCheckedChange(bool? value) {
    setState(() {
      _allChecked = value ?? false;
      checkboxValues.updateAll((key, _) => _allChecked);
      widget.onAllChecked(_allChecked);
    });
  }

  void _handleCheckboxChange(String key, bool? value) {
    setState(() {
      checkboxValues[key] = value!;
      _allChecked = areAllCheckboxesChecked();
      widget.onAllChecked(_allChecked);
    });
  }

  @override

  /// Builds a widget that displays a list of checkboxes with corresponding
  /// labels and a "Read and accept" checkbox.
  ///
  /// The function returns a [Column] widget containing:
  /// - A [Row] with a master [Checkbox] that controls the selection of all
  ///   items and a [Text] label "I have read and accept".
  /// - A [Column] displaying each item in the `checkboxValues` map as a [Text]
  ///   widget with an underline, which is tappable to open a URL associated
  ///   with the item.
  ///
  /// The state of the master checkbox is managed by `_allChecked`, and each
  /// item's checkbox state is stored in the `checkboxValues` map. The function
  /// also handles tap events to launch URLs using the `launchTheUrl` method.
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Row(
            children: [
              Checkbox(
                value: _allChecked,
                activeColor: AppColors.primaryColor,
                onChanged: _handleAllCheckedChange,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              const Text(
                "I have read and accept",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: checkboxValues.keys.map((String key) {
            return Padding(
              padding: const EdgeInsets.only(left: 28),
              child: Row(
                children: <Widget>[
                  // Checkbox(
                  //   value: checkboxValues[key],
                  //   activeColor: AppColors.primaryColor,
                  //   onChanged: (bool? value) {
                  //     _handleCheckboxChange(key, value);
                  //   },
                  //   materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  // ),
                  const SizedBox(width: 34),
                  InkWell(
                    onTap: () {
                      launchTheUrl(getUrlForKey(key));
                    },
                    child: Text(
                      key,
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        decoration: TextDecoration.underline, // Underline text
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String getUrlForKey(String key) {
    switch (key) {
      case 'Privacy Policy':
        return 'https://baistore.cochq.au/privacy-policy';
      case 'Terms and Conditions':
        return 'https://baistore.cochq.au/terms-conditions';
      case 'Refund & Cancellation Policies':
        return 'https://baistore.cochq.au/refund-and-cancellation-policies';
      default:
        return '';
    }
  }
}

class DocumentListWidgetBuyer extends StatefulWidget {
  final Function(bool) onAllChecked;

  const DocumentListWidgetBuyer({Key? key, required this.onAllChecked})
      : super(key: key);

  @override
  State<DocumentListWidgetBuyer> createState() =>
      _DocumentListWidgetBuyerState();
}

class _DocumentListWidgetBuyerState extends State<DocumentListWidgetBuyer> {
  Map<String, bool> checkboxValues = {
    'Privacy Policy': false,
    'Terms and Conditions': false,
    'Refund & Cancellation Policies': false,
  };

  bool _allChecked = false;

  bool areAllCheckboxesChecked() {
    return checkboxValues.values.every((value) => value);
  }

  void _handleAllCheckedChange(bool? value) {
    setState(() {
      _allChecked = value ?? false;
      checkboxValues.updateAll((key, _) => _allChecked);
      widget.onAllChecked(_allChecked);
    });
  }

  void _handleCheckboxChange(String key, bool? value) {
    setState(() {
      checkboxValues[key] = value!;
      _allChecked = areAllCheckboxesChecked();
      widget.onAllChecked(_allChecked);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 2),
          child: Row(
            children: [
              Checkbox(
                value: _allChecked,
                fillColor: const WidgetStatePropertyAll(Colors.white),
                checkColor: AppColors.primaryColor,
                onChanged: _handleAllCheckedChange,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              const Text(
                "I have read and accept",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: checkboxValues.keys.map((String key) {
            return Padding(
              padding: const EdgeInsets.only(left: 8, top: 6),
              child: Row(
                children: <Widget>[
                  // Checkbox(
                  //   value: checkboxValues[key],
                  //   fillColor: const MaterialStatePropertyAll(Colors.white),
                  //   checkColor: AppColors.primaryColor,
                  //   onChanged: (bool? value) {
                  //     _handleCheckboxChange(key, value);
                  //   },
                  //   materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  // ),
                  const SizedBox(width: 34),
                  InkWell(
                    onTap: () {
                      launchTheUrl(getUrlForKey(key));
                    },
                    child: Text(
                      key,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        decoration: TextDecoration.underline, // Underline text
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String getUrlForKey(String key) {
    switch (key) {
      case 'Privacy Policy':
        return 'https://baistore.cochq.au/privacy-policy';
      case 'Terms and Conditions':
        return 'https://baistore.cochq.au/terms-conditions';
      case 'Refund & Cancellation Policies':
        return 'https://baistore.cochq.au/refund-and-cancellation-policies';
      default:
        return '';
    }
  }
}
