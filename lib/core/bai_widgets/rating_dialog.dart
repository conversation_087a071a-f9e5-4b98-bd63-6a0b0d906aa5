import 'package:connectone/bai_blocs/rating/rating_cubit.dart';
import 'package:connectone/bai_models/rating_vendor_list_res.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_models/feedback_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class RatingDialog extends StatefulWidget {
  const RatingDialog({
    Key? key,
    required this.vendorId,
    required this.prchOrdrId,
  }) : super(key: key);

  final num vendorId;
  final num prchOrdrId;

  @override
  State<RatingDialog> createState() => _RatingDialogState();
}

class _RatingDialogState extends State<RatingDialog> {
  var _rating = 0.0;

  final TextEditingController _reviewController = TextEditingController();

  Datum? _selectedVendor;

  @override
  void initState() {
    super.initState();
    context.read<RatingCubit>().getRating(widget.prchOrdrId.toString());
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      insetPadding: const EdgeInsets.all(8),
      child: BlocConsumer<RatingCubit, RatingState>(
        listener: (context, state) {
          if (state is RatingSuccess) {
            alert(state.message);
            Navigator.of(context).pop();
          }
          if (state is RatingLoaded) {
            if (state.vendorListRes.data != null &&
                state.vendorListRes.data!.isNotEmpty) {
              _selectedVendor = state.vendorListRes.data?.first;
              _reviewController.text =
                  state.vendorListRes.data?.first.review ?? "";
              _rating = double.tryParse(_selectedVendor?.rating ?? "0") ?? 0;
            }
          }
        },
        builder: (context, state) {
          if (state is RatingLoading) {
            return const SizedBox(
              height: 200,
              width: 200,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          if (state is RatingLoaded) {
            var vendorList = state.vendorListRes.data;
            return Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    height: 52,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Center(
                      child: Text(
                        'Rating',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ).withCloseButton(() => Navigator.of(context).pop()),
                  const SizedBox(height: 24),
                  RatingBar.builder(
                    initialRating: _rating,
                    minRating: 1,
                    direction: Axis.horizontal,
                    allowHalfRating: false,
                    itemCount: 5,
                    itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                    itemBuilder: (context, _) => const Icon(
                      Icons.star,
                      color: Colors.amber,
                    ),
                    onRatingUpdate: (ratingNew) {
                      _rating = ratingNew;
                    },
                  ),
                  const SizedBox(height: 20),
                  // Dropdown for vendor selection
                  DropdownButtonFormField<Datum>(
                    value: _selectedVendor,
                    isDense: true,
                    items: vendorList
                        ?.map<DropdownMenuItem<Datum>>(
                          (vendor) => DropdownMenuItem<Datum>(
                            value: vendor,
                            child: Text(vendor.vendorName ?? "N/A"),
                          ),
                        )
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedVendor = value;
                        _reviewController.text = value?.review ?? "";
                        _rating = double.tryParse(value?.rating ?? "0") ?? 0;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: "Select Vendor",
                      labelStyle: TextStyle(fontWeight: FontWeight.bold),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Comments",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    maxLines: 4,
                    controller: _reviewController,
                    decoration: InputDecoration(
                      hintText: 'Enter your comments',
                      filled: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                            ),
                            child: const Text(
                              'CLOSE',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: ElevatedButton(
                            onPressed: () async {
                              if (_rating == 0) {
                                alert('Please provide a rating');
                                return;
                              }
                              if (_reviewController.text.isEmpty) {
                                alert('Please add a comment');
                                return;
                              }
                              if (_selectedVendor == null) {
                                alert('Please select a vendor');
                                return;
                              }
                              FeedbackData feedbackData = FeedbackData(
                                customerId: int.parse(getCustomerId()),
                                rating: _rating,
                                vendorId: int.tryParse(
                                  _selectedVendor?.vendorId?.toString() ?? '',
                                ),
                                prchOrdrId:
                                    int.tryParse(widget.prchOrdrId.toString()),
                                comment: _reviewController.text,
                              );
                              context
                                  .read<RatingCubit>()
                                  .rateVendor(feedbackData);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryColor,
                            ),
                            child: const Text(
                              'SUBMIT',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }
          return Container();
        },
      ),
    );
  }
}
