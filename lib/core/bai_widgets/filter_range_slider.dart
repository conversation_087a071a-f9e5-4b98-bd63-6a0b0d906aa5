import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';

class FilterRangeSlider extends StatefulWidget {
  final String? filterDisplayName;
  final RangeValues rangeValues;
  final double minValue;
  final double maxValue;
  final ValueChanged<RangeValues> onChangeEnd;
  final ValueChanged<RangeValues> onChanged;

  const FilterRangeSlider({
    Key? key,
    required this.filterDisplayName,
    required this.rangeValues,
    required this.minValue,
    required this.maxValue,
    required this.onChangeEnd,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<FilterRangeSlider> createState() => _FilterRangeSliderState();
}

class _FilterRangeSliderState extends State<FilterRangeSlider> {
  bool isExpanded = false;

  @override

  /// Builds the widget tree for the FilterRangeSlider.
  ///
  /// This widget displays a container that acts as a range slider with
  /// expandable/collapsible functionality. It shows the filter's display name
  /// and the current range values. When tapped, the container toggles its
  /// expanded state, revealing the range slider. The slider allows users to
  /// select a range within the specified minimum and maximum values.
  ///
  /// The container's appearance changes based on whether the range values are
  /// at their default positions or have been adjusted by the user.
  ///
  /// [context] is the BuildContext in which the widget is built.
  Widget build(BuildContext context) {
    var hasRangeValues = widget.rangeValues.start != widget.minValue ||
        widget.rangeValues.end != widget.maxValue;
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: hasRangeValues ? Colors.grey : Colors.grey),
          borderRadius: BorderRadius.circular(24),
          color: (!hasRangeValues)
              ? Colors.white
              : !isExpanded
                  ? Colors.greenAccent.withOpacity(0.6)
                  : Colors.white,
        ),
        padding: EdgeInsets.fromLTRB(16, 12, 16, isExpanded ? 16 : 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${widget.filterDisplayName}" ?? 'Range Filter',
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                if (hasRangeValues)
                  Text(
                    "${widget.rangeValues.start.toStringAsFixed(0)} - ${widget.rangeValues.end.toStringAsFixed(0)}",
                    style: TextStyle(
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                const SizedBox(width: 8),
                Icon(isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down),
              ],
            ),
            SizedBox(height: isExpanded ? 24 : 0),
            Visibility(
              visible: isExpanded,
              child: Column(
                children: [
                  SliderTheme(
                    data: SliderThemeData(
                      overlayShape: SliderComponentShape.noOverlay,
                      activeTrackColor: AppColors.primaryColor,
                      thumbColor: AppColors.primaryColor,
                      showValueIndicator: ShowValueIndicator.always,
                    ),
                    child: RangeSlider(
                      values: widget.rangeValues,
                      min: widget.minValue,
                      max: widget.maxValue,
                      onChanged: widget.onChanged,
                      onChangeEnd: widget.onChangeEnd,
                      labels: RangeLabels(
                          widget.rangeValues.start.toStringAsFixed(0),
                          widget.rangeValues.end.toStringAsFixed(0)),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
