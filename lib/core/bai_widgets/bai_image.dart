import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'dart:io';

import 'package:connectone/core/utils/tools.dart';
import 'package:dio/dio.dart';
import 'package:pdf/pdf.dart';
import 'package:path/path.dart' as p;
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:http/http.dart' as http;

class BaiImage extends StatelessWidget {
  const BaiImage({
    Key? key,
    required this.url,
  }) : super(key: key);

  final String url;

  @override

  /// A widget that displays an image from the given [url].
  ///
  /// The image is displayed in a [CachedNetworkImage] which is a widget that
  /// displays a network image. The image is displayed with a fit of [BoxFit.cover]
  /// so that it covers the entire area of the widget. The widget also displays a
  /// progress indicator while the image is being loaded.
  ///
  /// If the image fails to load, the widget displays an error icon.
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      progressIndicatorBuilder: (context, url, progress) {
        return LayoutBuilder(
          builder: (context, constraints) {
            double margin = constraints.maxWidth * 0.1;
            return Center(
              child: Container(
                margin: EdgeInsets.all(margin),
                child: CircularProgressIndicator(
                  strokeWidth: 1,
                  value: progress.progress,
                ),
              ),
            );
          },
        );
      },
      errorWidget: (context, url, error) => const Icon(Icons.error),
    );
  }
}

class ImageDialog extends StatefulWidget {
  final String imageUrl;

  const ImageDialog({Key? key, required this.imageUrl}) : super(key: key);

  @override
  State<ImageDialog> createState() => _ImageDialogState();
}

class _ImageDialogState extends State<ImageDialog> {
  Future<void> _downloadImage() async {
    try {
      Dio dio = Dio();
      Directory? downloadsDir = await getExternalStorageDirectory();
      String savePath =
          p.join(downloadsDir!.path, '${DateTime.now().toUtc()}.jpg');
      await dio.download(widget.imageUrl, savePath);
      alert('Image downloaded successfully!');
    } catch (e) {
      alert('Failed to download image: $e');
    }
  }

  Future<void> _shareImage() async {
    try {
      final ByteData byteData =
          await NetworkAssetBundle(Uri.parse(widget.imageUrl)).load("");
      final Uint8List imageBytes = byteData.buffer.asUint8List();
      final tempDir = await getTemporaryDirectory();
      final file =
          await File('${tempDir.path}/image.png').writeAsBytes(imageBytes);
      final XFile xFile = XFile(file.path);
      Share.shareXFiles([xFile], text: 'Check out this image!');
    } catch (error) {
      alert("Failed to share image: $error");
    }
  }

  Future<void> _printImage() async {
    try {
      final response = await http.get(Uri.parse(widget.imageUrl));
      if (response.statusCode == 200) {
        final Uint8List imageBytes = response.bodyBytes;
        final pdf = pw.Document();
        final image = pw.MemoryImage(imageBytes);
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(child: pw.Image(image));
            },
          ),
        );
        await Printing.layoutPdf(onLayout: (PdfPageFormat format) async {
          return pdf.save();
        });
      } else {
        throw Exception('Failed to load the image from the URL');
      }
    } catch (e) {
      alert('Error occurred while printing: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(""),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printImage,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadImage,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareImage,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(widget.imageUrl),
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void showImageDialog(BuildContext context, String? imageUrl) {
  if (imageUrl == null || imageUrl.isEmpty) {
    return;
  }
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return ImageDialog(imageUrl: imageUrl);
    },
  );
}
