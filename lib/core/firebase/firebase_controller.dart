import 'package:connectone/old_models/firebase_response.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:firebase_database/firebase_database.dart';

class FB {
  // Method to get offline Firebase stream
  Stream<FirebaseResponseOffline> getOfflineFbStream(
      String currentItem) async* {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/offline_auction')
        .child(currentItem);

    // Listen to changes in the database reference
    await for (DatabaseEvent event in dbReference.onValue) {
      final data = event.snapshot;
      FirebaseResponseOffline firebaseResponse =
          FirebaseResponseOffline.fromSnapshot(data);
      yield firebaseResponse;
    }
  }
}
