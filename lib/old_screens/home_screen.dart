// import 'package:connectone/controllers/network_controller.dart';
// import 'package:connectone/models/get_categories_response.dart';
// import 'package:connectone/utils/app_routes.dart';
// import 'package:connectone/utils/colors.dart';
// import 'package:connectone/utils/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:get/get.dart';
//
// import '../blocs/home/<USER>';
// import '../components/home_screen_card.dart';
// import '../utils/circular_progress.dart';
// import '../utils/navigate_safe.dart';
// import 'new_home.dart';
//
// class MyHomePage extends StatelessWidget {
//   const MyHomePage({
//     Key? key,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return MultiBlocProvider(
//       providers: [
//         BlocProvider(
//           create: (context) => HomeBloc()
//             ..add(InitializeHome(
//                 Category(data: [], status: 0, statusDescription: ''))),
//         ),
//       ],
//       child: Scaffold(
//         appBar: AppBar(
//           backgroundColor: AppColors.connectOneBlue,
//           title: const Text(appTitle),
//           actions: <Widget>[
//             IconButton(
//               icon: Image.asset(
//                 "assets/images/icon_info_home_screen.png",
//                 width: 24.0,
//                 height: 24.0,
//               ),
//               onPressed: () {
//                 Get.toNamed(AppRoutes.helpScreen, arguments: ["HOME"]);
//               },
//             )
//           ],
//         ),
//         body: BlocBuilder<HomeBloc, HomeState>(
//           builder: (context, state) {
//             if (state is HomeLoading) {
//               return const Center(child: circularProgressIndicator);
//             }
//             if (state is HomeLoaded) {
//               var categoryData = state.categoryData;
//               return SingleChildScrollView(
//                 physics: const ScrollPhysics(),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Padding(
//                       padding:
//                           const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 16.0),
//                       child: GestureDetector(
//                         onTap: () {
//                           Get.to(const NewHomeScreen());
//                         },
//                         child: const Text(
//                           "Buy Our Products",
//                           style: TextStyle(
//                             fontSize: 20.0,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         ),
//                       ),
//                     ),
//                     ListView.builder(
//                       physics: const NeverScrollableScrollPhysics(
//                           parent: BouncingScrollPhysics()),
//                       shrinkWrap: true,
//                       itemCount: categoryData.data.length,
//                       itemBuilder: (context, index) {
//                         var currentItem = categoryData.data[index];
//                         return GestureDetector(
//                           child: Material(
//                             child: (currentItem.categoryType == "LIVE" ||
//                                         currentItem.categoryType == "BIDD") &&
//                                     currentItem.isAvailable == true
//                                 ? InkWell(
//                                     onTap: () {
//                                       if (currentItem.categoryType == "LIVE") {
//                                         //Get.toNamed(AppRoutes.liveAuctionScreen);
//                                         // navigateSafe(AppRoutes.liveAuctionScreen);
//                                       } else {
//                                         navigateSafe(AppRoutes.offlineFilters);
//                                       }
//                                     },
//                                     child: HomeScreenCardItem(
//                                       imageUrl: currentItem.image,
//                                       title: currentItem.shortServiceName,
//                                       isLive: currentItem.isAvailable,
//                                     ),
//                                   )
//                                 : null,
//                           ),
//                         );
//                       },
//                     ),
//                     const Padding(
//                       padding: EdgeInsets.all(16.0),
//                       child: Text(
//                         "Sell Your Products",
//                         style: TextStyle(
//                             fontSize: 20.0, fontWeight: FontWeight.bold),
//                       ),
//                     ),
//                     ListView.builder(
//                       physics: const NeverScrollableScrollPhysics(
//                           parent: BouncingScrollPhysics()),
//                       shrinkWrap: true,
//                       itemCount: categoryData.data.length,
//                       itemBuilder: (context, index) {
//                         var currentItem = categoryData.data[index];
//                         return GestureDetector(
//                           child: Material(
//                             child: ((currentItem.hcType == "SELL") &&
//                                     currentItem.isAvailable == true &&
//                                     currentItem.name != "Sell Your Products" &&
//                                     currentItem.name != "Live Bidding")
//                                 ? InkWell(
//                                     onTap: () {
//                                       if (currentItem.categoryData != null) {
//                                         Get.toNamed(AppRoutes.sellProductScreen,
//                                             arguments: [
//                                               currentItem.categoryData
//                                             ]);
//                                       } else {
//                                         Get.toNamed(AppRoutes.sellProductScreen,
//                                             arguments: [
//                                               "$additionalUrl/biddingdetails/${currentItem.id}/${currentItem.name}"
//                                             ]);
//                                       }
//                                     },
//                                     child: HomeScreenCardItem(
//                                       imageUrl: currentItem.image,
//                                       title: currentItem.shortServiceName,
//                                       isLive: currentItem.isAvailable,
//                                     ),
//                                   )
//                                 : null,
//                           ),
//                         );
//                       },
//                     )
//                   ],
//                 ),
//               );
//             } else {
//               return const Center(child: circularProgressIndicator);
//             }
//           },
//         ),
//       ),
//     );
//   }
// }
