import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/get_account_response.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../old_blocs/my_account/my_account_bloc.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';

Future<void> launchTheUrl(String url) async {
  try {
    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  } catch (e) {
    safePrint(e);
  }
}

class MyAccount extends StatelessWidget {
  MyAccount({Key? key}) : super(key: key);
  final NetworkController networkController = NetworkController();

  final homeNumberController = TextEditingController();
  final addressLine1Controller = TextEditingController();
  final firstNameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    var privacyPolicy = NetworkController().organisationData?.privacyPolicy;
    var termsAndConditions =
        NetworkController().organisationData?.privacyPolicy;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        title: Text(networkController.organisationData?.organizationName ?? ""),
      ),
      body: BlocProvider(
        create: (context) => MyAccountBloc()
          ..add(InitializeMyAccount(
              account: Account(statusDescription: '', status: 0, data: []))),
        child: BlocConsumer<MyAccountBloc, MyAccountState>(
          listener: (context, state) async {
            if (state is Message) {
              if (state.message == "Success") {
                await NetworkController().deleteToken();
                box.erase();
                Get.to(() => const LoginScreen());
              }
            }
          },
          builder: (context, state) {
            if (state is MyAccountLoaded) {
              var data = state.account.data;
              return SingleChildScrollView(
                child: SizedBox(
                  height: MediaQuery.of(context).size.height - 96,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Spacer(),
                      Icon(
                        Icons.account_circle,
                        size: 96.0,
                        color: AppColors.primaryColor,
                      ),
                      TextField(
                        controller: firstNameController
                          ..text = data?.name ?? "null",
                        style: TextStyle(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 24.0,
                        ),
                        enabled: state.isEditable,
                        textAlign: TextAlign.center,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          hintText: 'User Name',
                        ),
                      ),
                      Icon(
                        Icons.add_location_rounded,
                        color: AppColors.primaryColor,
                        size: 24.0,
                      ),
                      TextField(
                        controller: addressLine1Controller
                          ..text = data?.addressLine1 ?? "null",
                        style: const TextStyle(color: Colors.black),
                        enabled: state.isEditable,
                        textAlign: TextAlign.center,
                        autofocus: true,
                        maxLines: 2,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          hintText: 'Address Line 1',
                        ),
                      ),
                      Icon(
                        Icons.email,
                        color: AppColors.primaryColor,
                        size: 24.0,
                      ),
                      TextField(
                        controller: TextEditingController()
                          ..text = data?.email ?? "null",
                        style: const TextStyle(color: Colors.black),
                        enabled: false,
                        textAlign: TextAlign.center,
                        autofocus: true,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          hintText: 'Email',
                        ),
                      ),
                      Icon(
                        Icons.phone_android,
                        color: AppColors.primaryColor,
                        size: 24.0,
                      ),
                      TextField(
                        controller: homeNumberController
                          ..text = data?.primaryPhone ?? "null",
                        style: const TextStyle(color: Colors.black),
                        enabled: state.isEditable,
                        textAlign: TextAlign.center,
                        autofocus: true,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          hintText: '1234567890',
                        ),
                      ),
                      const Spacer(),
                      SizedBox(
                        width: double.infinity,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(8, 0, 8, 4),
                          child: TextButton(
                            style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                    AppColors.primaryColor)),
                            onPressed: () {
                              // context.read<MyAccountBloc>().add(EditMyAccount(
                              //       isEditable: !state.isEditable,
                              //    website:
                              //     ));
                            },
                            child: Text(
                              state.isEditable == true ? "Update" : "Edit",
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        height: 36,
                        width: double.infinity,
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red.shade800),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: InkWell(
                            onTap: () {
                              showDialog<String>(
                                context: context,
                                builder: (BuildContext context) =>
                                    DeleteAccountWidget(
                                  delete: () async {
                                    try {
                                      Navigator.pop(context);
                                      await NetworkController()
                                          .deleteAccountApi();
                                      await NetworkController().deleteToken();
                                      box.erase();
                                      Get.offAll(const LoginScreen());
                                    } catch (e) {
                                      alert("Could not delete acoount");
                                    }
                                  },
                                ),
                              );
                            },
                            child: Text(
                              'Delete Account',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.red.shade800),
                            ),
                          ),
                        ),
                      ),
                      const Spacer(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              launchTheUrl(
                                  "https://baistore.cochq.au/privacy-policy");
                            },
                            child: Text(
                              'Privacy Policy',
                              style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () {
                              launchTheUrl(
                                  "https://baistore.cochq.au/terms-conditions");
                            },
                            child: Text(
                              'Terms and Conditions',
                              style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () {
                              launchTheUrl(
                                  "https://baistore.cochq.au/refund-and-cancellation-policies");
                            },
                            child: Text(
                              'Refund & Cancellation Policies',
                              style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              );
            } else {
              return Center(
                child: progressIndicator,
              );
            }
          },
        ),
      ),
    );
  }
}

class DeleteAccountWidget extends StatelessWidget {
  DeleteAccountWidget({
    required this.delete,
    Key? key,
  }) : super(key: key);

  Function delete;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Delete Account',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: const Text(
          'This operation will delete your account from the system, and this operation is not reversible. Do you want to continue?'),
      actions: <Widget>[
        TextButton(
          onPressed: () async {
            Navigator.of(context, rootNavigator: true).pop();
          },
          child: Text(
            'No',
            style: TextStyle(
              color: AppColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        TextButton(
          onPressed: () async {
            // context.read<MyAccountBloc>().add(DeleteMyAccount());
            delete();
          },
          child: const Text(
            'Yes',
            style: TextStyle(
              color: AppColors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
