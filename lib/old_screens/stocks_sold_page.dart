import 'package:connectone/old_models/sold_model.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../core/network/network_controller.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/safe_print.dart';

class StocksSoldPage extends StatefulWidget {
  const StocksSoldPage({Key? key}) : super(key: key);

  @override
  State<StocksSoldPage> createState() => _StocksSoldPageState();
}

class _StocksSoldPageState extends State<StocksSoldPage> {
  int _page = 0;
  final int _limit = 10;
  bool _isFirstLoadRunning = false;
  bool _hasNextPage = true;
  bool _isLoadMoreRunning = false;
  final List<Content> _posts = [];
  late ScrollController _controller;
  DateTime nowFrom = DateTime.now();
  DateTime nowTo = DateTime.now();

  @override
  void initState() {
    super.initState();
    _controller = ScrollController()..addListener(_loadMore);
    _firstLoad();
  }

  void _loadMore() async {
    if (_hasNextPage &&
        !_isFirstLoadRunning &&
        !_isLoadMoreRunning &&
        _controller.position.extentAfter < 300) {
      setState(() => _isLoadMoreRunning = true);

      _page += 1;
      try {
        final response = await NetworkController()
            .getSoldTable(getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
        if (response.content.isNotEmpty) {
          setState(() => _posts.addAll(response.content!));
        } else {
          setState(() => _hasNextPage = false);
        }
      } catch (err) {
        if (kDebugMode) {
          safePrint('$err');
          safePrint('Something went wrong!');
        }
      } finally {
        setState(() => _isLoadMoreRunning = false);
      }
    }
  }

  void _firstLoad() async {
    setState(() => _isFirstLoadRunning = true);

    try {
      final response = await NetworkController()
          .getSoldTable(getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
      setState(() => _posts.addAll(response.content!));
    } catch (err) {
      if (kDebugMode) {
        safePrint('$err');
        safePrint('Something went wrong');
      }
    } finally {
      setState(() => _isFirstLoadRunning = false);
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDatePickerMode: DatePickerMode.year,
      initialDate: nowFrom,
      firstDate: DateTime(2000),
      lastDate: DateTime(2023),
    );
    if (picked != null && picked != nowFrom) {
      setState(() {
        nowFrom = picked;
        _posts.clear();
        _firstLoad();
      });
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDatePickerMode: DatePickerMode.year,
      initialDate: nowTo,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != nowTo) {
      setState(() {
        nowTo = picked;
        _posts.clear();
        _firstLoad();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Stocks - Sold'),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 15),
          const Text(
            'Select Date',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 15),
          _buildDateSelectors(context),
          const SizedBox(height: 16),
          Expanded(
            child: _isFirstLoadRunning
                ? Center(child: progressIndicator)
                : _posts.isEmpty
                    ? const Center(child: Text('No data to show here!'))
                    : Column(
                        children: [
                          const Center(
                            child: Text(
                              'My Stocks - Sold',
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(height: 20),
                          _buildPostsList(),
                          if (_isLoadMoreRunning)
                            Center(
                              child: SizedBox(
                                height: 20,
                                width: 20,
                                child: progressIndicator,
                              ),
                            ),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelectors(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildDateSelector('From', nowFrom, () => _selectFromDate(context)),
        const SizedBox(width: 20),
        _buildDateSelector('To', nowTo, () => _selectToDate(context)),
        const SizedBox(width: 15),
        Column(
          children: [
            const SizedBox(height: 20),
            TextButton(
              style:
                  TextButton.styleFrom(backgroundColor: AppColors.primaryColor),
              onPressed: () {
                setState(() {
                  _posts.clear();
                  _firstLoad();
                });
              },
              child: const Text('Go', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateSelector(
      String label, DateTime date, VoidCallback onPressed) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
            side: const BorderSide(color: Colors.black),
          ),
          onPressed: onPressed,
          child: Text(
            DateFormat('dd-MM-yyyy').format(date),
            style: const TextStyle(color: Colors.black),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsList() {
    return Expanded(
      child: ListView.separated(
        controller: _controller,
        separatorBuilder: (context, index) => const Divider(thickness: 2),
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          final post = _posts[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    DateFormat('dd-MM-yyyy')
                        .format(post.createdAt ?? DateTime.now()),
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      Text(
                        '${post.stockId}/\n${post.quantity}',
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      post.buyerName ?? '--',
                      style: const TextStyle(fontSize: 12, color: Colors.red),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      post.totalPurchaseAmount.toString(),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
