import 'package:connectone/old_models/bought_model.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../core/network/network_controller.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/safe_print.dart';

class StocksBoughtPage extends StatefulWidget {
  const StocksBoughtPage({Key? key}) : super(key: key);

  @override
  State<StocksBoughtPage> createState() => _StocksBoughtPageState();
}

class _StocksBoughtPageState extends State<StocksBoughtPage> {
  int _page = 0;
  final int _limit = 10;

  bool _isFirstLoadRunning = false;
  bool _hasNextPage = true;
  bool _isLoadMoreRunning = false;

  final List<Content> _posts = [];

  int len = 0;
  bool st = true;
  DateTime now = DateTime.now();
  DateTime nowFrom = DateTime.now();
  DateTime nowTo = DateTime.now();

  late ScrollController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ScrollController()..addListener(_loadMore);
    _firstLoad();
  }

  void _loadMore() async {
    if (_hasNextPage &&
        !_isFirstLoadRunning &&
        !_isLoadMoreRunning &&
        _controller.position.extentAfter < 300) {
      setState(() {
        _isLoadMoreRunning = true;
      });

      _page += 1;

      try {
        var response = await NetworkController().getBoughtTable(
            getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
        if (response.content.isNotEmpty) {
          setState(() {
            _posts.addAll(response.content!);
          });
        } else {
          setState(() {
            _hasNextPage = false;
          });
        }
      } catch (err) {
        if (kDebugMode) {
          safePrint('$err');
          safePrint('Something went wrong!');
        }
      }

      setState(() {
        _isLoadMoreRunning = false;
      });
    }
  }

  void _firstLoad() async {
    setState(() {
      _isFirstLoadRunning = true;
    });

    try {
      var response = await NetworkController()
          .getBoughtTable(getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
      setState(() {
        _posts.addAll(response.content!);
        len = _posts.length;
      });
    } catch (err) {
      if (kDebugMode) {
        safePrint('$err');
        safePrint('Something went wrong');
      }
    }

    setState(() {
      _isFirstLoadRunning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Stocks - Bought'),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 15),
          const Text(
            'Select Date',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 15),
          _buildDateSelectors(),
          const SizedBox(height: 16),
          if (st) _buildContent() else const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildDateSelectors() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildDateSelector('From', nowFrom, (picked) {
          if (picked != null) {
            setState(() {
              nowFrom = picked;
            });
          }
        }),
        const SizedBox(width: 20),
        _buildDateSelector('To', nowTo, (picked) {
          if (picked != null) {
            setState(() {
              nowTo = picked;
            });
          }
        }),
        const SizedBox(width: 15),
        TextButton(
          style: TextButton.styleFrom(backgroundColor: AppColors.primaryColor),
          onPressed: () {
            setState(() {
              _posts.clear();
              st = true;
              _firstLoad();
            });
          },
          child: const Text(
            'Go',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildDateSelector(
      String label, DateTime date, Function(DateTime?) onDatePicked) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25)),
              side: const BorderSide(color: Colors.black)),
          onPressed: () async {
            setState(() {
              st = false;
            });
            DateTime? picked = await showDatePicker(
              context: context,
              initialDatePickerMode: DatePickerMode.year,
              initialDate: date,
              firstDate: DateTime(2000),
              lastDate: DateTime.now(),
            );
            onDatePicked(picked);
          },
          child: Text(
            DateFormat('dd-MM-yyyy').format(date),
            style: const TextStyle(color: Colors.black),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return SizedBox(
      height: MediaQuery.of(context).size.height / 1.5,
      child: len != 0
          ? Column(
              children: [
                const Center(
                  child: Text(
                    'My Stocks - Bought',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height / 1.75,
                    width: double.infinity,
                    child: Column(
                      children: [
                        _buildTableHeader(),
                        _buildTableBody(),
                        if (_isLoadMoreRunning)
                          Center(
                            child: SizedBox(
                              height: 20,
                              width: 20,
                              child: progressIndicator,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            )
          : Container(
              margin: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text('No data to show here!'),
              ),
            ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10), topRight: Radius.circular(10)),
        color: AppColors.primaryColor,
      ),
      width: double.infinity,
      height: 40,
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            flex: 1,
            child: Center(
              child: Text('Date',
                  style: TextStyle(fontSize: 12, color: Colors.white)),
            ),
          ),
          VerticalDivider(color: Colors.white, width: 10, thickness: 1),
          Expanded(
            flex: 1,
            child: Center(
              child: Text('Stock Id /\nQty',
                  style: TextStyle(fontSize: 12, color: Colors.white),
                  textAlign: TextAlign.center),
            ),
          ),
          VerticalDivider(color: Colors.white, width: 10, thickness: 1),
          Expanded(
            flex: 1,
            child: Center(
              child: Text('Seller Name',
                  style: TextStyle(fontSize: 12, color: Colors.white)),
            ),
          ),
          VerticalDivider(color: Colors.white, width: 10, thickness: 1),
          Expanded(
            flex: 1,
            child: Center(
              child: Text('Total Sales\nAmount',
                  style: TextStyle(fontSize: 12, color: Colors.white),
                  textAlign: TextAlign.center),
            ),
          ),
          VerticalDivider(color: Colors.white, width: 10, thickness: 1),
          Expanded(
            flex: 1,
            child: Center(
              child: Text('Receivable',
                  style: TextStyle(fontSize: 12, color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableBody() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(20.0),
            bottomRight: Radius.circular(20.0)),
        border: Border.all(color: Colors.grey),
      ),
      height: MediaQuery.of(context).size.height / 2.1,
      width: double.infinity,
      child: ListView.separated(
        controller: _controller,
        separatorBuilder: (context, index) => const Divider(thickness: 2),
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      DateFormat('dd-MM-yyyy')
                          .format(_posts[index].createdAt ?? DateTime.now()),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "${_posts[index].stockId} /\n${_posts[index].quantity}",
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      _posts[index].sellerName ?? '--',
                      style: const TextStyle(fontSize: 12, color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      "${_posts[index].totalPurchaseAmount}",
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      "${_posts[index].remainingPurchaseAmount}",
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
