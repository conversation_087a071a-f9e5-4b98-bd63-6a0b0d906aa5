// import 'package:connectone/screens/my_home_sell.dart';
// import 'package:connectone/utils/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// import '../components/new_home_card.dart';
// import '../utils/colors.dart';
// import 'new_home.dart';
//
// class MembersScreen extends StatelessWidget {
//   const MembersScreen({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: AppColors.connectOneBlue,
//         title: Text(appTitle),
//       ),
//       body: Column(
//         children: [
//           NewHomeCard(
//             iconData: Icons.sell_outlined,
//             text: 'Sell Your Products',
//             onTap: () {
//               Get.to(const MyHomeSell());
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
