// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/old_models/category_new.dart';
// import 'package:connectone/old_screens/home_children.dart';
// import 'package:connectone/old_screens/live_auctions_screen.dart';
// import 'package:connectone/old_screens/web_view_ui.dart';
// import 'package:connectone/core/utils/circular_progress.dart';
// import 'package:connectone/core/utils/constants.dart';
// import 'package:connectone/core/utils/tools.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:get/get.dart';

// import '../old_blocs/home/<USER>';
// import '../core/old_widgets/common/new_home_card.dart';
// import '../core/old_widgets/common/show_feedback_dialog.dart';
// import '../core/old_widgets/common/side_drawer.dart';
// import '../core/network/network_controller.dart';
// import '../core/utils/app_routes.dart';

// class NewHomeScreen extends StatefulWidget {
//   const NewHomeScreen({Key? key, required this.color}) : super(key: key);

//   final Color color;

//   @override
//   State<NewHomeScreen> createState() => _NewHomeScreenState();
// }

// class _NewHomeScreenState extends State<NewHomeScreen> {
//   NetworkController networkController = NetworkController();
//   String org = '';

//   @override
//   void initState() {
//     setOrg();
//     super.initState();
//   }

//   Future<void> setOrg() async {
//     await Future.delayed(const Duration(seconds: 2));
//     if (mounted) {
//       setState(() {
//         org = networkController.organisationData?.organizationName ?? "";
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: widget.color,
//         title: Text(org),
//         actions: <Widget>[
//           IconButton(
//             icon: Image.asset(
//               "assets/images/icon_info_home_screen.png",
//               width: 24.0,
//               height: 24.0,
//             ),
//             onPressed: () {
//               Get.toNamed(AppRoutes.helpScreen, arguments: ["HOME"]);
//             },
//           )
//         ],
//         systemOverlayStyle: SystemUiOverlayStyle(
//           statusBarBrightness: Brightness.light,
//           statusBarColor: AppColors.primaryColor,
//         ),
//         elevation: 0,
//       ),
//       body: BlocProvider(
//         create: (context) => HomeBloc()..add(const InitializeHome()),
//         child: BlocConsumer<HomeBloc, HomeState>(
//           listener: (context, state) {
//             // TODO: implement listener
//           },
//           builder: (context, state) {
//             if (state is HomeLoaded) {
//               List<CategoryNew> items = filterAvailableCategories(state.categoryData);
//               print(items[0].name);
//               for (var item in items) {
//                 item.children?.sort((a, b) => a.sequence!.compareTo(b.sequence as num));
//               }
//               items.sort((a, b) => a.sequence!.compareTo(b.sequence as num));
//               return ListView.builder(
//                 itemCount: items.length,
//                 itemBuilder: (context, index) {
//                   var item = items[index];
//                   if (index == items.length - 1) {
//                     return Column(
//                       children: [
//                         NewHomeCard(
//                           imageUrl: item.image ?? "",
//                           text: item.name ?? "",
//                           onTap: () {
//                             if (item.children != null && item.children!.isNotEmpty) {
//                               Get.to(
//                                 () => HomeChildren(
//                                   title: item.name ?? "",
//                                   imageUrl: item.image ?? "",
//                                   children: item.children ?? [],
//                                 ),
//                               );
//                             } else {
//                               if (item.categoryType == "BIDD" || item.categoryType == "BUY" || item.categoryType == "BYCN") {
//                                 NetworkController networkController = NetworkController();
//                                 if (networkController.organisationData?.showOfflineCalendarYn == 'N') {
//                                   if (isLoggedIn()) {
//                                     Get.toNamed(AppRoutes.offlineAuctionScreen);
//                                   } else {
//                                     Get.offAllNamed(AppRoutes.loginScreen);
//                                   }
//                                 } else {
//                                   Get.toNamed(AppRoutes.offlineFilters, arguments: [item.categoryFilter]);
//                                 }
//                                 return;
//                               }
//                               if (item.categoryType == "LIVE") {
//                                 Get.to(() => const LiveAuctions());
//                                 return;
//                               }
//                               if (item.categoryType == "SELL" || item.categoryType == "HC" || item.categoryType == "SLCN") {
//                                 // if (item.publicInsertPageLink == null) {
//                                 //   alert(
//                                 //       "Please configure the page from admin!");
//                                 //   return;
//                                 // }
//                                 // launchInAppBrowser(
//                                 //     item.publicInsertPageLink ?? additionalUrl);
//                                 // return;
//                                 Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                                 if (item.publicInsertPageLink == null) {
//                                   alert("Please configure the page from admin");
//                                   return;
//                                 }
//                                 Get.to(() => WebViewCover(url: item.publicInsertPageLink ?? additionalUrl));
//                                 return;
//                               }
//                               if (item.categoryType == "RDRT") {
//                                 if (item.categoryData == null) {
//                                   alert("Please configure the page from admin!");
//                                   return;
//                                 }
//                                 // launchInAppBrowser(item.categoryData ?? additionalUrl);
//                                 Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                                 return;
//                               }
//                               if (item.categoryType == "RQST") {
//                                 // launchInAppBrowser(item.categoryData ?? additionalUrl);
//                                 Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                                 return;
//                               }
//                             }
//                           },
//                         ),
//                         NewHomeCard(
//                           text: "Send Feedback",
//                           onTap: () {
//                             if (isLoggedIn()) {
//                               showDialog(
//                                 context: context,
//                                 builder: (BuildContext context) {
//                                   return const ShowFeedbackDialogue(
//                                     title: "Thank you",
//                                     stockId: '-1',
//                                   );
//                                 },
//                               );
//                             } else {
//                               Get.offAllNamed(AppRoutes.loginScreen);
//                             }
//                           },
//                           imageUrl:
//                               'https://images.unsplash.com/photo-1613963931023-5dc59437c8a6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1469&q=80',
//                         ),
//                       ],
//                     );
//                   }
//                   return NewHomeCard(
//                     imageUrl: item.image ?? "",
//                     text: item.name ?? "",
//                     onTap: () {
//                       if (item.children != null && item.children!.isNotEmpty) {
//                         Get.to(
//                           () => HomeChildren(
//                             title: item.name ?? "",
//                             imageUrl: item.image ?? "",
//                             children: item.children ?? [],
//                           ),
//                         );
//                       } else {
//                         if (item.categoryType == "BIDD" || item.categoryType == "BUY" || item.categoryType == "BYCN") {
//                           NetworkController networkController = NetworkController();
//                           if (networkController.organisationData?.showOfflineCalendarYn == 'N') {
//                             if (isLoggedIn()) {
//                               Get.toNamed(AppRoutes.offlineAuctionScreen);
//                             } else {
//                               Get.offAllNamed(AppRoutes.loginScreen);
//                             }
//                           } else {
//                             Get.toNamed(AppRoutes.offlineFilters, arguments: [item.categoryFilter]);
//                           }
//                           return;
//                         }
//                         if (item.categoryType == "LIVE") {
//                           Get.to(() => const LiveAuctions());
//                           return;
//                         }
//                         if (item.categoryType == "SELL" || item.categoryType == "HC" || item.categoryType == "SLCN") {
//                           if (item.publicInsertPageLink == null) {
//                             alert("Please configure the page from admin");
//                             return;
//                           }
//                           Get.to(() => WebViewCover(url: item.publicInsertPageLink ?? additionalUrl));
//                           return;
//                         }
//                         if (item.categoryType == "RDRT") {
//                           if (item.categoryData == null) {
//                             alert("Please configure the page from admin");
//                             return;
//                           }
//                           // launchInAppBrowser(item.categoryData ?? "");
//                           Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                           // Get.toNamed(AppRoutes.sellProductScreen,
//                           //     arguments: [item.categoryData]);
//                           return;
//                         }
//                       }
//                     },
//                   );
//                 },
//               );
//             } else {
//               return Center(child: circularProgressIndicator);
//             }
//           },
//         ),
//       ),
//       drawer: SideDrawer(
//         userName: userName,
//       ),
//     );
//   }

//   List<CategoryNew> filterAvailableCategories(List<CategoryNew> categories) {
//     List<CategoryNew> filteredCategories = [];

//     for (var category in categories) {
//       if (category.isAvailable == true) {
//         filteredCategories.add(category);
//       }
//       if (category.children != null && category.children!.isNotEmpty) {
//         category.children = filterAvailableCategories(category.children!);
//       }
//     }

//     return filteredCategories;
//   }
// }
