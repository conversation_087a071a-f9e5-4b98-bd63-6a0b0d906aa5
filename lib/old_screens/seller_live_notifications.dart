import 'dart:async';

import 'package:connectone/core/old_widgets/common/auction_closed_popup.dart';
import 'package:connectone/core/old_widgets/common/decrease_control_price_popup.dart';
import 'package:connectone/core/old_widgets/common/withdraw_popup.dart';
import 'package:connectone/old_models/firebase_response.dart';
import 'package:connectone/old_models/notifs_based_on_stock.dart'
    as notifs_based_on_stock;
import 'package:connectone/core/old_widgets/tables/app_table.dart';
import 'package:connectone/core/utils/dialog_utils.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';

import '../old_blocs/seller_live_notifs/seller_live_notifs_bloc.dart';
import '../core/network/network_controller.dart';
import '../old_models/highest_bid_after_close.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/constants.dart';
import '../core/utils/table_utils.dart';
import '../core/utils/time_utils.dart';
import '../old_models/notifications_list_res.dart';
import 'offline_screen.dart';

class SellerLiveNotifications extends StatefulWidget {
  const SellerLiveNotifications({Key? key}) : super(key: key);

  @override
  State<SellerLiveNotifications> createState() =>
      _SellerLiveNotificationsState();
}

class _SellerLiveNotificationsState extends State<SellerLiveNotifications> {
  @override
  Widget build(BuildContext context) {
    Datum notificationSelected = Get.arguments;
    var stockId = notificationSelected.id;
    late bool isLive;
    var notificationCode = notificationSelected.notificationCode;
    if (notificationSelected.notificationCode == "OFBR" ||
        notificationSelected.notificationCode == "OFSD" ||
        notificationSelected.notificationCode == "OFUD" ||
        notificationSelected.notificationCode == "DCCP" ||
        notificationSelected.notificationCode == "NGCP") {
      isLive = false;
    } else {
      isLive = true;
    }
    var color = Colors.black;
    if (notificationCode == "OFUD" ||
        notificationCode == "DCCP" ||
        notificationCode == "NGCP") {
      color = AppColors.red;
    }
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: isLive
            ? const Text("Seller Live Notifications")
            : const Text("Seller Notifications"),
      ),
      body: BlocProvider(
        create: (context) =>
            SellerLiveNotifsBloc()..add(Load(stockId: stockId ?? 0)),
        child: BlocBuilder<SellerLiveNotifsBloc, SellerLiveNotifsState>(
          builder: (context, state) {
            if (state is Loaded) {
              if (notificationCode == "LAST" || notificationCode == "LASS") {
                context
                    .read<SellerLiveNotifsBloc>()
                    .add(SellerWatchingNotifs(stockId: stockId!));
              }

              return Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      children: [
                        const SizedBox(height: 8),
                        Text(
                          notificationSelected.messageTitle!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: color,
                            fontSize: 18,
                          ),
                        ),
                        Text(
                          notificationSelected.message!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Visibility(
                          visible: !isLive &&
                              (notificationCode == "OFUD" ||
                                  notificationCode == "DCCP" ||
                                  notificationCode == "NGCP"),
                          child: Row(
                            children: [
                              Expanded(
                                child: SizedBox(
                                  height: 40,
                                  child: MaterialButton(
                                    /// withdraw
                                    onPressed: () async {
                                      var withdrawActiveYN =
                                          await getHighestBidAfterClose(
                                              state.notifs.data[0].stockId);
                                      if (withdrawActiveYN) {
                                        alert(
                                            "Can not withdraw - Bid amount exceeds expected price. Contact admin for more details");
                                      } else {
                                        final bloc = context
                                            .read<SellerLiveNotifsBloc>();
                                        showDialog(
                                            context: context,
                                            builder: (BuildContext cardBc) {
                                              return BlocProvider<
                                                  SellerLiveNotifsBloc>.value(
                                                value: bloc,
                                                child: Withdraw(
                                                  title: "Withdraw",
                                                  state: state,
                                                ),
                                              );
                                            });
                                      }
                                    },
                                    color: AppColors.green,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                    child: const Text("Withdraw",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                            color: Colors.white)),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: SizedBox(
                                  height: 40,
                                  child: MaterialButton(
                                    //control price
                                    onPressed: () {
                                      final bloc =
                                          context.read<SellerLiveNotifsBloc>();
                                      showDialog(
                                          context: context,
                                          builder: (BuildContext cardBc) {
                                            return BlocProvider<
                                                SellerLiveNotifsBloc>.value(
                                              value: bloc,
                                              child: DecControlPrice(
                                                title:
                                                    "Decrease Control Price Lot No. ${state.stock.data.lotNo}",
                                                state: state,
                                              ),
                                            );
                                          });
                                    },
                                    color: Colors.indigo,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                    child: const Text("Decrease Control Price",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                            color: Colors.white)),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: SellerLiveNotificationsItem(
                        state: state,
                        isLive: isLive,
                        stockId: stockId!,
                        notificationCode:
                            notificationSelected.notificationCode.toString(),
                      ),
                    ),
                  ),
                ],
              );
            } else if (state is Error) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(state.error),
                ),
              );
            } else {
              return Center(
                child: progressIndicator,
              );
            }
          },
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  Future<bool> getHighestBidAfterClose(int stockId) async {
    HighestBidAfterClose response =
        await NetworkController().getHighestBidAfterClose(stockId);
    return response.data.withDrawActiveYN == "Y";
  }
}

class SellerLiveNotificationsItem extends StatefulWidget {
  const SellerLiveNotificationsItem({
    required this.state,
    required this.isLive,
    required this.stockId,
    required this.notificationCode,
    Key? key,
  }) : super(key: key);

  final Loaded state;
  final bool isLive;
  final int stockId;
  final String notificationCode;

  @override
  State<SellerLiveNotificationsItem> createState() =>
      _SellerLiveNotificationsItemState();
}

class _SellerLiveNotificationsItemState
    extends State<SellerLiveNotificationsItem> {
  var soldStatus = "";
  var liveAuctionStatus = "";
  var statusText = "";
  var countdown = 0;
  var fbStockId = 0;
  var liveBiddingLotNo = -1;
  int? bidId;
  final List<FirebaseResponseSellerNotifs> list = [];

  var notificationCode = "";

  StreamSubscription? streamSubscription1;
  StreamSubscription? streamSubscription2;
  StreamSubscription? streamSubscription3;
  StreamSubscription? streamSubscription4;
  StreamSubscription? streamSubscription5;
  StreamSubscription? streamSubscription6;
  StreamSubscription? streamSubscription7;

  Timer? timer;
  late int remaining = 0;

  Future<bool> getHighestBidAfterClose(int stockId) async {
    HighestBidAfterClose? response =
        await NetworkController().getHighestBidAfterClose(stockId);
    bidId = response?.data.bidId;
    return response?.data.withDrawActiveYN == "Y";
  }

  void startTimer() {
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (remaining == 0) {
          setState(() {
            timer.cancel();
            statusText = "Time Exceeded";
            context.read<SellerLiveNotifsBloc>().add(const Lock(true));
          });
        } else {
          if (remaining > 0) {
            setState(() {
              remaining--;
              statusText = "Countdown: ${remaining.toMMSS()}";
            });
          } else {
            setState(() {
              statusText = "";
            });
          }
        }
      },
    );
  }

  void firebaseDataListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/seller_bids');
    streamSubscription1 = dbReference.onValue.listen((DatabaseEvent event) {
      setState(() {
        final data = event.snapshot.child(widget.stockId.toString());
        if (data.exists) {
          for (var i = 0; i < data.children.length; i++) {
            list.add(FirebaseResponseSellerNotifs.fromSnapshot(
                data.children.elementAt(i)));
          }
          list.sort((a, b) {
            return b.bid_id?.compareTo(a.bid_id ?? 0) ?? 0;
          });
        }
      });
    });
  }

  void soldStatusListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_seller_sold_bool');
    streamSubscription2 = dbReference.onValue.listen((DatabaseEvent event) {
      final data = event.snapshot.value;
      setState(() {
        if (data.toString() == "0") {
          soldStatus = data.toString();
        } else if (data.toString() == "1") {
          soldStatus = data.toString();
        }
        auctionStatusLogic();
      });
    });
  }

  void liveBiddingStockId() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_stockid');
    streamSubscription6 = dbReference.onValue.listen((DatabaseEvent event) {
      if (event.snapshot.exists) {
        final data = event.snapshot.value;
        if (mounted) {
          setState(() {
            fbStockId = int.parse(data.toString());
            auctionStatusLogic();
          });
        }
      }
    });
  }

  void liveBiddingLotNoListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_stock_lot_no');
    streamSubscription6 = dbReference.onValue.listen((DatabaseEvent event) {
      if (event.snapshot.exists) {
        final data = event.snapshot.value;
        setState(() {
          liveBiddingLotNo = int.parse(data.toString());
          auctionStatusLogic();
        });
      }
    });
  }

  void auctionStatusListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/live_auction_status');
    streamSubscription3 = dbReference.onValue.listen((DatabaseEvent event) {
      setState(() {
        final data = event.snapshot.value;
        liveAuctionStatus = data.toString();
        auctionStatusLogic();
      });
    });
  }

  Future<void> auctionStatusLogic() async {
    var data = liveAuctionStatus;
    if (widget.stockId == fbStockId &&
        (notificationCode == "LAST" || notificationCode == "LASS")) {
      if (data.toString() == "2") {
        /// closed
        setState(() {
          statusText = "Closed";
          remaining = 0;
          context.read<SellerLiveNotifsBloc>().add(const Lock(false));
        });
        final bloc = context.read<SellerLiveNotifsBloc>();
        showDialog(
            context: context,
            builder: (BuildContext cardBc) {
              return BlocProvider<SellerLiveNotifsBloc>.value(
                value: bloc,
                child: AuctionClosed(
                  title: "Auction Closed",
                  state: widget.state,
                  countdown: countdown,
                ),
              );
            });
      } else if (data.toString() == "0") {
        /// open
        setState(() {
          statusText = "Open";
          context.read<SellerLiveNotifsBloc>().add(const Lock(false));
        });
      } else if (data.toString() == "3" && soldStatus == "0") {
        /// sold by admin
        setState(() {
          statusText = "Your Lot is Sold";
          remaining = 0;
          context.read<SellerLiveNotifsBloc>().add(const Lock(true));
        });
        showSoldDialog(context);
      } else if (data.toString() == "4" && soldStatus == "0") {
        /// unsold by admin
        setState(() {
          statusText = "Your Lot is Unsold";
          remaining = 0;
          context.read<SellerLiveNotifsBloc>().add(const Lock(true));
        });
        showUnSoldDialog(context);
      }
    } else {
      if (notificationCode == "LASS" && liveBiddingLotNo != -1) {
        setState(() {
          statusText =
              "Selling of Lot $liveBiddingLotNo is in progress. Selling of Lot ${widget.state.stock.data.lotNo} will be starting soon.";
          context.read<SellerLiveNotifsBloc>().add(const Lock(true));
        });
      } else {
        statusText = "";
      }
    }
  }

  void countdownListener() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/finalbid');
    streamSubscription4 = dbReference.onValue.listen((DatabaseEvent event) {
      ///************************************///
      Map<dynamic, dynamic> map = event.snapshot.value as Map;
      var seconds = map["seconds"] as int;
      DateTime? parsedTimestamp;
      if (map.containsKey('timestamp')) {
        var timestamp = map["timestamp"] as String;
        parsedTimestamp =
            DateFormat("yyyy-MM-dd HH:mm:ss").parse(timestamp, true).toLocal();
      }
      var newSeconds = seconds;
      if (parsedTimestamp != null) {
        newSeconds -= calculateSecondsBehind(timestamp: parsedTimestamp);
      }
      if (seconds.toString() != "0") {
        if ((notificationCode == "LAST" || notificationCode == "LASS") &&
            fbStockId == widget.stockId) {
          setState(() {
            remaining = newSeconds;
            statusText = "Countdown : ${newSeconds.toMMSS()}";
            timer?.cancel();
            startTimer();
          });
        }
      }
    });
  }

  void decisionCountdown() {
    DatabaseReference dbReference = FirebaseDatabase.instance.ref(
        '$firebaseBaseUrl/live_auction/1/livebidding_seller_decision_countdown');
    streamSubscription5 = dbReference.onValue.listen((DatabaseEvent event) {
      final data = event.snapshot.value;
      if (data != 0) {
        setState(() {
          countdown = int.parse(data.toString());
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    notificationCode = widget.notificationCode;
    liveBiddingStockId();
    liveBiddingLotNoListener();
    soldStatusListener();
    firebaseDataListener();
    countdownListener();
    decisionCountdown();
    auctionStatusListener();
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription1?.cancel();
    streamSubscription2?.cancel();
    streamSubscription3?.cancel();
    streamSubscription4?.cancel();
    streamSubscription5?.cancel();
    streamSubscription6?.cancel();
    streamSubscription7?.cancel();
    timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    var currentItem = widget.state.notifs.data;
    return SingleChildScrollView(
      child: Column(
        children: [
          SingleChildScrollView(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              foregroundDecoration: RotatedCornerDecoration.withColor(
                badgeSize: const Size(40, 40),
                badgeCornerRadius: const Radius.circular(8),
                badgePosition: BadgePosition.topStart,
                color: AppColors.primaryColor,
                textSpan: null,
              ),
              child: Card(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(12.0),
                  ),
                  side: BorderSide(
                    color: AppColors.green,
                    width: 2.0,
                  ),
                ),
                elevation: 4.0,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SizedBox(
                            height: 160.0,
                            child: Row(
                              children: [
                                SellerLiveRow1(state: widget.state),
                                const SizedBox(width: 16.0),
                                const SellerLiveRow2(),
                                const SellerLiveRow3(),
                                SellerLiveRow4(state: widget.state)
                              ],
                            ),
                          ),
                        ),
                        //level 2
                        const SizedBox(height: 16.0),
                        Visibility(
                          visible: statusText.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              statusText,
                              style: const TextStyle(
                                color: AppColors.maroon,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),

                        /// Sell or withdraw green bar
                        GestureDetector(
                          onTap: () async {
                            var withdrawActiveYN =
                                await getHighestBidAfterClose(
                                    widget.state.notifs.data[0].stockId);
                            // print(state.stock.toJson());
                            if (withdrawActiveYN) {
                              alert(
                                  "Can not withdraw - Bid amount exceeds expected price. Contact admin for more details.");
                            } else {
                              if (!widget.state.isLocked) {
                                final bloc =
                                    context.read<SellerLiveNotifsBloc>();
                                showDialog(
                                    context: context,
                                    builder: (BuildContext cardBc) {
                                      return BlocProvider<
                                          SellerLiveNotifsBloc>.value(
                                        value: bloc,
                                        child: AuctionClosed(
                                          title: "Auction Closed",
                                          state: widget.state,
                                          countdown: countdown,
                                        ),
                                      );
                                    });
                              }
                            }
                          },
                          child: Visibility(
                            visible: widget.isLive,
                            child: Container(
                              height: 32,
                              width: double.infinity,
                              margin: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: AppColors.green),
                              child: const Center(
                                child: Text(
                                  "Sell / Withdraw",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ),

                        /// Live Bid Table
                        BidTable(widget: widget, list: list),

                        /// Bid History Table
                        BidHistoryTable(widget: widget),

                        /// Time Activity Table
                        TimeActivityTable(currentItem: currentItem),

                        /// Table
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 8,
                            left: 8,
                            right: 8,
                          ),
                          child: widget.state.stock.data.product.tableType() ==
                                  "CARDAMOM"
                              ? AppTable(
                                  stockId: widget.stockId.toString(),
                                  tableType: widget.state.stock.data.product
                                      .tableType(),
                                )
                              : const SizedBox(),
                        ),
                        // const SizedBox(height: 0),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TimeActivityTable extends StatelessWidget {
  const TimeActivityTable({
    Key? key,
    required this.currentItem,
  }) : super(key: key);

  final List<notifs_based_on_stock.Datum> currentItem;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.green),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12.0),
                    topRight: Radius.circular(12.0)),
                color: AppColors.green),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text("Time",
                    style: TextStyle(
                        color: AppColors.white, fontWeight: FontWeight.bold)),
                Text("Activity",
                    style: TextStyle(
                        color: AppColors.white, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(
                  parent: BouncingScrollPhysics()),
              padding: EdgeInsets.zero,
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: currentItem.length,
              itemBuilder: (BuildContext context, int index) {
                if (index == currentItem.length - 1) {
                  return Table(
                    columnWidths: const {
                      0: FlexColumnWidth(1),
                      1: FlexColumnWidth(2),
                    },
                    border: TableBorder.all(
                        color: AppColors.green,
                        borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))),
                    children: [
                      TableRow(children: [
                        TableContentCommon(
                            content: dateToNewDate(
                                currentItem[index].createdAt.toIso8601String()),
                            isBold: true),
                        TableContentCommon(
                            content: currentItem[index].messageTitle,
                            isBold: true),
                      ]),
                    ],
                  );
                } else {
                  return Table(
                    columnWidths: const {
                      0: FlexColumnWidth(1),
                      1: FlexColumnWidth(2),
                    },
                    border: TableBorder.all(color: AppColors.green),
                    children: [
                      TableRow(children: [
                        TableContentCommon(
                            content: dateToNewDate(
                                currentItem[index].createdAt.toIso8601String()),
                            isBold: true),
                        TableContentCommon(
                            content: currentItem[index].messageTitle,
                            isBold: true),
                      ]),
                    ],
                  );
                }
              }),
        ],
      ),
    );
  }
}

class BidHistoryTable extends StatelessWidget {
  const BidHistoryTable({
    Key? key,
    required this.widget,
  }) : super(key: key);

  final SellerLiveNotificationsItem widget;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.green),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12.0),
                    topRight: Radius.circular(12.0)),
                color: AppColors.green),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    "Bid History No",
                    style: TextStyle(
                        color: AppColors.white, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    "Time",
                    style: TextStyle(
                        color: AppColors.white, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    "Amount",
                    style: TextStyle(
                        color: AppColors.white, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(
                  parent: BouncingScrollPhysics()),
              padding: EdgeInsets.zero,
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemCount: widget.state.bidlist.data.length,
              itemBuilder: (BuildContext context, int index) {
                var bidlist = widget.state.bidlist.data;
                if (index == bidlist.length - 1) {
                  return Table(
                    border: TableBorder.all(
                        color: AppColors.green,
                        borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))),
                    children: [
                      TableRow(children: [
                        TableContentCommon(
                            content: bidlist[index].lotNo.toString(),
                            isBold: true),
                        TableContentCommon(
                            content: timeToNewTime(bidlist[index].time ?? ""),
                            isBold: true),
                        TableContentCommon(
                            content: bidlist[index].price.toString(),
                            isBold: true),
                      ]),
                    ],
                  );
                } else {
                  return Table(
                    border: TableBorder.all(color: AppColors.green),
                    children: [
                      TableRow(children: [
                        TableContentCommon(
                            content: bidlist[index].lotNo.toString(),
                            isBold: true),
                        TableContentCommon(
                            content: timeToNewTime(bidlist[index].time ?? ""),
                            isBold: true),
                        TableContentCommon(
                            content: bidlist[index].price.toString(),
                            isBold: true),
                      ]),
                    ],
                  );
                }
              }),
        ],
      ),
    );
  }
}

class BidTable extends StatelessWidget {
  const BidTable({
    Key? key,
    required this.widget,
    required this.list,
  }) : super(key: key);

  final SellerLiveNotificationsItem widget;
  final List<FirebaseResponseSellerNotifs> list;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isLive,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                  border: Border.all(color: AppColors.green),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12.0),
                      topRight: Radius.circular(12.0)),
                  color: AppColors.green),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text("Bid No",
                      style: TextStyle(
                          color: AppColors.white, fontWeight: FontWeight.bold)),
                  Text("Time",
                      style: TextStyle(
                          color: AppColors.white, fontWeight: FontWeight.bold)),
                  Text("Amount",
                      style: TextStyle(
                          color: AppColors.white, fontWeight: FontWeight.bold)),
                ],
              ),
            ),
            ListView.builder(
                physics: const NeverScrollableScrollPhysics(
                    parent: BouncingScrollPhysics()),
                padding: EdgeInsets.zero,
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                itemCount: list.length,
                itemBuilder: (BuildContext context, int index) {
                  if (index == list.length - 1) {
                    return Table(
                      border: TableBorder.all(
                          color: AppColors.green,
                          borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8))),
                      children: [
                        TableRow(children: [
                          TableContentCommon(
                              content: list[index].bid_id.toString(),
                              isBold: true),
                          TableContentCommon(
                              content: timeToNewTimeFirebase(
                                  list[index].bid_insert_timestamp ?? ''),
                              isBold: true),
                          TableContentCommon(
                              content: list[index].price.toString(),
                              isBold: true),
                        ]),
                      ],
                    );
                  } else {
                    return Table(
                      border: TableBorder.all(color: AppColors.green),
                      children: [
                        TableRow(children: [
                          TableContentCommon(
                              content: list[index].bid_id.toString(),
                              isBold: true),
                          TableContentCommon(
                              content: timeToNewTimeFirebase(
                                  list[index].bid_insert_timestamp ?? ''),
                              isBold: true),
                          TableContentCommon(
                              content: list[index].price.toString(),
                              isBold: true),
                        ]),
                      ],
                    );
                  }
                }),
          ],
        ),
      ),
    );
  }
}

class SellerLiveRow4 extends StatelessWidget {
  const SellerLiveRow4({Key? key, required this.state}) : super(key: key);

  final Loaded state;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 5,
      child: Column(
        children: [
          ProductValues(state.stock.data.product, false),
          ProductValues(state.stock.data.vendorName, false),
          ProductValues(state.stock.data.quantity.toString(), false),
          ProductValues(
              "${state.stock.data.deliveryDate.year.toString().padLeft(4, '0')}-${state.stock.data.deliveryDate.month.toString().padLeft(2, '0')}-${state.stock.data.deliveryDate.day.toString().padLeft(2, '0')}",
              false),
          ProductValues(state.stock.data.pickupCity, false),
          ProductValues(
              state.stock.data.expectedPrice!.toStringAsFixed(2) ?? "", false),
        ],
      ),
    );
  }
}

class SellerLiveRow3 extends StatelessWidget {
  const SellerLiveRow3({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Expanded(
      flex: 1,
      child: Column(
        children: [
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
          Expanded(child: Center(child: Text(":"))),
        ],
      ),
    );
  }
}

class SellerLiveRow2 extends StatelessWidget {
  const SellerLiveRow2({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Expanded(
      flex: 4,
      child: Column(
        children: [
          ProductKeys("Product"),
          ProductKeys("Seller"),
          ProductKeys("Quantity"),
          ProductKeys("Delivery"),
          ProductKeys("Location"),
          ProductKeys("Control"),
        ],
      ),
    );
  }
}

class SellerLiveRow1 extends StatelessWidget {
  const SellerLiveRow1({Key? key, required this.state}) : super(key: key);

  final Loaded state;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 5,
      child: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                Card(
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                  ),
                  elevation: 2.0,
                  child: Center(
                    child: Image.asset(
                      'assets/images/no_image_available.jpeg',
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  left: 0,
                  child: IconButton(
                    icon: Icon(Icons.favorite, color: Colors.yellow.shade800),
                    onPressed: () {},
                  ),
                )
              ],
            ),
          ),
          const SizedBox(
            height: 8.0,
          ),
          Text(
            state.stock.data.id.toString(),
            style: const TextStyle(fontSize: 12.0, fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 8.0,
          ),
          Text(
            "Lot No ${state.stock.data.lotNo.toString()}",
            style: const TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
          )
        ],
      ),
    );
  }
}

class ProductValues1SellerLive extends StatelessWidget {
  final String productValue;
  final bool isGreen;

  const ProductValues1SellerLive(this.productValue, this.isGreen, {Key? key})
      : super(key: key);

  //const ProductValues(this.productValue, this.isGreen);

  @override
  Widget build(BuildContext context) {
    var color = Colors.black;
    if (isGreen) {
      color = AppColors.green;
    }
    return Expanded(
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          productValue,
          style: TextStyle(fontWeight: FontWeight.bold, color: color),
        ),
      ),
    );
  }
}

class ProductKeys1SellerLive extends StatelessWidget {
  final String productKey;

  const ProductKeys1SellerLive(this.productKey, {Key? key}) : super(key: key);

  //const ProductKeys(this.productKey);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 16.0),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            productKey,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }
}
