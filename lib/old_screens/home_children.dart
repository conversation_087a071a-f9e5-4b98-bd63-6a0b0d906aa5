// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/old_screens/web_view_ui.dart';
// import 'package:connectone/core/utils/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import '../core/old_widgets/common/new_home_card.dart';
// import '../core/network/network_controller.dart';
// import '../old_models/category_new.dart';
// import '../core/utils/app_routes.dart';
// import '../core/utils/tools.dart';
// import 'live_auctions_screen.dart';

// class HomeChildren extends StatelessWidget {
//   const HomeChildren({
//     Key? key,
//     required this.title,
//     required this.children,
//     required this.imageUrl,
//   }) : super(key: key);
//   final String title;
//   final List<CategoryNew> children;
//   final String imageUrl;

//   @override
//   Widget build(BuildContext context) {
//     List<CategoryNew> items = children.where((element) => element.isAvailable == true).toList();
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(title),
//         backgroundColor: AppColors.primaryColor,
//         elevation: 0,
//       ),
//       body: items.isNotEmpty
//           ? ListView.builder(
//               itemCount: items.length,
//               itemBuilder: (context, index) {
//                 var item = items[index];
//                 return NewHomeCard(
//                   imageUrl: item.image ?? "",
//                   text: item.name ?? "",
//                   onTap: () {
//                     print("haha ${item.categoryType}");
//                     if (item.children != null && item.children!.isNotEmpty) {
//                       Get.to(
//                         () => HomeChildren(
//                           title: item.name ?? "",
//                           imageUrl: item.image ?? "",
//                           children: item.children ?? [],
//                         ),
//                         preventDuplicates: false,
//                       );
//                     } else {
//                       if (item.categoryType == "BIDD" || item.categoryType == "BYCN" || item.categoryType == "BUY") {
//                         NetworkController networkController = NetworkController();
//                         if (networkController.organisationData?.showOfflineCalendarYn == 'N') {
//                           if (isLoggedIn()) {
//                             Get.toNamed(AppRoutes.offlineAuctionScreen);
//                           } else {
//                             Get.offAllNamed(AppRoutes.loginScreen);
//                           }
//                         } else {
//                           Get.toNamed(AppRoutes.offlineFilters, arguments: [item.categoryFilter]);
//                         }
//                         return;
//                       }
//                       if (item.categoryType == "LIVE") {
//                         if (isLoggedIn()) {
//                           Get.to(() => const LiveAuctions());
//                         } else {
//                           Get.offAllNamed(AppRoutes.loginScreen);
//                         }
//                         return;
//                       }
//                       if (item.categoryType == "SELL" || item.categoryType == "HC" || item.categoryType == "SLCN") {
//                         // if (item.publicInsertPageLink == null) {
//                         //   alert("Please configure the page from admin!");
//                         //   return;
//                         // }
//                         // launchInAppBrowser(
//                         //     item.publicInsertPageLink ?? additionalUrl);
//                         // return;
//                         if (item.publicInsertPageLink == null) {
//                           alert("Please configure the page from admin");
//                           return;
//                         }
//                         Get.to(() => WebViewCover(url: item.publicInsertPageLink ?? additionalUrl));
//                         return;
//                       }
//                       if (item.categoryType == "RDRT") {
//                         if (item.categoryData == null) {
//                           alert("Please configure the page from admin!");
//                           return;
//                         }
//                         // launchInAppBrowser(item.categoryData ?? additionalUrl);
//                         Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                         return;
//                       }
//                       if (item.categoryType == "RQST") {
//                         Get.to(() => WebViewCover(url: item.categoryData ?? additionalUrl));
//                         // launchInAppBrowser(item.categoryData ?? additionalUrl);
//                         return;
//                       }
//                     }
//                   },
//                 );
//               },
//             )
//           : const Center(
//               child: Text("Item is not available, please contact admin."),
//             ),
//     );
//   }
// }
