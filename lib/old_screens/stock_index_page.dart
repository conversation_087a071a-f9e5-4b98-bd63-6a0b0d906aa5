import 'package:connectone/old_screens/stocks_bought_page.dart' as mm;
import 'package:connectone/old_screens/stocks_sold_page.dart';
import 'package:flutter/material.dart';

import '../core/utils/colors.dart';

class StockIndexButton extends StatefulWidget {
  const StockIndexButton({Key? key}) : super(key: key);

  @override
  State<StockIndexButton> createState() => _StockIndexButtonState();
}

class _StockIndexButtonState extends State<StockIndexButton> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trading'),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            _buildButton(
              context,
              'My Stocks Sold',
              const StocksSoldPage(),
            ),
            const SizedBox(height: 16),
            _buildButton(
              context,
              'My Stocks Bought',
              const mm.StocksBoughtPage(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context, String text, Widget page) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        fixedSize: Size(MediaQuery.of(context).size.width, 60),
      ),
      onPressed: () => Navigator.of(context)
          .push(MaterialPageRoute(builder: (index) => page)),
      child: Text(
        text,
        style: const TextStyle(
          color: AppColors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
