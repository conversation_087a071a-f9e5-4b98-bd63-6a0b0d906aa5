import 'dart:async';
import 'dart:ui';

import 'package:badges/badges.dart' as badge;
import 'package:connectone/bai_screens/single_mr_screen.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/old_models/notifications_list_res.dart';
import 'package:connectone/core/utils/notifications_manager.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';
import '../old_blocs/notifications/notifications_bloc.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  late StreamSubscription streamSubscription;

  @override
  void initState() {
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "This is your notifications counter.\n\nIt shows the number of unread notifications you have. \n\nBelow you can see your notification details.\n\nEach notification shows the MR number, message content, and when it was received. \n\nTap on any notification to view its full details.\n\nUnread notifications are highlighted in green.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NotificationsBloc(NetworkController())
        ..add(InitializeNotifications(
            NotificationsListRes(statusDescription: '', status: 0, data: []))),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: AppColors.primaryColor,
          title: const Text("Your Notifications"),
          actions: <Widget>[
            // IconButton(
            //   onPressed: () {
            //     showTutorial();
            //   },
            //   icon: const Icon(Icons.help_center),
            // ),
            InfoHelp(
              onTap: () {
                showTutorial();
              },
            ),
            BlocBuilder<NotificationsBloc, NotificationsState>(
              builder: (context, state) {
                if (state is NotificationsLoaded) {
                  var list = state.notifications.data;
                  return Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: badge.Badge(
                      key: key1,
                      badgeContent: Text(
                        NotificationsManager().getCount(list).toString(),
                        style:
                            const TextStyle(color: Colors.white, fontSize: 8.0),
                      ),
                      position: badge.BadgePosition.topEnd(top: 4, end: 4),
                      child: IconButton(
                        icon: const Icon(Icons.notifications),
                        onPressed: () {},
                      ),
                    ),
                  );
                } else {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text("Loading..."),
                    ),
                  );
                }
              },
            ),
          ],
        ),
        body: BlocBuilder<NotificationsBloc, NotificationsState>(
            builder: (context, state) {
          if (state is NotificationsLoaded) {
            ///listens notifications to auto refresh list
            streamSubscription = FirebaseMessaging.onMessage
                .listen((RemoteMessage message) async {
              context.read<NotificationsBloc>().add(
                    InitializeNotifications(
                      NotificationsListRes(
                        statusDescription: '',
                        status: 0,
                        data: [],
                      ),
                    ),
                  );
            });
            return state.notifications.data?.length != 0
                ? ListView.builder(
                    key: key2,
                    physics: const BouncingScrollPhysics(),
                    itemCount: state.notifications.data?.length,
                    itemBuilder: (BuildContext context, int index) {
                      var currentItem = state.notifications.data![index];
                      return InkWell(
                        key: index == 0 ? key3 : null,
                        child: GestureDetector(
                          onTap: () async {
                            await NetworkController().readNotification(
                              currentItem.id?.toInt() ?? 0,
                            );
                            var notificationSelected = currentItem;
                            NotificationData notificationData =
                                NotificationData(
                              id: notificationSelected.prchOrdrId.toString(),
                              subject: notificationSelected.messageTitle ?? "",
                              content: notificationSelected.message ?? "",
                              event: notificationSelected.messageTitle ?? "",
                              code: notificationSelected.notificationCode ?? "",
                            );
                            Get.to(SingleMRScreen(
                                    notificationData: notificationData))
                                ?.then((value) {
                              context.read<NotificationsBloc>().add(
                                    InitializeNotifications(
                                      NotificationsListRes(
                                        statusDescription: '',
                                        status: 0,
                                        data: [],
                                      ),
                                    ),
                                  );
                            });
                          },
                          child: NotificationItem(
                            currentItem.mrNo ?? "N/A",
                            currentItem.message.toString(),
                            currentItem.createdAt ?? DateTime.now(),
                            currentItem,
                          ),
                        ),
                      );
                    },
                  )
                : Container(
                    child: const Center(
                      child: Text(
                        "No notifications found",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
          }
          return Center(child: progressIndicator);
        }),
      ),
    );
  }
}

class NotificationItem extends StatelessWidget {
  final String lotNo;
  final String message;
  final DateTime date;
  final Datum currentItem;

  const NotificationItem(this.lotNo, this.message, this.date, this.currentItem,
      {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: currentItem.readYn == 'Y' ? Colors.white : Colors.green.shade100,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(8.0),
        ),
      ),
      elevation: 4.0,
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Container(
              height: 60.0,
              width: 60.0,
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                borderRadius: const BorderRadius.all(
                  Radius.circular(8.0),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(2),
                    child: Text(
                      lotNo.toString(),
                      style: const TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.center,
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(width: 16.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(message,
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8.0),
                  Text(
                    date.toCreatedOn(),
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 10.0,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                ],
              ),
            ),
            // GestureDetector(
            //   onTap: () {},
            //   child: const Row(
            //     children: [
            //       Icon(
            //         Icons.delete,
            //         color: AppColors.darkRed,
            //         size: 16,
            //       ),
            //       Text(
            //         "Delete",
            //         style: TextStyle(
            //           color: AppColors.darkRed,
            //           fontWeight: FontWeight.bold,
            //           fontSize: 10,
            //         ),
            //       )
            //     ],
            //   ),
            // ),
            const SizedBox(width: 8.0)
          ],
        ),
      ),
    );
  }
}
