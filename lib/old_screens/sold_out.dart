// import 'package:connectone/blocs/sold_out/soldout_bloc.dart';
// import 'package:connectone/blocs/sold_out/soldout_event.dart';
// import 'package:connectone/blocs/sold_out/soldout_state.dart';
// import 'package:connectone/core/utils/safe_print.dart';
// import 'package:connectone/core/utils/theme_utils.dart';
// import 'package:connectone/core/widgets/common/card_list_sold_item.dart';
// import 'package:connectone/core/widgets/common/sold_stock_dialog.dart';
// import 'package:connectone/models/total_stock_soldout.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// import '../core/widgets/common/card_view_stocks.dart';
// import '../core/network/network_controller.dart';
// import '../core/utils/circular_progress.dart';
// import '../core/utils/colors.dart';
// import '../core/utils/constants.dart';
// import '../core/utils/tools.dart';

// class SoldOutPage extends StatefulWidget {
//   const SoldOutPage({Key? key}) : super(key: key);

//   @override
//   State<SoldOutPage> createState() => _SoldOutPageState();
// }

// class _SoldOutPageState extends State<SoldOutPage> {
//   var list = TotalStocks1(
//     noOfStocksSold: 0,
//     totalAmountOfSoldStocks: 0,
//     totalQuantitySold: 0,
//   );

//   Dio dio = Dio();
//   late ScrollController scrollController;

//   Future<void> fetch(String customer) async {
//     try {
//       var response = await NetworkController().dio.get('$baseAppUrl/apis/bidding/customer/stock-summary-sold/$customer');
//       var json = response.data;

//       setState(() {
//         list = TotalStocks1.fromJson(json);
//       });
//     } on DioException catch (error) {
//       safePrint('Error fetching data: $error');
//       throw Exception(error.message);
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     scrollController = ScrollController();
//     context.read<SoldOutBloc>().add(LoadSoldStocks());
//     fetch(getCustomerId());
//   }

//   @override
//   Widget build(BuildContext context) {
//     // var format = NumberFormat.simpleCurrency(locale: 'HI');
//     return BlocBuilder<SoldOutBloc, SoldOutState>(
//       builder: (context, state) {
//         return Scaffold(
//           appBar: AppBar(
//             elevation: 0,
//             actions: [
//               IconButton(
//                 onPressed: () {
//                   context.read<SoldOutBloc>().add(LoadSoldStocks());
//                 },
//                 icon: const Icon(Icons.refresh),
//               ),
//             ],
//             backgroundColor: AppColors.primaryColor,
//             title: Text(
//               "Sold Out",
//               style: TextStyle(color: AppColors.white, fontFamily: getTitleFont()),
//             ),
//           ),
//           body: BlocBuilder<SoldOutBloc, SoldOutState>(
//             builder: (context, state) {
//               if (state is SoldOutStockLoaded) {
//                 return NotificationListener<ScrollNotification>(
//                   onNotification: (ScrollNotification notification) {
//                     if (notification is ScrollEndNotification && scrollController.position.extentAfter == 0) {
//                       context.read<SoldOutBloc>().add(LoadNextSoldStocks(state.content));
//                     }
//                     return false;
//                   },
//                   child: Column(
//                     children: [
//                       _buildSummarySection(),
//                       const SizedBox(height: 10),
//                       _buildSummaryCards(),
//                       _buildStockList(state),
//                     ],
//                   ),
//                 );
//               } else {
//                 return Center(child: circularProgressIndicator);
//               }
//             },
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildSummarySection() {
//     return Container(
//       color: Colors.grey.shade300,
//       height: 50,
//       width: MediaQuery.of(context).size.width,
//       child: Row(
//         children: [
//           const Expanded(flex: 1, child: SizedBox()),
//           const Expanded(
//             flex: 2,
//             child: Center(
//               child: Text(
//                 "Summary",
//                 style: TextStyle(
//                   color: Colors.black,
//                   fontWeight: FontWeight.w600,
//                   fontSize: 18,
//                 ),
//               ),
//             ),
//           ),
//           Expanded(
//             flex: 1,
//             child: Center(
//               child: IconButton(
//                 onPressed: () {
//                   showDialog(
//                     context: context,
//                     builder: (BuildContext context) {
//                       return CustomSoldOutDialogBox(title: "Search");
//                     },
//                   );
//                 },
//                 icon: const Icon(
//                   Icons.search,
//                   size: 32,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildSummaryCards() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       child: Row(
//         children: [
//           CardViewMyStocksSoldOut(
//             number: list.noOfStocksSold?.toString() ?? '0',
//             label: totalStocks,
//             suffix: "",
//           ),
//           CardViewMyStocksSoldOut(
//             number: list.totalQuantitySold?.toString() ?? '0',
//             label: totalQty,
//             suffix: "Kg",
//           ),
//           CardViewMyStocksSoldOut(
//             number: list.totalAmountOfSoldStocks?.toString() ?? '0',
//             label: totalAmnt,
//             suffix: "",
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildStockList(SoldOutStockLoaded state) {
//     return Expanded(
//       child: ListView.builder(
//         controller: scrollController,
//         physics: const BouncingScrollPhysics(),
//         itemCount: state.content.length,
//         itemBuilder: (context, index) {
//           final item = state.content[index];
//           return CardListItemSoldOut(
//             sellerId: item.customerId.toString(),
//             index: index,
//             item: item,
//             stockid: item.id.toString(),
//             date: item.deliveryDate.toString(),
//             highestBid: item.highestBidAmount.toString(),
//             gradeText: item.grade ?? "",
//             rating: double.parse(item.averageRating ?? "0"),
//             lotNo: item.lotNo.toString(),
//             locationText: item.pickupCity ?? "",
//             orderId: '',
//             noBidText: '',
//             qtyText: item.quantity.toString(),
//             sellerName: item.vendorName ?? "",
//           );
//         },
//       ),
//     );
//   }
// }

import 'package:connectone/old_blocs/my_stocks/mystocks_bloc.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_events.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_state.dart';
import 'package:connectone/core/old_widgets/common/custom_dialog_mystock.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/margin_history.dart';
import 'package:connectone/old_screens/my_stocks_summary.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../core/old_widgets/common/card_list_item.dart';
import '../old_models/total_stock_soldout.dart';
import '../old_models/total_stock_mystock.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/tools.dart';

class SoldOutPage extends StatefulWidget {
  const SoldOutPage({Key? key}) : super(key: key);

  @override
  State<SoldOutPage> createState() => _SoldOutPageState();
}

class _SoldOutPageState extends State<SoldOutPage> {
  var list = TotalStockSold(
      noOfStocksSold: 0, totalAmountOfSoldStocks: 0, totalQuantitySold: 0);
  var list1 = TotalStocks1(
    noOfStocksSold: 0,
    totalAmountOfSoldStocks: 0,
    totalQuantitySold: 0,
  );
  Dio dio = Dio();

  bool isLoading = false;

  late ScrollController scrollController;

  bool st = true;
  DateTime now = DateTime.now().toUtc();
  DateTime nowFrom = DateTime.now().toUtc().subtract(const Duration(days: 14));
  DateTime nowTo = DateTime.now().toUtc();

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    context.read<MyStocksBloc>().add(
          LoadStocks(
            from: DateFormat('yyyy-MM-dd').format(nowFrom),
            to: DateFormat('yyyy-MM-dd').format(nowTo),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    var format = NumberFormat.simpleCurrency(locale: 'HI');
    return BlocBuilder<MyStocksBloc, MyStocksState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            actions: [
              IconButton(
                onPressed: () {
                  setState(() {
                    now = DateTime.now().toUtc();
                    nowFrom = DateTime.now()
                        .toUtc()
                        .subtract(const Duration(days: 14));
                    nowTo = DateTime.now().toUtc();
                  });
                  context.read<MyStocksBloc>().add(
                        LoadStocks(
                          from: DateFormat('yyyy-MM-dd').format(nowFrom),
                          to: DateFormat('yyyy-MM-dd').format(nowTo),
                        ),
                      );
                },
                icon: const Icon(Icons.refresh),
              ),
            ],
            backgroundColor: AppColors.primaryColor,
            title: const Text(
              "Sold Outs",
              style: TextStyle(color: AppColors.white),
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                BlocConsumer<MyStocksBloc, MyStocksState>(
                  listener: (BuildContext context, MyStocksState state) {},
                  builder: (context, state) {
                    print(state);
                    if (state is MyStocksLoaded) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height,
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification notification) {
                            if (notification is ScrollEndNotification &&
                                scrollController.position.extentAfter == 0) {
                              setState(() {
                                isLoading = true;
                              });
                              context.read<MyStocksBloc>().add(
                                    LoadStocksNextPage(
                                      state.content,
                                      from: DateFormat('yyyy-MM-dd')
                                          .format(nowFrom),
                                      to: DateFormat('yyyy-MM-dd')
                                          .format(nowTo),
                                    ),
                                  );
                            }
                            return false;
                          },
                          child: ListView(
                            controller: scrollController,
                            children: [
                              Container(
                                color: Colors.grey.shade300,
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                child: Row(
                                  children: [
                                    const Expanded(
                                      //2022-05-12,190888
                                      flex: 1,
                                      child: Text(""),
                                    ),
                                    const Expanded(
                                      flex: 2,
                                      child: Center(
                                        child: Text(
                                          "Select Date",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 18),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Center(
                                        child: IconButton(
                                          onPressed: () {
                                            showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return CustomDialogBox(
                                                      title: "Search");
                                                });
                                          },
                                          icon: const Icon(
                                            Icons.search,
                                            size: 32,
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              MyStocksSummary(
                                isSold: true,
                                onGoPressed: (DateTime from, DateTime to) {
                                  setState(() {
                                    nowFrom = from;
                                    nowTo = to;
                                  });
                                  context.read<MyStocksBloc>().add(
                                        LoadStocks(
                                          from: DateFormat('yyyy-MM-dd')
                                              .format(nowFrom),
                                          to: DateFormat('yyyy-MM-dd')
                                              .format(nowTo),
                                        ),
                                      );
                                  print(
                                      "haha ${DateFormat('yyyy-MM-dd').format(from)} ${DateFormat('yyyy-MM-dd').format(to)}");
                                },
                              ),
                              const SizedBox(height: 15),
                              ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: state.content.length,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  if (state.content[index].customerId
                                              .toString() ==
                                          getCustomerId().toString() &&
                                      (state.content[index].buyerCustomerId ??
                                                  0)
                                              .toInt() >
                                          0) {
                                    return CardListItemMyStocks(
                                      sellerId: state.content[index].customerId
                                          .toString(),
                                      index: index,
                                      imageUrl: state.content[index].image1,
                                      item: state.content[index],
                                      stockId:
                                          state.content[index].id.toString(),
                                      date: state.content[index].deliveryDate
                                          .toString(),
                                      highestBid: state
                                          .content[index].highestBidAmount
                                          .toString(),
                                      gradeText: state.content[index].grade!,
                                      rating: double.parse(
                                          state.content[index].averageRating ??
                                              "0"),
                                      currentMargin: "40",
                                      currentLimit: "20",
                                      lotNo:
                                          state.content[index].lotNo.toString(),
                                      locationText:
                                          state.content[index].pickupCity!,
                                      orderId: '',
                                      noBidText: '',
                                      qtyText: state.content[index].quantity
                                          .toString(),
                                      sellerName:
                                          state.content[index].vendorName!,
                                      buyerName:
                                          state.content[index].vendorName!,
                                      isSold: state.content[index].customerId
                                              .toString() ==
                                          getCustomerId(),
                                      isBought: state
                                              .content[index].buyerCustomerId
                                              .toString() ==
                                          getCustomerId(),
                                      fromSold: true,
                                      editExpectedPrice: (String price) async {
                                        if (price.isEmpty) {
                                          return;
                                        }
                                        NetworkController().editExpectedPrice(
                                          expectedPrice: price,
                                          stockId: state.content[index].id
                                                  ?.toInt() ??
                                              0,
                                        );
                                        await Future.delayed(
                                            const Duration(seconds: 2));
                                        context.read<MyStocksBloc>().add(
                                              LoadStocks(
                                                from: DateFormat('yyyy-MM-dd')
                                                    .format(nowFrom),
                                                to: DateFormat('yyyy-MM-dd')
                                                    .format(nowTo),
                                              ),
                                            );
                                      },
                                    );
                                  } else {
                                    return const SizedBox();
                                  }
                                },
                              ),
                              isLoading
                                  ? Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Center(
                                        child: progressIndicator,
                                      ),
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height - 56,
                        child: Center(child: progressIndicator),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class MarginHistory1 extends StatefulWidget {
  const MarginHistory1(this.marginHistory, {Key? key}) : super(key: key);
  final MarginHistory marginHistory;

  @override
  State<MarginHistory1> createState() => _MarginHistory1State();
}

class _MarginHistory1State extends State<MarginHistory1> {
  final DateFormat formatter = DateFormat('dd-MM-yyyy');
  var format = NumberFormat.simpleCurrency(locale: 'HI');

  @override
  Widget build(BuildContext context) {
    var list = widget.marginHistory.data;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        elevation: 0,
        title: const Text("Margin"),
        backgroundColor: AppColors.primaryColor,
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        padding: const EdgeInsets.symmetric(vertical: 24),
        constraints: const BoxConstraints.expand(),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0)),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(2),
                2: FlexColumnWidth(2),
                3: FlexColumnWidth(2),
                4: FlexColumnWidth(2),
              },
              border: TableBorder.all(
                  borderRadius: BorderRadius.circular(12.0),
                  color: AppColors.green),
              children: [
                TableRow(
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12.0),
                          topRight: Radius.circular(12.0)),
                      color: AppColors.primaryColor),
                  children: const [
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Date",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Transaction",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Stock No",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Amount",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Balance",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                for (var item in list!)
                  TableRow(
                    children: [
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            formatter.format(DateTime.parse(item.updatedAt!)),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            item.type!,
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            item.attachedEntityId!.toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            format.format(item.amount!).toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            format.format(item.balance!).toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
