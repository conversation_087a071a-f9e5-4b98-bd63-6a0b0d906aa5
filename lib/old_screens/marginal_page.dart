import 'package:connectone/core/utils/circular_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../old_blocs/my_stocks/mystocks_bloc.dart';
import '../old_blocs/my_stocks/mystocks_events.dart';
import '../old_blocs/my_stocks/mystocks_state.dart';
import '../core/utils/colors.dart';
import '../core/utils/table_utils.dart';
import '../core/utils/tools.dart';
import 'my_stocks_sold.dart';

class MarginalDetails extends StatefulWidget {
  const MarginalDetails({Key? key}) : super(key: key);

  @override
  State<MarginalDetails> createState() => _MarginalDetailsState();
}

class _MarginalDetailsState extends State<MarginalDetails> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<MyStocksBloc>().add(
          LoadStocks(
            from: '1971-01-01',
            to: DateFormat('yyyy-MM-dd').format(DateTime.now()),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    var format = NumberFormat.simpleCurrency(locale: 'HI');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Margin'),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
      ),
      body: BlocBuilder<MyStocksBloc, MyStocksState>(
        builder: (context, state) {
          if (state is MyStocksLoaded) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(8, 16, 8, 0),
              child: Table(
                border: TableBorder.all(
                    borderRadius: BorderRadius.circular(12.0),
                    color: AppColors.green),
                children: [
                  TableRow(
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12.0),
                              topRight: Radius.circular(12.0)),
                          color: AppColors.primaryColor),
                      children: const [
                        TableHeadingCommon("Current Margin"),
                        TableHeadingCommon("Buying limit"),
                        TableHeadingCommon("History"),
                      ]),
                  TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          state.marginDetails != null
                              ? format
                                  .format(state
                                      .marginDetails!.data!.availableMargin)
                                  .toString()
                              : "nil",
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 15),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          state.marginDetails != null
                              ? format
                                  .format(state.marginDetails!.data!
                                      .biddingLimitRemaining)
                                  .toString()
                              : "nil",
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 15),
                        ),
                      ),
                      TextButton(
                        style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            alignment: Alignment.center),
                        onPressed: () {
                          if (state.marginHistory?.data == null) {
                            alert('Margin history not found!');
                            return;
                          }
                          Get.to(() => MarginHistory1(state.marginHistory!));
                          // var errorDialog =
                          //     MarginHistory1(state.marginHistory!);

                          // showDialog(
                          //     context: context,
                          //     builder: (BuildContext context) => errorDialog);
                        },
                        child: const Text(
                          "View",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.bold,
                              fontSize: 15),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            );
          }
          return Center(child: progressIndicator);
        },
      ),
    );
  }
}
