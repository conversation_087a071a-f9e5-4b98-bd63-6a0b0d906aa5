import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../old_blocs/help/help_bloc.dart';
import '../core/network/network_controller.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';

class HelpScreenNew extends StatelessWidget {
  final String? url;
  final String? name;
  final NetworkController networkController = NetworkController();

  HelpScreenNew(
      {Key? key, this.url = 'https://baistore.cochq.au/bai', this.name})
      : super(key: key);

  String getUrl(String passedUrl) {
    if (passedUrl.isNotEmpty) {
      return passedUrl;
    } else {
      return "https://baistore.in/bai";
    }
  }

  @override
  Widget build(BuildContext context) {
    print('jjjjjjjjj0000000');

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        title: Text(name ?? ""),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.close),
            color: Colors.red,
            onPressed: () {
              Get.back();
            },
          )
        ],
      ),
      body: BlocProvider(
        create: (context) => HelpBloc()..add(const LoadHelp()),
        child: BlocBuilder<HelpBloc, HelpState>(
          builder: (context, state) {
            if (state is HelpInitial) {
              print('jjjjjjjjj');
              return Stack(
                children: [
                  WebViewWidget(
                    controller: WebViewController()
                      ..setJavaScriptMode(JavaScriptMode.unrestricted)
                      ..setNavigationDelegate(
                        NavigationDelegate(
                          onPageFinished: (string) {
                            context.read<HelpBloc>().add(const LoadedHelp());
                          },
                        ),
                      )
                      ..loadRequest(Uri.parse(getUrl(url ?? "https://www.google.co.in/"))),
                  ),
                  if (state.isLoading) Center(child: progressIndicator),
                ],
              );
            } else {
              return const Center(
                child: Text("Error occurred!"),
              );
            }
          },
        ),
      ),
    );
  }
}
