import 'package:connectone/old_screens/my_stocks_bought.dart';
import 'package:connectone/old_screens/my_stocks_sold.dart';
import 'package:flutter/material.dart';

import '../core/utils/colors.dart';

class my_stock_index_button extends StatefulWidget {
  const my_stock_index_button({Key? key}) : super(key: key);

  @override
  State<my_stock_index_button> createState() => _stock_index_buttonState();
}

class _stock_index_buttonState extends State<my_stock_index_button> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Stocks'),
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // Row(
            //   children: [
            //     Expanded(
            //       child: Padding(
            //         padding: const EdgeInsets.all(8.0),
            //         child: Container(
            //           padding: EdgeInsets.all(8),
            //           height: MediaQuery.of(context).size.width / 5,
            //           child: Align(
            //             alignment: Alignment.center,
            //             child: Text(
            //               "My Stocks - Sold",
            //               style: TextStyle(
            //                 color: Colors.white,
            //                 fontSize: 16,
            //               ),
            //             ),
            //           ),
            //           decoration: BoxDecoration(
            //             color: AppColors.connectOneBlue,
            //             borderRadius: BorderRadius.circular(12),
            //           ),
            //         ),
            //       ),
            //     ),
            //     Expanded(
            //       child: Padding(
            //         padding: const EdgeInsets.all(8.0),
            //         child: Container(
            //           padding: EdgeInsets.all(8),
            //           height: MediaQuery.of(context).size.width / 5,
            //           child: Align(
            //             alignment: Alignment.center,
            //             child: Text(
            //               "My Stocks - Sold",
            //               style: TextStyle(
            //                 color: Colors.white,
            //                 fontSize: 16,
            //               ),
            //             ),
            //           ),
            //           decoration: BoxDecoration(
            //             color: AppColors.connectOneBlue,
            //             borderRadius: BorderRadius.circular(12),
            //           ),
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
            const SizedBox(
              height: 8,
            ),
            ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    fixedSize: Size(MediaQuery.of(context).size.width, 60)),
                onPressed: (() => Navigator.of(context).push(MaterialPageRoute(
                    builder: (index) => const MyStocksSold()))),
                child: const Text(
                  'My Stocks Sold',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                )),
            const SizedBox(
              height: 16,
            ),
            ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    fixedSize: Size(MediaQuery.of(context).size.width, 60)),
                onPressed: (() => Navigator.of(context).push(MaterialPageRoute(
                    builder: (index) => const MyStocksBought()))),
                child: const Text(
                  'My Stocks Bought',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
