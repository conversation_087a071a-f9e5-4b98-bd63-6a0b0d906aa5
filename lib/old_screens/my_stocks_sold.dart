import 'package:connectone/old_blocs/my_stocks/mystocks_bloc.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_events.dart';
import 'package:connectone/old_blocs/my_stocks/mystocks_state.dart';
import 'package:connectone/core/old_widgets/common/custom_dialog_mystock.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/margin_history.dart';
import 'package:connectone/old_screens/my_stocks_summary.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../core/old_widgets/common/card_list_item.dart';
import '../old_models/total_stock_soldout.dart';
import '../old_models/total_stock_mystock.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/tools.dart';

class MyStocksSold extends StatefulWidget {
  const MyStocksSold({Key? key}) : super(key: key);

  @override
  State<MyStocksSold> createState() => _MyStocksSoldState();
}

class _MyStocksSoldState extends State<MyStocksSold> {
  var list = TotalStockSold(
      noOfStocksSold: 0, totalAmountOfSoldStocks: 0, totalQuantitySold: 0);
  var list1 = TotalStocks1(
    noOfStocksSold: 0,
    totalAmountOfSoldStocks: 0,
    totalQuantitySold: 0,
  );
  Dio dio = Dio();

  bool isLoading = false;

  late ScrollController scrollController;

  bool st = true;
  DateTime now = DateTime.now().toUtc();
  DateTime nowFrom = DateTime.now().toUtc().subtract(const Duration(days: 14));
  DateTime nowTo = DateTime.now().toUtc();

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    context.read<MyStocksBloc>().add(
          LoadStocks(
            from: DateFormat('yyyy-MM-dd').format(nowFrom),
            to: DateFormat('yyyy-MM-dd').format(nowTo),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    var format = NumberFormat.simpleCurrency(locale: 'HI');
    return BlocBuilder<MyStocksBloc, MyStocksState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            actions: [
              IconButton(
                onPressed: () {
                  setState(() {
                    now = DateTime.now().toUtc();
                    nowFrom = DateTime.now()
                        .toUtc()
                        .subtract(const Duration(days: 14));
                    nowTo = DateTime.now().toUtc();
                  });
                  context.read<MyStocksBloc>().add(
                        LoadStocks(
                          from: DateFormat('yyyy-MM-dd').format(nowFrom),
                          to: DateFormat('yyyy-MM-dd').format(nowTo),
                        ),
                      );
                },
                icon: const Icon(Icons.refresh),
              ),
            ],
            backgroundColor: AppColors.primaryColor,
            title: const Text(
              "My Stocks - Sold",
              style: TextStyle(color: AppColors.white),
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                BlocConsumer<MyStocksBloc, MyStocksState>(
                  listener: (BuildContext context, MyStocksState state) {},
                  builder: (context, state) {
                    if (state is MyStocksLoaded) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height,
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification notification) {
                            if (notification is ScrollEndNotification &&
                                scrollController.position.extentAfter == 0) {
                              setState(() {
                                isLoading = true;
                              });
                              context.read<MyStocksBloc>().add(
                                    LoadStocksNextPage(
                                      state.content,
                                      from: DateFormat('yyyy-MM-dd')
                                          .format(nowFrom),
                                      to: DateFormat('yyyy-MM-dd')
                                          .format(nowTo),
                                    ),
                                  );
                            }
                            return false;
                          },
                          child: ListView(
                            controller: scrollController,
                            children: [
                              Container(
                                color: Colors.grey.shade300,
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                child: Row(
                                  children: [
                                    const Expanded(
                                      //2022-05-12,190888
                                      flex: 1,
                                      child: Text(""),
                                    ),
                                    const Expanded(
                                      flex: 2,
                                      child: Center(
                                        child: Text(
                                          "Select Date",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 18),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Center(
                                        child: IconButton(
                                          onPressed: () {
                                            showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return CustomDialogBox(
                                                      title: "Search");
                                                });
                                          },
                                          icon: const Icon(
                                            Icons.search,
                                            size: 32,
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              MyStocksSummary(
                                isSold: true,
                                onGoPressed: (DateTime from, DateTime to) {
                                  setState(() {
                                    nowFrom = from;
                                    nowTo = to;
                                  });
                                  context.read<MyStocksBloc>().add(
                                        LoadStocks(
                                          from: DateFormat('yyyy-MM-dd')
                                              .format(nowFrom),
                                          to: DateFormat('yyyy-MM-dd')
                                              .format(nowTo),
                                        ),
                                      );
                                  print(
                                      "haha ${DateFormat('yyyy-MM-dd').format(from)} ${DateFormat('yyyy-MM-dd').format(to)}");
                                },
                              ),
                              const SizedBox(height: 15),
                              ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: state.content.length,
                                shrinkWrap: true,
                                itemBuilder: (context, index) {
                                  if (state.content[index].customerId
                                          .toString() ==
                                      getCustomerId().toString()) {
                                    return CardListItemMyStocks(
                                      sellerId: state.content[index].customerId
                                          .toString(),
                                      index: index,
                                      imageUrl: state.content[index].image1,
                                      item: state.content[index],
                                      stockId:
                                          state.content[index].id.toString(),
                                      date: state.content[index].deliveryDate
                                          .toString(),
                                      highestBid: state
                                          .content[index].highestBidAmount
                                          .toString(),
                                      gradeText: state.content[index].grade!,
                                      rating: double.parse(
                                          state.content[index].averageRating ??
                                              "0"),
                                      currentMargin: "40",
                                      currentLimit: "20",
                                      lotNo:
                                          state.content[index].lotNo.toString(),
                                      locationText:
                                          state.content[index].pickupCity!,
                                      orderId: '',
                                      noBidText: '',
                                      qtyText: state.content[index].quantity
                                          .toString(),
                                      sellerName:
                                          state.content[index].vendorName!,
                                      buyerName:
                                          state.content[index].vendorName!,
                                      isSold: state.content[index].customerId
                                              .toString() ==
                                          getCustomerId(),
                                      isBought: state
                                              .content[index].buyerCustomerId
                                              .toString() ==
                                          getCustomerId(),
                                      fromSold: true,
                                      editExpectedPrice: (String price) async {
                                        if (price.isEmpty) {
                                          return;
                                        }
                                        NetworkController().editExpectedPrice(
                                          expectedPrice: price,
                                          stockId: state.content[index].id
                                                  ?.toInt() ??
                                              0,
                                        );
                                        await Future.delayed(
                                            const Duration(seconds: 2));
                                        context.read<MyStocksBloc>().add(
                                              LoadStocks(
                                                from: DateFormat('yyyy-MM-dd')
                                                    .format(nowFrom),
                                                to: DateFormat('yyyy-MM-dd')
                                                    .format(nowTo),
                                              ),
                                            );
                                      },
                                    );
                                  } else {
                                    return const SizedBox();
                                  }
                                },
                              ),
                              isLoading
                                  ? Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Center(
                                        child: progressIndicator,
                                      ),
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height - 56,
                        child: Center(child: progressIndicator),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class MarginHistory1 extends StatefulWidget {
  const MarginHistory1(this.marginHistory, {Key? key}) : super(key: key);
  final MarginHistory marginHistory;

  @override
  State<MarginHistory1> createState() => _MarginHistory1State();
}

class _MarginHistory1State extends State<MarginHistory1> {
  final DateFormat formatter = DateFormat('dd-MM-yyyy');
  var format = NumberFormat.simpleCurrency(locale: 'HI');

  @override
  Widget build(BuildContext context) {
    var list = widget.marginHistory.data;

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        elevation: 0,
        title: const Text("Margin"),
        backgroundColor: AppColors.primaryColor,
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        padding: const EdgeInsets.symmetric(vertical: 24),
        constraints: const BoxConstraints.expand(),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0)),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            child: Table(
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(2),
                2: FlexColumnWidth(2),
                3: FlexColumnWidth(2),
                4: FlexColumnWidth(2),
              },
              border: TableBorder.all(
                  borderRadius: BorderRadius.circular(12.0),
                  color: AppColors.green),
              children: [
                TableRow(
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12.0),
                          topRight: Radius.circular(12.0)),
                      color: AppColors.primaryColor),
                  children: const [
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Date",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Transaction",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Stock No",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Amount",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          "Balance",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                for (var item in list!)
                  TableRow(
                    children: [
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            formatter.format(DateTime.parse(item.updatedAt!)),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            item.type!,
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            item.attachedEntityId!.toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            format.format(item.amount!).toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Text(
                            format.format(item.balance!).toString(),
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// class MarginHistory1 extends StatefulWidget {
//   const MarginHistory1(this.marginhistory, {Key? key}) : super(key: key);
//   final MarginHistory marginhistory;

//   @override
//   State<MarginHistory1> createState() => _MarginHistory1State();
// }

// class _MarginHistory1State extends State<MarginHistory1> {
//   int _page = 0;
//   int len = 0;

//   final int _limit = 10;

//   bool _isFirstLoadRunning = false;
//   bool _hasNextPage = true;

//   bool _isLoadMoreRunning = false;

//   final List<MarginHistory> _posts = [];
//   final DateFormat formatter = DateFormat('dd-MM-yyyy');
//   var format = NumberFormat.simpleCurrency(locale: 'HI');
//   void _loadMore() async {
//     if (_hasNextPage == true &&
//         _isFirstLoadRunning == false &&
//         _isLoadMoreRunning == false &&
//         _controller.position.extentAfter < 300) {
//       setState(() {
//         _isLoadMoreRunning = true; // Display a progress indicator at the bottom
//       });

//       _page += 1; // Increase _page by 1
//       // var url =
//       //     "$baseAppUrl/apis/bidding/seller-payout/buyer-with-date-filter/294535?page=0&size=20&from_date=2000-02-20 00:58:17&to_date=2023-02-20 20:58:17";

//       try {
//         List a = _posts;
//         if (a.isNotEmpty) {
//           setState(() {
//             _posts.addAll(a as Iterable<MarginHistory>);
//           });
//         } else {
//           setState(() {
//             _hasNextPage = false;
//           });
//         }
//       } catch (err) {
//         if (kDebugMode) {
//           print('$err//////////////////////////////////111111111111111111');
//           print('Something went wrong!');
//         }
//       }

//       setState(() {
//         _isLoadMoreRunning = false;
//       });
//     }
//   }

//   void _firstLoad() async {
//     setState(() {
//       _isFirstLoadRunning = true;
//     });

//     try {
//       setState(() {
//         _posts.addAll(MarginHistory as Iterable<MarginHistory>);
//         len = _posts.length;
//       });
//     } catch (err) {
//       if (kDebugMode) {
//         print('$err//////////////////////////////////');
//         print('Something went wrong');
//       }
//     }

//     setState(() {
//       _isFirstLoadRunning = false;
//     });
//   }

//   late ScrollController _controller;
//   @override
//   void initState() {
//     super.initState();
//     _firstLoad();
//     _controller = ScrollController()..addListener(_loadMore);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBar(automaticallyImplyLeading: true),
//         body: Column(children: [
//           const SizedBox(
//             height: 20,
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: const [
//               Center(
//                 child: Text(
//                   "Margin History",
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                       color: Colors.black,
//                       fontSize: 20,
//                       fontWeight: FontWeight.bold),
//                 ),
//               )
//             ],
//           ),
//           const SizedBox(
//             height: 25,
//           ),
//           Column(children: [
//             Padding(
//               padding: const EdgeInsets.all(5.0),
//               child: Container(
//                 decoration: const BoxDecoration(
//                     borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(10),
//                         topRight: Radius.circular(10)),
//                     color: AppColors.connectOneBlue),
//                 width: double.infinity,
//                 height: 40,
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                   children: const [
//                     Expanded(
//                       flex: 1,
//                       child: Center(
//                         child: Text('Date',
//                             style: TextStyle(
//                               fontSize: 12,
//                               color: Colors.white,
//                             )),
//                       ),
//                     ),
//                     VerticalDivider(
//                       color: Colors.white, //color of divider
//                       width: 10, //width space of divider
//                       thickness: 1, //thickness of divier line
//                       indent: 10, //Spacing at the top of divider.
//                       endIndent: 2, //Spacing at the bottom of divider.
//                     ),
//                     Expanded(
//                       flex: 1,
//                       child: Text("Transaction",
//                           style: TextStyle(fontSize: 12, color: Colors.white)),
//                     ),
//                     VerticalDivider(
//                       color: Colors.white, //color of divider
//                       width: 10, //width space of divider
//                       thickness: 1, //thickness of divier line
//                       indent: 10, //Spacing at the top of divider.
//                       endIndent: 2, //Spacing at the bottom of divider.
//                     ),
//                     Expanded(
//                       flex: 1,
//                       child: Center(
//                         child: Text("Stock No",
//                             style: TextStyle(
//                               fontSize: 12,
//                               color: Colors.white,
//                             )),
//                       ),
//                     ),
//                     VerticalDivider(
//                       color: Colors.white, //color of divider
//                       width: 10, //width space of divider
//                       thickness: 1, //thickness of divier line
//                       indent: 10, //Spacing at the top of divider.
//                       endIndent: 2, //Spacing at the bottom of divider.
//                     ),
//                     Expanded(
//                       flex: 1,
//                       child: Text('Amount',
//                           style: TextStyle(fontSize: 12, color: Colors.white)),
//                     ),
//                     VerticalDivider(
//                       color: Colors.white, //color of divider
//                       width: 10, //width space of divider
//                       thickness: 1, //thickness of divier line
//                       indent: 10, //Spacing at the top of divider.
//                       endIndent: 2, //Spacing at the bottom of divider.
//                     ),
//                     Expanded(
//                       flex: 1,
//                       child: Text('Balance',
//                           style: TextStyle(fontSize: 12, color: Colors.white)),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: MediaQuery.of(context).size.height / 1.5,
//               child: ListView.builder(
//                   physics: const ScrollPhysics(),
//                   itemBuilder: (BuildContext context, int index) {
//                     return Column(
//                       children: [
//                         Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                             children: [
//                               Center(
//                                   child: Text('${_posts[index].data}',
//                                       style: const TextStyle(fontSize: 12))),
//                               Center(
//                                   child: Text('${_posts[index].data}',
//                                       style: const TextStyle(fontSize: 12))),
//                               Center(
//                                   child: Text('${_posts[index].data}',
//                                       style: const TextStyle(fontSize: 12))),
//                               Center(
//                                   child: Text('${_posts[index].data}',
//                                       style: const TextStyle(fontSize: 12))),
//                               Center(
//                                   child: Text('${_posts[index].data}',
//                                       style: const TextStyle(fontSize: 12))),
//                             ])
//                       ],
//                     );
//                   },
//                   itemCount: _posts.length),
//             ),
//           ]),
//         ]));
//   }
// }

// import 'dart:async';

// import 'package:connectone/blocs/mystocks/mystocks_bloc.dart';
// import 'package:connectone/blocs/mystocks/mystocks_events.dart';
// import 'package:connectone/blocs/mystocks/mystocks_state.dart';
// import 'package:connectone/components/card_view_stocks.dart';
// import 'package:connectone/components/custom_dialog_mystock.dart';
// import 'package:connectone/models/margin_history.dart';
// import 'package:connectone/utils/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:intl/intl.dart';

// import '../components/card_list_item.dart';
// import '../utils/circular_progress.dart';
// import '../utils/colors.dart';
// import '../utils/table_utils.dart';
// import '../utils/tools.dart';

// class MyStocks extends StatefulWidget {
//   const MyStocks({Key? key}) : super(key: key);

//   @override
//   State<MyStocks> createState() => _MyStocksState();
// }

// class _MyStocksState extends State<MyStocks> {
//   final Completer<GoogleMapController> _controller =
//       Completer<GoogleMapController>();
//   static const CameraPosition _kGooglePlex = CameraPosition(
//     target: LatLng(8.5581, 76.8816),
//     zoom: 15.4746,
//   );
//   final List<Marker> _markers = <Marker>[];

//   @override
//   void initState() {
//     super.initState();
//     context.read<MyStocksBloc>().add(LoadStocks());
//   }

//   @override
//   Widget build(BuildContext context) {
//     var format = NumberFormat.simpleCurrency(locale: 'HI');
//     return BlocBuilder<MyStocksBloc, MystocksState>(
//       builder: (context, state) {
//         return Scaffold(
//           appBar: AppBar(
//             actions: [
//               IconButton(
//                   onPressed: (() {
//                     context.read<MyStocksBloc>().add(LoadStocks());
//                   }),
//                   icon: const Icon(Icons.refresh))
//             ],
//             backgroundColor: AppColors.connectOneBlue,
//             title: const Text(
//               "My Stocks",
//               style: TextStyle(color: AppColors.white),
//             ),
//           ),
//           body: BlocBuilder<MyStocksBloc, MystocksState>(
//             builder: (context, state) {
//               if (state is MyStocksLoaded) {
//                 return SizedBox(
//                   height: MediaQuery.of(context).size.height,
//                   child: ListView(
//                       // mainAxisSize: MainAxisSize.min,
//                       //   mainAxisAlignment: MainAxisAlignment.start,
//                       //   crossAxisAlignment: CrossAxisAlignment.center,
//                       children: [
//                         Container(
//                           color: Colors.grey.shade300,
//                           height: 50,
//                           child: Row(
//                             children: [
//                               const Expanded(
//                                 //2022-05-12,190888
//                                 flex: 1,
//                                 child: Text(""),
//                               ),
//                               const Expanded(
//                                 flex: 2,
//                                 child: Center(
//                                   child: Text(
//                                     "My Stocks",
//                                     style: TextStyle(
//                                         color: Colors.black,
//                                         fontWeight: FontWeight.w600,
//                                         fontSize: 18),
//                                   ),
//                                 ),
//                               ),
//                               Expanded(
//                                 flex: 1,
//                                 child: Center(
//                                   child: IconButton(
//                                     onPressed: () {
//                                       showDialog(
//                                           context: context,
//                                           builder: (BuildContext context) {
//                                             return CustomDialogBox(
//                                                 title: "Search");
//                                           });
//                                     },
//                                     icon: const Icon(
//                                       Icons.search,
//                                       size: 32,
//                                     ),
//                                   ),
//                                 ),
//                               )
//                             ],
//                           ),
//                           width: MediaQuery.of(context).size.width,
//                         ),
//                         const SizedBox(height: 10),
//                         Row(
//                           children: [
//                             CardViewMyStocksSoldOut(
//                               number: state.content.length.toString(),
//                               label: totalStocks,
//                               suffix: "",
//                             ),
//                             CardViewMyStocksSoldOut(
//                               number: state.qty.toString(),
//                               label: totalQty,
//                               suffix: "Kg",
//                             ),
//                             CardViewMyStocksSoldOut(
//                               number: format.format(state.amt).toString(),
//                               label: totalAmnt,
//                               suffix: "",
//                             )
//                           ],
//                         ),
//                         const SizedBox(height: 15),
//                         Padding(
//                           padding: const EdgeInsets.fromLTRB(5, 0, 5, 0),
//                           child: Table(
//                             border: TableBorder.all(
//                                 borderRadius: BorderRadius.circular(12.0),
//                                 color: AppColors.green),
//                             children: [
//                               const TableRow(
//                                   decoration: BoxDecoration(
//                                       borderRadius: BorderRadius.only(
//                                           topLeft: Radius.circular(12.0),
//                                           topRight: Radius.circular(12.0)),
//                                       color: AppColors.connectOneBlue),
//                                   children: [
//                                     TableHeadingCommon("Current Margin"),
//                                     TableHeadingCommon("Buying limit"),
//                                     TableHeadingCommon("History"),
//                                   ]),
//                               TableRow(
//                                 children: [
//                                   Padding(
//                                     padding: const EdgeInsets.all(8.0),
//                                     child: Text(
//                                       state.marginDetails != null
//                                           ? format
//                                               .format(state.marginDetails!.data!
//                                                   .availableMargin)
//                                               .toString()
//                                           : "nil",
//                                       textAlign: TextAlign.center,
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold,
//                                           fontSize: 15),
//                                     ),
//                                   ),
//                                   Padding(
//                                     padding: const EdgeInsets.all(8.0),
//                                     child: Text(
//                                       state.marginDetails != null
//                                           ? format
//                                               .format(state.marginDetails!.data!
//                                                   .biddingLimitRemaining)
//                                               .toString()
//                                           : "nil",
//                                       textAlign: TextAlign.center,
//                                       style: const TextStyle(
//                                           fontWeight: FontWeight.bold,
//                                           fontSize: 15),
//                                     ),
//                                   ),
//                                   TextButton(
//                                     style: TextButton.styleFrom(
//                                         padding: EdgeInsets.zero,
//                                         tapTargetSize:
//                                             MaterialTapTargetSize.shrinkWrap,
//                                         alignment: Alignment.center),
//                                     onPressed: () {
//                                       if (state.marginHistory?.data == null) {
//                                         alert('Margin history not found');
//                                         return;
//                                       }
//                                       var errorDialog =
//                                           MarginHistory1(state.marginHistory!);

//                                       showDialog(
//                                           context: context,
//                                           builder: (BuildContext context) =>
//                                               errorDialog);
//                                     },
//                                     child: const Text(
//                                       "View",
//                                       textAlign: TextAlign.center,
//                                       style: TextStyle(
//                                           decoration: TextDecoration.underline,
//                                           fontWeight: FontWeight.bold,
//                                           fontSize: 15),
//                                     ),
//                                   )
//                                 ],
//                               ),
//                             ],
//                           ),
//                         ),
//                         const SizedBox(height: 15),
//                         ListView.builder(
//                           physics: const NeverScrollableScrollPhysics(),
//                           itemCount: state.content.length,
//                           shrinkWrap: true,
//                           itemBuilder: (context, index) {
//                             return CardListItemMyStocks(
//                               sellerId:
//                                   state.content[index].customerId.toString(),
//                               index: index,
//                               imageUrl: state.content[index].image1,
//                               item: state.content[index],
//                               stockid: state.content[index].id.toString(),
//                               date:
//                                   state.content[index].deliveryDate.toString(),
//                               highestBid: state.content[index].highestBidAmount
//                                   .toString(),
//                               gradeText: state.content[index].grade!,
//                               rating: double.parse(
//                                   state.content[index].averageRating ?? "0"),
//                               currentmargin: "40",
//                               currentlimit: "20",
//                               lotNo: state.content[index].lotNo.toString(),
//                               locationText: state.content[index].pickupCity!,
//                               orderId: '',
//                               noBidText: '',
//                               qtyText: state.content[index].quantity.toString(),
//                               sellerName: state.content[index].vendorName!,
//                               isSold:
//                                   state.content[index].customerId.toString() ==
//                                       getCustomerId(),
//                               isBought: state.content[index].buyerCustomerId
//                                       .toString() ==
//                                   getCustomerId(),
//                             );
//                           },
//                         ),
//                       ]),
//                 );
//               } else {
//                 return const Center(child: circularProgressIndicator);
//               }
//             },
//           ),
//         );
//       },
//     );
//   }
// }

// class MarginHistory1 extends StatefulWidget {
//   const MarginHistory1(this.marginhistory, {Key? key}) : super(key: key);
//   final MarginHistory marginhistory;

//   @override
//   State<MarginHistory1> createState() => _MarginHistory1State();
// }

// class _MarginHistory1State extends State<MarginHistory1> {
//   final DateFormat formatter = DateFormat('dd-MM-yyyy');
//   var format = NumberFormat.simpleCurrency(locale: 'HI');

//   @override
//   Widget build(BuildContext context) {
//     var list = widget.marginhistory.data;

//     return Scaffold(
//       appBar: AppBar(automaticallyImplyLeading: true),
//       body: Column(
//         children: [
//           const SizedBox(
//             height: 20,
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: const [
//               Center(
//                 child: Text(
//                   "Margin History",
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                       color: Colors.black,
//                       fontSize: 20,
//                       fontWeight: FontWeight.bold),
//                 ),
//               )
//             ],
//           ),
//           const SizedBox(
//             height: 25,
//           ),
//           SizedBox(
//             height: MediaQuery.of(context).size.height / 1.5,
//             child: SingleChildScrollView(
//               child: Padding(
//                 padding: const EdgeInsets.fromLTRB(10, 0, 10, 20),
//                 child: Table(
//                   columnWidths: const {
//                     0: FlexColumnWidth(1),
//                     1: FlexColumnWidth(2),
//                     2: FlexColumnWidth(2),
//                     3: FlexColumnWidth(2),
//                     4: FlexColumnWidth(2),
//                   },
//                   border: TableBorder.all(
//                       borderRadius: BorderRadius.circular(12.0),
//                       color: AppColors.green),
//                   children: [
//                     const TableRow(
//                         decoration: BoxDecoration(
//                             borderRadius: BorderRadius.only(
//                                 topLeft: Radius.circular(12.0),
//                                 topRight: Radius.circular(12.0)),
//                             color: AppColors.connectOneBlue),
//                         children: [
//                           Center(
//                               child: Text("Date",
//                                   style: TextStyle(
//                                       fontSize: 15, color: Colors.white))),
//                           Center(
//                               child: Text("Transaction",
//                                   style: TextStyle(
//                                       fontSize: 15, color: Colors.white))),
//                           Center(
//                               child: Text("Stock No",
//                                   style: TextStyle(
//                                       fontSize: 15, color: Colors.white))),
//                           Center(
//                               child: Text("Amount",
//                                   style: TextStyle(
//                                       fontSize: 15, color: Colors.white))),
//                           Center(
//                               child: Text("Balance",
//                                   style: TextStyle(
//                                       fontSize: 15, color: Colors.white))),
//                         ]),
//                     for (var item in list!)
//                       TableRow(children: [
//                         Center(
//                             child: Text(
//                                 formatter
//                                     .format(DateTime.parse(item.updatedAt!)),
//                                 style: const TextStyle(fontSize: 12))),
//                         Center(
//                             child: Text(item.type!,
//                                 style: const TextStyle(fontSize: 12))),
//                         Center(
//                             child: Text(item.attachedEntityId!.toString(),
//                                 style: const TextStyle(fontSize: 12))),
//                         Center(
//                             child: Text(format.format(item.amount!).toString(),
//                                 style: const TextStyle(fontSize: 12))),
//                         Center(
//                             child: Text(format.format(item.balance!).toString(),
//                                 style: const TextStyle(fontSize: 12))),
//                       ])
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// class SellerInfoDialogue extends StatelessWidget {
//   const SellerInfoDialogue({
//     Key? key,
//     required List<Marker> markers,
//     required Completer<GoogleMapController> controller,
//     required CameraPosition kGooglePlex,
//     required this.context,
//     required this.marginhistory,
//   }) : _markers = markers, _controller = controller, _kGooglePlex = kGooglePlex, super(key: key);
//
//   final List<Marker> _markers;
//   final Completer<GoogleMapController> _controller;
//   final CameraPosition _kGooglePlex;
//   final BuildContext context;
//   final MarginHistory marginhistory;
//
//   @override
//   Widget build(BuildContext context) {
//
//    return Dialog(
//      insetPadding: EdgeInsets.all(10),
//      backgroundColor: Colors.transparent,
//      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
//      child: (Stack(
//        children: <Widget>[
//          Container(
//            width:335,
//            height:550,
//            padding: const EdgeInsets.only(
//                left: 20,
//                top: 45 +
//                    20,
//                right: 20,
//                bottom: 20),
//            margin:
//            const EdgeInsets.only(top: 45),
//            decoration: BoxDecoration(
//                shape: BoxShape.rectangle,
//                color: Colors.white,
//                borderRadius:
//                BorderRadius.circular(20),
//                boxShadow: const [
//                  BoxShadow(
//                      color: Colors.black,
//                      offset: Offset(0, 10),
//                      blurRadius: 10),
//                ]),
//            child: SingleChildScrollView(
//              child: Column(
//                mainAxisSize: MainAxisSize.min,
//                children: <Widget>[
//                  Text(
//                    "Seller Info User 3",
//                    style: const TextStyle(
//                        color: Colors.black,
//                        fontSize: 18,
//                        fontWeight: FontWeight.w700),
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Align(
//                    alignment:Alignment.topLeft,
//                    child: Text(
//                      "Personal Info",
//                      style: const TextStyle(
//                          color: Colors.black,
//                          decoration: TextDecoration.underline,
//                          fontSize: 14,
//                          fontWeight: FontWeight.w800),
//                    ),
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//
//
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.perm_contact_cal_rounded,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Name",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: Text(
//                          "Sam",
//                          style: const TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.my_location_outlined,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Address",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: Text(
//                          "Tc 57/281,Padmasree,Menilam,thiruvallam po trivanamdum,695027",
//                          style: const TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.email,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Email",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: Text(
//                          "<EMAIL>",
//                          style: const TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.phone,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Phone",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: Text(
//                          "+919995677972",
//                          style: const TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.star,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Rating",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: RatingBarIndicator(
//                          rating: 4,
//                          itemBuilder: (context, index) => const Icon(
//                            Icons.star,
//                            color: Colors.amber,
//                          ),
//                          itemCount: 5,
//                          itemSize: 18.0,
//                          unratedColor: Colors.amber.withAlpha(50),
//                          direction: Axis.horizontal,
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//
//                  Row(
//                    children: [
//                      const Icon(
//                        Icons.location_on,
//                        color: AppColors.green,
//                        size: 24.0,
//                      ),
//                      const SizedBox(
//                        width: 15,
//                      ),
//                      Container(
//                        width: 90,
//                        child: const Text(
//                          "Location",
//                          style: TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      ),
//                      const Text(":"),
//                      const SizedBox(
//                        width: 2,
//                      ),
//                      Container(
//                        width: 150,
//                        child: Text(
//                          "Kerala",
//                          style: const TextStyle(
//                              color: Colors.black,
//                              fontWeight: FontWeight.bold),
//                        ),
//                      )
//                    ],
//                  ),
//                  const SizedBox(
//                    height: 20,
//                  ),
//                  Container(
//                    decoration: BoxDecoration(
//                        color:Colors.white,
//                        border: Border.all(color: Colors.black)
//                    ),
//                    height:150,
//                    child:GoogleMap(
//                      zoomControlsEnabled: false,
//                      myLocationButtonEnabled: false,
//                      markers: Set<Marker>.of(_markers),
//                      onMapCreated: (GoogleMapController controller) {
//                        _controller.complete(controller);
//                      },
//                      mapToolbarEnabled: false,
//                      mapType: MapType.normal,
//                      initialCameraPosition: _kGooglePlex,
//                      gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
//                        Factory<OneSequenceGestureRecognizer>(
//                              () => EagerGestureRecognizer(),
//                        ),
//                      },
//
//                    ),
//
//                  ),
//
//                  const SizedBox(
//                    height: 10,
//                  ),
//                ],
//              ),
//            ),
//          ),
//          const Positioned(
//            left: 20,
//            right: 20,
//            child: CircleAvatar(
//              radius: 48,
//              backgroundColor: Colors.grey,
//              child: CircleAvatar(
//                backgroundColor: Colors.white,
//                radius: 44,
//                child: Icon(
//                  Icons.check_circle,
//                  color: Colors.green,
//                  size: 60.0,
//                ),
//              ),
//            ),
//          ),
//          Positioned(
//              child: GestureDetector(
//                  onTap: () {
//                    Navigator.pop(context);
//                  },
//                  child: const Icon(
//                    Icons.cancel,
//                    color: AppColors.white,
//                  )),
//              top: 0,
//              right: 0)
//        ],
//      )),
//    );
//
//   }
// }
