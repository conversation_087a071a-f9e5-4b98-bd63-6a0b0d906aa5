import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

import '../core/network/network_controller.dart';
import '../old_models/bought_model.dart';
import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import '../core/utils/constants.dart';
import '../core/utils/safe_print.dart';
// import '../utils/tools.dart';

class MyStocksBoughtList extends StatefulWidget {
  const MyStocksBoughtList({Key? key}) : super(key: key);

  @override
  State<MyStocksBoughtList> createState() => _MyStocksBoughtListState();
}

class _MyStocksBoughtListState extends State<MyStocksBoughtList> {
  int _page = 0;

  final int _limit = 10;

  bool _isFirstLoadRunning = false;
  bool _hasNextPage = true;

  bool _isLoadMoreRunning = false;

  final List<Content> _posts = [];

  int len = 0;
  bool st = true;
  DateTime now = DateTime.now();
  DateTime nowFrom = DateTime.now().subtract(const Duration(days: 14));
  DateTime nowTo = DateTime.now();

  void _loadMore() async {
    if (_hasNextPage == true &&
        _isFirstLoadRunning == false &&
        _isLoadMoreRunning == false &&
        _controller.position.extentAfter < 300) {
      setState(() {
        _isLoadMoreRunning = true; // Display a progress indicator at the bottom
      });

      _page += 1;

      try {
        var response = await NetworkController().getBoughtTable(
            getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);

        var a = response;
        if (a.content.isNotEmpty) {
          setState(() {
            _posts.addAll(a.content!);
          });
        } else {
          setState(() {
            _hasNextPage = false;
          });
        }
      } catch (err) {
        if (kDebugMode) {
          print('Something went wrong!');
        }
      }

      setState(() {
        _isLoadMoreRunning = false;
      });
    }
  }

  void _firstLoad() async {
    setState(() {
      _isFirstLoadRunning = true;
    });

    try {
      var url =
          "$baseAppUrl/apis/bidding/seller-payout/buyer-with-date-filter/${getCustomerId()}?page=$_page&size=$_limit&from_date=$nowFrom 00:58:17&to_date=$nowTo 20:58:17";
      var response = await NetworkController()
          .getBoughtTable(getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);

      var a = response;

      setState(() {
        _posts.addAll(a.content!);
        len = _posts.length;
      });
    } catch (err) {
      if (kDebugMode) {
        print('Something went wrong');
      }
    }

    setState(() {
      _isFirstLoadRunning = false;
    });
  }

  late ScrollController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ScrollController()..addListener(_loadMore);
    _firstLoad();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(
          height: 24,
        ),
        const Text(
          'Select Date',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text('From',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25)),
                      side: const BorderSide(color: Colors.black)),
                  onPressed: () async {
                    setState(() {
                      st = false;
                    });
                    DateTime? picked = await showDatePicker(
                        context: context,
                        initialDatePickerMode: DatePickerMode.year,
                        initialDate: DateTime(2000),
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now());
                    setState(() {
                      nowFrom = picked ?? DateTime.now();
                    });
                  },
                  child: Text(
                    DateFormat('dd-MM-yyyy').format(nowFrom),
                    style: const TextStyle(color: Colors.black),
                  ),
                ),
              ],
            ),
            const SizedBox(
              width: 20,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  'To',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                          side: const BorderSide(color: Colors.black))),
                  onPressed: () async {
                    setState(() {
                      st = false;
                    });
                    DateTime? picked1 = await showDatePicker(
                        context: context,
                        initialDatePickerMode: DatePickerMode.year,
                        initialDate: DateTime(2000),
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now());
                    setState(() {
                      nowTo = picked1 ?? DateTime.now();
                      safePrint(nowTo);
                    });
                  },
                  child: Text(
                    DateFormat('dd-MM-yyyy').format(nowTo),
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              width: 15,
            ),
            Column(
              children: [
                const SizedBox(
                  height: 20,
                ),
                TextButton(
                  style: TextButton.styleFrom(
                      backgroundColor: AppColors.primaryColor),
                  onPressed: () {
                    setState(() {
                      _posts.clear();
                      st = true;
                      _firstLoad();
                    });
                  },
                  child: const Text(
                    'Go',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            )
          ],
        ),
        const SizedBox(
          height: 25,
        ),
        Visibility(
          visible: (st != true) ? false : true,
          child: SizedBox(
            height: MediaQuery.of(context).size.height / 2.45,
            child: len != 0
                ? Column(
                    children: [
                      const Center(
                        child: Text(
                          'My Stocks - Bought',
                          style: TextStyle(
                              fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SizedBox(
                          height: MediaQuery.of(context).size.height / 3.1,
                          width: double.infinity,
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(10),
                                        topRight: Radius.circular(10)),
                                    color: AppColors.primaryColor),
                                width: double.infinity,
                                height: 40,
                                child: const Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: Center(
                                        child: Text(
                                          'Date',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                    VerticalDivider(
                                      color: Colors.white,
                                      width: 10,
                                      thickness: 1,
                                      indent: 0,
                                      endIndent:
                                          0, //Spacing at the bottom of divider.
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Stock Id /\nQty',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                    VerticalDivider(
                                      color: Colors.white,
                                      width: 10,
                                      thickness: 1,
                                      indent: 0,
                                      endIndent:
                                          0, //Spacing at the bottom of divider.
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Seller Name',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    VerticalDivider(
                                      color: Colors.white,
                                      width: 10,
                                      thickness: 1,
                                      indent: 0,
                                      endIndent:
                                          0, //Spacing at the bottom of divider.
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Total Sales\nAmount',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                    VerticalDivider(
                                      color: Colors.white,
                                      width: 10,
                                      thickness: 1,
                                      indent: 0,
                                      endIndent:
                                          0, //Spacing at the bottom of divider.
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Text(
                                        'Receivable',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(20.0),
                                        bottomRight: Radius.circular(20.0)),
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    )),
                                height: MediaQuery.of(context).size.height / 4,
                                width: double.infinity,
                                child: ListView.separated(
                                  shrinkWrap: true,
                                  separatorBuilder: (context, index) =>
                                      const Divider(
                                    thickness: 2,
                                  ),
                                  controller: _controller,
                                  physics: const ScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceEvenly,
                                        children: [
                                          Expanded(
                                            flex: 1,
                                            child: Center(
                                              child: Text(
                                                DateFormat('dd-MM-yyyy').format(
                                                    _posts[index].createdAt ??
                                                        DateTime.now()),
                                                style: const TextStyle(
                                                    fontSize: 12),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  "${_posts[index].stockId} /\n${_posts[index].quantity}",
                                                  style: const TextStyle(
                                                      fontSize: 12),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Center(
                                              child: Text(
                                                _posts[index].sellerName ??
                                                    '--',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.red,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Center(
                                              child: Text(
                                                "${_posts[index].totalPurchaseAmount?.toStringAsFixed(2)}",
                                                style: const TextStyle(
                                                    fontSize: 12),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Center(
                                              child: Text(
                                                  "${_posts[index].remainingPurchaseAmount?.toStringAsFixed(2)}",
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                  )),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  itemCount: _posts.length,
                                ),
                              ),
                              if (_isLoadMoreRunning == true)
                                Center(
                                  child: SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: progressIndicator,
                                  ),
                                ),
                              // if (_hasNextPage == false)
                              //   Container(
                              //       width: double.infinity,
                              //       height: 30,
                              //       decoration: BoxDecoration(
                              //         borderRadius:
                              //             BorderRadius.circular(20),
                              //         color: const Color.fromARGB(
                              //             255, 172, 165, 146),
                              //       ),
                              //       child: const Center(
                              //         child: Text(
                              //           'You have fetched all of the content !',
                              //           style: TextStyle(
                              //               color: Colors.red,
                              //               fontSize: 15,
                              //               fontWeight: FontWeight.bold),
                              //         ),
                              //       )),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                : Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Text('No data to show here!'),
                    ),
                  ),
          ),
        )
      ],
    );
  }
}
