import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/old_blocs/live_list/live_list_bloc.dart';
import 'package:connectone/old_models/live_auction_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';

import '../core/utils/circular_progress.dart';
import '../core/utils/colors.dart';
import 'live_auctions_screen.dart';

class LiveListScreen extends StatelessWidget {
  const LiveListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        title: const Text("Live Auction Stats"),
        elevation: 0,
        actions: const <Widget>[
          InfoHelp(),
        ],
      ),
      body: BlocProvider(
        create: (context) => LiveListBloc()..add(Load()),
        child:
            <PERSON><PERSON>uilder<LiveListBloc, LiveListState>(builder: (context, state) {
          if (state is Loaded) {
            final list = state.list ??
                LiveAuctionList(
                  data: [],
                  status: 0,
                  statusDescription: '',
                );
            // var reversedData = list.data.reversed.toList();
            // list.data = reversedData;
            return ListView.builder(
              physics: const BouncingScrollPhysics(),
              itemCount: list.data.length,
              itemBuilder: (BuildContext context, int index) {
                var date = list.data[index].date;
                return GestureDetector(
                  onTap: () {
                    if (list.data[index].status == "IN PROGRESS") {
                      Get.to(() => const LiveAuctions());
                    }
                  },
                  child: LiveAuctionItem(
                    imageUrl: list.data[index].image,
                    status: list.data[index].status,
                    auctionNo: list.data[index].auctionNo,
                    date: "${date.day}-${date.month}-${date.year}",
                    product: list.data[index].product ?? "",
                    stocks: list.data[index].stocks.toString(),
                    quantity:
                        list.data[index].quantity?.toStringAsFixed(2) ?? "0",
                    averagePrice: list.data[index].avgPrice.toString(),
                  ),
                );
              },
            );
          } else {
            return Center(child: progressIndicator);
          }
        }),
      ),
    );
  }
}

//ignore: must_be_immutable
class LiveAuctionItem extends StatelessWidget {
  final String imageUrl;
  late String status;
  final String auctionNo;
  final String date;
  final String product;
  final String stocks;
  final String quantity;
  final String averagePrice;

  LiveAuctionItem({
    Key? key,
    required this.imageUrl,
    required this.status,
    required this.auctionNo,
    required this.date,
    required this.product,
    required this.stocks,
    required this.quantity,
    required this.averagePrice,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color color = Colors.red;

    if (status == "IN_PROGRESS") {
      status = "IN PROGRESS";
    }

    if (status == "CLOSED") {
      color = Colors.red;
    } else if (status == "SCHEDULED") {
      color = Colors.orange;
    } else if (status == "IN PROGRESS") {
      color = Colors.green;
    }

    return Container(
      height: 200.0,
      margin: const EdgeInsets.all(4.0),
      child: Card(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0))),
        elevation: 4.0,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                flex: 5,
                child: Column(
                  children: [
                    Expanded(
                      child: Card(
                        shape: const RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(12.0))),
                        elevation: 2.0,
                        child: Image.network(
                          imageUrl,
                          errorBuilder: (context, exception, stackTrace) {
                            return const Center(
                              child: Text("NO IMAGE"),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 8.0,
                    ),
                    Container(
                      padding: const EdgeInsets.fromLTRB(12.0, 6.0, 12.0, 6.0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.0),
                          color: color),
                      child: Text(
                        status,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8.0,
                            fontWeight: FontWeight.bold),
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(
                width: 16.0,
              ),
              const Expanded(
                flex: 5,
                child: Column(
                  children: [
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Auction No",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Date",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Product",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Stocks",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Quantity",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Avg Price",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 13),
                            ))),
                  ],
                ),
              ),
              const Expanded(
                flex: 1,
                child: Column(
                  children: [
                    Expanded(child: Center(child: Text(":"))),
                    Expanded(child: Center(child: Text(":"))),
                    Expanded(child: Center(child: Text(":"))),
                    Expanded(child: Center(child: Text(":"))),
                    Expanded(child: Center(child: Text(":"))),
                    Expanded(child: Center(child: Text(":"))),
                  ],
                ),
              ),
              Expanded(
                flex: 5,
                child: Column(
                  children: [
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              auctionNo,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              date,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              product,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              stocks,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              quantity,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                    Expanded(
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              averagePrice,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "poppins",
                                  fontSize: 16),
                            ))),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
