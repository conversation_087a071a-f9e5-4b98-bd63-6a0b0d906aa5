import 'package:connectone/old_blocs/settings/settings_bloc.dart';
import 'package:connectone/core/utils/circular_progress.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SettingsBloc()..add(LoadSettings()))
      ],
      child: BlocBuilder<SettingsBloc, SettingsBlocState>(
        builder: (context, state) {
          if (state is SettingsLoaded) {
            return Scaffold(
              appBar: AppBar(
                elevation: 0,
                backgroundColor: AppColors.primaryColor,
                title: const Text(
                  "Settings",
                  style: TextStyle(color: AppColors.white),
                ),
              ),
              body: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    _buildSwitchRow(
                      context,
                      "Bid Alert Tone",
                      state.toneF,
                      (value) => context
                          .read<SettingsBloc>()
                          .add(UpdateToneEvent(toneF: value)),
                    ),
                    _buildSwitchRow(
                      context,
                      "Bid Alert Vibration",
                      state.vibF,
                      (value) => context
                          .read<SettingsBloc>()
                          .add(UpdateVibEvent(vibF: value)),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Center(child: progressIndicator);
          }
        },
      ),
    );
  }

  Widget _buildSwitchRow(BuildContext context, String title, bool value,
      ValueChanged<bool> onChanged) {
    return Row(
      children: [
        Text(title),
        const Spacer(),
        Switch(
          value: value,
          onChanged: onChanged,
          activeTrackColor: AppColors.primaryColor.withOpacity(0.7),
          activeColor: AppColors.primaryColor,
        ),
      ],
    );
  }
}
