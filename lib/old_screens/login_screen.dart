import 'package:connectone/bai_blocs/color/color_cubit.dart';
import 'package:connectone/bai_screens/bai_home.dart';
import 'package:connectone/bai_screens/registration_screen.dart';
import 'package:connectone/core/utils/data_storage.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/old_blocs/login/login_bloc.dart';
import 'package:connectone/old_blocs/login/login_state.dart';
import 'package:connectone/old_screens/forget_password.dart';
import 'package:connectone/core/utils/circular_progress.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/custom_border.dart';
import 'package:connectone/core/utils/decorations.dart';
import 'package:connectone/core/utils/storage_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

import '../core/network/network_controller.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool isPasswordHidden = true;
  bool rememberMe = true;
  NetworkController networkController = NetworkController();
  String orgName = '';

  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final _razorpay = Razorpay();

  @override
  void initState() {
    super.initState();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    setState(() {
      orgName = networkController.organisationData?.organizationName ?? "";
    });
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    Get.offAll(const BaiHome());
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    alert("Payment failed!");
    _showRetryPaymentDialog(response);
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet
  }

  void _showRetryPaymentDialog(PaymentFailureResponse response) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Payment Failed',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: const Text('Payment failed. Would you like to retry?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _retryPayment();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryColor,
                textStyle: const TextStyle(fontWeight: FontWeight.bold),
              ),
              child: const Text('Retry'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryColor,
                textStyle: const TextStyle(fontWeight: FontWeight.bold),
              ),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _retryPayment() {
    if (context.read<LoginBloc>().state.userV2 != null &&
        context.read<LoginBloc>().state.orderId != null) {
      var data = context.read<LoginBloc>().state.userV2?.data?[0].vendor;
      var subscriptionAmountPending = data?.subscriptionAmountPending ?? 0;
      _razorpay.open(createPaymentOptions(
        amount: subscriptionAmountPending.toDouble(),
        orderId: context.read<LoginBloc>().state.orderId ?? "",
        email: data?.email ?? "",
        phone: "${data?.primaryPhoneCountryCode}${data?.primaryPhone}",
      ));
    }
  }

  Map<String, dynamic> createPaymentOptions({
    required double amount,
    required String orderId,
    required String email,
    required String phone,
  }) {
    return {
      'key': 'rzp_test_3ipVrFfZAVlrUt',
      'amount': amount * 100,
      'name': 'BAI Store',
      'currency': 'INR',
      'description': 'Payment for registration',
      'order_id': orderId,
      'prefill': {
        'contact': phone,
        'email': email,
      },
    };
  }

  void _showPendingPaymentDialog(double amount) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Pending Payment',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: Text(
              'You have a pending payment of ${amount.toStringAsFixed(2).toIndianCurrencyFormat()}. Please complete the payment.'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryColor,
                textStyle: const TextStyle(fontWeight: FontWeight.bold),
              ),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _retryPayment();
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryColor,
                textStyle: const TextStyle(fontWeight: FontWeight.bold),
              ),
              child: const Text('Pay Now'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarBrightness: Brightness.dark,
          statusBarColor: AppColors.primaryColor,
        ),
      ),
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) async {
          if (state.loginSuccess) {
            String keyName = isBuyer() ? "buyer_colour" : "seller_colour";
            String apiColor = DataStorage.configData
                    ?.firstWhere(
                      (element) => element?.keyName1 == keyName,
                    )
                    ?.valueName1 ??
                "#121212";

            Color myColor =
                Color(int.parse(apiColor.substring(1), radix: 16) + 0xFF000000);
            context.read<ColorCubit>().changeColor(myColor);
            if (state.userV2 != null) {
              if (state.userV2!.data?[0].customerApprovalStatusCd != "APPR") {
                properAlert(
                    "Your registration is under review. \n\nPlease contact the admin for further details.");
                await networkController.deleteToken();
                clearAuthToken();
                writeToStorage(loggedIn, "FALSE");
                return;
              }
            }
            if (state.userV2 != null && state.orderId != null) {
              var data = state.userV2?.data?[0].vendor;
              var subscriptionAmountPending =
                  data?.subscriptionAmountPending ?? 0;
              if (subscriptionAmountPending > 0 && !kIsWeb) {
                _showPendingPaymentDialog(subscriptionAmountPending.toDouble());
              } else {
                Get.offAll(const BaiHome());
              }
            } else {
              Get.offAll(const BaiHome());
            }
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return Center(child: progressIndicator);
          }
          if (getUser() != null && getPass() != null) {
            if (usernameController.text.isEmpty) {
              usernameController.text = getUser();
            }
            if (passwordController.text.isEmpty) {
              passwordController.text = getPass();
            }
          }
          return Center(
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    networkController.organisationData?.organizationName ?? "",
                    style: const TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Container(
                    height: 80,
                    width: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      image:
                          networkController.organisationData?.orderingAppUrl !=
                                  null
                              ? DecorationImage(
                                  image: NetworkImage(networkController
                                          .organisationData?.orderingAppUrl ??
                                      ""),
                                  fit: BoxFit.contain, // Change this line
                                )
                              : const DecorationImage(
                                  image: AssetImage(
                                      'assets/images/no_image_available.jpeg'),
                                  fit: BoxFit.contain,
                                ),
                    ),
                  ),
                  // Container(
                  //   height: 80,
                  //   width: 80,
                  //   decoration: BoxDecoration(
                  //     color: Colors.white,
                  //     borderRadius: BorderRadius.circular(15),
                  //     image:
                  //         networkController.organisationData?.orderingAppUrl !=
                  //                 null
                  //             ? DecorationImage(
                  //                 image: NetworkImage(networkController
                  //                         .organisationData?.orderingAppUrl ??
                  //                     ""),
                  //                 fit: BoxFit.cover,)
                  //             : const DecorationImage(
                  //                 image: AssetImage(
                  //                     'assets/images/no_image_available.jpeg'),
                  //                 fit: BoxFit.contain,
                  //               ),
                  //   ),
                  // ),
                  const SizedBox(height: 20),
                  Visibility(
                    visible: !isPhoneNumber,
                    child: Form(
                      autovalidateMode: AutovalidateMode.always,
                      child: TextFormField(
                        validator: validateEmail,
                        controller: usernameController,
                        decoration: getLoginInputDecoration(
                          "Email",
                          Icons.email,
                          true,
                        ),
                        keyboardType: TextInputType.emailAddress,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: isPhoneNumber,
                    child: Form(
                      autovalidateMode: AutovalidateMode.always,
                      child: TextFormField(
                        controller: usernameController,
                        decoration: getLoginInputDecoration(
                          "Phone Number",
                          Icons.phone_android,
                          false,
                        ),
                        style: const TextStyle(fontSize: 18),
                        keyboardType: TextInputType.number,
                        validator: validatePhone,
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  TextField(
                    controller: passwordController,
                    obscureText: isPasswordHidden,
                    style: const TextStyle(fontSize: 18),
                    decoration: InputDecoration(
                      prefixIcon: Icon(
                        Icons.lock,
                        size: 20,
                        color: AppColors.primaryColor,
                      ),
                      hintText: sPassword,
                      contentPadding: const EdgeInsets.all(15),
                      focusedBorder: SelectedInputBorderWithShadow(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color.fromARGB(255, 180, 178, 178),
                          width: .1,
                        ),
                      ),
                      enabledBorder: SelectedInputBorderWithShadow(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color.fromARGB(255, 180, 178, 178),
                          width: .1,
                        ),
                      ),
                      border: SelectedInputBorderWithShadow(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color.fromARGB(255, 180, 178, 178),
                          width: .1,
                        ),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          !isPasswordHidden
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: AppColors.primaryColor,
                        ),
                        onPressed: () => setState(
                          () {
                            isPasswordHidden = !isPasswordHidden;
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 28,
                        width: 28,
                        child: Checkbox(
                          value: rememberMe,
                          fillColor: WidgetStateProperty.all(rememberMe
                              ? AppColors.primaryColor
                              : Colors.white),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          onChanged: (val) => setState(
                            () {
                              rememberMe = !rememberMe;
                            },
                          ),
                        ),
                      ),
                      const Text(
                        sRememberMe,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      InkWell(
                        onTap: () {
                          Get.to(() => const ForgotPasswordScreen());
                        },
                        child: const Text(
                          sForgotPassword,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.maxFinite,
                    height: 45,
                    child: TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      onPressed: () {
                        writeToStorage("user", usernameController.text);
                        writeToStorage("pass", passwordController.text);
                        context.read<LoginBloc>().initLogin(
                              user: usernameController.text,
                              pass: passwordController.text,
                            );
                      },
                      child: const Text(
                        sLogin,
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap: () {
                      Get.to(const RegistrationScreen());
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "New to $orgName?",
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          sRegister,
                          style: TextStyle(
                            color: AppColors.textGreen,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid email address'
        : null;
  }

  String? validatePhone(String? value) {
    const pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
    final regex = RegExp(pattern);
    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid phone number'
        : null;
  }

  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    _razorpay.clear();
    super.dispose();
  }
}
