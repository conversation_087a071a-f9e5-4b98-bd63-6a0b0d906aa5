import 'dart:async';

//import 'package:ant_media_flutter/ant_media_flutter.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/old_blocs/live_auction/live_auction_bloc.dart';
import 'package:connectone/core/old_widgets/common/increase_bid_amount_popup.dart';
import 'package:connectone/old_models/live_auction_response.dart';
import 'package:connectone/core/utils/dialog_utils.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/live_auction_utils.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/sound_and_vibration_utils.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

import '../core/old_widgets/common/dots.dart';
import '../core/utils/data_storage.dart';
import '../core/network/network_controller.dart';
import '../old_models/active_buyers.dart' as ab;
import '../old_models/card_res.dart' as card_res;
import '../core/old_widgets/tables/live_card_table.dart';
import '../core/utils/colors.dart';
import '../core/utils/constants.dart';
import '../core/utils/time_utils.dart';

class LiveAuctions extends StatefulWidget {
  const LiveAuctions({Key? key}) : super(key: key);

  @override
  State<LiveAuctions> createState() => _LiveAuctionsState();

  static List<charts.Series<OrdinalSales, String>> createData(Data? item) {
    var currentItem = item?.orders[0].orderSpecifications[0];
    final cleanData = [
      OrdinalSales(
          '>8mm',
          currentItem?.grade8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      OrdinalSales(
          '7-8mm',
          currentItem?.grade7T8MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      OrdinalSales(
          '6-7mm',
          currentItem?.grade17MmClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
      OrdinalSales(
          '<6mm',
          currentItem?.rejectionsClean?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 171, 170, 170))),
    ];

    final sickSplitData = [
      OrdinalSales('>8mm', currentItem?.grade8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      OrdinalSales('7-8mm', currentItem?.grade7T8MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      OrdinalSales('6-7mm', currentItem?.grade17MmSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
      OrdinalSales('<6mm', currentItem?.rejectionsSickdsplit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(const Color.fromRGBO(45, 2, 217, 1))),
    ];

    final otherData = [
      OrdinalSales(
          '>8mm',
          currentItem?.grade8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      OrdinalSales(
          '7-8mm',
          currentItem?.grade7T8MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      OrdinalSales(
          '6-7mm',
          currentItem?.grade17MmFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
      OrdinalSales(
          '<6mm',
          currentItem?.rejectionsFruit?.toInt() ?? 0,
          charts.ColorUtil.fromDartColor(
              const Color.fromARGB(255, 224, 136, 5))),
    ];

    return [
      charts.Series<OrdinalSales, String>(
          id: 'Clean',
          domainFn: (OrdinalSales sales, _) => sales.year,
          measureFn: (OrdinalSales sales, _) => sales.sales,
          data: cleanData,
          colorFn: (OrdinalSales sales, _) => sales.barColor),
      charts.Series<OrdinalSales, String>(
          id: 'Sick/Split',
          domainFn: (OrdinalSales sales, _) => sales.year,
          measureFn: (OrdinalSales sales, _) => sales.sales,
          data: sickSplitData,
          colorFn: (OrdinalSales sales, _) => sales.barColor),
      charts.Series<OrdinalSales, String>(
          id: 'Other',
          domainFn: (OrdinalSales sales, _) => sales.year,
          measureFn: (OrdinalSales sales, _) => sales.sales,
          data: otherData,
          colorFn: (OrdinalSales sales, _) => sales.barColor),
    ];
  }
}

class _LiveAuctionsState extends State<LiveAuctions> {
  Timer? timer;

  var isShowMargin = false;
  double margin = 0;

  var tab1 = false;
  var tab2 = true;
  var tab3 = false;
  var tab4 = false;
  var tab5 = false;

  var title = "Auction";

  var pageController = PageController(initialPage: 1);

  var showActiveBuyerCount = false;

  //final RTCVideoRenderer remoteRenderer = RTCVideoRenderer();

  StreamSubscription? streamSubscription;
  StreamSubscription? stockIdSubscription;

  StreamSubscription? streamSubscription1;
  StreamSubscription? streamSubscription2;
  StreamSubscription? streamSubscription3;
  StreamSubscription? streamSubscription4;
  StreamSubscription? streamSubscription5;
  StreamSubscription? streamSubscription6;
  StreamSubscription? streamSubscription7;
  StreamSubscription? streamSubscription8;
  StreamSubscription? streamSubscription9;

  StreamSubscription? streamSubscriptionV2;

  var totalLots = 0;
  var remainingLots = 0;
  var totalStocks = 0;
  num averageSalesAmount = 0;
  num minimumSalesAmount = 0;
  num maximumSalesAmount = 0;
  var liveBiddingStockNo = 0;
  num highestBid = 0;
  var highestBidCustomerId = 0;
  num liveBiddingIncrementValue = 0;
  var auctionStatus = '';
  var liveAuctionStatus = 0;
  var liveBiddingMeeting = '';
  var liveBidding = 1;
  var stockId = '';
  var message = '';
  num myBid = 0.0;
  num yourBid = 0;
  var liveBiddingSellerId = 0;
  num ourBid = 0;
  var finalBid = 0;
  String timestamp = "";
  String timestampNew = "";
  late int remaining = finalBid;
  bool isClickable = false;
  Color color = AppColors.darkRed;
  var numberFormat = NumberFormat.simpleCurrency(locale: 'HI');
  String customerId = "";

  var auctionDate = '';
  var auctionDescription = '';
  var auctionName = '';
  var auctionNo = '';
  var auctionTime = '';
  var liveBiddingWhatsappChat = '';

  // To decide the status of the live bidding page
  // 0 - Not started,
  // 1 - Started
  // 2 - Pause Live bidding
  void firebaseListener1() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding');
    dbReference.keepSynced(false);
    streamSubscription1 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        var data = event.snapshot.value as num;
        safePrint("1*================$data");
        if (mounted) {
          setState(() {
            liveBidding = data.toInt();
            liveAuctionController();
          });
        }
      }
    });
  }

  ///tbr
  void firebaseListener9() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_increment_value');
    dbReference.keepSynced(false);
    streamSubscription9 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        var data = event.snapshot.value as num;
        if (mounted) {
          setState(() {
            liveBiddingIncrementValue = data.toDouble();
            liveAuctionController();
          });
        }
      }
    });
  }

  ///tbr
  void firebaseListener8() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_stock_lot_no');
    dbReference.keepSynced(false);
    streamSubscription8 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        var data = event.snapshot.value as num;
        safePrint("8*================$data");
        if (mounted) {
          setState(() {
            liveBiddingStockNo = data.toInt();
            liveAuctionController();
          });
        }
      }
    });
  }

  var table = "";

  // Lot's StockId
  void firebaseListener2() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/livebidding_stockid');
    dbReference.keepSynced(false);
    streamSubscription2 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        var data = event.snapshot.value as num;
        safePrint("2*================$data");
        if (mounted) {
          setState(() {
            stockId = data.toString();
            lastBid = -1;
            fetchCardTable(stockId, table);
            liveAuctionController();
          });
        }
        try {
          context.read<LiveAuctionBloc>().add(const Load(roomId: 999));
        } catch (e) {
          safePrint(e);
        }
      }
    });
  }

  // Status of Lot's Auction
  // 0 - Started the auction
  // 1 - Stopped the auction (will start the countdown)
  // 2 - Closed the auction (countdown is completed)
  // 3 - Sold the lot
  // 4 - Lot is unsold
  void firebaseListener3() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/live_auction_status');
    dbReference.keepSynced(false);
    streamSubscription3 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        var data = event.snapshot.value as num;
        safePrint("3*================$data");
        if (mounted) {
          setState(() {
            liveAuctionStatus = data.toInt();
            liveAuctionController();
          });
        }
        initAllOtherListeners();
      }
    });
  }

  // finalbid.seconds	- Count down Duration
  // finalbid.timestamp -	Timestamp to notify the change in BidAmount
  void firebaseListener4() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/finalbid');
    dbReference.keepSynced(false);
    streamSubscription4 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        ///*****************************************///
        Map<dynamic, dynamic> map = event.snapshot.value as Map;
        safePrint("4*================$map");
        if (mounted) {
          // setState(() {
          //   var seconds = map["seconds"] as num;
          //   finalBid = seconds.toInt();
          //   remaining = finalBid;
          //   if (map.containsKey('timestamp')) {
          //     timestamp = map["timestamp"] as String;
          //     timestampNew = timestamp;
          //   }
          //   liveAuctionController();
          // });

          ///***************************************///
          Map<dynamic, dynamic> map = event.snapshot.value as Map;
          var seconds = map["seconds"] as int;
          DateTime? parsedTimestamp;
          if (map.containsKey('timestamp')) {
            var timestamp = map["timestamp"] as String;
            parsedTimestamp = DateFormat("yyyy-MM-dd HH:mm:ss")
                .parse(timestamp, true)
                .toLocal();
          }
          var newSeconds = seconds;
          if (parsedTimestamp != null) {
            newSeconds -= calculateSecondsBehind(timestamp: parsedTimestamp);
          }
          remaining = newSeconds;
          liveAuctionController();
        }
      }
    });
  }

  // void firebaseListener4() {
  //   DatabaseReference dbReference = FirebaseDatabase.instance.ref(
  //       '$firebaseBaseUrl/live_auction/1/livebidding_seller_decision_countdown');
  //   streamSubscription4 =
  //       dbReference.onValue.listen((DatabaseEvent event) async {
  //     if (event.snapshot.exists) {
  //       var data = event.snapshot.value as num;
  //       printer("4*================$data");
  //       if (mounted) {
  //         setState(() {
  //           var seconds = data;
  //           finalBid = seconds.toInt();
  //           remaining = seconds.toInt();
  //           printer("4*================$seconds");
  //         });
  //       }
  //       liveAuctionController();
  //     }
  //   });
  // }

  num highestBidCopy = 0;

  // highest_bids.{id}.customer_id	eg - Joseph
  // highest_bids.{id}.highest_bid	eg - 1005
  void firebaseListener5() {
    safePrint("objecttttttbchdbfvhdbvhfbvhfbhv $stockId");
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/highest_bids');
    dbReference.keepSynced(false);
    streamSubscription5 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        Map<dynamic, dynamic> map = event.snapshot.children.first.value as Map;
        // printer("5*================$map");
        if (mounted) {
          setState(() {
            highestBidCustomerId = (map['customer_id'] as num).toInt();
            highestBid = map['highest_bid'] as num;
            highestBidCopy = highestBid;
            safePrint("5*================$customerId $highestBid");
            liveAuctionController();
            fetchCount(stockId);
          });
        }
      }
    });
  }

  // soldout_stats.averageSalesAmount	- Average sales amount for current date
  // soldout_stats.maximumSalesAmount	- Maximum sales amount for current date
  // soldout_stats.minimumSalesAmount	- Minimum sales amount for current date
  // soldout_stats.remainingLots - No of remaining lots to be sold
  // soldout_stats.totalLots - No of total lots
  // soldout_stats.totalStocks - Total quantity of stocks
  void firebaseListener6() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/soldout_stats');
    dbReference.keepSynced(false);
    streamSubscription6 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        Map<dynamic, dynamic> map = event.snapshot.value as Map;
        safePrint("6*================$map");
        if (mounted) {
          setState(() {
            averageSalesAmount = map['averageSalesAmount'] as num;
            maximumSalesAmount = map['maximumSalesAmount'] as num;
            minimumSalesAmount = map['minimumSalesAmount'] as num;
            remainingLots = map['remainingLots'] as int;
            totalLots = (map['totalLots'] as num).toInt();
            totalStocks = (map['totalStocks'] as num).toInt();
            safePrint(
                "6*================$averageSalesAmount $maximumSalesAmount $minimumSalesAmount $remainingLots $totalLots $totalStocks");
            liveAuctionController();
          });
        }
      }
    });
  }

  // live_auction_data.auction_date
  // live_auction_data.auction_description
  // live_auction_data.auction_name
  // live_auction_data.auction_no
  // live_auction_data.auction_time
  // live_auction_data.livebidding_increment_value
  // live_auction_data.livebidding_meeting
  // live_auction_data.livebidding_whatsapp_chat
  void firebaseListener7() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/live_auction_data');
    dbReference.keepSynced(false);
    streamSubscription7 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        Map<dynamic, dynamic> map = event.snapshot.value as Map;
        safePrint("7*================$map");
        if (mounted) {
          setState(() {
            auctionDate = map['auction_date'];
            auctionDescription = map['auction_description'];
            auctionName = map['auction_name'];
            auctionNo = map['auction_no'];
            auctionTime = map['auction_time'];
            liveBiddingIncrementValue =
                double.tryParse(map['livebidding_increment_value']) ?? 0;
            liveBiddingMeeting = map['livebidding_meeting'];
            liveBiddingWhatsappChat = map['livebidding_whatsapp_chat'];
            safePrint(
                "7*================$auctionDate $auctionDescription $auctionName $auctionNo $auctionTime $liveBiddingIncrementValue $liveBiddingMeeting $liveBiddingWhatsappChat");
            liveAuctionController();
          });
        }
      }
    });
  }

  /// v2 listener
  void firebaseListenerV2() {
    DatabaseReference dbReference = FirebaseDatabase.instance
        .ref('$firebaseBaseUrl/live_auction/1/finalbid');
    dbReference.keepSynced(false);
    streamSubscriptionV2 =
        dbReference.onValue.listen((DatabaseEvent event) async {
      if (event.snapshot.exists) {
        Map<dynamic, dynamic> map = event.snapshot.value as Map;
        safePrint("4*================$map");
        if (mounted) {
          setState(() {
            var seconds = map["seconds"] as num;
            finalBid = seconds.toInt();
            remaining = finalBid;
            if (map.containsKey('timestamp')) {
              timestamp = map["timestamp"] as String;
              timestampNew = timestamp;
            }
            liveAuctionController();
          });
        }
        // liveAuctionController();
      }
    });
  }

  var auctionGoingOn = false;

  Future<void> liveAuctionController() async {
    try {
      // liveBidding
      // To decide the status of the live bidding page
      // 0 - Not started
      // 1 - Started
      // 2 - Pause live bidding
      /// live bidding not started
      if (liveBidding.toString() == "0") {
        {
          showFailureDialog(context, description: "Live bidding not started!");
        }
      }

      /// live bidding started
      if (liveBidding.toString() == "1") {
        // liveAuctionStatus
        // Status of lot's auction
        // 0 - Started the auction
        // 1 - Stopped the auction (will start the countdown)
        // 2 - Closed the auction (countdown is completed)
        // 3 - Sold the lot
        // 4 - Lot is unsold

        safePrint("haha123 $liveAuctionStatus");

        if (liveAuctionStatus.toString() == "0") {
          auctionGoingOn = true;
          auctionStatus = "Open";
          message = "Your bid is under bid now.";
        }

        /// live bidding stopped
        if (liveAuctionStatus.toString() == "1") {
          // timestamp = timestampNew;
          // if (calculateAdjustedRemainingTime(finalBid) > 0) {
          //   remaining = calculateAdjustedRemainingTime(finalBid);
          // } else {
          //   remaining = finalBid;
          // }
          startTimer();
        } else {
          if (isTimerActive) {
            closeTimer();
          }
        }

        /// bid won
        if (liveAuctionStatus.toString() == "3" &&
            highestBidCustomerId.toString() == getCustomerId().toString()) {
          safePrint("//bid won===================");
          clearData();
          auctionStatus = "Close";
          message = "";
          isClickable = false;
          myBid = 0;
          yourBid = 0;
          late Order item;
          final response = await NetworkController()
              .getLiveAuction(999, int.parse(await LiveUtils().getStockId()));
          response.fold<dynamic>(
            (failure) {},
            (success) {
              item = success.data.orders[0];
              showWinningDialog(
                context,
                volume: item.quantity.toString(),
                bidAmount: highestBidCopy.toString(),
                totalAmount:
                    (item.quantity * highestBidCopy).toStringAsFixed(2),
              );
            },
          );
        }

        /// bid lost
        if (liveAuctionStatus.toString() == "3" &&
            highestBidCustomerId.toString() != getCustomerId().toString()) {
          safePrint(
              "//bid lost===================${highestBidCustomerId.toString()} ${getCustomerId().toString()}");
          clearData();
          auctionStatus = "Close";
          message = "";
          yourBid = 0;
          isClickable = false;
          showFailureDialog(context,
              description:
                  "Lot no ${await LiveUtils().getLotNo()} is sold out. Next Lot bidding will be starting soon.");
        }

        /// bid rejected
        if (yourBid == highestBid && highestBidCustomerId != getCustomerId()) {
          // auctionStatus = "OpenSuccess";
          // color = Colors.amber;
          // message =
          //     "Sorry, your bid got rejected because of another early equal bid.";
          // isClickable = false;
        }

        /// highest bid
        if (highestBidCustomerId.toString() == getCustomerId().toString() &&
            liveAuctionStatus.toString() != "3" &&
            liveAuctionStatus.toString() != "4") {
          if (highestBid != 0) {
            auctionStatus = "Open";
            message = "Your bid is the highest bid now.";
            color = AppColors.green;
          }
          play1();
          isClickable = false;
        }

        /// lowest bid
        if (highestBidCustomerId.toString() != getCustomerId().toString() &&
            liveAuctionStatus.toString() != "3" &&
            liveAuctionStatus.toString() != "4") {
          if (highestBid != 0) {
            auctionStatus = "Open";
            message = "Your bid is under bid now.";
            color = AppColors.darkRed;
          }
          play1();
          isClickable = true;
        }

        /// lot unsold
        if (liveAuctionStatus.toString() == "4") {
          clearData();
          showFailureDialog(context,
              description:
                  "Lot no ${await LiveUtils().getLotNo()} is not sold.");
          message = "";
          isClickable = false;
          myBid = 0;
          yourBid = 0;
          auctionStatus = "Close";
        }

        /// lot paused
        if (liveAuctionStatus.toString() == "2") {
          auctionStatus = "Close";
          isClickable = false;
          yourBid = 0;
        }
        // fetchCount(stockId);
      }

      /// live bidding paused
      if (liveBidding.toString() == "2") {
        {
          showFailureDialog(context, description: "Live bidding is paused.");
        }
      }
    } catch (e) {
      safePrint("LAC $e");
    }
  }

  void clearData() {
    if (mounted) {
      setState(() {
        auctionGoingOn = false;
      });
    }
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted && !auctionGoingOn) {
        // if (mounted) {
        setState(() {
          count = "0";
          message = "Auction closed";
          isClickable = false;
          myBid = 0;
          yourBid = 0;
          highestBid = 0;
          auctionStatus = "Close";
          liveAuctionStatus = 0;
          liveBiddingIncrementValue = 0;
          color = AppColors.red;
        });
      }
    });
  }

  bool isTimerActive = false;

  int calculateAdjustedRemainingTime(int remaining) {
    if (timestamp.isNotEmpty) {
      var parsedTimestamp =
          DateFormat("yyyy-MM-dd HH:mm:ss").parse(timestamp, true).toLocal();
      var localTimestamp = DateTime.now();
      if (parsedTimestamp.isBefore(localTimestamp)) {
        var remaining1 =
            remaining - (localTimestamp.difference(parsedTimestamp).inSeconds);
        return remaining1;
      }
    }
    return remaining;
  }

  void startTimer() {
    safePrint("=============timer started $remaining");

    ///
    // if (timestamp.isNotEmpty) {
    //   var parsedTimestamp =
    //       DateFormat("yyyy-MM-dd HH:mm:ss").parse(timestamp, true).toLocal();
    //   var localTimestamp = DateTime.now();
    //   print(
    //       "hahable $remaining ${localTimestamp.difference(parsedTimestamp).inSeconds}");
    //   if (parsedTimestamp.isBefore(localTimestamp)) {
    //     setState(() {
    //       remaining = remaining -
    //           (localTimestamp.difference(parsedTimestamp).inSeconds);
    //     });
    //   }
    // }
    ///

    if (!isTimerActive) {
      isTimerActive = true;
      timer = Timer.periodic(
        const Duration(seconds: 1),
        (Timer timer) {
          safePrint("==========timer running $remaining");
          if (remaining == 0) {
            if (mounted) {
              setState(() {
                timer.cancel();
                isTimerActive = false;
                isClickable = true;
                auctionStatus = "Close";
              });
            }
          } else {
            if (remaining > 0) {
              if (mounted) {
                setState(() {
                  remaining--;
                  auctionStatus = "Countdown: ${remaining.toMMSS()}";
                });
              }
            } else {
              setState(() {
                auctionStatus = "";
              });
            }
          }
        },
      );
    }
  }

  void closeTimer() {
    if (mounted) {
      setState(() {
        timer?.cancel();
        isTimerActive = false;
      });
    }
  }

  Color getTextColor(Color color) {
    if (color == AppColors.yellowColor) {
      return Colors.black;
    } else {
      return Colors.white;
    }
  }

  card_res.CardRes? res;

  fetchCardTable(String stockId, String tableType) async {
    if (tableType != "CARDAMOM") {
      // return;
    }
    try {
      var response = res = await NetworkController().getCardamomTable(stockId);
      setState(() {
        res = response;
      });
    } catch (e) {
      safePrint(e);
    }
  }

  String? count = "0";

  num lastBid = -1;

  Future<void> fetchCount(String stockId) async {
    try {
      ab.ActiveBuyers activeBuyers =
          await NetworkController().getActiveBuyers(stockId);
      setState(() {
        count = activeBuyers.data?.noOfBuyers;
      });
    } catch (e) {
      safePrint(e);
    }
  }

  @override
  initState() {
    super.initState();
    DataStorage.clearOldStockId();
    setStockId();
    myBid = 0;
    yourBid = 0;
    //AntMediaFlutter.requestPermissions();
    // if (Platform.isAndroid) {
    //AntMediaFlutter.startForegroundService();
    // }
    //liveFirebaseListener();
    firebaseListener5();
    context.read<LiveAuctionBloc>().add(const Load(roomId: 999));
    // stockIdListener();
    //initRenderers();
    //connect();

    // setStockId();
    firebaseListener1();
    // firebaseListener2();
    // firebaseListener1();
    firebaseListener3();
    // firebaseListener4();
    // firebaseListener6();
    // firebaseListener7();
    // firebaseListener8();
    // firebaseListener9();
    // fetchCardTable(stockId, tableType);
  }

  setStockId() async {
    var id = await LiveUtils().getStockId();
    if (mounted) {
      setState(() {
        stockId = id;
      });
    }
  }

  void initAllOtherListeners() {
    // setStockId();
    firebaseListener1();
    firebaseListener2();
    //firebaseListener3();
    firebaseListener4();
    firebaseListener5();
    firebaseListener6();
    firebaseListener7();
    firebaseListener8();
    // firebaseListener9();
  }

  // Future<void> setStockId() async {
  //   var tempStockId = await LiveUtils().getStockId();
  //   setState(() {
  //     stockId = tempStockId;
  //   });
  // }

  initRenderers() async {
    //await remoteRenderer.initialize();
  }

  setMarginDetails() async {
    //await remoteRenderer.initialize();
  }

  @override
  deactivate() {
    super.deactivate();
    // if (AntMediaFlutter.anthelper != null) AntMediaFlutter.anthelper?.close();
    //remoteRenderer.dispose();
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription?.cancel();
    stockIdSubscription?.cancel();
    streamSubscription1?.cancel();
    streamSubscription2?.cancel();
    streamSubscription3?.cancel();
    streamSubscription4?.cancel();
    streamSubscription5?.cancel();
    streamSubscription6?.cancel();
    streamSubscription7?.cancel();
    streamSubscription8?.cancel();
    streamSubscription9?.cancel();
    streamSubscriptionV2?.cancel();
    timer?.cancel();
  }

  void connect() async {
    // AntMediaFlutter.connect(
    //   'wss://test.antmedia.io:5443/WebRTCAppEE/play.html',
    //   '554221610006205395756180',
    //   '',
    //   AntMediaType.Play,
    //   true,
    //   false,
    //   (HelperState state) {
    //     switch (state) {
    //       case HelperState.CallStateNew:
    //       case HelperState.CallStateBye:
    //         setState(() {
    //           //remoteRenderer.srcObject = null;
    //           Navigator.pop(context);
    //         });
    //         break;
    //       case HelperState.ConnectionClosed:
    //       case HelperState.ConnectionError:
    //       case HelperState.ConnectionOpen:
    //         break;
    //     }
    //   },
    //   ((stream) {
    //     setState(() {
    //       //remoteRenderer.srcObject = stream;
    //     });
    //   }),
    //   ((stream) {
    //     setState(() {
    //       //remoteRenderer.srcObject = stream;
    //     });
    //   }),
    //   ((dataChannel) {}),
    //   (channel, message, isReceived) {},
    //   (stream) {},
    //   ((stream) {
    //     setState(() {
    //       //remoteRenderer.srcObject = null;
    //     });
    //   }),
    // );
  }

  Data? getItem(LiveAuctionState state) {
    if (state is Loaded) {
      return state.list?.data;
    } else {
      return null;
    }
  }

  Loaded? getState(LiveAuctionState state) {
    if (state is Loaded) {
      return state;
    } else {
      return null;
    }
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LiveAuctionBloc, LiveAuctionState>(
      listener: (context, state) async {
        if (state is Loaded) {
          if (state.list?.data.orders[0].itemName.tableType() == "CARDAMOM") {
            // fetchCardTable(
            //     stockId, state.list!.data.orders[0].itemName.tableType());
          }
          safePrint("callllllllled");
          safePrint(state.bidSuccess);
          stockId = await LiveUtils().getStockId();
          safePrint("activeeeeeeeeeeee$stockId");
          if (state.bidSuccess == true) {
            setState(() {
              yourBid = state.yourBid;
              // lastBid = highestBid;
            });
          }
        }
        if (state is Error) {
          var stockId = await LiveUtils().getStockId();
          alert(state.error?.statusDescription);
          ScaffoldMessenger.of(context).clearSnackBars();
          var snackBar = SnackBar(
            content: Text("${state.error?.statusDescription} $stockId"),
            duration: const Duration(seconds: 10),
            action: SnackBarAction(
              label: 'RETRY',
              onPressed: () {
                context.read<LiveAuctionBloc>().add(const Load(roomId: 999));
              },
            ),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
        }
      },
      builder: (context, state) {
        // (state is LiveAuctionInitial)
        //     ? context.loaderOverlay.show()
        //     : context.loaderOverlay.hide();
        // if (state is Loaded) {
        safePrint(highestBidCustomerId.toString());
        safePrint(getCustomerId().toString());

        ///
        _marginPercentageLogic(getState(state));
        _activeBuyerLogic();

        ///
        var item = getItem(state);
        var offerYn = NetworkController().organisationData?.offerYn == "Y";
        var lot =
            item?.orders[0].lotNo != null ? "- ${item?.orders[0].lotNo}" : "";
        return ModalProgressHUD(
          inAsyncCall: (state is LiveAuctionInitial),
          opacity: 0,
          child: DefaultTabController(
            length: 4,
            child: Scaffold(
              appBar: AppBar(
                backgroundColor: AppColors.primaryColor,
                elevation: 0,
                title: Text(offerYn == true
                    ? "Auction${auctionNo.isNotEmpty ? " - $auctionNo" : ""}"
                    : "Trade${auctionNo.isNotEmpty ? " - $auctionNo" : ""}"),
                actions: const <Widget>[
                  InfoHelp(),
                ],
              ),
              body: SafeArea(
                child: Column(
                  children: [
                    const SizedBox(height: 8),
                    SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(8),
                      scrollDirection: Axis.horizontal,
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            // LiveTab(
                            //   tab: tab1,
                            //   title: "Video Call",
                            //   onClick: () {
                            //     setState(() {
                            //       tab1Selected();
                            //       pageController.jumpToPage(0);
                            //     });
                            //   },
                            //   image: 'assets/images/videocall.png',
                            // ),
                            LiveTab(
                              tab: tab1,
                              title: "Stats",
                              onClick: () {
                                if (mounted) {
                                  setState(() {
                                    tab1Selected();
                                    pageController.jumpToPage(0);
                                  });
                                }
                              },
                              image: 'assets/images/bidding.png',
                            ),
                            LiveTab(
                              tab: tab2,
                              title: "Lot Details",
                              onClick: () {
                                if (mounted) {
                                  setState(() {
                                    tab2Selected();
                                    // DataStorage.clearOldStockId();
                                    pageController.jumpToPage(1);
                                  });
                                }
                              },
                              image: 'assets/images/product_details.png',
                            ),
                            LiveTab(
                              tab: tab3,
                              title: "Images",
                              onClick: () {
                                if (mounted) {
                                  setState(() {
                                    tab3Selected();
                                    pageController.jumpToPage(2);
                                  });
                                }
                              },
                              image: 'assets/images/images.png',
                            ),
                            item?.orders[0].itemName.tableType() == "CARDAMOM"
                                ? LiveTab(
                                    tab: tab4,
                                    title: "Graph",
                                    onClick: () {
                                      if (mounted) {
                                        setState(() {
                                          tab4Selected();
                                          pageController.jumpToPage(3);
                                        });
                                      }
                                    },
                                    image: 'assets/images/graph.png',
                                  )
                                : const SizedBox(),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: PageView(
                        physics: const NeverScrollableScrollPhysics(),
                        onPageChanged: (index) async {
                          table = item?.orders[0].itemName.tableType() ??
                              "CARDAMOM";
                          stockId = await LiveUtils().getStockId();
                          safePrint("activeeeeeeeeeeee$stockId");
                          if (mounted) {
                            setState(() {
                              if (index == 0) {
                                tab1Selected();
                              } else if (index == 1) {
                                tab2Selected();
                              } else if (index == 2) {
                                tab3Selected();
                              } else if (index == 3) {
                                tab4Selected();
                              } else if (index == 4) {
                                tab5Selected();
                              }
                            });
                          }
                        },
                        controller: pageController,
                        children: [
                          /// first screen
                          /*    Stack(alignment: Alignment.center, children: [
                              // Container(
                              //   color: Colors.black,
                              //   child: RTCVideoView(remoteRenderer),
                              // ),
                              Container(
                                color: Colors.black,
                              ),
                              Positioned(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      //initRenderers();
                                      //connect();
                                      alert("Coming soon!");
                                    },
                                    style: ButtonStyle(
                                        backgroundColor:
                                            MaterialStateProperty.all(
                                                AppColors.connectOneBlue)),
                                    child: const Text("START PLAYING"),
                                  ),
                                  bottom: 8),
                              Positioned(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      alert("Coming soon!");
                                    },
                                    style: ButtonStyle(
                                        backgroundColor:
                                            MaterialStateProperty.all(
                                                AppColors.connectOneBlue)),
                                    child: const Text("VIEW FULL SCREEN"),
                                  ),
                                  top: 8)
                            ]),

                         */

                          /// second page
                          SingleChildScrollView(
                            physics: const BouncingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            child: Column(
                              children: [
                                TodaysBidItem(
                                  itemName: 'Total Lots',
                                  itemValue: totalLots.toString(),
                                ),
                                TodaysBidItem(
                                  itemName: 'Remaining Lots',
                                  itemValue: remainingLots.toString(),
                                ),
                                TodaysBidItem(
                                  itemName: 'Auction Stock',
                                  itemValue: "${totalStocks.toString()} Kg",
                                ),
                                TodaysBidItem(
                                  itemName: 'Avg Price',
                                  itemValue: averageSalesAmount.toString(),
                                ),
                                TodaysBidItem(
                                  itemName: 'Min Price',
                                  itemValue: minimumSalesAmount.toString(),
                                ),
                                TodaysBidItem(
                                  itemName: 'Max Price',
                                  itemValue: maximumSalesAmount.toString(),
                                ),
                              ],
                            ),
                          ),

                          /// third page
                          SingleChildScrollView(
                            physics: const BouncingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            child: Column(
                              children: [
                                TodaysBidItem(
                                  itemName: 'Lot Number',
                                  itemValue: item?.orders[0].lotNo != null
                                      ? item?.orders[0].lotNo.toString()
                                      : "",
                                ),
                                TodaysBidItem(
                                  itemName: 'Category',
                                  itemValue: item?.orders[0].itemName != null
                                      ? item?.orders[0].itemName.toString()
                                      : "",
                                ),
                                TodaysBidItem(
                                  itemName: 'Quantity',
                                  itemValue: item?.orders[0].quantity != null
                                      ? item?.orders[0].quantity.toString()
                                      : "",
                                ),
                                const SizedBox(height: 8),
                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: stockId.isNotEmpty &&
                                          item?.orders[0].itemName
                                                  .tableType() ==
                                              "CARDAMOM"
                                      ? LiveCardTable(res: res)
                                      : const SizedBox(),
                                ),
                                // CardamomTable(
                                //   grade8MmClean: matrix?.grade8MmClean,
                                //   grade8MmSickdsplit:
                                //       matrix?.grade8MmSickdsplit,
                                //   grade8MmFruit: matrix?.grade8MmFruit,
                                //   grade7T8MmClean: matrix?.grade7T8MmClean,
                                //   grade7T8MmSickdsplit:
                                //       matrix?.grade7T8MmSickdsplit,
                                //   grade7T8MmFruit: matrix?.grade7T8MmFruit,
                                //   grade17MmClean: matrix?.grade17MmClean,
                                //   grade17MmSickdsplit:
                                //       matrix?.grade17MmSickdsplit,
                                //   grade17MmFruit: matrix?.grade17MmFruit,
                                //   totalPercentage: matrix?.totalPercentage,
                                //   size: matrix?.size,
                                //   literWeight: matrix?.ltrWt,
                                //   moisture: matrix?.moisture,
                                //   colour: matrix?.colour,
                                //   totalClean: matrix?.totalClean,
                                //   rejectionsClean: matrix?.rejectionsClean,
                                //   rejectionsSickdsplit:
                                //       matrix?.rejectionsSickdsplit,
                                //   rejectionsFruit: matrix?.rejectionsFruit,
                                //   quantity: item?.orders[0].quantity,
                                // ),
                                //TableWidgetLive(data: item)
                              ],
                            ),
                          ),

                          /// fourth page
                          SingleChildScrollView(
                            physics: const BouncingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            child: Column(
                              children: [
                                TodaysBidItem(
                                  itemName: 'Lot Number',
                                  itemValue: item?.orders[0].lotNo.toString(),
                                ),
                                TodaysBidItem(
                                  itemName: 'Category',
                                  itemValue: item?.orders[0].itemName,
                                ),
                                TodaysBidItem(
                                  itemName: 'Quantity',
                                  itemValue:
                                      item?.orders[0].quantity.toString(),
                                ),
                                GridView.count(
                                  padding: const EdgeInsets.all(8),
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  crossAxisCount: 2,
                                  children: List.generate(
                                    item?.orders[0].images.length ?? 0,
                                    (index) {
                                      var currentImage =
                                          item?.orders[0].images[index];
                                      return Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: currentImage != null
                                            ? GestureDetector(
                                                onTap: () {
                                                  showImageDialog(
                                                      context, currentImage);
                                                },
                                                child:
                                                    Image.network(currentImage))
                                            : Image.asset(
                                                "assets/images/no_image_available.jpeg"),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),

                          /// fifth page
                          item?.orders[0].itemName.tableType() == "CARDAMOM"
                              ? SingleChildScrollView(
                                  physics: const BouncingScrollPhysics(
                                      parent: AlwaysScrollableScrollPhysics()),
                                  child: Column(
                                    children: [
                                      TodaysBidItem(
                                        itemName: 'Lot Number',
                                        itemValue:
                                            item?.orders[0].lotNo.toString(),
                                      ),
                                      TodaysBidItem(
                                        itemName: 'Category',
                                        itemValue: item?.orders[0].itemName,
                                      ),
                                      TodaysBidItem(
                                        itemName: 'Quantity',
                                        itemValue:
                                            item?.orders[0].quantity.toString(),
                                      ),
                                      const SizedBox(height: 16),
                                      Container(
                                        height: 240,
                                        color: Colors.black,
                                        child: charts.BarChart(
                                          LiveAuctions.createData(item),
                                          animate: true,
                                          barGroupingType:
                                              charts.BarGroupingType.grouped,
                                          // Add the series legend behavior to the chart to turn on series legends.
                                          // By default the legend will display above the chart.
                                          behaviors: [charts.SeriesLegend()],
                                          domainAxis: charts.OrdinalAxisSpec(
                                            renderSpec:
                                                charts.SmallTickRendererSpec(
                                              labelStyle: charts.TextStyleSpec(
                                                fontSize: 12,
                                                color: charts.ColorUtil
                                                    .fromDartColor(
                                                        Colors.white),
                                              ),
                                            ),
                                          ),
                                          primaryMeasureAxis:
                                              charts.NumericAxisSpec(
                                            renderSpec:
                                                charts.SmallTickRendererSpec(
                                              labelStyle: charts.TextStyleSpec(
                                                fontSize: 12,
                                                color: charts.ColorUtil
                                                    .fromDartColor(
                                                        Colors.white),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                        ],
                      ),
                    ),

                    /// bottom part
                    Container(
                      color: color,
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const SizedBox(width: 12),
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      const TextSpan(
                                          text: "Lot No : ",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold)),
                                      TextSpan(
                                          text: liveBiddingStockNo
                                              .toString()
                                              .checkIfClosedAndReplaceWithZero(
                                                  liveAuctionStatus.toString()),
                                          style: const TextStyle(
                                              color: Colors.yellow,
                                              fontWeight: FontWeight.bold))
                                    ],
                                  ),
                                ),
                                const Spacer(),
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      const TextSpan(
                                          text: "Status : ",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold)),
                                      TextSpan(
                                          text: auctionStatus.checkClosed(
                                              liveAuctionStatus.toString()),
                                          style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold))
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 12),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Material(
                                    borderRadius: BorderRadius.circular(8.0),
                                    elevation: 8,
                                    child: Container(
                                      height: 64.0,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                          color: Colors.white),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                              "₹$highestBid"
                                                  .checkIfClosedAndReplaceWithZero(
                                                      liveAuctionStatus
                                                          .toString()),
                                              style: const TextStyle(
                                                  color: Colors.black,
                                                  fontFamily: 'poppins',
                                                  fontSize: 16)),
                                          const SizedBox(height: 8.0),
                                          Text(
                                            offerYn
                                                ? "Current Offer"
                                                : "Current Bid",
                                            style: const TextStyle(
                                              fontFamily: 'poppins',
                                              fontSize: 12,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Container(
                                    height: 64.0,
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        color: Colors.white),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                            "₹$yourBid"
                                                .checkIfClosedAndReplaceWithZero(
                                                    liveAuctionStatus
                                                        .toString()),
                                            style: TextStyle(
                                                color: color,
                                                fontFamily: 'poppins',
                                                fontSize: 16)),
                                        const SizedBox(height: 8.0),
                                        Text(
                                          offerYn == true
                                              ? "Your Offer"
                                              : "Your Bid",
                                          style: const TextStyle(
                                            fontFamily: 'poppins',
                                            fontSize: 12,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, right: 8.0),
                                  child: SizedBox(
                                    height: 64.0,
                                    child: Material(
                                      elevation: 8,
                                      borderRadius: BorderRadius.circular(8.0),
                                      color: Colors.white,
                                      clipBehavior: Clip.hardEdge,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: InkWell(
                                                borderRadius:
                                                    BorderRadius.circular(8.0),
                                                onTap: () async {
                                                  if (auctionStatus ==
                                                          "Close" ||
                                                      highestBid == 0) {
                                                    return;
                                                  }
                                                  safePrint(highestBid);
                                                  safePrint(myBid);
                                                  safePrint(
                                                      liveBiddingIncrementValue);
                                                  // if (liveAuctionStatus.toString() !=
                                                  //     "0" || liveAuctionStatus.toString() !=
                                                  //     "1") {
                                                  //   return;
                                                  // }
                                                  if (isClickable &&
                                                      getState(state)
                                                              ?.list
                                                              ?.status !=
                                                          0) {
                                                    if (getState(state)
                                                            ?.list
                                                            ?.data
                                                            .orders[0]
                                                            .customerId ==
                                                        getCustomerId()) {
                                                      alert(
                                                          "You are the seller of this stock");
                                                      return;
                                                    }
                                                    if (mounted) {
                                                      setState(() {
                                                        safePrint(
                                                            "checkkkkkkkkkkkkkkkkkkkk");
                                                        safePrint(
                                                            getState(state)
                                                                ?.bidSuccess);
                                                        if (myBid <=
                                                            highestBid +
                                                                liveBiddingIncrementValue) {
                                                          myBid = highestBid
                                                                  .toDouble() +
                                                              liveBiddingIncrementValue
                                                                  .toDouble();
                                                          // yourBid = myBid;
                                                        }
                                                      });
                                                    }
                                                    safePrint(myBid);

                                                    var date = DateTime.now()
                                                        .toLocal()
                                                        .toIso8601String();
                                                    var formattedDate =
                                                        date.substring(
                                                            0, date.length - 3);
                                                    safePrint(
                                                        "cust id ${getCustomerId()} api cust id ${getState(state)?.list?.data.orders[0].customerId}");
                                                    if (lastBid ==
                                                        myBid.toDouble()) {
                                                      return;
                                                    }
                                                    context
                                                        .read<LiveAuctionBloc>()
                                                        .add(
                                                          Bid(
                                                            stockId: int.parse(
                                                                (await LiveUtils()
                                                                    .getStockId())),
                                                            amount: myBid
                                                                .toDouble(),
                                                            bidDate:
                                                                "${formattedDate}Z",
                                                            quantity: getState(
                                                                        state)
                                                                    ?.list!
                                                                    .data
                                                                    .orders[0]
                                                                    .orderSpecifications[
                                                                        0]
                                                                    .totalWeight ??
                                                                0,
                                                            auctionId:
                                                                int.parse("0"),
                                                            vendorId: getState(
                                                                        state)
                                                                    ?.list!
                                                                    .data
                                                                    .orders[0]
                                                                    .sellerVendorId ??
                                                                0,
                                                            highestBid:
                                                                highestBid
                                                                    .toDouble(),
                                                            incrementValue:
                                                                liveBiddingIncrementValue
                                                                    .toDouble(),
                                                          ),
                                                        );
                                                    setState(() {
                                                      lastBid =
                                                          myBid.toDouble();
                                                    });
                                                    // play1();
                                                  }
                                                },
                                                onLongPress: () {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) {
                                                      return IncreaseBidAmountPopup(
                                                        title: "",
                                                        parentContext: context,
                                                        currentBid: highestBid
                                                            .toDouble(),
                                                      );
                                                    },
                                                  ).then((value) async {
                                                    if (value == null) {
                                                      return;
                                                    } else {
                                                      if (auctionStatus ==
                                                              "Close" ||
                                                          highestBid == 0) {
                                                        return;
                                                      }
                                                      if (isClickable &&
                                                          getState(state)
                                                                  ?.list
                                                                  ?.status !=
                                                              0) {
                                                        if (getState(state)
                                                                ?.list
                                                                ?.data
                                                                .orders[0]
                                                                .customerId ==
                                                            getCustomerId()) {
                                                          alert(
                                                              "You are the seller of this stock");
                                                          return;
                                                        }
                                                        if (mounted) {
                                                          setState(() {
                                                            if (myBid <=
                                                                highestBid +
                                                                    liveBiddingIncrementValue) {
                                                              myBid = highestBid
                                                                      .toDouble() +
                                                                  liveBiddingIncrementValue
                                                                      .toDouble();
                                                              // yourBid = value[0];
                                                            }
                                                          });
                                                        }

                                                        var date = DateTime
                                                                .now()
                                                            .toLocal()
                                                            .toIso8601String();
                                                        var formattedDate =
                                                            date.substring(
                                                                0,
                                                                date.length -
                                                                    3);

                                                        if (lastBid ==
                                                            value[0]
                                                                .toDouble()) {
                                                          return;
                                                        }

                                                        context
                                                            .read<
                                                                LiveAuctionBloc>()
                                                            .add(
                                                              Bid(
                                                                stockId: int.parse(
                                                                    (await LiveUtils()
                                                                        .getStockId())),
                                                                amount: value[0]
                                                                    .toDouble(),
                                                                bidDate:
                                                                    "${formattedDate}Z",
                                                                quantity: getState(
                                                                            state)
                                                                        ?.list!
                                                                        .data
                                                                        .orders[
                                                                            0]
                                                                        .orderSpecifications[
                                                                            0]
                                                                        .totalWeight ??
                                                                    0,
                                                                auctionId:
                                                                    int.parse(
                                                                        "0"),
                                                                vendorId: getState(
                                                                            state)
                                                                        ?.list!
                                                                        .data
                                                                        .orders[
                                                                            0]
                                                                        .sellerVendorId ??
                                                                    0,
                                                                highestBid:
                                                                    highestBid
                                                                        .toDouble(),
                                                                incrementValue:
                                                                    liveBiddingIncrementValue
                                                                        .toDouble(),
                                                                customBid: true,
                                                              ),
                                                            );
                                                        setState(() {
                                                          lastBid = value[0]
                                                              .toDouble();
                                                        });
                                                        // play1();
                                                      }
                                                    }
                                                  });
                                                },
                                                child: SizedBox(
                                                  height: 64.0,
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                          "+$liveBiddingIncrementValue"
                                                              .checkIfClosedAndReplaceWithZero(
                                                                  liveAuctionStatus
                                                                      .toString()),
                                                          style: const TextStyle(
                                                              color: AppColors
                                                                  .green,
                                                              fontFamily:
                                                                  'poppins',
                                                              fontSize: 16)),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const VerticalDivider(),
                                            DotColumn(onTap: () {
                                              showDialog(
                                                context: context,
                                                builder: (context) {
                                                  return IncreaseBidAmountPopup(
                                                    title: "",
                                                    parentContext: context,
                                                    currentBid:
                                                        highestBid.toDouble(),
                                                  );
                                                },
                                              ).then((value) async {
                                                if (value == null) {
                                                  return;
                                                } else {
                                                  if (auctionStatus ==
                                                          "Close" ||
                                                      highestBid == 0) {
                                                    return;
                                                  }
                                                  if (isClickable &&
                                                      getState(state)
                                                              ?.list
                                                              ?.status !=
                                                          0) {
                                                    if (getState(state)
                                                            ?.list
                                                            ?.data
                                                            .orders[0]
                                                            .customerId ==
                                                        getCustomerId()) {
                                                      alert(
                                                          "You are the seller of this stock");
                                                      return;
                                                    }
                                                    if (mounted) {
                                                      setState(() {
                                                        if (myBid <=
                                                            highestBid +
                                                                liveBiddingIncrementValue) {
                                                          myBid = highestBid
                                                                  .toDouble() +
                                                              liveBiddingIncrementValue
                                                                  .toDouble();
                                                          // yourBid = value[0];
                                                        }
                                                      });
                                                    }

                                                    var date = DateTime.now()
                                                        .toLocal()
                                                        .toIso8601String();
                                                    var formattedDate =
                                                        date.substring(
                                                            0, date.length - 3);

                                                    if (lastBid ==
                                                        value[0].toDouble()) {
                                                      return;
                                                    }

                                                    context
                                                        .read<LiveAuctionBloc>()
                                                        .add(
                                                          Bid(
                                                            stockId: int.parse(
                                                                (await LiveUtils()
                                                                    .getStockId())),
                                                            amount: value[0]
                                                                .toDouble(),
                                                            bidDate:
                                                                "${formattedDate}Z",
                                                            quantity: getState(
                                                                        state)
                                                                    ?.list!
                                                                    .data
                                                                    .orders[0]
                                                                    .orderSpecifications[
                                                                        0]
                                                                    .totalWeight ??
                                                                0,
                                                            auctionId:
                                                                int.parse("0"),
                                                            vendorId: getState(
                                                                        state)
                                                                    ?.list!
                                                                    .data
                                                                    .orders[0]
                                                                    .sellerVendorId ??
                                                                0,
                                                            highestBid:
                                                                highestBid
                                                                    .toDouble(),
                                                            incrementValue:
                                                                liveBiddingIncrementValue
                                                                    .toDouble(),
                                                            customBid: true,
                                                          ),
                                                        );
                                                    setState(() {
                                                      lastBid =
                                                          value[0].toDouble();
                                                    });
                                                    // play1();
                                                  }
                                                }
                                              });
                                            }),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 8, left: 8, right: 8),
                            child: Center(
                              child: Text(
                                message
                                    .checkClosed(liveAuctionStatus.toString()),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: getTextColor(color)),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                showActiveBuyerInfo()
                                    ? Visibility(
                                        visible: highestBid != 0 &&
                                            showActiveBuyerCount,
                                        child: Text(
                                          "No of Active Buyers: $count",
                                          style: const TextStyle(
                                            color: AppColors.white,
                                            // fontFamily: 'avenir_black',
                                            fontSize: 14,
                                          ),
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                const Spacer(),
                                marginEnabled()
                                    ? Visibility(
                                        visible: DataStorage.configData
                                                    ?.firstWhereOrNull(
                                                        (element) =>
                                                            element?.keyName1 ==
                                                            "margin_required_yn")
                                                    ?.valueName1 ==
                                                "Y" &&
                                            highestBid != 0,
                                        child: Text(
                                            "Margin: ${numberFormat.format(margin).checkIfClosedAndReplaceWithZero(liveAuctionStatus.toString())}",
                                            style: const TextStyle(
                                                color: AppColors.white,
                                                fontFamily: 'poppins',
                                                fontSize: 14)),
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
        // } else {
        //   return Container(
        //     color: Colors.white,
        //     child: Center(
        //       child: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           circularProgressIndicator,
        //           const SizedBox(height: 16),
        //           const Text("Loading..."),
        //         ],
        //       ),
        //     ),
        //   );
        // }
      },
    );
  }

  void _marginPercentageLogic(Loaded? state) {
    var configData = DataStorage.configData?.firstWhereOrNull(
        (element) => element?.keyName1 == "margin_required_yn");

    if (configData?.valueName1 == "Y") {
      var marginPercentageData = DataStorage.configData?.firstWhereOrNull(
          (element) => element?.keyName1 == "margin_percentage");
      var percentage = marginPercentageData?.valueName1 ?? "0";

      var totalVolume = state?.list?.data.orders[0].quantity ?? 0;
      var currentBid = highestBid;

      var totalAmount = totalVolume * currentBid;
      var marginValue = double.parse(percentage) / 100 * totalAmount;

      safePrint("Calculationdata");

      safePrint(totalVolume);
      safePrint(currentBid);
      safePrint(percentage);
      safePrint(totalAmount);
      safePrint(marginValue);

      margin = marginValue.toDouble();
      //setState(() {
      isShowMargin = true;
      //});
    } else {
      //setState(() {
      isShowMargin = false;
      //});
    }
  }

  void _activeBuyerLogic() {
    if (DataStorage.configData
            ?.firstWhereOrNull(
                (element) => element?.keyName1 == "active_buyer_info_yn")
            ?.valueName1 ==
        "Y") {
      if (highestBidCustomerId != getCustomerId()) {
        showActiveBuyerCount = true;
      } else {
        showActiveBuyerCount = false;
      }
    }
  }

  void tab5Selected() {
    tab5 = true;
    tab2 = false;
    tab3 = false;
    tab4 = false;
    tab1 = false;
    title = "Matrix";
  }

  void tab4Selected() {
    tab4 = true;
    tab2 = false;
    tab3 = false;
    tab1 = false;
    tab5 = false;
    title = "Graph";
  }

  void tab3Selected() {
    tab3 = true;
    tab2 = false;
    tab1 = false;
    tab4 = false;
    tab5 = false;
    title = "Images";
  }

  void tab2Selected() {
    tab2 = true;
    tab1 = false;
    tab3 = false;
    tab4 = false;
    tab5 = false;
    title = "Lot Details";
  }

  void tab1Selected() {
    tab1 = true;
    tab2 = false;
    tab3 = false;
    tab4 = false;
    tab5 = false;
    title = "Stats";
  }
}

/// Sample ordinal data type.
class OrdinalSales {
  final String year;
  final int sales;
  final charts.Color barColor;

  OrdinalSales(this.year, this.sales, this.barColor);
}

class TodaysBidItem extends StatelessWidget {
  const TodaysBidItem(
      {Key? key, required this.itemName, required this.itemValue})
      : super(key: key);

  final String? itemName;
  final String? itemValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 48,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3), // changes position of shadow
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(itemName!,
              style: const TextStyle(fontSize: 14, fontFamily: 'poppins')),
          Text(itemValue!,
              style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                  fontFamily: 'poppins')),
        ],
      ),
    );
  }
}

class LiveTab extends StatelessWidget {
  const LiveTab(
      {Key? key,
      required this.tab,
      required this.onClick,
      required this.image,
      required this.title})
      : super(key: key);

  final bool tab;
  final Function onClick;
  final String image;
  final String title;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick();
      },
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(4),
            padding: const EdgeInsets.all(4),
            height: 56,
            width: 56,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              color: tab ? AppColors.primaryColor : AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3), // changes position of shadow
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Image(
                color: tab ? AppColors.white : AppColors.primaryColor,
                image: AssetImage(image),
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
                color: AppColors.primaryColor, fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          )
        ],
      ),
    );
  }
}

extension LiveAuctionExtension on String {
  String checkClosed(String key) {
    if (key == "3") {
      return "";
    } else {
      return this;
    }
  }

  String checkIfClosedAndReplaceWithZero(String key) {
    if (key == "3") {
      return "0";
    } else {
      return this;
    }
  }
}
