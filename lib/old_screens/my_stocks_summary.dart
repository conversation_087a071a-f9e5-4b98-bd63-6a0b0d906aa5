import 'package:connectone/core/old_widgets/common/loading_overlay.dart';
import 'package:connectone/old_models/sold_model.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../core/old_widgets/common/card_view_stocks.dart';
import '../core/network/network_controller.dart';
import '../old_models/total_stock_mystock.dart';
import '../core/utils/colors.dart';
import '../core/utils/constants.dart';
import '../core/utils/safe_print.dart';

class MyStocksSummary extends StatefulWidget {
  const MyStocksSummary({
    Key? key,
    required this.isSold,
    required this.onGoPressed,
  }) : super(key: key);

  final bool isSold;
  final Function onGoPressed;

  @override
  State<MyStocksSummary> createState() => _MyStocksSummaryState();
}

class _MyStocksSummaryState extends State<MyStocksSummary>
    with AutomaticKeepAliveClientMixin {
  int _page = 0;

  final int _limit = 10;

  bool _isFirstLoadRunning = false;
  bool _hasNextPage = true;

  bool _isLoadMoreRunning = false;

  final List<Content> _posts = [];

  int len = 0;
  bool st = true;
  DateTime now = DateTime.now().toUtc();
  DateTime nowFrom = DateTime.now().toUtc().subtract(const Duration(days: 14));
  DateTime nowTo = DateTime.now().toUtc();

  void _loadMore() async {
    if (_hasNextPage == true &&
        _isFirstLoadRunning == false &&
        _isLoadMoreRunning == false &&
        _controller.position.extentAfter < 300) {
      setState(() {
        _isLoadMoreRunning = true;
      });

      _page += 1;

      try {
        var response;
        if (widget.isSold) {
          response = await NetworkController().getSoldTable(
              getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
        } else {
          response = await NetworkController().getBoughtTable(
              getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
        }
        var a = response;
        if (a.content.isNotEmpty) {
          setState(() {
            _posts.addAll(a.content!);
          });
        } else {
          setState(() {
            _hasNextPage = false;
          });
        }
      } catch (err) {
        if (kDebugMode) {
          safePrint('$err//////////////////////////////////111111111111111111');
        }
      }

      setState(() {
        _isLoadMoreRunning = false;
      });
    }
  }

  void _firstLoad() async {
    setState(() {
      _isFirstLoadRunning = true;
    });

    try {
      var response;
      if (widget.isSold) {
        response = await NetworkController()
            .getSoldTable(getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
      } else {
        response = await NetworkController().getBoughtTable(
            getCustomerId(), '$nowFrom', '$nowTo', _page, _limit);
      }

      var a = response;

      setState(() {
        _posts.addAll(a.content!);
        len = _posts.length;
      });
    } catch (err) {
      if (kDebugMode) {
        safePrint('$err//////////////////////////////////');
      }
    }

    setState(() {
      _isFirstLoadRunning = false;
    });
  }

  var list = TotalStockSold(
    noOfStocksSold: 0,
    totalAmountOfSoldStocks: 0,
    totalQuantitySold: 0,
  );

  Future fetchSold(String customer) async {
    safePrint('33333333333333333333333333334');
    try {
      var response1 = await NetworkController().dio.get(
          '$baseAppUrl/apis/bidding/customer/stock-summary-sold/$customer');

      var json = response1.data;
      safePrint('11111111111111111111111111122zz$response1');

      setState(() {
        list = TotalStockSold.fromJson(json);
      });
    } on DioException catch (error) {
      safePrint('9090$error');
    }
  }

  Future fetchBought(String customer) async {
    safePrint('33333333333333333333333333334');
    try {
      var response1 = await NetworkController().dio.get(
          '$baseAppUrl/apis/bidding/customer/stock-summary-bought/$customer');

      var json = response1.data;
      safePrint('11111111111111111111111111122zz$response1');
      setState(() {
        var bought = TotalStockBought.fromJson(json);
        list = TotalStockSold(
          noOfStocksSold: bought.noOfStocksBought,
          totalAmountOfSoldStocks: bought.totalAmountOfBoughtStocks,
          totalQuantitySold: bought.totalQuantityBought,
        );
      });
    } on DioException catch (error) {
      safePrint('9090$error');
    }
  }

  late ScrollController _controller;

  @override
  void initState() {
    super.initState();
    if (marginEnabled()) {
      _controller = ScrollController()..addListener(_loadMore);
      _firstLoad();
      if (widget.isSold) {
        fetchSold(getCustomerId());
      } else {
        fetchBought(getCustomerId());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  'From',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25)),
                      side: const BorderSide(color: Colors.black)),
                  onPressed: () async {
                    setState(() {
                      st = false;
                    });
                    DateTime? picked = await showDatePicker(
                      initialEntryMode: DatePickerEntryMode.calendar,
                      context: context,
                      initialDatePickerMode: DatePickerMode.year,
                      initialDate: DateTime(2015),
                      firstDate: DateTime(2015),
                      lastDate: DateTime.now(),
                    );
                    setState(() {
                      nowFrom = picked ?? DateTime.now();
                    });
                  },
                  child: Text(
                    DateFormat('dd-MM-yyyy').format(nowFrom),
                    style: const TextStyle(color: Colors.black),
                  ),
                )
              ],
            ),
            const SizedBox(
              width: 16,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text(
                  'To',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                      side: const BorderSide(color: Colors.black),
                    ),
                  ),
                  onPressed: () async {
                    setState(() {
                      st = false;
                    });
                    DateTime? picked = await showDatePicker(
                        initialEntryMode: DatePickerEntryMode.calendar,
                        context: context,
                        initialDatePickerMode: DatePickerMode.year,
                        initialDate: DateTime(2015),
                        firstDate: DateTime(2015),
                        lastDate:
                            DateTime.now().add(const Duration(days: 365)));
                    setState(() {
                      nowTo = picked ?? DateTime.now();
                    });
                  },
                  child: Text(
                    DateFormat('dd-MM-yyyy').format(nowTo),
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              width: 16,
            ),
            Column(
              children: [
                const SizedBox(
                  height: 20,
                ),
                TextButton(
                  style: TextButton.styleFrom(
                      backgroundColor: AppColors.primaryColor),
                  onPressed: () {
                    widget.onGoPressed(nowFrom, nowTo);
                    if (marginEnabled()) {
                      setState(() {
                        _page = 0;
                        _posts.clear();
                        st = true;
                        if (_isFirstLoadRunning || _isLoadMoreRunning) {
                          return;
                        }
                        _firstLoad();
                      });
                    }
                  },
                  child: const Text(
                    'Go',
                    style: TextStyle(color: Colors.white),
                  ),
                )
              ],
            )
          ],
        ),
        marginEnabled()
            ? Column(
                children: [
                  const SizedBox(height: 16),
                  const Text(
                    'Summary',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        CardViewMyStocksSoldOut(
                          number: list.noOfStocksSold?.toString() ?? '0',
                          label: totalStocks,
                          suffix: "",
                        ),
                        CardViewMyStocksSoldOut(
                          number: list.totalQuantitySold?.toString() ?? '0',
                          label: totalQty,
                          suffix: "Kg",
                        ),
                        CardViewMyStocksSoldOut(
                          number:
                              list.totalAmountOfSoldStocks?.toString() ?? '0',
                          label: totalAmnt,
                          suffix: "",
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  const Text(
                    'Stocks List',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Visibility(
                    visible: true,
                    child: LoadingOverlay(
                      isLoading: _isLoadMoreRunning || _isFirstLoadRunning,
                      child: SizedBox(
                        height: MediaQuery.of(context).size.height / 2.45,
                        child: len != 0
                            ? Column(
                                children: [
                                  // const SizedBox(height: 8),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: SizedBox(
                                      // color: Colors.green,
                                      height:
                                          MediaQuery.of(context).size.height /
                                              2.6,
                                      width: double.infinity,
                                      child: Column(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  const BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10),
                                              ),
                                              color: AppColors.primaryColor,
                                            ),
                                            width: double.infinity,
                                            height: 40,
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                const Expanded(
                                                  flex: 1,
                                                  child: Center(
                                                    child: Text(
                                                      'Date',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const VerticalDivider(
                                                  color: Colors.white,
                                                  width: 10,
                                                  thickness: 1,
                                                  indent: 0,
                                                  endIndent:
                                                      0, //Spacing at the bottom of divider.
                                                ),
                                                const Expanded(
                                                  flex: 1,
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        'Stock Id / Qty',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                const VerticalDivider(
                                                  color: Colors.white,
                                                  width: 10,
                                                  thickness: 1,
                                                  indent: 0,
                                                  endIndent:
                                                      0, //Spacing at the bottom of divider.
                                                ),
                                                Expanded(
                                                  flex: 1,
                                                  child: Center(
                                                    child: Text(
                                                      '${widget.isSold == true ? "Buyer" : "Seller"} Name',
                                                      style: const TextStyle(
                                                        fontSize: 12,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const VerticalDivider(
                                                  color: Colors.white,
                                                  width: 10,
                                                  thickness: 1,
                                                  indent: 0,
                                                  endIndent:
                                                      0, //Spacing at the bottom of divider.
                                                ),
                                                const Expanded(
                                                  flex: 1,
                                                  child: Column(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        'Total Sales\nAmt',
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Colors.white,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Container(
                                              decoration: BoxDecoration(
                                                  // color: Colors.deepOrange,
                                                  borderRadius:
                                                      const BorderRadius.only(
                                                          bottomLeft:
                                                              Radius.circular(
                                                                  20.0),
                                                          bottomRight:
                                                              Radius.circular(
                                                                  20.0)),
                                                  border: Border.all(
                                                    color: Colors.grey.shade300,
                                                  )),
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height /
                                                  3.1,
                                              width: double.infinity,
                                              child: ListView.separated(
                                                controller: _controller,
                                                separatorBuilder:
                                                    (context, index) =>
                                                        const Divider(
                                                  thickness: 2,
                                                ),
                                                physics: const ScrollPhysics(),
                                                itemBuilder: (context, index) {
                                                  return Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 10,
                                                        vertical: 5),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceEvenly,
                                                      children: [
                                                        Expanded(
                                                          flex: 1,
                                                          child: Text(
                                                            DateFormat(
                                                                    'dd-MM-yyyy')
                                                                .format(_posts[
                                                                            index]
                                                                        .lotSoldAt ??
                                                                    DateTime
                                                                        .now()),
                                                            style:
                                                                const TextStyle(
                                                                    fontSize:
                                                                        12),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 1,
                                                          child: Column(
                                                            children: [
                                                              Text(
                                                                '${_posts[index].stockId}/\n${_posts[index].quantity}',
                                                                style:
                                                                    const TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 1,
                                                          child: Center(
                                                            child: Text(
                                                              widget.isSold
                                                                  ? ' ${_posts[index].buyerName ?? '--'}'
                                                                  : ' ${_posts[index].sellerName ?? '--'}',
                                                              style: const TextStyle(
                                                                  fontSize: 12,
                                                                  color: Colors
                                                                      .red),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                            ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          flex: 1,
                                                          child: Center(
                                                            child: Text(
                                                              ' ${_posts[index].totalPurchaseAmount}',
                                                              style:
                                                                  const TextStyle(
                                                                      fontSize:
                                                                          12),
                                                            ),
                                                          ),
                                                        ),
                                                        const Divider(
                                                          thickness: 5,
                                                        )
                                                      ],
                                                    ),
                                                  );
                                                },
                                                itemCount: _posts.length,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            : Container(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  // color: Colors.blueAccent,
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Center(
                                  child: Text('No data to show here!'),
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              )
            : const SizedBox.shrink(),
      ],
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
