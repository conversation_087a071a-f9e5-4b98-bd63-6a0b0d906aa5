import 'package:connectone/old_blocs/forgot_password/forgot_password_bloc.dart';
import 'package:connectone/old_blocs/forgot_password/forgot_password_state.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/utils/custom_border.dart';
import 'package:connectone/core/utils/decorations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

//import 'package:sms_autofill/sms_autofill.dart';

import '../core/network/network_controller.dart';
import '../core/utils/circular_progress.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController usernameController = TextEditingController();

  final TextEditingController authCodeController = TextEditingController();

  var authCode = "";

  final TextEditingController newPassword = TextEditingController();

  final TextEditingController confirmPassword = TextEditingController();
  NetworkController networkController = NetworkController();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ForgotPasswordBloc>(
          create: (BuildContext context) =>
              ForgotPasswordBloc(ForgotPasswordState()),
        )
      ],
      child: Scaffold(
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarBrightness: Brightness.light,
            statusBarColor: AppColors.primaryColor,
          ),
          backgroundColor: AppColors.primaryColor,
          elevation: 0,
        ),
        body: BlocBuilder<ForgotPasswordBloc, ForgotPasswordState>(
            builder: (context, state) {
          if (state.isLoading) {
            return Center(
              child: progressIndicator,
            );
          }
          return WillPopScope(
            onWillPop: () {
              if (state.confirmationCode) {
                context.read<ForgotPasswordBloc>().willPopClick();
                return Future.value(false);
              } else {
                return Future.value(true);
              }
            },
            child: Center(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                          networkController
                                  .organisationData?.organizationName ??
                              "",
                          style: const TextStyle(
                            fontSize: 20, color: Colors.black,
                            // fontFamily: 'avenir_black',
                          )),
                      const SizedBox(
                        height: 20,
                      ),
                      // Image.asset(
                      //   iconUrl,
                      //   height: 80,
                      // ),
                      Container(
                        height: 80,
                        width: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          image: networkController
                                      .organisationData?.orderingAppUrl !=
                                  null
                              ? DecorationImage(
                                  image: NetworkImage(networkController
                                          .organisationData?.orderingAppUrl ??
                                      ""),
                                  fit: BoxFit.cover)
                              : const DecorationImage(
                                  image: AssetImage(
                                      'assets/images/no_image_available.jpeg'),
                                  fit: BoxFit.cover),
                        ),
                      ),
                      const SizedBox(height: 32),
                      Visibility(
                        visible: !state.confirmationCode,
                        child: const Text('Forgot Password?',
                            style: TextStyle(
                              fontSize: 20, color: Colors.black,
                              fontWeight: FontWeight.bold,
                              // fontFamily: 'avenir_light',
                            )),
                      ),
                      Visibility(
                        visible: state.confirmationCode,
                        child: const Text('Reset Password',
                            style: TextStyle(
                              fontSize: 20, color: Colors.black,
                              fontWeight: FontWeight.bold,
                              // fontFamily: 'avenir_light',
                            )),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Visibility(
                        visible: state.confirmationCode,
                        child: Text(
                          isPhoneNumber
                              ? "Verification code has been send to +91${usernameController.text}"
                              : "Verification code has been send to ${usernameController.text}",
                          textAlign: TextAlign.center,
                          // style: const TextStyle(fontFamily: 'avenir_light'),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Visibility(
                        visible: !state.confirmationCode,
                        child: Visibility(
                          visible: !isPhoneNumber,
                          child: Form(
                            autovalidateMode: AutovalidateMode.always,
                            child: TextFormField(
                                validator: validateEmail,
                                enabled: state.emailEditable,
                                controller: usernameController,
                                decoration: getLoginInputDecoration(
                                    "Email", Icons.email, true),
                                keyboardType: TextInputType.emailAddress),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: !state.confirmationCode,
                        child: Visibility(
                          visible: isPhoneNumber,
                          child: Form(
                            autovalidateMode: AutovalidateMode.always,
                            child: TextFormField(
                              validator: validatephone,
                              enabled: state.emailEditable,
                              controller: usernameController,
                              decoration: getLoginInputDecoration(
                                  "Phone Number", Icons.phone_android, false),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Visibility(
                        visible: state.confirmationCode,
                        child: TextField(
                          controller: authCodeController,
                          keyboardType: TextInputType.number,
                          maxLength: 6,
                          decoration: InputDecoration(
                            prefixIcon: Icon(Icons.email,
                                size: 20, color: AppColors.primaryColor),
                            hintText: 'Recovery Code',
                            focusedBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            enabledBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            border: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            contentPadding: const EdgeInsets.all(15),
                            //filled: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Visibility(
                        visible: state.confirmationCode,
                        child: TextField(
                          controller: newPassword,
                          decoration: InputDecoration(
                            prefixIcon: Icon(Icons.lock,
                                size: 20, color: AppColors.primaryColor),
                            hintText: 'New Password',
                            focusedBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            enabledBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            border: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            contentPadding: const EdgeInsets.all(15),
                            //filled: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Visibility(
                        visible: state.confirmationCode,
                        child: TextField(
                          controller: confirmPassword,
                          decoration: InputDecoration(
                            prefixIcon: Icon(Icons.lock,
                                size: 20, color: AppColors.primaryColor),
                            hintText: 'Confirm Password',
                            focusedBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            enabledBorder: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            border: SelectedInputBorderWithShadow(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: .1)),
                            contentPadding: const EdgeInsets.all(15),
                            //filled: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.maxFinite,
                        height: 45,
                        child: TextButton(
                          style: TextButton.styleFrom(
                              backgroundColor: AppColors.primaryColor,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10))),
                          onPressed: () async {
                            //print(authCode);
                            if (!state.confirmationCode) {
                              //await SmsAutoFill().listenForCode;
                              context
                                  .read<ForgotPasswordBloc>()
                                  .forgotPassword(usernameController.text);
                            } else {
                              context
                                  .read<ForgotPasswordBloc>()
                                  .confirmPassword(
                                      usernameController.text,
                                      authCodeController.text,
                                      newPassword.text,
                                      confirmPassword.text);
                            }
                          },
                          child: Text(
                            (!state.confirmationCode) ? sContinue : sVerifyOtp,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid email address'
        : null;
  }

  String? validatephone(String? value) {
    const pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
    final regex = RegExp(pattern);
    return value!.isNotEmpty && !regex.hasMatch(value)
        ? 'Enter a valid phone number'
        : null;
  }
}
