import 'package:equatable/equatable.dart';

abstract class OfflineFilterEvent extends Equatable {
  const OfflineFilterEvent();

  @override
  List<Object> get props => [];
}

class GetFilters extends OfflineFilterEvent {
  final String? category;

  const GetFilters(this.category);
}

class SaveSearch extends OfflineFilterEvent {
  final int? buyBidPriceFrom;
  final int? buyBidPriceTo;
  final List<String>? days;
  final int? priceFrom;
  final int? priceTo;
  final int? quantityFrom;
  final int? quantityTo;
  final String? recurrence;
  final String? searchName;
  final String? location;
  final String? product;

  const SaveSearch({
    this.searchName,
    this.buyBidPriceFrom,
    this.buyBidPriceTo,
    this.priceFrom,
    this.priceTo,
    this.quantityFrom,
    this.quantityTo,
    this.days,
    this.recurrence,
    this.location,
    this.product,
  });

  @override
  List<Object> get props => [];
}

class DeleteSearch extends OfflineFilterEvent {
  final int id;

  const DeleteSearch(this.id);

  @override
  List<Object> get props => [id];
}

class FilterChange extends OfflineFilterEvent {
  final int time;

  final List filterProducts;
  final List filterGrade;
  final List filterLocation;
  final String quantity;
  final String priceRange;
  final String buyBidPrice;
  final String deliveryDate;

  @override
  List<Object> get props => [time];

  const FilterChange(
      this.time,
      this.filterProducts,
      this.filterGrade,
      this.filterLocation,
      this.quantity,
      this.priceRange,
      this.buyBidPrice,
      this.deliveryDate);
}

class LoadMonthCalendar extends OfflineFilterEvent {
  final Set<int> stocksAvailableDays;

  @override
  List<Object> get props => [stocksAvailableDays];

  const LoadMonthCalendar(this.stocksAvailableDays);
}
