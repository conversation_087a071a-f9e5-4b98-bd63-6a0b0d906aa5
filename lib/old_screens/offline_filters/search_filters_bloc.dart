// ignore_for_file: invalid_use_of_visible_for_testing_member, must_be_immutable

import 'package:connectone/old_screens/offline_filters/search_events.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/network/network_controller.dart';
import '../../old_models/search_model.dart';

class SearchFilterBloc extends Bloc<SearchFilterEvent, SearchFilterState> {
  SearchFilterBloc() : super(Loading()) {
    final networkController = NetworkController();

    on<LoadFilters>((event, emit) async {
      emit(Loading());
      var data = await networkController.searchFilters();
      try {
        emit(SearchDataLoaded(data));
      } catch (_) {}
    });
  }
}

class SearchFilterState extends Equatable {
  @override
  List<Object> get props => [];
}

class Loading extends SearchFilterState {}

class SearchDataLoaded extends SearchFilterState {
  List<SearchModel> data = [];

  SearchDataLoaded(this.data);
}
