// ignore_for_file: invalid_use_of_visible_for_testing_member, must_be_immutable

import 'package:connectone/bai_models/bai_filter_res.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/network/network_controller.dart';

class OfflineFilterCubit extends Cubit<OfflineFilterState> {
  OfflineFilterCubit() : super(OfflineFilterState());
  final networkController = NetworkController();

  void getFilters(String? query, String code) async {
    emit(FilterLoading());
    try {
      BaiFilterRes data;

      // Conditional logic to determine which network call to make
      // if (code == 'OPOR' || code == 'EPOR' || code == 'CPOR') {
      // data = await networkController.getPoFilters("", code);
      // } else {
      data = await networkController.getBaiFilters(query ?? "", code);
      data.filters?.sort((a, b) {
        if (a.sequence == null) return 1;
        if (b.sequence == null) return -1;
        return a.sequence!.compareTo(b.sequence!);
      });
      // }

      // Ensure that data.filters is not null before proceeding
      if (data.filters != null) {
        List<List<String>> initialSelectedValues =
            List.generate(data.filters!.length, (_) => []);

        // Properly map dynamic filter values to RangeValues
        List<RangeValues> initialRangeValues =
            data.filters!.map<RangeValues>((filter) {
          return const RangeValues(
            0.0,
            100.0,
          );
        }).toList();

        // Emit the loaded state with the necessary data
        emit(FilterLoaded(
          data,
          selectedValues: initialSelectedValues,
          rangeValues: initialRangeValues,
        ));
      } else {
        throw Exception("No filters found in the data.");
      }
    } catch (e) {
      // Handle exceptions and print the error for debugging purposes
      alert(e.toString());
      safePrint(e.toString());
    }
  }

  void changedFilters(String queryString, String code) async {
    var initialState = state as FilterLoaded;
    emit(FilterLoading());
    try {
      BaiFilterRes data;

      // Conditional logic to determine which network call to make based on the code
      // if (code == 'OPOR' || code == 'EPOR' || code == 'CPOR') {
      // data = await networkController.getPoFilters(queryString, code);
      // } else {
      data = await networkController.getBaiFilters(queryString, code);
      data.filters?.sort((a, b) {
        if (a.sequence == null) return 1;
        if (b.sequence == null) return -1;
        return a.sequence!.compareTo(b.sequence!);
      });
      // }

      // Properly map dynamic filter values to RangeValues
      List<RangeValues> initialRangeValues =
          data.filters!.map<RangeValues>((filter) {
        return const RangeValues(
          0.0,
          100.0,
        );
      }).toList();

      // Emit the loaded state with the updated data, preserving the selected and range values
      emit(FilterLoaded(
        data,
        selectedValues: initialState.selectedValues,
        rangeValues: initialRangeValues,
      ));
    } catch (e) {
      // Handle exceptions and print the error for debugging purposes
      alert(e.toString());
      safePrint(e.toString());
    }
  }

  void updateSelectedValues(int index, List<String> values) {
    if (state is FilterLoaded) {
      final currentState = state as FilterLoaded;
      final newSelectedValues =
          List<List<String>>.from(currentState.selectedValues);
      newSelectedValues[index] = values;
      emit(FilterLoaded(
        currentState.filterRes,
        selectedValues: newSelectedValues,
        rangeValues: currentState.rangeValues,
      ));
    }
  }

  void updateRangeValues(int index, RangeValues values) {
    if (state is FilterLoaded) {
      final currentState = state as FilterLoaded;
      final newRangeValues = List<RangeValues>.from(currentState.rangeValues);
      newRangeValues[index] = values;
      emit(FilterLoaded(currentState.filterRes,
          selectedValues: currentState.selectedValues,
          rangeValues: newRangeValues));
    }
  }

  void clearFilters() {
    if (state is FilterLoaded) {
      final currentState = state as FilterLoaded;
      var data = currentState.filterRes;
      List<List<String>> initialSelectedValues =
          List.generate(data.filters!.length, (_) => []);
      // List<RangeValues> initialRangeValues = data.filters!.map((filter) {
      //   return RangeValues(
      //     filter.minValue?.toDouble() ?? 0.0,
      //     filter.maxValue?.toDouble() ?? 100.0,
      //   );
      // }).toList();
      List<RangeValues> initialRangeValues = data.filters!.map((filter) {
        return const RangeValues(
          0.0,
          100.0,
        );
      }).toList();
      emit(FilterLoaded(data,
          selectedValues: initialSelectedValues,
          rangeValues: initialRangeValues));
    }
  }

  void saveSearch() async {
    // await NetworkController().saveSearch(
    //   buybidPriceFrom: event.buyBidPriceFrom,
    //   buybidPriceTo: event.buyBidPriceTo,
    //   priceFrom: event.priceFrom,
    //   priceTo: event.priceTo,
    //   quantityFrom: event.quantityFrom,
    //   quantityTo: event.quantityTo,
    //   days: event.days,
    //   location: event.location,
    //   searchName: event.searchName,
    //   product: event.product,
    // );
  }

  void deleteSearch() async {
    await NetworkController().deleteSearch(id: 0);
  }

  // void filterChange() async {
  //   try {
  //     var data = await networkController.getBaiFilters();
  //     emit(FilterLoaded(filterRes: data));
  //   } catch (e) {
  //     safePrint(e);
  //   }
  // }
}

class OfflineFilterState extends Equatable {
  @override
  List<Object> get props => [];
}

class FilterLoading extends OfflineFilterState {}

class FilterLoaded extends OfflineFilterState {
  final BaiFilterRes filterRes;
  final List<List<String>> selectedValues;
  final List<RangeValues> rangeValues;

  FilterLoaded(
    this.filterRes, {
    required this.selectedValues,
    required this.rangeValues,
  });

  @override
  List<Object> get props => [filterRes, selectedValues, rangeValues];
}
