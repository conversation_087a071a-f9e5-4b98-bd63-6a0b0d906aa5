// import 'dart:collection';
//
// import 'package:connectone/components/calendar_header.dart';
// import 'package:connectone/models/auction_arg.dart';
// import 'package:connectone/screens/offline_filters/drop_down_style_button.dart';
// import 'package:connectone/screens/offline_filters/offline_bloc.dart';
// import 'package:connectone/screens/offline_filters/offline_events.dart';
// import 'package:connectone/screens/offline_filters/slider_connectone.dart';
// import 'package:connectone/utils/app_routes.dart';
// import 'package:connectone/utils/calendar_utils.dart';
// import 'package:connectone/utils/colors.dart';
// import 'package:connectone/utils/constants.dart';
// import 'package:connectone/utils/navigate_safe.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:intl/intl.dart';
// import 'package:syncfusion_flutter_sliders/sliders.dart';
// import 'package:table_calendar/table_calendar.dart';
//
// import '../../models/offline_filters_model.dart';
// import '../utils/circular_progress.dart';
// import 'offline_filters/dropdown_multi_select.dart';
// import 'offline_filters/filters_bloc.dart';
//
// class OfflineMonthWiseScreen extends StatefulWidget {
//   final String day;
//
//   const OfflineMonthWiseScreen({Key? key, required this.day}) : super(key: key);
//
//   @override
//   State<OfflineMonthWiseScreen> createState() => _OfflineFiltersState();
// }
//
// class _OfflineFiltersState extends State<OfflineMonthWiseScreen> {
//   late final PageController _pageController;
//   final ValueNotifier<DateTime> _focusedDay = ValueNotifier(DateTime.now());
//   final Set<DateTime> _selectedDays = LinkedHashSet<DateTime>(
//     equals: isSameDay,
//     hashCode: getHashCode,
//   );
//
//   final CalendarFormat _calendarFormat = CalendarFormat.month;
//   DateTime? _rangeStart;
//   DateTime? _rangeEnd;
//
//   var filterProducts = [];
//   var filterLocation = [];
//   var filterGrade = [];
//   bool buyNowSlider = false;
//   bool priceSlider = false;
//   bool quantitySlider = false;
//   SfRangeValues buyNowValues = const SfRangeValues(0.0, 0.0);
//   SfRangeValues priceSliderValues = const SfRangeValues(0.0, 0.0);
//   SfRangeValues quantityValues = const SfRangeValues(0.0, 0.0);
//   var filterDeliveryDate = [];
//
//   String quantity = '';
//   String priceRange = '';
//   String deliveryDate = '';
//   String buyBidPrice = '';
//
//   @override
//   void initState() {
//     super.initState();
//     _selectedDays.add(_focusedDay.value);
//   }
//
//   @override
//   void dispose() {
//     _focusedDay.dispose();
//     super.dispose();
//   }
//
//   bool get canClearSelection =>
//       _selectedDays.isNotEmpty || _rangeStart != null || _rangeEnd != null;
//
//   void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
//     setState(() {
//       if (_selectedDays.isNotEmpty) {
//         _selectedDays.clear();
//         _selectedDays.add(selectedDay);
//       } else {
//         _selectedDays.add(selectedDay);
//       }
//
//       _focusedDay.value = focusedDay;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<OfflineCubit, OfflineCubitStateHolder>(
//         builder: (context, state) {
//       OfflineCalenderRoot? data;
//       if (state is OfflineCalenderRoot) {
//         data = state;
//       }
//       return Scaffold(
//         appBar: AppBar(
//           title: Text(appTitle),
//           actions: [
//             IconButton(
//                 icon: Image.asset(
//                   "assets/images/icon_info_home_screen.png",
//                   width: 24.0,
//                   height: 24.0,
//                 ),
//                 onPressed: () {
//                   Get.toNamed(AppRoutes.helpScreen, arguments: ["CALENDAR"]);
//                 }),
//           ],
//         ),
//         body: Stack(
//           children: [
//             SingleChildScrollView(
//               child: Padding(
//                 padding: const EdgeInsets.all(8),
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Container(
//                       decoration: const BoxDecoration(
//                         color: AppColors.connectOneBlue,
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(10),
//                           topRight: Radius.circular(10),
//                         ),
//                       ),
//                       child: Row(
//                         children: [
//                           const Expanded(
//                             child: Center(
//                               child: Text(
//                                 "SEARCH",
//                                 style: TextStyle(color: AppColors.white),
//                               ),
//                             ),
//                           ),
//                           TextButton(
//                             child: const Text(
//                               "Clear Filters",
//                               style: TextStyle(color: AppColors.white),
//                             ),
//                             onPressed: () {
//                               BlocProvider.of<OfflineCubit>(context)
//                                   .add(GetFilters());
//                             },
//                           )
//                         ],
//                       ),
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//
//                     ///select search
//                     Row(
//                       children: [
//                         BlocBuilder<SearchCubit, SearchCubitState>(
//                           builder: (context, state) {
//                             if (state is Loading) {
//                               return circularProgressIndicator;
//                             } else if (state is SearchData) {
//                               return DropDownSearch(
//                                 selectedEntities: const [],
//                                 products: state.data,
//                                 label: "Select Search",
//                                 onPress: (val) {
//                                   if (val.isNotEmpty) {
//                                     var selectedData = val.first;
//                                     filterProducts = [
//                                       selectedData.product ?? ""
//                                     ];
//                                     filterLocation = [
//                                       selectedData.location ?? ""
//                                     ];
//                                     filterGrade = [selectedData.grade ?? ""];
//                                     filterDeliveryDate = [
//                                       selectedData.deliveryDate ?? ""
//                                     ];
//                                     setState(() {});
//                                   }
//                                 },
//                               );
//                             } else {
//                               return Container();
//                             }
//                           },
//                         ),
//                       ],
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//
//                     ///location
//                     Row(
//                       children: [
//                         MultiSelectDropDown(
//                           selectedEntities: filterLocation,
//                           products: (data?.filters.first.location) ?? [],
//                           label: "Location",
//                           onPress: (val) {
//                             filterLocation = val.map((e) => e.name).toList();
//                             BlocProvider.of<OfflineCubit>(context).add(
//                                 FilterChange(
//                                     DateTime.now().millisecondsSinceEpoch,
//                                     filterProducts,
//                                     filterGrade,
//                                     filterLocation,
//                                     quantity,
//                                     priceRange,
//                                     buyBidPrice,
//                                     deliveryDate));
//                           },
//                         ),
//                         const SizedBox(
//                           width: 5,
//                         ),
//
//                         ///products
//                         MultiSelectDropDown(
//                           selectedEntities: filterProducts,
//                           products: (data?.filters.first.product) ?? [],
//                           label: "Products",
//                           onPress: (val) {
//                             filterProducts = val.map((e) => e.name).toList();
//                             BlocProvider.of<OfflineCubit>(context).add(
//                                 FilterChange(
//                                     DateTime.now().millisecondsSinceEpoch,
//                                     filterProducts,
//                                     filterGrade,
//                                     filterLocation,
//                                     quantity,
//                                     priceRange,
//                                     buyBidPrice,
//                                     deliveryDate));
//                           },
//                         ),
//                       ],
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//
//                     ///grade
//                     Row(
//                       children: [
//                         MultiSelectDropDown(
//                           selectedEntities: filterGrade,
//                           products: (data?.filters.first.grade) ?? [],
//                           label: "Grade",
//                           onPress: (val) {
//                             filterGrade = val.map((e) => e.name).toList();
//                             BlocProvider.of<OfflineCubit>(context).add(
//                                 FilterChange(
//                                     DateTime.now().millisecondsSinceEpoch,
//                                     filterProducts,
//                                     filterGrade,
//                                     filterLocation,
//                                     quantity,
//                                     priceRange,
//                                     buyBidPrice,
//                                     deliveryDate));
//                           },
//                         ),
//                         const SizedBox(
//                           width: 5,
//                         ),
//
//                         ///quantity
//                         FilterDropDown(
//                           label: "Quantity",
//                           onPress: () {
//                             setState(() {
//                               if (quantitySlider == true) {
//                                 quantitySlider = false;
//                               } else {
//                                 quantitySlider = true;
//                               }
//                             });
//                           },
//                         ),
//                       ],
//                     ),
//                     Visibility(
//                       visible: quantitySlider,
//                       child: Row(
//                         children: [
//                           Expanded(
//                               child: SliderConnectOne(
//                             values: quantityValues,
//                             min: data?.filters.first.range.first
//                                     .quantityRangeMin ??
//                                 0,
//                             max: data?.filters.first.range.first
//                                     .quantityRangeMax ??
//                                 0,
//                             callback: (data) {
//                               setState(() {
//                                 buyNowValues = data;
//                               });
//                             },
//                             completedCallback: (data) {
//                               BlocProvider.of<OfflineCubit>(context).add(
//                                   FilterChange(
//                                       DateTime.now().millisecondsSinceEpoch,
//                                       filterProducts,
//                                       filterGrade,
//                                       filterLocation,
//                                       quantity,
//                                       priceRange,
//                                       buyBidPrice,
//                                       deliveryDate));
//                             },
//                           )),
//                         ],
//                       ),
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//
//                     ///price
//                     Row(
//                       children: [
//                         FilterDropDown(
//                           label: "Price",
//                           onPress: () {
//                             if (priceSlider == true) {
//                               priceSlider = false;
//                             } else {
//                               priceSlider = true;
//                             }
//                           },
//                         ),
//                       ],
//                     ),
//                     Visibility(
//                       visible: priceSlider,
//                       child: Row(
//                         children: [
//                           Expanded(
//                             child: SliderConnectOne(
//                               values: priceSliderValues,
//                               min: double.tryParse(data?.filters.first.range
//                                           .first.priceRangeMin ??
//                                       "0") ??
//                                   0,
//                               max: data?.filters.first.range.first
//                                       .priceRangeMax ??
//                                   0,
//                               callback: (data) {
//                                 setState(() {
//                                   buyNowValues = data;
//                                 });
//                               },
//                               completedCallback: (data) {
//                                 BlocProvider.of<OfflineCubit>(context).add(
//                                     FilterChange(
//                                         DateTime.now().millisecondsSinceEpoch,
//                                         filterProducts,
//                                         filterGrade,
//                                         filterLocation,
//                                         quantity,
//                                         priceRange,
//                                         buyBidPrice,
//                                         deliveryDate));
//                               },
//                             ),
//                           )
//                         ],
//                       ),
//                     ),
//                     const SizedBox(
//                       height: 5,
//                     ),
//
//                     ///buy now
//                     Row(
//                       children: [
//                         FilterDropDown(
//                           label: "Buy Now Price",
//                           onPress: () {
//                             setState(() {
//                               if (buyNowSlider == true) {
//                                 buyNowSlider = false;
//                               } else {
//                                 buyNowSlider = true;
//                               }
//                             });
//                           },
//                         ),
//                       ],
//                     ),
//                     const SizedBox(
//                       height: 2,
//                     ),
//                     Visibility(
//                       visible: buyNowSlider,
//                       child: Row(
//                         children: [
//                           Expanded(
//                             child: SliderConnectOne(
//                               values: buyNowValues,
//                               min: data?.filters.first.range.first
//                                       .buyBidPriceRangeMin ??
//                                   0,
//                               max: data?.filters.first.range.first
//                                       .buyBidPriceMax ??
//                                   0,
//                               callback: (data) {
//                                 setState(() {
//                                   buyNowValues = data;
//                                 });
//                               },
//                               completedCallback: (data) {
//                                 BlocProvider.of<OfflineCubit>(context).add(
//                                     FilterChange(
//                                         DateTime.now().millisecondsSinceEpoch,
//                                         filterProducts,
//                                         filterGrade,
//                                         filterLocation,
//                                         quantity,
//                                         priceRange,
//                                         buyBidPrice,
//                                         deliveryDate));
//                               },
//                             ),
//                           )
//                         ],
//                       ),
//                     ),
//                     Row(
//                       children: [
//                         Expanded(
//                             child: TextButton(
//                                 style: TextButton.styleFrom(
//                                   backgroundColor: AppColors.connectOneBlue,
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(30),
//                                   ),
//                                 ),
//                                 onPressed: () {},
//                                 child: const Text(
//                                   "Delete",
//                                   style: TextStyle(color: Colors.white),
//                                 ))),
//                         const SizedBox(
//                           width: 5,
//                         ),
//                         Expanded(
//                           child: TextButton(
//                             style: TextButton.styleFrom(
//                               backgroundColor: AppColors.connectOneBlue,
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(30),
//                               ),
//                             ),
//                             onPressed: () {},
//                             child: const Text(
//                               "Save",
//                               style: TextStyle(color: Colors.white),
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                     Card(
//                       shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(20.0)),
//                       elevation: 10.0,
//                       clipBehavior: Clip.antiAliasWithSaveLayer,
//                       child: Column(
//                         children: [
//                           ValueListenableBuilder<DateTime>(
//                             valueListenable: _focusedDay,
//                             builder: (context, value, _) {
//                               return CalendarHeader(
//                                 focusedDay: DateFormat("yyyy-MM-dd hh:mm:ss")
//                                     .parse(widget.day),
//                                 clearButtonVisible: canClearSelection,
//                                 onTodayButtonTap: () {
//                                   setState(
//                                       () => _focusedDay.value = DateTime.now());
//                                 },
//                                 onClearButtonTap: () {
//                                   setState(() {});
//                                 },
//                                 onLeftArrowTap: () {
//                                   _pageController.previousPage(
//                                     duration: const Duration(milliseconds: 300),
//                                     curve: Curves.easeOut,
//                                   );
//                                 },
//                                 onRightArrowTap: () {
//                                   _pageController.nextPage(
//                                     duration: const Duration(milliseconds: 300),
//                                     curve: Curves.easeOut,
//                                   );
//                                 },
//                               );
//                             },
//                           ),
//                           const SizedBox(
//                             height: 10,
//                           ),
//                           TableCalendar<Event>(
//                             firstDay: kFirstDay,
//                             lastDay: kLastDay,
//                             calendarStyle: calendarCustomStyle(),
//                             focusedDay: DateFormat("yyyy-MM-dd hh:mm:ss")
//                                 .parse(widget.day),
//                             headerVisible: false,
//                             selectedDayPredicate: (day) =>
//                                 _selectedDays.contains(day),
//                             rangeStartDay: _rangeStart,
//                             rangeEndDay: _rangeEnd,
//                             calendarFormat: _calendarFormat,
//                             holidayPredicate: (day) => data != null
//                                 ? processModel(day, data.calender)
//                                 : false,
//                             onDaySelected: _onDaySelected,
//                             onCalendarCreated: (controller) =>
//                                 _pageController = controller,
//                             onPageChanged: (focusedDay) =>
//                                 _focusedDay.value = focusedDay,
//                             // onFormatChanged: (format) {
//                             //   if (_calendarFormat != format) {
//                             //     setState(() => _calendarFormat = format);
//                             //   }
//                             // },
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(height: 16),
//                     const Padding(
//                       padding: EdgeInsets.only(left: 8.0),
//                       child: Text(
//                         "Summary",
//                         style: TextStyle(
//                             fontWeight: FontWeight.bold, fontSize: 20),
//                       ),
//                     ),
//                     SizedBox(height: 16),
//                     Container(
//                       decoration: const BoxDecoration(
//                           color: AppColors.connectOneBlue,
//                           borderRadius: BorderRadius.only(
//                               topRight: Radius.circular(6),
//                               topLeft: Radius.circular(6))),
//                       child: Padding(
//                         padding: const EdgeInsets.all(8.0),
//                         child: Column(
//                           children: [
//                             Row(
//                               children: const [
//                                 Expanded(
//                                   child: Text(
//                                     "Product",
//                                     style: TextStyle(color: AppColors.white),
//                                   ),
//                                   flex: 2,
//                                 ),
//                                 Expanded(
//                                   child: Text(
//                                     "Date",
//                                     textAlign: TextAlign.center,
//                                     style: TextStyle(color: AppColors.white),
//                                   ),
//                                   flex: 1,
//                                 ),
//                                 Expanded(
//                                   child: Text(
//                                     "Stock",
//                                     textAlign: TextAlign.center,
//                                     style: TextStyle(color: AppColors.white),
//                                   ),
//                                   flex: 1,
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     ListView(
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       padding: const EdgeInsets.only(bottom: 120),
//                       children: data?.summary.map<Widget>((e) {
//                             if (e.deliveryDate ==
//                                 DateFormat('yyyy-MM-dd')
//                                     .format(_selectedDays.last)) {
//                               return Container(
//                                 color: Colors.grey.shade200,
//                                 child: Padding(
//                                   padding: const EdgeInsets.all(8.0),
//                                   child: Row(
//                                     children: [
//                                       Expanded(
//                                         child: Text(
//                                           e.product,
//                                           style: const TextStyle(
//                                               color: AppColors.mainColor),
//                                         ),
//                                         flex: 2,
//                                       ),
//                                       Expanded(
//                                         child: Text(
//                                           e.deliveryDate,
//                                           textAlign: TextAlign.center,
//                                           style: const TextStyle(
//                                               color: AppColors.mainColor),
//                                         ),
//                                         flex: 1,
//                                       ),
//                                       Expanded(
//                                         child: Text(
//                                           e.quantity.toString(),
//                                           textAlign: TextAlign.center,
//                                           style: const TextStyle(
//                                               color: AppColors.mainColor),
//                                         ),
//                                         flex: 1,
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               );
//                             }
//
//                             return Container();
//                           }).toList() ??
//                           [],
//                     )
//                   ],
//                 ),
//               ),
//             ),
//             Align(
//               alignment: Alignment.bottomLeft,
//               child: Container(
//                 color: AppColors.connectOneBlue,
//                 width: double.maxFinite,
//                 child: Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: Row(
//                     children: [
//                       const SizedBox(
//                         width: 20,
//                       ),
//                       data != null
//                           ? Text(
//                               "Stock : ${processStockSum(_selectedDays.last, data.calender)}",
//                               style: const TextStyle(color: AppColors.white),
//                             )
//                           : const Text(
//                               "0",
//                               style: TextStyle(color: AppColors.white),
//                             ),
//                       const Spacer(),
//                       TextButton(
//                           style: TextButton.styleFrom(
//                               backgroundColor: Colors.white,
//                               shape: RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(8))),
//                           onPressed: () {
//                             navigateSafe(AppRoutes.offlineAuctionScreen,
//                                 arguments: AuctionArg(
//                                     filterLocation.join(","),
//                                     filterGrade.join(","),
//                                     filterProducts.join(","),
//                                     DateFormat('yyyy-MM-dd')
//                                         .format(_selectedDays.last)));
//                           },
//                           child: const Text("View Stock",
//                               style:
//                                   TextStyle(color: AppColors.connectOneBlue))),
//                       const SizedBox(
//                         width: 20,
//                       )
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     });
//   }
//
//   CalendarStyle calendarCustomStyle() => CalendarStyle(
//       selectedDecoration: const BoxDecoration(
//           shape: BoxShape.circle, color: AppColors.connectOneBlue),
//       holidayDecoration: BoxDecoration(
//           shape: BoxShape.circle,
//           color: AppColors.white,
//           border: Border.all(color: AppColors.connectOneBlue)),
//       todayDecoration: BoxDecoration(
//           color: AppColors.connectOneBlue.withOpacity(.6),
//           shape: BoxShape.circle));
//
//   bool processModel(DateTime day, List<Calender> calender) {
//     for (var cal in calender) {
//       String formattedDate = DateFormat('yyyy-MM-dd').format(day);
//       if (cal.deliveryDate.contains(formattedDate)) {
//         return true;
//       } else {
//         return false;
//       }
//     }
//     return false;
//   }
//
//   int processStockSum(DateTime day, List<Calender> calender) {
//     var data = 0;
//     for (var cal in calender) {
//       String formattedDate = DateFormat('yyyy-MM-dd').format(day);
//       if (cal.deliveryDate == formattedDate) {
//         data += cal.stockCount;
//       }
//     }
//     return data;
//   }
// }
