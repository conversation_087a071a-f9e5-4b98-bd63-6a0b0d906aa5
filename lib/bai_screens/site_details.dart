import 'dart:io';

import 'package:connectone/bai_blocs/site_details/site_details_cubit.dart';
import 'package:connectone/bai_models/add_employee_req.dart';
import 'package:connectone/bai_models/member_list_res.dart';
import 'package:connectone/bai_models/site_details_res.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

import '../core/bai_widgets/help_info.dart';

class SiteDetails extends StatefulWidget {
  const SiteDetails({
    Key? key,
    required this.projectId,
  }) : super(key: key);

  final String? projectId;

  @override
  State<SiteDetails> createState() => _SiteDetailsState();
}

class _SiteDetailsState extends State<SiteDetails> {
  MapboxMap? mapboxMap;
  late PointAnnotationManager _pointAnnotationManager;

  void _onMapCreated(MapboxMap mapboxMapNew) async {
    mapboxMap = mapboxMapNew;

    // Disable map scrolling and zooming
    await mapboxMap?.gestures.updateSettings(
      GesturesSettings(
        scrollEnabled: false,
        pitchEnabled: false,
        rotateEnabled: false,
        pinchToZoomEnabled: false,
        quickZoomEnabled: false,
      ),
    );

    // Create point annotation manager
    _pointAnnotationManager =
        await mapboxMap!.annotations.createPointAnnotationManager();
  }

  @override
  void initState() {
    super.initState();
    _getData();
  }

  void _getData() {
    context.read<SiteDetailsCubit>().getSiteDetails(widget.projectId ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Site Details'),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        backgroundColor: AppColors.primaryColor,
        actions: const [
          InfoHelp(
            url: 'https://www.youtube.com/shorts/4HmsmT0qPts',
            name: '',
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<SiteDetailsCubit, SiteDetailsState>(
          listener: (context, state) {
            if (state is SiteDetailsError) {
              alert(state.message);
            }
            if (state is SiteDetailsLoaded) {
              _addMapMarker(state.siteDetailsRes);
            }
            if (state is EmployeeAdded) {
              alert(state.message);
              _getData();
            }
            if (state is EmployeeStatusChanged) {
              alert(state.message);
              _getData();
            }
            if (state is ProjectStatusChanged) {
              alert(state.message);
              _getData();
            }
          },
          builder: (context, state) {
            // Show or hide loader based on state
            (state is SiteDetailsLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();

            // Display site details if loaded
            if (state is SiteDetailsLoaded) {
              var data = state.siteDetailsRes;
              var isCompleted = data.projectStatus == "Completed";
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle('Site Name'),
                      Text(
                        data.projectName ?? "N/A",
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      _buildSectionTitle('Site Address'),
                      Text(
                        "${data.address?.sellingAddressLine1 ?? "N/A"}\n"
                        "${data.address?.city ?? "N/A"}, "
                        "${data.address?.state ?? "N/A"}\n"
                        "${data.address?.country ?? "N/A"}",
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      _buildMapSection(data),
                      const SizedBox(height: 20),
                      _buildSectionTitle('Site Access'),
                      Text(
                        data.siteAccess ?? "N/A",
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 10),
                      _buildSiteStatusSection(isCompleted),
                      const SizedBox(height: 4),
                      const Divider(color: Colors.black),
                      const SizedBox(height: 8),
                      _buildEmployeesSection(data),
                    ],
                  ),
                ),
              );
            }
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildMapSection(SiteDetailsRes data) {
    return GestureDetector(
      onTap: () => _openExternalMap(data),
      child: Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: MapWidget(
            key: const ValueKey("MapBoxWidget"),
            onMapCreated: _onMapCreated,

            cameraOptions: data.address?.latitude != null
                ? CameraOptions(
                    center: Point(
                      coordinates: Position(
                        data.address!.longitude!,
                        data.address!.latitude!,
                      ),
                    ),
                    zoom: 15.0,
                  )
                : null,
          ),
        ),
      ),
    );
  }

  void _openExternalMap(SiteDetailsRes data) async {
    if (data.address?.latitude != null && data.address?.longitude != null) {
      final availableMaps = await MapLauncher.isMapAvailable(MapType.google);
      if (availableMaps == true) {
        await MapLauncher.showMarker(
          mapType: MapType.google,
          coords: Coords(data.address!.latitude!, data.address!.longitude!),
          title: data.projectName ?? "Location",
        );
      } else {
        await MapLauncher.showMarker(
          mapType: MapType.apple,
          coords: Coords(data.address!.latitude!, data.address!.longitude!),
          title: data.projectName ?? "Location",
        );
      }
    }
  }

  Future<void> _addMapMarker(SiteDetailsRes data) async {
    await Future.delayed(const Duration(seconds: 2));

    if (mapboxMap == null ||
        data.address?.latitude == null ||
        data.address?.longitude == null) {
      safePrint('Map or coordinates are null');
      return;
    }

    try {
      final ByteData bytes =
          await rootBundle.load('assets/images/ic_marker.png');
      final Uint8List marker = bytes.buffer.asUint8List();

      final options = PointAnnotationOptions(
        geometry: Point(
          coordinates: Position(
            data.address!.longitude!,
            data.address!.latitude!,
          ),
        ),
        image: marker,
        iconSize: Platform.isIOS ? 1 : 2,
      );

      await _pointAnnotationManager.create(options);

      await mapboxMap?.flyTo(
        CameraOptions(
          center: Point(
            coordinates: Position(
              data.address!.longitude!,
              data.address!.latitude!,
            ),
          ),
          zoom: 15.0,
        ),
        MapAnimationOptions(
          duration: 1000,
        ),
      );
    } catch (e) {
      safePrint('Error adding map marker: $e');
    }
  }

  Widget _buildSiteStatusSection(bool isCompleted) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "Change Site Status",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        Switch(
          value: isCompleted,
          onChanged: (val) {
            context.read<SiteDetailsCubit>().changeProjectStatus(
                  widget.projectId ?? "",
                  isCompleted ? "Active" : "Completed",
                );
          },
          activeColor: Colors.green,
        ),
      ],
    );
  }

  Widget _buildEmployeesSection(SiteDetailsRes data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 3,
              child: Text(
                "Employees",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: BaiButton(
                onTap: () {
                  showDialog(
                      context: context,
                      builder: (context) {
                        return EmployeeListPopup(
                          projectId: widget.projectId?.toString() ?? "",
                        );
                      });
                },
                height: 40,
                text: "Add Employees",
                backgoundColor: AppColors.primaryColor,
                borderRadius: 32,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        data.employeeDetails?.isNotEmpty == true
            ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: data.employeeDetails?.length ?? 0,
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  var employee = data.employeeDetails?[index];
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade200,
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  employee?.designation ?? "N/A",
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 6),
                                Text(
                                  "Role: ${employee?.employeeName ?? "N/A"}",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: employee?.employeeStatus == "ACTV",
                            activeColor: Colors.green.shade600,
                            onChanged: (bool value) {
                              context
                                  .read<SiteDetailsCubit>()
                                  .changeEmployeeStatus(
                                    widget.projectId ?? "",
                                    employee?.employeeId ?? "",
                                    employee?.employeeStatus == "ACTV"
                                        ? "IACT"
                                        : "ACTV",
                                  );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              )
            : const SizedBox(
                height: 120,
                child: Center(
                  child: Text(
                    "No employees found",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
      ],
    );
  }
}

class EmployeeListPopup extends StatefulWidget {
  final String projectId;

  const EmployeeListPopup({
    Key? key,
    required this.projectId,
  }) : super(key: key);

  @override
  State<EmployeeListPopup> createState() => _EmployeeListPopupState();
}

class _EmployeeListPopupState extends State<EmployeeListPopup> {
  MemberListItem? _selectedEmployee;

  @override
  void initState() {
    super.initState();
    // Fetch employee list when the widget is first initialized
    context.read<SiteDetailsCubit>().getEmployeeList(widget.projectId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SiteDetailsCubit, SiteDetailsState>(
      listener: (context, state) {
        // Handle any specific state changes if needed
      },
      builder: (context, state) {
        // Handle different states
        if (state is SiteDetailsLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is SiteDetailsError) {
          return Center(
            child: Text(
              'Error loading employees: ${state.message}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        if (state is SiteDetailsLoaded) {
          final employees = state.employeeList ?? [];

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            insetPadding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  height: 52,
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.person_pin_rounded,
                        size: 24,
                        color: Colors.white,
                      ),
                      SizedBox(width: 10),
                      Text(
                        'Add Employee',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ).withCloseButton(() => Navigator.pop(context)),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DropdownButtonFormField<MemberListItem>(
                    decoration: InputDecoration(
                      labelText: 'Select Employee',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.person),
                    ),
                    value: _selectedEmployee,
                    hint: const Text('Choose an employee'),
                    isExpanded: true,
                    items: employees.map((employee) {
                      return DropdownMenuItem<MemberListItem>(
                        value: employee,
                        child: Text(
                          employee.name ?? "",
                          overflow: TextOverflow.ellipsis,
                        ),
                      );
                    }).toList(),
                    onChanged: (MemberListItem? newValue) {
                      setState(() {
                        _selectedEmployee = newValue;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Please select an employee';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const SizedBox(width: 16),
                    Expanded(
                      child: BaiButton(
                        onTap: () => Navigator.pop(context),
                        text: "Cancel",
                        backgoundColor: AppColors.darkRed,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: BaiButton(
                        onTap: () {
                          if (_selectedEmployee != null) {
                            var req = AddEmployeeReq(
                                projectId: int.tryParse(widget.projectId),
                                employeeIds: [
                                  int.parse(_selectedEmployee?.id ?? "0")
                                ],
                                assignedByCustomerId:
                                    int.tryParse(getCustomerId()));
                            context.read<SiteDetailsCubit>().addEmployee(req);
                            Navigator.pop(context);
                          }
                        },
                        text: "Add",
                        backgoundColor: AppColors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        }
        // Fallback empty state
        return const SizedBox(
          height: 200,
          child: Center(
            child: Text('No employees found'),
          ),
        );
      },
    );
  }
}
