import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_screens/select_services.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../core/bai_widgets/help_info.dart';

class VendorGroupsScreen extends StatefulWidget {
  const VendorGroupsScreen({
    Key? key,
    required this.pricing,
    required this.isMember,
    required this.isManufacturer,
    required this.req,
    this.fromProfile = false,
  }) : super(key: key);

  final List<PricingRes> pricing;
  final bool isMember;
  final bool isManufacturer;
  final RegisterReq req;
  final bool fromProfile;

  @override
  State<VendorGroupsScreen> createState() => _VendorGroupsScreenState();
}

class _VendorGroupsScreenState extends State<VendorGroupsScreen> {
  List<String> groups = [];
  List<String> selectedGroups = [];

  final ValueNotifier<List<String>> selectedGroupsNotifier =
      ValueNotifier<List<String>>([]);

  @override
  initState() {
    super.initState();
    setGroups(widget.pricing);
  }

  setGroups(List<PricingRes> pricing) {
    setState(() {
      groups = pricing.map((e) => e.group.toString()).toSet().toList();
    });
  }

  @override

  /// The main build method of the VendorGroupsScreen
  ///
  /// This builds the UI of the screen
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Category Groups'),
        backgroundColor: AppColors.primaryColor,
        actions: const [
          InfoHelp(
            url: '',
            name: '',
          )
        ],
      ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: Column(
          children: [
            /// The list of groups
            Expanded(
              child: ListView.builder(
                itemCount: groups.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  var item = groups[index];
                  return GroupItem(
                    title: item,
                    selectedGroupsNotifier: selectedGroupsNotifier,
                    onChanged: () {
                      setState(() {});
                    },
                  );
                },
              ),
            ),

            /// The button to navigate to the next screen
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 12,
              ),
              child: BaiButton(
                onTap: () {
                  if (selectedGroupsNotifier.value.isEmpty) {
                    alert("Please select at least one group.");
                    return;
                  }
                  Get.to(
                    SelectServicesPage(
                      selectedGroups: selectedGroupsNotifier.value,
                      currentGroup: 0,
                      pricing: widget.pricing,
                      isMember: widget.isMember,
                      isManufacturer: widget.isManufacturer,
                      req: widget.req,
                      fromProfile: widget.fromProfile,
                    ),
                    preventDuplicates: false,
                  );
                },
                text: selectedGroupsNotifier.value.isNotEmpty
                    ? "Next > Categories under ${selectedGroupsNotifier.value.first}"
                    : "Next",
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GroupItem extends StatefulWidget {
  const GroupItem({
    Key? key,
    required this.title,
    required this.selectedGroupsNotifier,
    required this.onChanged,
  }) : super(key: key);

  final String title;
  final Function onChanged;
  final ValueNotifier<List<String>> selectedGroupsNotifier;

  @override
  State<GroupItem> createState() => _GroupItemState();
}

class _GroupItemState extends State<GroupItem> {
  bool isSelected = false;

  void _onCheckboxChanged(bool? val) {
    widget.onChanged();
    if (val == null) return;
    if (widget.selectedGroupsNotifier.value.length >= 2 && val == true) {
      // alert("You can select only 2 groups.");
      // return;
    }

    setState(() {
      if (val != isSelected) {
        isSelected = val;
        if (isSelected) {
          widget.selectedGroupsNotifier.value.add(widget.title);
        } else {
          widget.selectedGroupsNotifier.value.remove(widget.title);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          value: isSelected,
          onChanged: _onCheckboxChanged,
          activeColor: AppColors.primaryColor,
        ),
        Expanded(
          child: Text(
            widget.title,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
