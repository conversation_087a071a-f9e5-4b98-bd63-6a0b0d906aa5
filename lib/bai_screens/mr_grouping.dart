import 'dart:io';

import 'package:connectone/bai_blocs/mr_grouping/cubit/mr_grouping_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/bai_screens/seller_offers_page.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/mr_grouping_item.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../core/bai_widgets/mr_summary_dialog.dart';

class MrGrouping extends StatefulWidget {
  const MrGrouping({
    Key? key,
    required this.itemName,
    required this.itemId,
  }) : super(key: key);
  final String itemName;
  final String itemId;

  @override
  State<MrGrouping> createState() => _MrGroupingState();
}

class _MrGroupingState extends State<MrGrouping> {
  var style = const TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: 15,
  );
  List<Content> data = [];

  @override
  void initState() {
    super.initState();
    context.read<MrGroupingCubit>().getGrouping(widget.itemId);
  }

  @override

  /// The main widget for the MrGrouping screen
  ///
  /// This widget displays the items grouped by order group id and category id
  /// and allows the user to view the quote summary, create a purchase order,
  /// and manage discounts and transportation charges.
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(widget.itemName),
        backgroundColor: AppColors.primaryColor,
      ),
      body: Stack(
        children: [
          AppLoader(
            child: BlocConsumer<MrGroupingCubit, MrGroupingState>(
              listener: (context, state) {
                if (state is MRGroupingLoaded) {
                  setState(() {
                    data = state.groupRes;
                  });
                }
              },
              builder: (context, state) {
                (state is MRGroupingLoading)
                    ? context.loaderOverlay.show()
                    : context.loaderOverlay.hide();
                return ListView.separated(
                  itemCount: data.length,
                  padding: const EdgeInsets.only(
                    bottom: 144,
                    top: 16,
                    left: 6,
                    right: 6,
                  ),
                  itemBuilder: (context, index) {
                    return MrGroupingItem(
                      item: data[index],
                      style: style,
                      refresh: () {
                        context
                            .read<MrGroupingCubit>()
                            .getGrouping(widget.itemId);
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(height: 0);
                  },
                );
              },
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              color: AppColors.primaryColor,
              height: 56,
              child: Padding(
                padding: EdgeInsets.only(
                  top: 12,
                  left: 12,
                  right: 12,
                  bottom: kIsWeb
                      ? 20
                      : Platform.isIOS
                          ? 20
                          : 12,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: BaiButton(
                        elevation: 0,
                        backgoundColor: AppColors.primaryColor,
                        onTap: () async {
                          if (getRoleLevel() == 15) {
                            alert("For quotes, please contact the manager.");
                            return;
                          }
                          var date = await showDialog(
                              context: context,
                              builder: (context) {
                                return MRSummaryDialog(
                                  prchOrdrSplitId:
                                      data[0].prchOrdrSplitId.toString(),
                                  categoryId:
                                      data[0].cappCategoriesId.toString(),
                                  orderGroupId: data[0].orderGroupId.toString(),
                                );
                              });

                          if (date[0] == null) {
                            return;
                          }
                          if (isBuyer()) {
                            Get.to(BuyerOffersPage(
                              item: data.isNotEmpty ? data[0] : Content(),
                              showSummary: true,
                              deliveryDate: date[0],
                              categoryId: date[1]?.toString(),
                              splitName: date[2]?.toString(),
                              steelMr: date[3] ?? false,
                            ))?.then((val) {
                              context
                                  .read<MrGroupingCubit>()
                                  .getGrouping(widget.itemId);
                            });
                          } else {
                            Get.to(SellerOffersPage(
                              item: data.isNotEmpty ? data[0] : Content(),
                              showSummary: true,
                              deliveryDate: date[0],
                              categoryId: date[1]?.toString(),
                              splitName: date[2]?.toString(),
                              steelMr: date[3] ?? false,
                            ))?.then((val) {
                              context
                                  .read<MrGroupingCubit>()
                                  .getGrouping(widget.itemId);
                            });
                          }
                        },
                        text: "SHOW  QUOTE  SUMMARY",
                      ),
                    ),
                    // Expanded(
                    //   child: BigButtonMr(
                    //     title: 'Quote Summary',
                    //     icon: Icons.summarize_outlined,
                    //     onPressed: () {
                    //       /// Show the quote summary when the button is pressed
                    //       if (getDesignation().toLowerCase() ==
                    //           "site in charge") {
                    //         alert("For quotes, please contact the manager.");
                    //         return;
                    //       }
                    //       if (isBuyer()) {
                    //         Get.to(BuyerOffersPage(
                    //           item: data.isNotEmpty ? data[0] : Content(),
                    //           showSummary: true,
                    //         ))?.then((val) {
                    //           context
                    //               .read<MrGroupingCubit>()
                    //               .getGrouping(widget.itemId);
                    //         });
                    //       } else {
                    //         Get.to(SellerOffersPage(
                    //           item: data.isNotEmpty ? data[0] : Content(),
                    //           showSummary: true,
                    //         ))?.then((val) {
                    //           context
                    //               .read<MrGroupingCubit>()
                    //               .getGrouping(widget.itemId);
                    //         });
                    //       }
                    //     },
                    //   ),
                    // ),
                    // const SizedBox(width: 8),

                    // /// Discounts & Transport Charges Button
                    // ///
                    // /// This button is only visible for the site in charge
                    // /// and allows them to manage discounts and transportation
                    // /// charges for the selected item.
                    // if (!isBuyer())
                    //   Expanded(
                    //     child: BigButtonMr(
                    //       title: 'Discount & Transport Charges',
                    //       icon: Icons.local_shipping,
                    //       onPressed: () {
                    //         /// Show the discount and transportation charges dialog
                    //         /// when the button is pressed
                    //         showDialog(
                    //           context: context,
                    //           builder: (context) {
                    //             return DiscountTransportationDialog(
                    //               orderGroupId:
                    //                   data[0].orderGroupId?.toInt() ?? 0,
                    //               sellerId: getVendorId(),
                    //               categoryId:
                    //                   data[0].cappCategoriesId?.toInt() ?? 0,
                    //             );
                    //           },
                    //         );
                    //       },
                    //     ),
                    //   ),
                    // const SizedBox(width: 8),

                    // /// Create PO Button
                    // ///
                    // /// This button is only visible for the site in charge
                    // /// and allows them to create a purchase order for the
                    // /// selected item.
                    // Expanded(
                    //   child: BigButtonMr(
                    //     title: 'Create PO',
                    //     icon: Icons.note_add,
                    //     onPressed: () {
                    //       if (getDesignation().toLowerCase() ==
                    //           "site in charge") {
                    //         alert("For quotes, please contact the manager.");
                    //         return;
                    //       }

                    //       /// Add action for Create PO button if necessary
                    //     },
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BigButtonMr extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;

  const BigButtonMr({
    Key? key,
    required this.title,
    required this.icon,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
        ),
        padding: const EdgeInsets.all(6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 2),
            Align(
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: AppColors.primaryColorOld,
                size: 24,
              ),
            ),
            Expanded(
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
