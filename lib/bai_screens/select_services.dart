import 'dart:ui';

import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_screens/registration_summary_seller.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class SelectServicesPage extends StatefulWidget {
  const SelectServicesPage({
    Key? key,
    required this.selectedGroups,
    required this.currentGroup,
    required this.pricing,
    required this.isMember,
    required this.isManufacturer,
    required this.req,
    this.fromProfile = false,
  }) : super(key: key);

  final List<String> selectedGroups;
  final int currentGroup;
  final List<PricingRes> pricing;
  final bool isMember;
  final bool isManufacturer;
  final RegisterReq req;
  final bool fromProfile;

  @override
  State<SelectServicesPage> createState() => _SelectServicesPageState();
}

List<SelectedCategory> selectedCategories = [];

class _SelectServicesPageState extends State<SelectServicesPage> {
  List<PricingRes> selectedPricing = [];

  // _parsePricing() {
  //   var currentGroup = widget.currentGroup;
  //   var pricing = widget.pricing;
  //   var parsedPricing = pricing.where((element) {
  //     if (element.group == widget.selectedGroups[currentGroup]) {
  //       return true;
  //     }
  //     return false;
  //   }).toList();
  //   setState(() {
  //     selectedPricing = parsedPricing;
  //     selectedPricing.sort((a, b) => (a.categories ?? "").compareTo(b.categories ?? ""));
  //   });
  // }

  void _parsePricing() {
    var currentGroup = widget.currentGroup;
    var pricing = widget.pricing;

    var parsedPricing = pricing.where((element) {
      return element.group == widget.selectedGroups[currentGroup];
    }).toList();

    setState(() {
      selectedPricing = parsedPricing;

      for (var index = 0; index < selectedPricing.length; index++) {
        num rate;
        if (widget.isManufacturer && widget.isMember) {
          rate = selectedPricing[index].memberManufacturerRate1 ?? 0;
        } else if (widget.isManufacturer && !widget.isMember) {
          rate = selectedPricing[index].manufacturerRate1 ?? 0;
        } else if (!widget.isManufacturer && widget.isMember) {
          rate = selectedPricing[index].memberDealerRate1 ?? 0;
        } else {
          rate = selectedPricing[index].dealerRate1 ?? 0;
        }

        if (rate == 0) {
          selectedPricing.removeAt(index);
          index--;
        }
      }

      selectedPricing
          .sort((a, b) => (a.categories ?? "").compareTo(b.categories ?? ""));
    });
  }

  @override
  void initState() {
    _parsePricing();
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Service Category Selection!\n\nHere's how to select your services:\n\n1. Browse through the available categories in this group\n\n2. Check the boxes next to services you want to offer\n\n3. Note the pricing for each selected category\n\n4. You can select multiple categories as needed\n\n5. Click Next to move to other category groups\n\nComplete your selections to proceed to the summary.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the UI for selecting services under a specific category group.
  ///
  /// This widget displays a list of categories with checkboxes to select them.
  /// It also includes a button to navigate to the next group or the summary.
  Widget build(BuildContext context) {
    // Get the name of the current group and check if it's the last group
    var groupName = widget.selectedGroups[widget.currentGroup];
    bool isLastGroup = widget.currentGroup == widget.selectedGroups.length - 1;

    return Scaffold(
      appBar: AppBar(
        // Display the title with the current group name
        title: Text('Categories under $groupName'),
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height - 72,
        child: Column(
          children: [
            // Display the list of categories with checkboxes
            Expanded(
              child: selectedPricing.isNotEmpty
                  ? ListView.builder(
                      padding: const EdgeInsets.only(
                        right: 8,
                        top: 8,
                      ),
                      itemCount: selectedPricing.length,
                      itemBuilder: (context, index) {
                        var item = selectedPricing[index];
                        // Check if the category is already selected
                        bool isSelected = selectedCategories
                            .any((service) => service.id == item.categoryId);
                        return Row(
                          children: [
                            // Checkbox to select or deselect the category
                            Checkbox(
                              value: isSelected,
                              onChanged: (val) {
                                setState(() {
                                  if (val == true) {
                                    // Add the category to the selected list
                                    selectedCategories.add(SelectedCategory(
                                      id: item.categoryId,
                                      group: item.group,
                                      categories: item.categories,
                                      rate: _getPrice(index),
                                    ));
                                  } else {
                                    // Remove the category from the selected list
                                    selectedCategories.removeWhere((category) =>
                                        category.id == item.categoryId);
                                  }
                                });
                              },
                              activeColor: AppColors.primaryColor,
                            ),
                            // Display the category name
                            Expanded(
                              child: Text(
                                item.categories ?? "",
                                style: const TextStyle(color: Colors.black),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 6,
                              ),
                            ),
                            const SizedBox(width: 4),
                            // Display the price for the category
                            Text(
                              _getPrice(index)
                                  .toString()
                                  .toIndianCurrencyFormat(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        );
                      })
                  : const Center(
                      child: Text("No items found"),
                    ),
            ),
            // Button to navigate to the next group or summary
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 12,
              ),
              child: BaiButton(
                onTap: () {
                  // Navigate to summary if it's the last group
                  if (widget.currentGroup == widget.selectedGroups.length - 1) {
                    Get.to(RegistrationSummarySeller(
                      selectedCategories: selectedCategories,
                      req: widget.req,
                      pricing: widget.pricing,
                      isMember: widget.isMember,
                      isManufacturer: widget.isManufacturer,
                      fromProfile: widget.fromProfile,
                    ));
                  } else {
                    // Navigate to the next group
                    Get.to(
                      SelectServicesPage(
                        selectedGroups: widget.selectedGroups,
                        currentGroup: widget.currentGroup + 1,
                        pricing: widget.pricing,
                        isMember: widget.isMember,
                        isManufacturer: widget.isManufacturer,
                        req: widget.req,
                        fromProfile: widget.fromProfile,
                      ),
                      preventDuplicates: false,
                    );
                  }
                },
                text: isLastGroup
                    ? "Summary"
                    : "Next > Categories under ${widget.selectedGroups[widget.currentGroup + 1]}",
              ),
            )
          ],
        ),
      ),
    );
  }

  _getPrice(int index) {
    bool isFirstCategory = false;

    selectedCategories.sort((a, b) {
      bool aIsPriority = a.categories == "Cement" || a.categories == "Steel";
      bool bIsPriority = b.categories == "Cement" || b.categories == "Steel";

      // Move Cement and Steel to the end of the sorting
      if (!aIsPriority && bIsPriority) return -1; // a comes before b
      if (aIsPriority && !bIsPriority) return 1; // b comes before a

      // If both are priority or both are not, sort by rate
      return (b.rate ?? 0).compareTo(a.rate ?? 0);
    });

    selectedCategories.isNotEmpty
        ? isFirstCategory = selectedPricing[index].categories ==
            selectedCategories.first.categories
        : isFirstCategory = true;

    if (isFirstCategory) {
      if (widget.isManufacturer && widget.isMember) {
        return selectedPricing[index].memberManufacturerRate1 ?? 0;
      } else if (widget.isManufacturer && !widget.isMember) {
        return selectedPricing[index].manufacturerRate1 ?? 0;
      } else if (!widget.isManufacturer && widget.isMember) {
        return selectedPricing[index].memberDealerRate1 ?? 0;
      } else {
        return selectedPricing[index].dealerRate1 ?? 0;
      }
    } else {
      if (widget.isManufacturer && widget.isMember) {
        return selectedPricing[index].memberManufacturerRate2 ?? 0;
      } else if (widget.isManufacturer && !widget.isMember) {
        return selectedPricing[index].manufacturerRate2 ?? 0;
      } else if (!widget.isManufacturer && widget.isMember) {
        return selectedPricing[index].memberDealerRate2 ?? 0;
      } else {
        return selectedPricing[index].dealerRate2 ?? 0;
      }
    }
  }
}

class SelectedCategory {
  num? id;
  String? group;
  String? categories;
  num? rate;
  SelectedCategory({
    required this.id,
    required this.group,
    required this.categories,
    required this.rate,
  });
}
