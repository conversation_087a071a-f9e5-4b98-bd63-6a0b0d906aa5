import 'dart:async';
import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:audioplayers/audioplayers.dart' as ap;
import 'package:connectone/bai_blocs/add_product/cubit/add_product_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/allow_edit_res.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/item_offering_res.dart';
import 'package:connectone/bai_models/mr_req.dart' as mr_rq;
import 'package:connectone/bai_models/search_categories_res.dart';
import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_screens/add_product.dart';
import 'package:connectone/core/bai_widgets/add_project_text_form_field.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/filter_multi_select.dart';
import 'package:connectone/core/bai_widgets/mr_dropdown.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:permission_handler/permission_handler.dart';

class EditProductPage extends StatefulWidget {
  const EditProductPage({
    Key? key,
    required this.item,
    this.fromHome = false,
    required this.isMr,
  }) : super(key: key);

  final bpr.Content item;
  final bool fromHome;
  final bool isMr;

  @override
  State<EditProductPage> createState() => _EditProductPageState();
}

class _EditProductPageState extends State<EditProductPage> {
  Codec _codec = Codec.aacMP4;
  final String _mPath = 'audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
  FlutterSoundPlayer? _mPlayer = FlutterSoundPlayer();
  FlutterSoundRecorder? _mRecorder = FlutterSoundRecorder();
  var theSource = AudioSource.microphone;
  bool _mPlayerIsInited = false;
  bool _mRecorderIsInited = false;
  bool _mplaybackReady = false;

  final _formKey = GlobalKey<FormState>();

  final List<String> _images = [];
  final List<String> _files = [];
  final List<String> _audios = [];

  final List<String> _deletedMedia = [];

  bool isAddedToCart = false;

  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _instructionsController = TextEditingController();

  final ScrollController _scrollController = ScrollController();

  String? _selectedUnit;
  String? _selectedUnitId;

  bool recording = false;

  final ImagePicker picker = ImagePicker();

  bool playing = false;

  final player = ap.AudioPlayer();

  bool isSteelReinforcement = false;

  var dateController = TextEditingController();
  var deliveryDateItem = DateTime.now();

  final List<Uint8List> _imageBytes = [];
  final List<Uint8List> _fileBytes = [];

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  pickFiles() async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;

        if (kIsWeb) {
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              _files.add(file.name);
            });
          }
        } else {
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              _files.add(file.path ?? file.name);
            });
          } else if (file.path != null) {
            File selectedFile = File(file.path!);
            setState(() {
              _files.add(file.path!);
              selectedFile.readAsBytes().then((value) {
                _fileBytes.add(value);
              });
            });
          }
        }
      }
    }
  }

  bool hasAttachmentsMaxed() {
    return _images.length + _files.length + _audios.length >= 5;
  }

  @override
  void initState() {
    // NetworkController().getUnits();
    super.initState();
    context
        .read<AddEditProductCubit>()
        .loadDataForEdit(widget.item.prchOrdrId?.toString() ?? "");
    _mPlayer!.openPlayer().then((value) {
      setState(() {
        _mPlayerIsInited = true;
      });
    });

    openTheRecorder().then((value) {
      setState(() {
        _mRecorderIsInited = true;
      });
    });
    checkIfLimitExceeded();
  }

  checkIfLimitExceeded() async {
    if (BaiCart.cartItems.length >= 10) {
      alert("You can only add 10 items at a time.");
      await Future.delayed(const Duration(seconds: 4)).then((value) {
        Navigator.pop(context);
      });
    }
  }

  var queryLength = 0;

  Future<void> openTheRecorder() async {
    if (!kIsWeb) {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException(
            'Microphone permission not granted.');
      }
    }
    await _mRecorder!.openRecorder();
    if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
      _codec = Codec.opusWebM;
      if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
        _mRecorderIsInited = true;
        return;
      }
    }
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.voiceCommunication,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));

    _mRecorderIsInited = true;
  }

  List<Variant> variants = [];
  List<Variant> deletedVariants = [];

  Map<int, TextEditingController> controllers = {};

  List<String> getSelectedValues(int optionGroupId) {
    List<String> selectedValues = [];
    for (var i in variants) {
      if (i.optionGroupId == optionGroupId) {
        selectedValues.add(i.optionName ?? "");
      }
    }
    return selectedValues;
  }

  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Select Image Source",
              style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                GestureDetector(
                  child: const Text("Take using Camera"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: true);
                  },
                ),
                const SizedBox(height: 16),
                GestureDetector(
                  child: const Text("Pick from Gallery"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: false);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> getImage({bool fromCamera = true}) async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      try {
        final XFile? image = await picker.pickImage(
          source: fromCamera ? ImageSource.camera : ImageSource.gallery,
          requestFullMetadata: false,
          imageQuality: 25,
        );

        if (image != null) {
          final Uint8List imageBytes = await image.readAsBytes();

          setState(() {
            _imageBytes.add(imageBytes);
            _images.add(image.path);
          });

          safePrint('Image captured: ${image.path}');
        }
      } catch (e) {
        safePrint(e);
      }
    }
  }

  Content? selectedCategory;
  List<Content> categories = [];

  @override
  Widget build(BuildContext context) {
    var item = widget.item;
    return BlocConsumer<AddEditProductCubit, AddEditProductState>(
      listener: (context, state) {
        if (state is EditProductFailed) {
          alert(state.message);
          context
              .read<AddEditProductCubit>()
              .loadDataForEdit(widget.item.prchOrdrId?.toString() ?? "");
        }
        if (state is EditProductSuccessful) {
          alert("Edited Successfully");
          context.read<OfflineMainBloc>().add(const RefreshOfflineStocks());
          Navigator.pop(context, true);
          // context.read<AddEditProductCubit>().loadDataForEdit(widget.item.prchOrdrId?.toString() ?? "");
        }
        if (state is DeleteProductFailed) {
          alert(state.message);
          context
              .read<AddEditProductCubit>()
              .loadDataForEdit(widget.item.prchOrdrId?.toString() ?? "");
        }
        if (state is DeleteProductSuccessful) {
          alert("Deleted Successfully");
          context.read<OfflineMainBloc>().add(const RefreshOfflineStocks());
          Navigator.pop(context, true);
          // context.read<AddEditProductCubit>().loadDataForEdit(widget.item.prchOrdrId?.toString() ?? "");
        }
        if (state is EditProductLoaded) {
          var optionGroups = state.itemOfferingRes?.data?.optionGroups ?? [];
          for (var group in optionGroups) {
            if (group.optionType == "VARI" && group.valueType == "TXTX") {
              controllers[group.id?.toInt() ?? 0] = TextEditingController();
            }
          }

          dateController.text = formatDateToDDMMYYYY(
              state.mrRes?.prchOrdr?.deliveryDate ?? DateTime(2024));
          deliveryDateItem =
              state.mrRes?.prchOrdr?.deliveryDate ?? DateTime(2025);

          //////////////////////////////////////////////////////////////////

          for (var i in state.categoryRes?.content ?? []) {
            categories.add(i);
          }

          selectedCategory = categories.firstWhereOrNull(
              (element) => element.id == item.cappCategoriesId?.toString());

          variants.clear();

          // quantity
          var mrRes = state.mrRes;
          _quantityController.text =
              mrRes?.prchOrdr?.quantity?.toStringAsFixed(0) ?? "0";

          // instructions
          _instructionsController.text = mrRes?.prchOrdr?.instructions ?? "";

          // unit
          var alreadyAssignedUnit = mrRes?.optionsValues?.firstWhereOrNull(
              (elemant) =>
                  elemant.optionGroupName?.toLowerCase().contains("unit") ??
                  false);
          variants.add(Variant(
            optionGroupId: alreadyAssignedUnit?.optionGroupId?.toInt() ?? 0,
            optionGroupName: alreadyAssignedUnit?.optionGroupName ?? "",
            optionId: alreadyAssignedUnit?.optionId?.toInt() ?? 0,
            optionName: alreadyAssignedUnit?.optionName ?? "",
            offerCheckBoolean: false,
          ));
          _selectedUnit = alreadyAssignedUnit?.optionName;
          _selectedUnitId = alreadyAssignedUnit?.optionId?.toString();

          // other options
          for (var i in (mrRes?.optionsValues ?? [])) {
            if (i.optionGroupName?.toLowerCase().contains("unit") ?? false) {
              continue;
            }
            variants.add(Variant(
              optionGroupId: i.optionGroupId?.toInt() ?? 0,
              optionGroupName: i.optionGroupName ?? "",
              optionId: i.optionId?.toInt() ?? 0,
              optionName: i.optionName ?? "",
              offerCheckBoolean: false,
            ));
          }

          // txtx
          for (var i in (mrRes?.optionsValues ?? [])) {
            if (i.optionGroupName?.toLowerCase().contains("unit") ?? false) {
              continue;
            }
            controllers[i.optionGroupId?.toInt() ?? 0]?.text =
                i.optionName ?? "";
          }

          // mult already done

          // sing already done

          // media
          _images.clear();
          _imageBytes.clear();
          _files.clear();
          _fileBytes.clear();
          _audios.clear();
          for (var i in (state.mrRes?.medias ?? [])) {
            var media = i;

            if (media.url != null &&
                (media.url!.endsWith('.jpg') ||
                    media.url!.endsWith('.png') ||
                    media.url!.endsWith('.gif') ||
                    media.url!.endsWith('.jpeg'))) {
              _images.add(media.url!);
              _imageBytes.add(Uint8List(0));
            } else if (media.url != null &&
                (media.url!.endsWith('.pdf') ||
                    media.url!.endsWith('.doc') ||
                    media.url!.endsWith('.xls') ||
                    media.url!.endsWith('.docx') ||
                    media.url!.endsWith('.xlsx'))) {
              _files.add(media.url!);
              _fileBytes.add(Uint8List(0));
            } else if (media.url != null &&
                (media.url!.endsWith('.mp3') ||
                    media.url!.endsWith('.wav') ||
                    media.url!.endsWith('.mp4'))) {
              _audios.add(media.url!);
            }
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
              title:
                  Text("Edit ${widget.isMr ? "Material" : "Service"} Request"),
              backgroundColor: AppColors.primaryColor,
              elevation: 0,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              actions: const [
                // IconButton(
                //   icon: const Icon(Icons.delete_outline),
                //   onPressed: () {
                //     showDialog(
                //         context: context,
                //         builder: (context) {
                //           var style = const TextStyle(
                //             fontWeight: FontWeight.bold,
                //             color: Colors.black,
                //           );
                //           return AlertDialog(
                //             title: Text(
                //               "Delete",
                //               style: style,
                //             ),
                //             content: const Text(
                //                 "Are you sure you want to delete this Material Request?"),
                //             actions: [
                //               TextButton(
                //                   onPressed: () {
                //                     Navigator.pop(context);
                //                   },
                //                   child: Text(
                //                     "Cancel",
                //                     style: style,
                //                   )),
                //               TextButton(
                //                   onPressed: () {
                //                     var req = mr_rq.MrReq(
                //                       id: widget.item.prchOrdrId?.toInt() ?? 0,
                //                       customerId: item.customerId?.toInt() ?? 0,
                //                       vendorId: item.vendorId?.toInt() ?? 0,
                //                       mvtItemId: item.mvtItemId?.toInt() ?? 0,
                //                       mvtItemName: "${item.mvtItemName}",
                //                       price: item.price?.toDouble() ?? 0.0,
                //                       deliveryDate: item.deliveryDate,
                //                       projectId: item.projectId?.toInt() ?? 0,
                //                       siteAccess: item.siteAccess,
                //                       projectName: "${item.projectName}",
                //                       roadAccess: item.roadAccess,
                //                       quantity: int.tryParse(
                //                               _quantityController.text) ??
                //                           0,
                //                       projectAddressId:
                //                           item.projectAddressId?.toInt() ?? 0,
                //                       deliveryAddressId:
                //                           item.deliveryAddressId?.toInt() ?? 0,
                //                       statusCd: "CLSE",
                //                       instructions:
                //                           _instructionsController.text,
                //                       customerName: "${item.customerName}",
                //                       customerPhone: "${item.customerPhone}",
                //                       variants: null,
                //                       optionsValues: [],
                //                       medias: [],
                //                       deleteAllMedias: false,
                //                     );

                //                     context.read<AddEditProductCubit>().delete(
                //                           req,
                //                           widget.item.prchOrdrId.toString(),
                //                         );

                //                     Navigator.pop(context);
                //                   },
                //                   child: Text(
                //                     "Delete",
                //                     style: TextStyle(
                //                       fontWeight: FontWeight.bold,
                //                       color: Colors.red.shade800,
                //                     ),
                //                   )),
                //             ],
                //           );
                //         });
                //   },
                // )
              ]),
          body: AppLoader(
            child: BlocConsumer<AddEditProductCubit, AddEditProductState>(
              listener: (context, state) {},
              builder: (context, state) {
                var border = const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.black,
                    width: 1,
                  ),
                );
                (state is EditProductLoading)
                    ? context.loaderOverlay.show()
                    : context.loaderOverlay.hide();
                if (state is EditProductLoaded) {
                  if ((state.itemOfferingRes?.data?.name ?? "")
                      .toLowerCase()
                      .contains("reinforcement")) {
                    isSteelReinforcement = true;
                  }
                  // selectedCategory = Content(
                  //   name: item.cappCategoriesName,
                  //   id: item.cappCategoriesId.toString(),
                  // );
                  var optionGroups =
                      state.itemOfferingRes?.data?.optionGroups ?? [];
                  var unitOg = optionGroups.isNotEmpty
                      ? optionGroups.firstWhere(
                          (e) =>
                              e.name != null &&
                              e.name!.toLowerCase().contains("unit"),
                          orElse: () => OptionGroup(),
                        )
                      : null;

                  List<OptionGroup> otherOptions = [];

                  if (optionGroups.isNotEmpty) {
                    for (var group in optionGroups) {
                      if (group.name == null ||
                          !group.name!.toLowerCase().contains("unit")) {
                        otherOptions.add(group);
                      }
                    }
                  }

                  return SizedBox(
                    height: MediaQuery.of(context).size.height - 72,
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Form(
                        key: _formKey,
                        child: ListView(
                          controller: _scrollController,
                          children: [
                            Text(
                              "${item.mvtItemName}",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 20),
                            if (item.cappCategoriesId == -3 &&
                                selectedCategory != null)
                              MRDropdown(
                                labelText: "Category",
                                items: state.categoryRes?.content ?? [],
                                onChanged: (value) {
                                  setState(() {
                                    selectedCategory = value;
                                  });
                                },
                                selectedValue: selectedCategory,
                              ),
                            if (item.cappCategoriesId == -3 &&
                                selectedCategory != null)
                              const SizedBox(height: 20),
                            if (!isSteelReinforcement)
                              Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: _quantityController,
                                      decoration: InputDecoration(
                                        labelText: "Quantity",
                                        focusedBorder: border,
                                        enabledBorder: border,
                                        border: border,
                                        labelStyle: const TextStyle(
                                            color: Colors.black),
                                        // isDense: true,
                                      ),
                                      keyboardType: TextInputType.number,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Quantity is required';
                                        }
                                        if (int.tryParse(value) == null) {
                                          print(int.tryParse(value));
                                          return 'Enter a valid number';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  if (unitOg != null) const SizedBox(width: 10),
                                  if (unitOg != null)
                                    Expanded(
                                      child: DropdownButtonFormField<String>(
                                        value: _selectedUnitId,
                                        decoration: InputDecoration(
                                          labelText: unitOg.name ?? "",
                                          focusedBorder: border,
                                          enabledBorder: border,
                                          border: border,
                                          labelStyle: const TextStyle(
                                              color: Colors.black),
                                          isDense: true,
                                        ),
                                        items: unitOg.options?.map((unit) {
                                          return DropdownMenuItem(
                                            value: unit.id.toString(),
                                            child: Text(unit.name ?? ""),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          var unitOp = unitOg.options
                                              ?.firstWhere(
                                                  (element) =>
                                                      element.id.toString() ==
                                                      value,
                                                  orElse: () => Option());
                                          if (unitOp != null) {
                                            setState(() {
                                              _selectedUnitId =
                                                  unitOp.id.toString();
                                              _selectedUnit = unitOp.name;

                                              // Remove existing variants for the selected option group
                                              variants.removeWhere((variant) =>
                                                  variant.optionGroupId ==
                                                  unitOg.id?.toInt());

                                              // Add the new selected unit as a variant
                                              variants.add(Variant(
                                                optionGroupId:
                                                    unitOg.id?.toInt() ?? 0,
                                                optionGroupName:
                                                    unitOg.name ?? "",
                                                optionId:
                                                    unitOp.id?.toInt() ?? 0,
                                                optionName: unitOp.name ?? "",
                                                offerCheckBoolean: false,
                                              ));
                                            });
                                          }
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Unit is required';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                ],
                              ),
                            const SizedBox(height: 16),
                            GestureDetector(
                              onTap: () async {
                                var date = await showDatePicker(
                                  context: context,
                                  initialDate: DateTime.now(),
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime(2028),
                                );
                                if (date != null) {
                                  dateController.text =
                                      formatDateToDDMMYYYY(date);
                                  deliveryDateItem = date;
                                }
                              },
                              child: AbsorbPointer(
                                child: AddProjectTextFormField(
                                  labelText: "Choose Delivery Date",
                                  controller: dateController,
                                ),
                              ),
                            ),
                            if (isSteelReinforcement)
                              const SizedBox(height: 16),
                            if (otherOptions.isNotEmpty &&
                                !isSteelReinforcement)
                              const SizedBox(height: 20),
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                var optionType = otherOptions[index].optionType;
                                var valueType = otherOptions[index].valueType;
                                if (optionType == "VARI") {
                                  if (valueType == "SING") {
                                    return DropdownButtonFormField<String>(
                                      decoration: InputDecoration(
                                        isDense: true,
                                        focusedBorder: border,
                                        enabledBorder: border,
                                        border: border,
                                        labelText:
                                            otherOptions[index].name ?? "",
                                        labelStyle: const TextStyle(
                                            color: Colors.black),
                                      ),
                                      value: getSelectedValueSing(
                                          otherOptions[index].id?.toInt() ?? 0),
                                      items: otherOptions[index]
                                          .options
                                          ?.map((Option value) {
                                        return DropdownMenuItem<String>(
                                          value: value.id.toString(),
                                          child: Text(value.name ?? ''),
                                        );
                                      }).toList(),
                                      onChanged: (String? newValue) {
                                        var itemOg = otherOptions[index];
                                        var itemOp = itemOg.options?.firstWhere(
                                            (element) =>
                                                element.id.toString() ==
                                                newValue);
                                        setState(() {
                                          List<Variant> itemsToRemove = [];

                                          for (var i in variants) {
                                            if (i.optionGroupId ==
                                                itemOp?.optionGroupId
                                                    ?.toInt()) {
                                              itemsToRemove.add(i);
                                            }
                                          }

                                          for (var item in itemsToRemove) {
                                            variants.remove(item);
                                          }

                                          variants.add(Variant(
                                            optionGroupId:
                                                itemOg.id?.toInt() ?? 0,
                                            optionGroupName: itemOg.name ?? "",
                                            optionId: itemOp?.id?.toInt() ?? 0,
                                            optionName: itemOp?.name ?? "",
                                            offerCheckBoolean: true,
                                          ));
                                        });
                                      },
                                      hint:
                                          Text(otherOptions[index].name ?? ''),
                                    );
                                  }
                                  if (valueType == "MULT") {
                                    var item = otherOptions[index];
                                    if (isSteelReinforcement &&
                                        (item.name ?? "")
                                            .toLowerCase()
                                            .contains('size')) {
                                      return FilterMultiSelectNewSteel(
                                        items: item.options!
                                            .map((e) => e.name.toString())
                                            .toList(),
                                        optionGroup: unitOg,
                                        onChanged: (selectedValues) {
                                          var itemOg = otherOptions[index];
                                          var itemOgId =
                                              itemOg.id?.toInt() ?? 0;

                                          // Remove the existing variants for the current item group
                                          setState(() {
                                            variants.removeWhere((variant) =>
                                                variant.optionGroupId ==
                                                itemOgId);
                                          });

                                          // Add the new selected values to the variants
                                          List<Variant> newVariants =
                                              selectedValues.map((value) {
                                            String itemValue =
                                                value.split(' - ')[0];

                                            var option = item.options!
                                                .firstWhere((element) => element
                                                    .name!
                                                    .contains(itemValue));
                                            return Variant(
                                              optionGroupId: itemOgId,
                                              optionGroupName:
                                                  itemOg.name ?? "",
                                              optionId: option.id?.toInt() ?? 0,
                                              optionName: value,
                                              offerCheckBoolean: true,
                                            );
                                          }).toList();

                                          setState(() {
                                            variants.addAll(newVariants);
                                          });
                                        },
                                        labelText: item.name ?? 'N/A',
                                        selectedValues: getSelectedValues(
                                            item.id?.toInt() ?? 0),
                                      );
                                    } else {
                                      return FilterMultiSelectNew(
                                        items: item.options!
                                            .map((e) => e.name.toString())
                                            .toList(),
                                        onChanged: (selectedValues) {
                                          var itemOg = otherOptions[index];
                                          var itemOgId =
                                              itemOg.id?.toInt() ?? 0;

                                          // Remove the existing variants for the current item group
                                          setState(() {
                                            variants.removeWhere((variant) =>
                                                variant.optionGroupId ==
                                                itemOgId);
                                          });

                                          // Add the new selected values to the variants
                                          List<Variant> newVariants =
                                              selectedValues.map((value) {
                                            String result =
                                                value.split(' - ')[0];
                                            var option = item.options!
                                                .firstWhere((element) =>
                                                    element.name == result);
                                            return Variant(
                                              optionGroupId: itemOgId,
                                              optionGroupName:
                                                  itemOg.name ?? "",
                                              optionId: option.id?.toInt() ?? 0,
                                              optionName: value,
                                              offerCheckBoolean: true,
                                            );
                                          }).toList();
                                          setState(() {
                                            variants.addAll(newVariants);
                                          });
                                        },
                                        labelText: item.name ?? 'N/A',
                                        selectedValues: getSelectedValues(
                                            item.id?.toInt() ?? 0),
                                      );
                                    }
                                  }
                                  if (valueType == "TXTX") {
                                    var itemOg = otherOptions[index];
                                    return TextFormField(
                                      controller:
                                          controllers[itemOg.id?.toInt() ?? 0],
                                      decoration: InputDecoration(
                                        isDense: true,
                                        labelText: itemOg.name ?? 'N/A',
                                        hintText: itemOg.name ?? 'N/A',
                                        labelStyle: const TextStyle(
                                            color: Colors.black),
                                        focusedBorder: border,
                                        enabledBorder: border,
                                        border: border,
                                      ),
                                      onChanged: (value) {
                                        variants.removeWhere((element) =>
                                            element.optionGroupId ==
                                            itemOg.id?.toInt());
                                        variants.add(Variant(
                                          optionGroupId:
                                              itemOg.id?.toInt() ?? 0,
                                          optionGroupName: itemOg.name ?? "",
                                          optionId: 0,
                                          optionName: value,
                                          offerCheckBoolean: true,
                                        ));
                                      },
                                    );
                                  }
                                }
                                return const SizedBox.shrink();
                              },
                              itemCount: otherOptions.length,
                              separatorBuilder:
                                  (BuildContext context, int index) {
                                return const SizedBox(height: 20);
                              },
                            ),
                            const SizedBox(height: 20),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Upload / Attach Photo",
                                      style: TextStyle(fontSize: 14),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        if (_images.isNotEmpty)
                                          Flexible(
                                            child: SizedBox(
                                              height: 76,
                                              child: ListView.separated(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemCount: _images.length,
                                                padding:
                                                    const EdgeInsets.all(6),
                                                itemBuilder: (context, index) {
                                                  return Stack(
                                                    clipBehavior: Clip.none,
                                                    children: [
                                                      Container(
                                                        decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8)),
                                                        clipBehavior:
                                                            Clip.hardEdge,
                                                        child: kIsWeb
                                                            ? Image.network(
                                                                _images[index],
                                                                width: 64,
                                                                height: 64,
                                                                fit: BoxFit
                                                                    .cover,
                                                              )
                                                            : isHttpsUrl(
                                                                    _images[
                                                                        index])
                                                                ? Image.network(
                                                                    _images[
                                                                        index],
                                                                    width: 64,
                                                                    height: 64,
                                                                    fit: BoxFit
                                                                        .cover,
                                                                  )
                                                                : Image.file(
                                                                    File(_images[
                                                                        index]),
                                                                    width: 64,
                                                                    height: 64,
                                                                    fit: BoxFit
                                                                        .cover,
                                                                  ),
                                                      ),
                                                      Positioned(
                                                        top: -8,
                                                        right: -8,
                                                        child: InkWell(
                                                          onTap: () {
                                                            setState(() {
                                                              _images.removeAt(
                                                                  index);
                                                              _imageBytes
                                                                  .removeAt(
                                                                      index);
                                                            });
                                                          },
                                                          child: Container(
                                                            decoration:
                                                                BoxDecoration(
                                                              border: Border.all(
                                                                  color: Colors
                                                                      .red),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          16),
                                                              color: Colors
                                                                  .transparent,
                                                            ),
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(2),
                                                            child: const Icon(
                                                              Icons.close,
                                                              size: 16,
                                                              color: Colors.red,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                                separatorBuilder:
                                                    (BuildContext context,
                                                        int index) {
                                                  return const SizedBox(
                                                      width: 12);
                                                },
                                              ),
                                            ),
                                          ),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            color: Colors.grey.shade200,
                                          ),
                                          child: IconButton(
                                            icon: const Icon(Icons.photo),
                                            onPressed: () {
                                              setState(() {
                                                showImageSourceDialog(context);
                                              });
                                            },
                                          ),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            Container(
                              // height: 56,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 8),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                            child: Text(_audios.isNotEmpty
                                                ? "Voice Note Added"
                                                : "Add Voice Note")),
                                        const Icon(Icons.mic),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Row(children: [
                                      SizedBox(
                                        width: kIsWeb
                                            ? 100
                                            : MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.25,
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  AppColors.primaryColor),
                                          onPressed: () {
                                            getRecorderFn();
                                          },
                                          child: Text(_mRecorder!.isRecording
                                              ? 'Stop'
                                              : 'Record'),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Text(_mRecorder!.isRecording
                                          ? 'Recording in progress'
                                          : _audios.isNotEmpty
                                              ? 'Discard & record again'
                                              : 'Recorder is stopped'),
                                    ]),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            if (_audios.isNotEmpty)
                              Container(
                                  padding: const EdgeInsets.all(8),
                                  height: 80,
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.black),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(children: [
                                    SizedBox(
                                      width: kIsWeb
                                          ? 100
                                          : MediaQuery.of(context).size.width *
                                              0.25,
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                AppColors.primaryColor),
                                        onPressed: () {
                                          isHttpsUrl(_audios[0])
                                              ? _togglePlayback()
                                              : getPlaybackFn();
                                        },
                                        child: Text(
                                            (_mPlayer!.isPlaying || playing)
                                                ? 'Stop'
                                                : 'Play'),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Text((_mPlayer!.isPlaying || playing)
                                        ? 'Playback in progress'
                                        : 'Play recorded audio'),
                                    const Spacer(),
                                    IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _audios.clear();
                                          });
                                        },
                                        icon: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                        ))
                                  ])),
                            if (_audios.isNotEmpty) const SizedBox(height: 20),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      "Upload Additional Specifications / Docs",
                                      style: TextStyle(fontSize: 14),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        if (_files.isNotEmpty)
                                          Flexible(
                                            child: SizedBox(
                                              height: 76,
                                              child: ListView.separated(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemCount: _files.length,
                                                padding:
                                                    const EdgeInsets.all(6),
                                                itemBuilder: (context, index) {
                                                  return Stack(
                                                    clipBehavior: Clip.none,
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(8),
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                          color: Colors
                                                              .grey.shade200,
                                                        ),
                                                        child: IconButton(
                                                          icon: Icon(
                                                              Icons
                                                                  .file_copy_outlined,
                                                              color: AppColors
                                                                  .primaryColor),
                                                          onPressed: () {},
                                                        ),
                                                      ),
                                                      Positioned(
                                                        top: -8,
                                                        right: -8,
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            setState(() {
                                                              _files.removeAt(
                                                                  index);
                                                              _fileBytes
                                                                  .removeAt(
                                                                      index);
                                                            });
                                                          },
                                                          child: Container(
                                                            decoration:
                                                                BoxDecoration(
                                                              border: Border.all(
                                                                  color: Colors
                                                                      .red),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          16),
                                                            ),
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(2),
                                                            child: const Icon(
                                                              Icons.close,
                                                              size: 16,
                                                              color: Colors.red,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                                separatorBuilder:
                                                    (BuildContext context,
                                                        int index) {
                                                  return const SizedBox(
                                                      width: 12);
                                                },
                                              ),
                                            ),
                                          ),
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            color: Colors.grey.shade200,
                                          ),
                                          child: IconButton(
                                            icon: const Icon(
                                                Icons.file_upload_outlined),
                                            onPressed: () {
                                              setState(() {
                                                pickFiles();
                                              });
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            TextField(
                              controller: _instructionsController,
                              textAlign: TextAlign.start,
                              maxLength: 500,
                              decoration: InputDecoration(
                                labelText: "Add Instructions",
                                alignLabelWithHint: true,
                                focusedBorder: border,
                                enabledBorder: border,
                                border: border,
                                labelStyle:
                                    const TextStyle(color: Colors.black),
                                isDense: true,
                              ),
                              maxLines: 4,
                            ),
                            const SizedBox(height: 20),
                            BaiButton(
                              onTap: () async {
                                ////////////////////////////////////////////////////
                                var oldMedias = state.mrRes?.medias;
                                List<String> newMedias = [
                                  ..._images,
                                  ..._audios,
                                  ..._files,
                                ];

                                List<Uint8List> newMediasWeb = [
                                  ..._imageBytes,
                                  ..._fileBytes,
                                ];

                                List<String> newMediaPaths = [];
                                List<Uint8List> newMediasWebFinal = [];

                                for (var i in newMedias) {
                                  if (!isHttpsUrl(i)) {
                                    newMediaPaths.add(i);
                                    newMediasWebFinal.add(
                                        newMediasWeb[newMediaPaths.indexOf(i)]);
                                  }
                                }

                                //  for (var i in newMediasWeb) {
                                //   if (!isHttpsUrl(i)) {
                                //     newMediasWebFinal.add(i);
                                //   }
                                // }

                                var deletedMediaIds = [];

                                for (var oldMedia in oldMedias ?? []) {
                                  if (!newMedias.contains(oldMedia.url)) {
                                    deletedMediaIds.add(oldMedia.id);
                                  }
                                }
                                /////////////////////////////////////////////////////
                                List<mr_rq.Media> deletedMedias = [];
                                List<mr_rq.Media> notDeletedMedias = [];
                                for (var i in deletedMediaIds) {
                                  deletedMedias.add(mr_rq.Media(
                                    id: i,
                                    delete: true,
                                  ));
                                }
                                for (var i in oldMedias ?? []) {
                                  if (newMedias.contains(i.url)) {
                                    notDeletedMedias.add(mr_rq.Media(
                                      id: i.id,
                                      url: i.url,
                                      delete: false,
                                      previewUrl: i.previewUrl,
                                      docType: i.docType,
                                      title: i.title,
                                      sequence: i.sequence,
                                    ));
                                  }
                                }
                                ////////////////////////////////////////////////////
                                var media = [
                                  ...deletedMedias,
                                  ...notDeletedMedias
                                ];
                                ////////////////////////////////////////////////////
                                var oldOptionValues =
                                    state.mrRes?.optionsValues ?? [];
                                var newOptionValues = variants;

                                List<mr_rq.OptionsValue> deletedOptionValues =
                                    [];
                                List<mr_rq.OptionsValue>
                                    notDeletedOptionValues = [];

                                var newOptionIds = newOptionValues
                                    .map((option) => option.optionId)
                                    .toSet();

                                for (var option in oldOptionValues) {
                                  if (!newOptionIds.contains(option.optionId)) {
                                    deletedOptionValues.add(mr_rq.OptionsValue(
                                      id: option.id?.toInt(),
                                      delete: true,
                                    ));
                                  } else {
                                    var item = newOptionValues.firstWhere(
                                        (e) => e.optionId == option.optionId);
                                    notDeletedOptionValues.add(
                                        mr_rq.OptionsValue(
                                            id: option.id?.toInt(),
                                            optionId: option.optionId?.toInt(),
                                            optionName: item.optionName,
                                            optionGroupId:
                                                option.optionGroupId?.toInt(),
                                            optionGroupName:
                                                option.optionGroupName,
                                            delete: false,
                                            offerCheckBoolean: (option
                                                        .optionGroupName
                                                        ?.toLowerCase()
                                                        .contains("unit") ??
                                                    false)
                                                ? false
                                                : true));
                                  }
                                }

                                for (var newOption in newOptionValues) {
                                  if (!oldOptionValues.any((oldOption) =>
                                      oldOption.optionId ==
                                      newOption.optionId)) {
                                    var item = newOptionValues.firstWhere((e) =>
                                        e.optionId == newOption.optionId);
                                    notDeletedOptionValues.add(
                                        mr_rq.OptionsValue(
                                            optionId:
                                                newOption.optionId?.toInt(),
                                            optionName: item.optionName,
                                            optionGroupId: newOption
                                                .optionGroupId
                                                ?.toInt(),
                                            optionGroupName:
                                                newOption.optionGroupName,
                                            delete: false,
                                            offerCheckBoolean: (newOption
                                                        .optionGroupName
                                                        ?.toLowerCase()
                                                        .contains("unit") ??
                                                    false)
                                                ? false
                                                : true));
                                  }
                                }

                                List<mr_rq.OptionsValue> finalOptionValues = [
                                  ...deletedOptionValues,
                                  ...notDeletedOptionValues,
                                ];
                                ////////////////////////////////////////////////////
                                var req = mr_rq.MrReq(
                                  id: widget.item.prchOrdrId?.toInt() ?? 0,
                                  customerId: item.customerId?.toInt() ?? 0,
                                  vendorId: item.vendorId?.toInt() ?? 0,
                                  mvtItemId: item.mvtItemId?.toInt() ?? 0,
                                  mvtItemName: "${item.mvtItemName}",
                                  price: item.price?.toDouble() ?? 0.0,
                                  deliveryDate: deliveryDateItem,
                                  projectId: item.projectId?.toInt() ?? 0,
                                  siteAccess: item.siteAccess,
                                  projectName: "${item.projectName}",
                                  roadAccess: item.roadAccess,
                                  quantity:
                                      int.tryParse(_quantityController.text) ??
                                          0,
                                  projectAddressId:
                                      item.projectAddressId?.toInt() ?? 0,
                                  deliveryAddressId:
                                      item.deliveryAddressId?.toInt() ?? 0,
                                  statusCd: item.statusCd,
                                  instructions: _instructionsController.text,
                                  customerName: "${item.customerName}",
                                  customerPhone: "${item.customerPhone}",
                                  variants: null,
                                  optionsValues: finalOptionValues,
                                  medias: media,
                                  deleteAllMedias: false,
                                  cappCategoriesId:
                                      ((item.cappCategoriesId == -3 &&
                                              selectedCategory != null))
                                          ? selectedCategory!.id
                                          : null,
                                  cappCategoriesName:
                                      ((item.cappCategoriesId == -3 &&
                                              selectedCategory != null))
                                          ? selectedCategory!.name
                                          : null,
                                );

                                if (_formKey.currentState!.validate()) {
                                  var allow = await allowEdit(
                                    deliveryDate: deliveryDateItem,
                                    orderGroupId:
                                        widget.item.orderGroupId?.toString() ??
                                            "0",
                                  );
                                  if (!allow) {
                                    // properAlert(
                                    //     'Can not edit the Delivery Date as there are more MRs attached to this group. \n\nPlease create a new MR to change the delivery date.');
                                    // return;
                                  }
                                  context.read<AddEditProductCubit>().update(
                                        req,
                                        newMediaPaths,
                                        widget.item.prchOrdrId?.toString() ??
                                            "0",
                                        newMediasWebFinal,
                                      );
                                  _scrollToBottom();
                                }
                              },
                              text: "SUBMIT EDIT",
                              backgoundColor: Colors.green.shade900,
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ),
        );
      },
    );
  }

  bool isHttpsUrl(String url) {
    return url.startsWith('http://') || url.startsWith('https://');
  }

  @override
  void dispose() {
    _mPlayer!.closePlayer();
    _mPlayer = null;
    _mRecorder!.closeRecorder();
    _mRecorder = null;
    super.dispose();
  }

  void record() {
    _mRecorder!
        .startRecorder(
      toFile: _mPath,
      codec: _codec,
      audioSource: theSource,
    )
        .then((value) {
      setState(() {});
    });
  }

  void stopRecorder() async {
    await _mRecorder!.stopRecorder().then((value) {
      setState(() {
        //var url = value;
        if (hasAttachmentsMaxed()) {
          _audios.clear();
          _mplaybackReady = true;
          alert(
              "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
        } else {
          _audios.clear();
          if (value != null) {
            _audios.add(value);
          }
          _mplaybackReady = true;
        }
      });
    });
  }

  void play() {
    assert(_mPlayerIsInited &&
        _mplaybackReady &&
        _mRecorder!.isStopped &&
        _mPlayer!.isStopped);
    _mPlayer!
        .startPlayer(
            fromURI: _mPath,
            whenFinished: () {
              setState(() {});
            })
        .then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer!.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(ap.UrlSource(_audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  getRecorderFn() {
    if (!_mRecorderIsInited || !_mPlayer!.isStopped) {
      return null;
    }

    return _mRecorder!.isStopped ? record() : stopRecorder();
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady || !_mRecorder!.isStopped) {
      return null;
    }

    return _mPlayer!.isStopped ? play() : stopPlayer();
  }

  getSelectedValueSing(int optionGroupId) {
    String? selectedValue;
    for (var i in variants) {
      if (i.optionGroupId == optionGroupId) {
        selectedValue = i.optionId.toString();
      }
    }
    return selectedValue;
  }

  Future<bool> allowEdit({
    required DateTime deliveryDate,
    required String orderGroupId,
  }) async {
    try {
      AllowEditRes res = await NetworkController().allowEdit(
        deliveryDate: deliveryDate.toIso8601String(),
        orderGroupId: orderGroupId,
      );
      return !(res.data?.isDeliveryDateDifferent ?? false);
    } catch (e) {
      return true;
    }
  }
}
