import 'dart:ui';

import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/insert_stock_res.dart';
import 'package:connectone/bai_screens/bai_home.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../bai_cart/bai_cart.dart';
import '../core/bai_widgets/help_info.dart';
import '../core/bai_widgets/split_group_name_dialog.dart';
import '../core/utils/app_routes.dart';
import 'project_details.dart';

class ContinueShoppingScreen extends StatefulWidget {
  const ContinueShoppingScreen({
    required this.insertStockRes,
    Key? key,
    required this.mobileNumber,
    required this.address,
    required this.req,
    required this.isMr,
    this.deliveryDate,
  }) : super(key: key);

  final InsertStockRes insertStockRes;
  final String mobileNumber;
  final Address address;
  final InsertStockReq req;
  final bool isMr;
  final DateTime? deliveryDate;

  @override
  State<ContinueShoppingScreen> createState() => _ContinueShoppingScreenState();
}

class _ContinueShoppingScreenState extends State<ContinueShoppingScreen> {
  bool emailMe = false;

  @override
  void initState() {
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  /// Shows the split group dialog for creating a new split
  Future<void> _showSplitGroupDialog(
    BuildContext context,
    int prchOrdrId,
    String categoryName,
  ) async {
    final splitGroupResult = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return SplitGroupNameDialog(
          fixedPrefix: "$categoryName ",
          prchOrdrId: prchOrdrId,
        );
      },
    );

    // If user confirmed the split, proceed to ProjectDetails
    if (splitGroupResult != null && splitGroupResult['id'] != null) {
      var content = BaiCart.getContent();
      if (content == null) {
        return;
      }
      var orderGroupId = content.orderGroupId;
      var category = content.cappCategoriesName;
      var categoryId = content.cappCategoriesId;
      Get.to(
        ProjectDetails(
          orderGroupId: orderGroupId?.toInt(),
          deliveryDate: splitGroupResult['date'],
          category: category,
          isMr: content.isMr ?? true,
          splitSiteId: content.projectId?.toInt(),
          splitGroupId: splitGroupResult['id'],
          categoryId: categoryId?.toInt(),
          splitGroupName: splitGroupResult['name'],
        ),
      );
    }
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to your request confirmation!\n\nHere you can find all the important details about your submitted request:\n\n• Your Material/Service Request ID\n• Delivery address and contact information\n• Complete list of requested items with specifications\n• Expected delivery date\n\nYou can also track the status of your request from the home screen.\n\nNeed help? Use the contact information provided below.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  /// Builds the widget tree for the ContinueShoppingScreen
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        elevation: 0,
        title: const Text('Site Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (widget.deliveryDate != null) {
              Get.until(
                  (route) => route.settings.name == AppRoutes.offlineScreen);
            } else {
              Get.offAll(const BaiHome());
            }
          },
        ),
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Display a confirmation message
            const SizedBox(height: 4),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(color: AppColors.primaryColor),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.done,
                          color: AppColors.primaryColor,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: kIsWeb
                                ? 300
                                : MediaQuery.of(context).size.width * 0.6,
                            child: Text(
                              // 'Confirmation for ${widget.isMr ? "Material" : "Service"} Request ID ${widget.insertStockRes.orders?.map((order) => order.id.toString()).join(', ') ?? 'N/A'}',
                              'Confirmation for ${widget.isMr ? "Material" : "Service"} Request ID ${widget.insertStockRes.orders?[0].orderGrpName ?? 'N/A'}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 2,
                            ),
                          ),
                          Text(
                            'Thank you, ${getVendorName()}!',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Display the material request details
                Container(
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        color: Colors.white,
                        padding: const EdgeInsets.only(
                          left: 12,
                          top: 12,
                          bottom: 12,
                          right: 24,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your ${widget.isMr ? "Material" : "Service"} request has been confirmed',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Text(
                              "You'll receive a confirmation email with your ${widget.isMr ? "Material" : "Service"} request number shortly.",
                              style: const TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Container(
                      //   color: Colors.grey.shade100,
                      //   padding: const EdgeInsets.symmetric(
                      //     horizontal: 0,
                      //     vertical: 6,
                      //   ),
                      //   child: Row(
                      //     children: [
                      //       Checkbox(
                      //         value: emailMe,
                      //         onChanged: (bool? value) {
                      //           setState(() {
                      //             emailMe = value!;
                      //           });
                      //         },
                      //       ),
                      //       const Text(
                      //         'Email me with news and offers',
                      //         style: TextStyle(
                      //           fontSize: 14,
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Display the contact information
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
              ),
              child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SectionTitle(
                          title:
                              '${widget.isMr ? "Material" : "Service"} Request Details'),
                      const SizedBox(height: 12),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: widget.req.poStockItems?.length ?? 0,
                        itemBuilder: (context, index) {
                          var item = widget.req.poStockItems?[index];
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item?.mvtItemName ?? "-",
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                  "Quantity: ${item?.quantity.toString() ?? "-"}"),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: (item?.variants ?? [])
                                    .fold<Map<String, List<String>>>({},
                                        (map, variant) {
                                      map
                                          .putIfAbsent(
                                              variant.optionGroupName ?? "",
                                              () => [])
                                          .add(variant.optionName ?? "");
                                      return map;
                                    })
                                    .entries
                                    .map<Widget>((entry) {
                                      return Text(
                                        "${entry.key}: ${entry.value.join(', ')}",
                                        style: const TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontSize: 14,
                                        ),
                                      );
                                    })
                                    .toList(),
                              ),
                            ],
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return Divider(
                            color: Colors.grey.shade300,
                            thickness: 1,
                          );
                        },
                      ),
                    ],
                  )),
            ),
            const SizedBox(height: 16),
            // Display the shipping address
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const SectionTitle(title: 'Contact Information'),
                    const SizedBox(height: 5),
                    Text(widget.mobileNumber),
                    const SizedBox(height: 10),
                    const SectionTitle(title: 'Shipping Address'),
                    const SizedBox(height: 5),
                    Text(
                        "${widget.req.projectName}\n${widget.address.addressLine1}\n${widget.address.addressLine2}\n${widget.address.city}\n${widget.address.country}\n${widget.address.state}\n${widget.address.pincode}"),
                    const SizedBox(height: 10),
                    const SectionTitle(title: 'Delivery Date'),
                    Text(widget.req.deliveryDate?.toDeliveryOn() ?? "N/A"),
                    const SizedBox(height: 10),
                    const SectionTitle(title: 'Site Access'),
                    Text(widget.req.roadAccess ?? "N/A"),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            BaiCart.hasSplitItems()
                ? BaiButton(
                    onTap: () async {
                      // Get the latest split group ID from cart
                      final prchOrdrId = int.tryParse(getPrchOrdrId());
                      // Category name
                      final categoryName = getCategoryName();
                      if (prchOrdrId != null) {
                        // Show the split dialog on current page
                        await _showSplitGroupDialog(
                          context,
                          prchOrdrId,
                          categoryName ?? "N/A",
                        );
                        // Clear the cart after split dialog is handled
                        _clearCart();
                      }
                    },
                    text: "NEW SPLIT",
                    backgoundColor: AppColors.maroon,
                  )
                : const SizedBox.shrink(),
            BaiCart.hasSplitItems()
                ? const SizedBox(height: 16)
                : const SizedBox.shrink(),
            BaiCart.hasSplitItems()
                ? BaiButton(
                    onTap: () {
                      context
                          .read<OfflineMainBloc>()
                          .add(const RefreshOfflineStocks());
                      if (BaiCart.fromMrList == true) {
                        Get.until((route) =>
                            route.settings.name == AppRoutes.offlineScreen);
                      } else {
                        Get.offAll(const BaiHome());
                      }
                      // Clear the cart
                      _clearCart();
                    },
                    text: "COMPLETE SPLIT",
                    backgoundColor: AppColors.primaryColorOld,
                  )
                : const SizedBox.shrink(),
            BaiCart.hasSplitItems()
                ? const SizedBox(height: 16)
                : const SizedBox.shrink(),
            // Display the "Go to Home" button
            BaiButton(
              onTap: () {
                // if (widget.deliveryDate != null) {
                //   context
                //       .read<OfflineMainBloc>()
                //       .add(const RefreshOfflineStocks());
                //   Get.until((route) =>
                //       route.settings.name == AppRoutes.offlineScreen);
                // } else {
                Get.offAll(const BaiHome());
                // Clear the cart
                _clearCart();
                // }
              },
              text: "GO TO HOME",
            ),
            const SizedBox(height: 16),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     const Text(
            //       'Need help?',
            //       style: TextStyle(
            //         fontSize: 12,
            //       ),
            //     ),
            //     TextButton(
            //       onPressed: () {
            //         var url = DataStorage.configData?.firstWhere((element) => element?.keyName1 == "contact_us_page_link")?.valueName1 ?? additionalUrl;
            //         Get.toNamed(AppRoutes.helpScreen1, arguments: ["CONTACTUS", url]);
            //       },
            //       child: const Text(
            //         'Contact us',
            //         style: TextStyle(
            //           color: Colors.blue,
            //           decoration: TextDecoration.underline,
            //           fontSize: 12,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 20),
            // const Center(
            //   child: Text(
            //     '© All rights reserved. Bai Store.',
            //     style: TextStyle(
            //       fontSize: 12,
            //       color: Colors.black,
            //     ),
            //   ),
            // ),
            // const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _clearCart() {
    BaiCart.cartItems.clear();
  }
}

class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 16,
      ),
    );
  }
}
