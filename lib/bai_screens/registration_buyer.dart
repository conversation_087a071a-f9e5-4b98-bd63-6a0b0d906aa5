import 'dart:ui';

import 'package:connectone/bai_blocs/buyer_reg/cubit/buyer_registration_cubit.dart';
import 'package:connectone/bai_models/vendors_res.dart';
import 'package:connectone/bai_screens/registration_summary_buyer.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class RegistrationBuyerScreen extends StatefulWidget {
  const RegistrationBuyerScreen({Key? key}) : super(key: key);

  @override
  _RegistrationBuyerScreenState createState() =>
      _RegistrationBuyerScreenState();
}

class _RegistrationBuyerScreenState extends State<RegistrationBuyerScreen> {
  final _formKey = GlobalKey<FormState>();

  bool _showVendors = false;
  bool _showNatures = false;
  bool _showDesignations = false;
  bool _showGst = false;
  bool _showAddress = false;
  bool _showNext1 = false;
  bool _showNext2 = false;

  final TextEditingController _gstController = TextEditingController();
  final TextEditingController _addressController1 = TextEditingController();
  final TextEditingController _addressController2 = TextEditingController();
  final TextEditingController _memberController = TextEditingController();

  final List<String> _selectedNatureOfBusinesses = [];
  String? _selectedDesignation;
  String _selectedDesignationId = "";
  String? _selectedVendor;
  Vendor? _selectedVendors;
  String? _selectedVendorId = "";
  String? _selectedDistrict;
  String _selectedDistrictId = "";

  int queryLength = 0;

  @override
  void initState() {
    super.initState();
    context.read<BuyerRegistrationCubit>().loadData();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Buyer Registration!\n\n1. Select if you are a BAI member\n\n2. Choose your BAI Centre/District\n\n3. Select your vendor and designation\n\n4. For admin roles, provide GST details and nature of business\n\n5. Fill in your address information\n\nComplete all required fields marked with * to proceed.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the registration form for a buyer.
  ///
  /// This widget displays a multi-step registration form where users can select
  /// bai Centres, vendors, designations, and nature of businesses. Based on the
  /// selections, additional fields such as GSTIN and address lines are shown.
  /// The form includes validation logic to ensure correct input for fields like
  /// GSTIN. Upon successful validation, the form data is encapsulated in a
  /// `BuyerRegData` object and navigates to the `RegistrationBuyerSummaryScreen`.
  ///
  /// The UI components displayed change dynamically based on user inputs,
  /// leveraging state variables like `_showVendors`, `_showDesignations`,
  /// `_showNatures`, `_showGst`, `_showAddress`, `_showNext1`, and `_showNext2`.
  ///
  /// Uses a `BlocConsumer` to handle state changes for loading and loaded states
  /// of buyer registration data.
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        title: const Text('Registration - Buyer'),
        elevation: 0,
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      body: AppLoader(
        color: Colors.white30,
        child: BlocConsumer<BuyerRegistrationCubit, BuyerRegistrationState>(
          listener: (context, state) {},
          builder: (context, state) {
            (state is BuyerRegistrationLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is BuyerRegistrationLoaded) {
              return SizedBox(
                height: MediaQuery.of(context).size.height - 72,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: <Widget>[
                        const SizedBox(height: 12),
                        RegistrationDropdown(
                          labelText: 'bai Centres',
                          selectedValue: _selectedDistrict,
                          items: state.districts!.districts!
                              .map((e) => e.name.toString())
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _showVendors = true;
                              _selectedDistrict = value;
                              _selectedVendor = null;
                              _selectedVendorId = "";
                              _selectedDistrictId = state.districts!.districts!
                                  .firstWhere(
                                      (element) => element.name == value)
                                  .id
                                  .toString();
                              context
                                  .read<BuyerRegistrationCubit>()
                                  .getVendors(_selectedDistrictId);
                            });
                          },
                        ),
                        const SizedBox(height: 15),
                        if (_showVendors)
                          DropdownSearch<Vendor>(
                            popupProps: PopupProps.menu(
                              showSearchBox: true,
                              itemBuilder: (context, item, isSelected) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 10),
                                  child: Text(item.name ?? "N/A",
                                      style: TextStyle(
                                          color: AppColors.primaryColor,
                                          fontWeight: FontWeight.bold)),
                                );
                              },
                            ),
                            items: state.vendors!.vendors!,
                            filterFn: (item, filter) {
                              return item.name
                                      ?.toLowerCase()
                                      .contains(filter) ??
                                  false;
                            },
                            dropdownDecoratorProps: DropDownDecoratorProps(
                              dropdownSearchDecoration: InputDecoration(
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4.0),
                                    borderSide: const BorderSide(
                                        color: Colors.white,
                                        width:
                                            1.0), // Change the border color here
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4.0),
                                    borderSide: const BorderSide(
                                        color: Colors.white,
                                        width:
                                            1.0), // Change the focused border color here
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(4.0),
                                    borderSide: const BorderSide(
                                        color: Colors.white, width: 1.0),
                                  ),
                                  isDense: true,
                                  labelText: "Members",
                                  // hintText: "country in menu mode",
                                  labelStyle:
                                      const TextStyle(color: AppColors.white)),
                            ),
                            dropdownBuilder: (context, item) {
                              print('value----------${item?.name.toString()}');
                              return Text(
                                item?.name ?? "N/A",
                                style: const TextStyle(color: Colors.white),
                              );
                            },
                            onChanged: (item) {
                              setState(() {
                                _selectedDesignation = null;
                                _selectedDesignationId = "";
                                _selectedVendor = item?.name;
                                _selectedVendors = item;
                                _selectedVendorId = item?.id.toString();

                                context
                                    .read<BuyerRegistrationCubit>()
                                    .getRoles(_selectedVendorId ?? "0", 'BUYR');
                                _showDesignations = true;
                              });
                            },
                            selectedItem: _selectedVendors,
                          ),
                        const SizedBox(height: 15),
                        if (_showDesignations)
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              RegistrationDropdown(
                                labelText: 'Designation',
                                selectedValue: _selectedDesignation,
                                items: state.roles!
                                    .map((e) => e.name.toString())
                                    .toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedDesignation = value;
                                    _selectedDesignationId = state.roles!
                                        .firstWhere(
                                            (element) => element.name == value)
                                        .id
                                        .toString();
                                    if (_selectedDesignation == "Admin") {
                                      _showNatures = true;
                                      _showGst = true;
                                      _showAddress = false;
                                      _showNext1 = false;
                                      _showNext2 = false;
                                    } else {
                                      _showNatures = false;
                                      _showGst = false;
                                      _showAddress = false;
                                      _showNext1 = true;
                                      _showNext2 = true;
                                    }
                                    // }
                                  });
                                },
                              ),
                            ],
                          ),
                        if (_showNatures)
                          Container(
                            margin: const EdgeInsets.only(top: 20),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Column(
                              children: [
                                MultiSelectDropdown(
                                  labelText: 'Nature of Businesses',
                                  items: state.natures!.natureOfBusiness!
                                      .map((e) => e.name.toString())
                                      .toList(),
                                  selectedValues: _selectedNatureOfBusinesses,
                                  onChanged: (value) {
                                    setState(() {
                                      if (_selectedNatureOfBusinesses.isEmpty) {
                                        setState(() {
                                          print(
                                              'val-----$_selectedNatureOfBusinesses');
                                          _gstController.clear();
                                          _addressController1.clear();
                                          _addressController2.clear();
                                        });
                                      }
                                      if (_selectedDesignation == "Admin") {
                                        _showGst = true;
                                        _showAddress = false;
                                        _showNext1 = false;
                                        _showNext2 = false;
                                      } else {
                                        _showGst = false;
                                        _showAddress = false;
                                        _showNext1 = true;
                                        _showNext2 = true;
                                      }
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        if (_showGst && _selectedNatureOfBusinesses.isNotEmpty)
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              RegistrationRichTextField(
                                labelText: 'GSTIN',
                                maxLength: 15,
                                controller: _gstController,
                                capitalize: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'This field is required';
                                  }
                                  if (!RegExp(r'^\d{2}').hasMatch(value)) {
                                    return 'First two characters must be digits';
                                  }
                                  if (!RegExp(r'^[A-Za-z0-9]{15}')
                                      .hasMatch(value)) {
                                    return 'Characters from 3 to 15 must be digits or alphanumeric';
                                  }
                                  if (value.length != 15) {
                                    return 'GSTIN must be 15 characters long';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {
                                    _showAddress = true;
                                  });
                                },
                              ),
                            ],
                          ),
                        if (_showAddress)
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              RegistrationTextField(
                                labelText: 'Address Line 1',
                                controller: _addressController1,
                                onChanged: (value) {
                                  setState(() {
                                    _showNext1 = true;
                                  });
                                },
                              ),
                              const SizedBox(height: 20),
                              RegistrationTextField(
                                labelText: 'Address Line 2',
                                controller: _addressController2,
                                onChanged: (value) {
                                  setState(() {
                                    _showNext2 = true;
                                  });
                                },
                              ),
                            ],
                          ),
                        if (_showNext1 && _showNext2)
                          Column(
                            children: [
                              const SizedBox(height: 32),
                              SizedBox(
                                width: MediaQuery.of(context).size.width - 32,
                                height: 56,
                                child: ElevatedButton(
                                  onPressed: () {
                                    if (_selectedNatureOfBusinesses.isEmpty &&
                                        _selectedDesignation == "Admin") {
                                      alert("Please select nature of business");
                                      return;
                                    }
                                    if (_formKey.currentState!.validate()) {
                                      var data = BuyerRegData(
                                        district: _selectedDistrict ?? "",
                                        vendor: _selectedVendor ?? "",
                                        vendorId: _selectedVendorId ?? "",
                                        natureOfBusiness:
                                            _selectedNatureOfBusinesses,
                                        designation: _selectedDesignation ?? "",
                                        gst: _gstController.text,
                                        address1:
                                            _addressController1.text.isEmpty
                                                ? " "
                                                : _addressController1.text,
                                        address2:
                                            _addressController2.text.isEmpty
                                                ? " "
                                                : _addressController2.text,
                                      );
                                      Get.to(RegistrationBuyerSummaryScreen(
                                          data: data));
                                    } else {
                                      setState(() {});
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: AppColors.primaryColor,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40, vertical: 20),
                                    textStyle: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  child: const Text("NEXT"),
                                ),
                              ),
                              if (_selectedNatureOfBusinesses.isEmpty &&
                                  _selectedDesignation == "Admin")
                                const Padding(
                                  padding: EdgeInsets.only(top: 20, bottom: 4),
                                  child: Text(
                                    'Please select "Nature of Businesses"',
                                    style: TextStyle(color: Colors.red),
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              const SizedBox(height: 16),
                            ],
                          )
                      ],
                    ),
                  ),
                ),
              );
            } else {
              return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }
}

class RegistrationTextField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final int? maxLength;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validator;
  final bool capitalize;
  final VoidCallback? onEditingComplete; // New parameter

  const RegistrationTextField({
    Key? key,
    required this.labelText,
    required this.controller,
    this.maxLength,
    this.onChanged,
    this.keyboardType,
    this.validator,
    this.capitalize = false,
    this.onEditingComplete, // Include the new parameter in the constructor
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      onChanged: onChanged,
      maxLength: maxLength,
      keyboardType: keyboardType,
      textCapitalization:
          capitalize ? TextCapitalization.characters : TextCapitalization.none,
      style: const TextStyle(color: Color.fromRGBO(255, 255, 255, 1)),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        labelText: labelText,
        helperStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
        labelStyle: const TextStyle(color: Colors.white),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        isDense: true,
      ),
      validator: validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'This field is required';
            }
            return null;
          },
      onEditingComplete: onEditingComplete, // Set the callback
    );
  }
}

class RegistrationRichTextField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final int? maxLength;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validator;
  final bool capitalize;
  final VoidCallback? onEditingComplete; // New parameter

  const RegistrationRichTextField({
    Key? key,
    required this.labelText,
    required this.controller,
    this.maxLength,
    this.onChanged,
    this.keyboardType,
    this.validator,
    this.capitalize = false,
    this.onEditingComplete, // Include the new parameter in the constructor
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      onChanged: onChanged,
      maxLength: maxLength,
      keyboardType: keyboardType,
      textCapitalization:
          capitalize ? TextCapitalization.characters : TextCapitalization.none,
      style: const TextStyle(color: Color.fromRGBO(255, 255, 255, 1)),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: labelText,
                style: const TextStyle(color: Colors.white),
              ),
              const TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        helperStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
        labelStyle: const TextStyle(color: Colors.white),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        isDense: true,
      ),
      validator: validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'This field is required';
            }
            return null;
          },
      onEditingComplete: onEditingComplete, // Set the callback
    );
  }
}

class RegistrationDropdown extends StatelessWidget {
  final String labelText;
  final List<String> items;
  final ValueChanged<String?>? onChanged;
  final String? selectedValue;

  const RegistrationDropdown({
    Key? key,
    required this.labelText,
    required this.items,
    this.onChanged,
    this.selectedValue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      dropdownColor: Colors.white,
      value: selectedValue,
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: const TextStyle(color: Colors.white),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 0.75,
          ),
        ),
        isDense: true,
      ),
      style: const TextStyle(color: Colors.black),
      items: items.map((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            width: MediaQuery.of(context).size.width - 88,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: value == selectedValue
                  ? AppColors.primaryColor
                  : Colors.white,
            ),
            child: Text(
              value,
              style: TextStyle(
                  color: value == selectedValue ? Colors.white : Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }).toList(),
      onChanged: onChanged,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'This field is required';
        }
        return null;
      },
    );
  }
}

class MultiSelectDropdown extends StatefulWidget {
  final String labelText;
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;

  const MultiSelectDropdown({
    Key? key,
    required this.labelText,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
  }) : super(key: key);

  @override
  _MultiSelectDropdownState createState() => _MultiSelectDropdownState();
}

class _MultiSelectDropdownState extends State<MultiSelectDropdown> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.labelText,
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
        const SizedBox(height: 8.0),
        SizedBox(
          width: MediaQuery.of(context).size.width - 32,
          child: Wrap(
            spacing: 8.0,
            children: widget.items.map((String item) {
              bool isSelected = widget.selectedValues.contains(item);
              return FilterChip(
                label: Text(item),
                selected: isSelected,
                onSelected: (bool selected) {
                  setState(() {
                    if (selected) {
                      widget.selectedValues.add(item);
                    } else {
                      widget.selectedValues.remove(item);
                    }
                    widget.onChanged(widget.selectedValues);
                  });
                },
                selectedColor: Colors.cyan.withOpacity(0.4),
                backgroundColor: Colors.white,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

class BuyerRegData {
  final String district;
  final String vendor;
  final String vendorId;
  final List<String> natureOfBusiness;
  final String designation;
  final String gst;
  final String address1;
  final String address2;

  BuyerRegData({
    required this.district,
    required this.vendor,
    required this.vendorId,
    required this.natureOfBusiness,
    required this.designation,
    required this.gst,
    required this.address1,
    required this.address2,
  });
}
