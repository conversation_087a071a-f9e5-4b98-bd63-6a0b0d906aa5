// import 'package:amazon_cognito_identity_dart_2/cognito.dart';
// import 'package:connectone/core/bai_widgets/bai_button.dart';
// import 'package:connectone/core/network/network_controller.dart';
// import 'package:connectone/core/utils/colors.dart';
// import 'package:connectone/core/utils/tools.dart';
// import 'package:connectone/old_screens/login_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';

// import '../core/bai_widgets/help_info.dart';

// class RegistrationOTP extends StatefulWidget {
//   const RegistrationOTP({
//     Key? key,
//     required this.user,
//     required this.isSeller,
//   }) : super(key: key);

//   final String user;
//   final bool isSeller;

//   @override
//   State<RegistrationOTP> createState() => _RegistrationOTPState();
// }

// class _RegistrationOTPState extends State<RegistrationOTP> {
//   final TextEditingController _otpController = TextEditingController();
//   final NetworkController _networkController = NetworkController();
//   final _razorpay = Razorpay();
//   final _formKey = GlobalKey<FormState>();

//   verifyOtp({required String user, required String otp}) async {
//     final userPool = CognitoUserPool(
//       _networkController.organisationData?.userPoolId ?? "",
//       _networkController.organisationData?.appClientWeb ?? "",
//     );
//     final cognitoUser = CognitoUser("${user}_email", userPool);
//     try {
//       var authResult = await cognitoUser.confirmRegistration(otp);
//       if (authResult) {
//         alert("OTP Verified");
//         // if (!widget.isSeller) {
//         Get.offAll(const CongratsScreen());
//         // } else {}
//       } else {
//         alert("Invalid OTP");
//       }
//     } catch (e) {
//       if (e is CognitoClientException) {
//         alert(e.message);
//       } else {
//         alert(e.toString());
//       }
//     }
//   }

//   resendOtp({required String user}) async {
//     final userPool = CognitoUserPool(
//       _networkController.organisationData?.userPoolId ?? "",
//       _networkController.organisationData?.appClientWeb ?? "",
//     );
//     final cognitoUser = CognitoUser("${user}_email", userPool);
//     try {
//       await cognitoUser.resendConfirmationCode();
//     } catch (e) {
//       if (e is CognitoClientException) {
//         alert(e.message);
//       } else {
//         alert(e.toString());
//       }
//     }
//   }

//   @override

//   /// Builds the UI for the OTP verification screen.
//   ///
//   /// The screen displays a form with a single field to enter the OTP.
//   /// The form also includes a button to verify the OTP.
//   ///
//   /// The OTP is verified by calling the `verifyOtp` function.
//   /// If the OTP is invalid, an error message is displayed.
//   /// If the OTP is valid, the user is redirected to the Congrats screen.
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('OTP Verification'),
//         backgroundColor: AppColors.primaryColor,
//         actions: const [
//           InfoHelp(
//             url: '',
//             name: '',
//           )
//         ],
//       ),
//       body: SizedBox(
//         height: MediaQuery.of(context).size.height - 72,
//         child: Padding(
//           padding: const EdgeInsets.all(20),
//           child: Form(
//             key: _formKey,
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 /// The OTP field.
//                 TextFormField(
//                   controller: _otpController,
//                   keyboardType: TextInputType.number,
//                   maxLength: 6,
//                   style: TextStyle(
//                     color: AppColors.primaryColor,
//                     fontWeight: FontWeight.bold,
//                     letterSpacing: 4,
//                   ),
//                   decoration: InputDecoration(
//                     labelText: 'OTP',
//                     labelStyle: const TextStyle(color: Colors.black),
//                     hintText: 'Enter OTP',
//                     hintStyle: const TextStyle(color: Colors.black),
//                     floatingLabelStyle: const TextStyle(color: Colors.black),
//                     border: const OutlineInputBorder(),
//                     focusedBorder: OutlineInputBorder(
//                       borderSide: BorderSide(
//                         color: AppColors.primaryColor,
//                         width: 1,
//                       ),
//                     ),
//                     isDense: true,
//                   ),
//                   validator: (value) {
//                     if (value == null || value.isEmpty) {
//                       return 'Please enter the OTP';
//                     } else if (value.length != 6) {
//                       return 'OTP must be 6 digits long';
//                     }
//                     return null;
//                   },
//                 ),
//                 const SizedBox(height: 16.0),

//                 /// The button to verify the OTP.
//                 BaiButton(
//                   onTap: () {
//                     if (_formKey.currentState?.validate() ?? false) {
//                       verifyOtp(
//                         user: widget.user,
//                         otp: _otpController.text,
//                       );
//                     }
//                   },
//                   text: "Verify OTP",
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     _razorpay.clear();
//     super.dispose();
//   }
// }

// class CongratsScreen extends StatefulWidget {
//   const CongratsScreen({Key? key}) : super(key: key);

//   @override
//   State<CongratsScreen> createState() => _CongratsScreenState();
// }

// class _CongratsScreenState extends State<CongratsScreen> {
//   @override
//   void initState() {
//     _navAfter2Sec();
//     super.initState();
//   }

//   _navAfter2Sec() {
//     Future.delayed(const Duration(seconds: 2), () {
//       Get.offAll(const LoginScreen());
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text("baiStore"),
//         backgroundColor: AppColors.primaryColor,
//       ),
//       body: Container(
//         padding: const EdgeInsets.all(32),
//         child: const Center(
//           child: Text(
//             "Congrats, OTP Verified!",
//             style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'package:amazon_cognito_identity_dart_2/cognito.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'dart:async'; // Import for Timer

import '../core/bai_widgets/help_info.dart';

class RegistrationOTP extends StatefulWidget {
  const RegistrationOTP({
    Key? key,
    required this.user,
    required this.isSeller,
    required this.sendOtpInitially,
  }) : super(key: key);

  final String user;
  final bool isSeller;
  final bool sendOtpInitially;

  @override
  State<RegistrationOTP> createState() => _RegistrationOTPState();
}

class _RegistrationOTPState extends State<RegistrationOTP> {
  final TextEditingController _otpController = TextEditingController();
  final NetworkController _networkController = NetworkController();
  final _razorpay = Razorpay();
  final _formKey = GlobalKey<FormState>();

  // Timer variables
  Timer? _timer;
  int _start = 60; // 60 seconds
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    sendOtpIfRequired();
    startTimer();
  }

  void sendOtpIfRequired() {
    if (widget.sendOtpInitially) {
      _canResend = true;
      resendOtp(user: widget.user);
    }
  }

  void startTimer() {
    _canResend = false; // Disable resend button initially
    _start = 60; // Reset timer
    _timer?.cancel(); // Cancel any existing timer
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
            _canResend = true; // Enable resend button
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  verifyOtp({required String user, required String otp}) async {
    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser("${user}_email", userPool);
    try {
      var authResult = await cognitoUser.confirmRegistration(otp);
      if (authResult) {
        alert("OTP Verified");
        _timer?.cancel(); // Stop the timer on successful verification
        Get.offAll(const CongratsScreen());
      } else {
        alert("Invalid OTP");
      }
    } catch (e) {
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(e.toString());
      }
    }
  }

  resendOtp({required String user}) async {
    if (!_canResend) return; // Prevent resend if timer is active

    final userPool = CognitoUserPool(
      _networkController.organisationData?.userPoolId ?? "",
      _networkController.organisationData?.appClientWeb ?? "",
    );
    final cognitoUser = CognitoUser("${user}_email", userPool);
    try {
      await cognitoUser.resendConfirmationCode();
      alert("OTP Resent!"); // Inform the user
      startTimer(); // Restart the timer
    } catch (e) {
      if (e is CognitoClientException) {
        alert(e.message);
      } else {
        alert(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OTP Verification'),
        backgroundColor: AppColors.primaryColor,
        actions: const [
          InfoHelp(
            url: '',
            name: '',
          )
        ],
      ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height - 72,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "OTP has been sent to your phone", // More user-friendly
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24), // More spacing

                TextFormField(
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 4, // Keep for visual separation
                  ),
                  decoration: InputDecoration(
                    labelText: 'OTP',
                    labelStyle: const TextStyle(color: Colors.black),
                    hintText: 'Enter OTP',
                    hintStyle: const TextStyle(color: Colors.black),
                    floatingLabelStyle: const TextStyle(color: Colors.black),
                    border: const OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.primaryColor,
                        width: 2, // Slightly thicker border
                      ),
                    ),
                    isDense: true,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the OTP';
                    } else if (value.length != 6) {
                      return 'OTP must be 6 digits long';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Resend OTP in ",
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    Text(
                      "$_start s",
                      style: TextStyle(
                        color: _start > 0
                            ? Colors.red
                            : Colors.green, // Color changes
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // if (_canResend)
                //   SizedBox(
                //     width: 128,
                //     child: BaiButton(
                //       onTap: () => resendOtp(user: widget.user),
                //       text: "Resend OTP",
                //       backgoundColor: AppColors.mainColor,
                //     ),
                //   ),

                const SizedBox(height: 16),
                _canResend
                    ? BaiButton(
                        onTap: () => resendOtp(user: widget.user),
                        text: "Resend OTP",
                      )
                    : BaiButton(
                        onTap: () {
                          if (_formKey.currentState?.validate() ?? false) {
                            verifyOtp(
                              user: widget.user,
                              otp: _otpController.text,
                            );
                          }
                        },
                        text: "Verify OTP",
                      ),
                const SizedBox(height: 32), // More space at the bottom
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel(); // Cancel the timer
    _razorpay.clear();
    _otpController.dispose(); // Dispose the controller
    super.dispose();
  }
}

class CongratsScreen extends StatefulWidget {
  const CongratsScreen({Key? key}) : super(key: key);

  @override
  State<CongratsScreen> createState() => _CongratsScreenState();
}

class _CongratsScreenState extends State<CongratsScreen> {
  @override
  void initState() {
    _navAfter2Sec();
    super.initState();
  }

  _navAfter2Sec() {
    Future.delayed(const Duration(seconds: 2), () {
      Get.offAll(const LoginScreen());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("baiStore"),
        backgroundColor: AppColors.primaryColor,
      ),
      body: Container(
        padding: const EdgeInsets.all(32),
        child: const Center(
          child: Text(
            "Congrats, OTP Verified!",
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
