import 'dart:io';

// import 'package:connectone/bai_blocs/profile/cubit/profile_cubit.dart';
import 'package:connectone/bai_models/user_profile_v2_res.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/circular_progress.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/my_account/my_account_bloc.dart';
import 'package:connectone/old_blocs/my_account/my_account_cubit.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher_string.dart';
import '../bai_models/area_of_business_res.dart';
import '../bai_models/edit_area_req.dart';
import '../bai_models/pricing_res.dart';
import '../bai_models/register_req.dart';
import '../core/bai_widgets/app_loader.dart';
import '../core/bai_widgets/filter_multi_select.dart';
import '../core/bai_widgets/help_info.dart';
import '../core/network/network_controller.dart';
import '../core/utils/storage_utils.dart';
import '../old_blocs/prof_bloc/cubit/user_profile_cubit.dart';
import '../old_screens/login_screen.dart';
import '../old_screens/my_account_screen.dart';
import 'vendor_groups_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final phoneController = TextEditingController();
  final emailController = TextEditingController();
  final websiteController = TextEditingController();
  final nameController = TextEditingController();
  final addressLine1Controller = TextEditingController();
  String? profilePic;

  List<String> groups = [];
  List<String> selectedGroups = [];
  List<AreaOfBusiness>? areas;

  setGroups(List<PricingRes> pricing) {
    setState(() {
      groups = pricing.map((e) => e.group.toString()).toSet().toList();
    });
  }

  @override
  void initState() {
    super.initState();
    context.read<UserProfileCubit>().getProfile();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        elevation: 0,
        title: const Text('Profile'),
        actions: const [
          InfoHelp(
            url: '',
            name: '',
          )
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<UserProfileCubit, UserprofileState>(
          listener: (context, state) async {
            if (state is UpdateSuccess) {
              context.read<UserProfileCubit>().getProfile();
            }
            if (state is ProfileLoaded) {
              context.read<UserProfileCubit>().getProfile();
            }
            if (state is AccountDataFetched) {
              setGroups(state.pricing ?? []);
              if (state.areaOfBusiness != null) {
                areas = state.areaOfBusiness?.areas;
              }
            }
          },
          builder: (context, state) {
            (state is UserprofileInitial)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();

            if (state is AccountDataFetched) {
              var data = state.account.data;
              var categories =
                  state.sellerData.content?.map((e) => e.name).toList();
              List<Medias>? profileImage = data?.medias
                  ?.where((media) => media.title == 'profile')
                  .toList();
              return Container(
                height: MediaQuery.of(context).size.height - 72,
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 20,
                ),
                child: Column(
                  // Changed from ListView to Column
                  children: [
                    Expanded(
                      // Wrap the ListView in an Expanded widget
                      child: ListView(
                        children: [
                          Center(
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: [
                                ((profileImage ?? []).isNotEmpty)
                                    ? Container(
                                        height: 100,
                                        width: 100,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(100),
                                        ),
                                        child: Image.network(
                                          profileImage?.first.url ?? "",
                                          fit: BoxFit.cover,
                                        ),
                                      )
                                    : CircleAvatar(
                                        radius: 50,
                                        backgroundColor: Colors.grey.shade300,
                                        child: Icon(
                                          Icons.person,
                                          size: 50,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                Positioned(
                                  bottom: 0,
                                  right: -10,
                                  child: Container(
                                    height: 25,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.yellowColor,
                                    ),
                                    child: Center(
                                      child: IconButton(
                                        onPressed: () async {
                                          _images.clear();
                                          await _getImageData(
                                            "photos",
                                            data?.id,
                                            profile: true,
                                          );
                                          if (_images.isNotEmpty) {
                                            context
                                                .read<UserProfileCubit>()
                                                .editProfile(
                                                  profilePic: _images.first,
                                                );
                                          }
                                        },
                                        iconSize: 12,
                                        icon: const Icon(
                                          Icons.edit,
                                          color: AppColors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Text(
                            data?.name ?? "N/A",
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Center(
                            // child: SizedBox(width: 100,
                            child: TextButton(
                              style: TextButton.styleFrom(
                                backgroundColor: const Color.fromARGB(
                                  255,
                                  205,
                                  222,
                                  246,
                                ),
                                side: BorderSide(
                                  width: 1.0,
                                  color: AppColors.primaryColor,
                                ),
                                padding: const EdgeInsets.all(2.0),
                              ),
                              onPressed: () {
                                showUpdateDialog(
                                  context,
                                  data?.email ?? '',
                                  data?.addressLine1 ?? '',
                                  data?.typeOfGstFiling ?? '',
                                  data?.companyEmail ?? '',
                                  data?.companyPhone ?? '',
                                );
                              },
                              child: const Text(
                                'Edit',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          _buildInfoRow(
                            'Location',
                            data?.addressLine1 ?? 'N/A',
                            state.isEditable,
                          ),
                          const SizedBox(height: 10),
                          _buildInfoRow(
                            'Phone',
                            data?.primaryPhone ?? 'N/A',
                            state.isEditable,
                          ),
                          const SizedBox(height: 10),
                          _buildInfoRow(
                            'Email',
                            data?.email ?? 'N/A',
                            state.isEditable,
                          ),
                          if (isAdmin()) const SizedBox(height: 10),
                          _buildInfoRow(
                            'Business Email',
                            data?.companyEmail ?? 'N/A',
                            state.isEditable,
                          ),
                          if (isAdmin()) const SizedBox(height: 10),
                          if (isAdmin())
                            _buildInfoRow(
                              'Business Phone',
                              data?.companyPhone ?? 'N/A',
                              state.isEditable,
                            ),
                          if (!isBuyer())
                            const Divider(
                              height: 32,
                              color: Colors.grey,
                            ),
                          if (!isBuyer()) const SizedBox(height: 10),
                          if (!isBuyer())
                            const Column(
                              children: [
                                // _buildInfoRow(
                                //   'Categories',
                                //   categories?.join(', ') ?? 'N/A',
                                //   state.isEditable,
                                // ),
                                // const SizedBox(height: 8),
                                // const SizedBox(height: 24),
                                // BaiButton(
                                //   height: 40,
                                //   onTap: () {
                                //     Get.to(
                                //       VendorGroupsScreen(
                                //         pricing: state.pricing ?? [],
                                //         isMember: true,
                                //         isManufacturer: true,
                                //         req: RegisterReq(),
                                //       ),
                                //     );
                                //   },
                                //   text: "Add Categories",
                                // ),
                              ],
                            ),
                          // if (isAdmin()) const SizedBox(height: 10),
                          // if (isAdmin())
                          //   const Divider(
                          //     height: 32,
                          //     color: Colors.grey,
                          //   ),
                          if (isAdmin())
                            _buildInfoRow(
                              'No. of Orders',
                              data?.totalOrder.toString() ?? "",
                              state.isEditable,
                              isHighlighted: true,
                            ),
                          if (isAdmin()) const SizedBox(height: 8),
                          if (isAdmin())
                            _buildInfoRow(
                              'Total Amount',
                              "₹ ${data?.totalAmount?.toString() ?? "N/A"}",
                              state.isEditable,
                              isHighlighted: true,
                            ),
                          if (isAdmin())
                            const Divider(
                              height: 24,
                              color: Colors.grey,
                            ),
                          _buildCertificationRow(
                            data?.medias,
                            data?.id,
                          ),
                          // Category, Place of Service, Expiry Date Block
                          if (!isBuyer())
                            Row(
                              children: [
                                _buildSectionTitle('Categories'),
                                const Spacer(),
                                TextButton(
                                  style: TextButton.styleFrom(
                                    backgroundColor: const Color.fromARGB(
                                        255, 205, 222, 246),
                                    side: BorderSide(
                                        width: 1.0,
                                        color: AppColors
                                            .primaryColor), // Outline border
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                  ),
                                  onPressed: () {
                                    Get.to(
                                      VendorGroupsScreen(
                                        pricing: state.pricing ?? [],
                                        isMember: true,
                                        isManufacturer: true,
                                        req: RegisterReq(),
                                        fromProfile: true,
                                      ),
                                    );
                                  },
                                  child: const Text(
                                    '+ Add New',
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ],
                            ),
                          if (!isBuyer()) const SizedBox(height: 6),
                          if (!isBuyer())
                            data?.registeredCategoryData?.isEmpty ?? true
                                ? const SizedBox(
                                    height: 48,
                                    child: Center(
                                      child: Text(
                                        'No categories found',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount:
                                        data?.registeredCategoryData?.length ??
                                            0,
                                    itemBuilder: (context, index) {
                                      var item =
                                          data?.registeredCategoryData?[index];
                                      return CategoryBlock(
                                        categoryName:
                                            item?.categoryName ?? "N/A",
                                        placeOfService:
                                            item?.area?.join(', ') ?? "N/A",
                                        expiryDate:
                                            item?.expDate?.toDeliveryOn() ??
                                                "N/A",
                                        availableAreas: areas
                                                ?.map((e) => e.name.toString())
                                                .toList() ??
                                            [],
                                        selectedAreas: item?.area ?? [],
                                        onAreasChanged: (newAreas) {
                                          if (newAreas.isEmpty) {
                                            properAlert("Area is empty.");
                                            return;
                                          }
                                          List<int> ids = [];
                                          for (var i
                                              in state.areaOfBusiness?.areas ??
                                                  []) {
                                            if (newAreas.contains(i.name)) {
                                              ids.add(int.parse(i.id));
                                            }
                                          }
                                          context
                                              .read<UserProfileCubit>()
                                              .updateArea(
                                                EditAreaReq(
                                                  categoryId: item?.categoryId,
                                                  newAreaOfBusinessId: ids,
                                                ),
                                              );
                                        },
                                      );
                                    }),
                          const SizedBox(height: 32),
                          _buildFooter(),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            } else {
              return Center(
                child: progressIndicator,
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              InkWell(
                onTap: () {
                  // var url = DataStorage.configData!
                  //         .firstWhere(
                  //           (element) => element?.keyName1 == "website_url",
                  //         )
                  //         ?.valueName1 ??
                  //     additionalUrl;
                  // Handle Privacy Policy tap
                  // Example:  launchURL("https://your-privacy-policy-url.com");
                  launchUrlString(
                    'https://baistore.cochq.au/privacy-policy',
                    mode: LaunchMode.externalApplication,
                  );
                },
                child: const Text(
                  'Privacy Policy',
                  style: TextStyle(
                    color: AppColors.primaryColorOld,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  // Handle Terms & Conditions tap
                  // Example: launchURL("https://your-terms-and-conditions-url.com");
                  launchUrlString(
                    'https://baistore.cochq.au/terms-conditions',
                    mode: LaunchMode.externalApplication,
                  );
                },
                child: const Text(
                  'Terms & Conditions',
                  style: TextStyle(
                    color: AppColors.primaryColorOld,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: BaiButton(
              height: 36,
              onTap: () {
                showDialog<String>(
                  context: context,
                  builder: (BuildContext context) => DeleteAccountWidget(
                    delete: () async {
                      try {
                        Navigator.pop(context);
                        await NetworkController().deleteAccountApi();
                        await NetworkController().deleteToken();
                        box.erase();
                        Get.offAll(const LoginScreen());
                      } catch (e) {
                        alert("Could not delete acoount");
                      }
                    },
                  ),
                );
              },
              text: "Delete Account",
              backgoundColor: AppColors.darkRed,
            ),
          ),
        ],
      ),
    );
  }

  void showUpdateDialog(
    BuildContext context,
    String email,
    String location,
    String gstType,
    String businessEmail,
    String businessPhone,
  ) {
    final TextEditingController locationController =
        TextEditingController(text: location);

    final TextEditingController emailController =
        TextEditingController(text: email);

    final TextEditingController businessEmailController =
        TextEditingController(text: businessEmail);

    final TextEditingController businessPhoneController =
        TextEditingController(text: businessPhone);

    final TextEditingController gstTypeController;

    gstTypeController = TextEditingController(
        text: (gstType == "Regular" || gstType == "Compound") ? gstType : "");

    showDialog(
      context: context,
      builder: (BuildContext context) {
        var style = const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.black,
        );
        return AlertDialog(
          title: const Text('Update Information'),
          content: SingleChildScrollView(
            child: Column(
              children: [
                TextField(
                  controller: locationController,
                  decoration: const InputDecoration(labelText: 'Location'),
                ),
                TextField(
                  controller: emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                ),
                if (isAdmin())
                  TextField(
                    controller: businessEmailController,
                    decoration:
                        const InputDecoration(labelText: 'Business Email'),
                  ),
                if (isAdmin())
                  TextField(
                    controller: businessPhoneController,
                    decoration:
                        const InputDecoration(labelText: 'Business Phone'),
                  ),
                if (isAdmin())
                  DropdownButtonFormField<String>(
                    value: gstTypeController.text.isNotEmpty
                        ? gstTypeController.text
                        : null,
                    decoration: const InputDecoration(labelText: 'GST Type'),
                    items: const [
                      DropdownMenuItem(
                          value: "Compound", child: Text("Compound")),
                      DropdownMenuItem(
                          value: "Regular", child: Text("Regular")),
                    ],
                    onChanged: (value) {
                      gstTypeController.text = value ?? "";
                    },
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: style,
              ),
            ),
            TextButton(
              onPressed: () async {
                String updatedLocation = locationController.text;
                String updatedEmail = emailController.text;
                String updatedBusinessEmail = businessEmailController.text;
                String updatedBusinessPhone = businessPhoneController.text;
                String updatedGstType = gstTypeController.text;

                if (!RegExp(
                        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                    .hasMatch(updatedEmail)) {
                  alert("Enter a valid email address");
                  return;
                }

                if (isAdmin()) {
                  if (!RegExp(
                          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                      .hasMatch(updatedBusinessEmail)) {
                    alert("Enter a valid email address for business email");
                    return;
                  }

                  if (!RegExp(r"^[6-9]\d{9}$").hasMatch(updatedBusinessPhone)) {
                    alert(
                        "Enter a valid Indian phone number for business phone");
                    return;
                  }
                }

                context
                    .read<UserProfileCubit>()
                    .editProfile(
                      location: updatedLocation,
                      email: updatedEmail,
                      businessEmail: updatedBusinessEmail,
                      businessPhone: updatedBusinessPhone,
                      gstType: updatedGstType,
                    )
                    .then((value) {
                  Navigator.of(context).pop();
                });
              },
              child: Text(
                'Update',
                style: style,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    bool isEditable, {
    bool isHighlighted = false,
    TextEditingController? controller,
  }) {
    return Row(
      crossAxisAlignment: (controller != null && isEditable)
          ? CrossAxisAlignment.center
          : CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isHighlighted ? Colors.red : Colors.black,
            ),
          ),
        ),
        const Text(':'),
        const SizedBox(width: 24),
        Expanded(
          flex: 3,
          child: (controller != null && isEditable)
              ? TextField(
                  controller: controller,
                  style: TextStyle(
                    fontWeight:
                        isHighlighted ? FontWeight.bold : FontWeight.normal,
                    color: isHighlighted ? Colors.red : Colors.black,
                  ),
                  enabled: isEditable,
                  textAlign: TextAlign.start,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    hintText: value,
                  ),
                )
              : Text(
                  value,
                  style: TextStyle(
                    fontWeight:
                        isHighlighted ? FontWeight.bold : FontWeight.normal,
                    color: isHighlighted ? Colors.red : Colors.black,
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildCertificationRow(
    List<Medias>? medias,
    int? id,
  ) {
    List<Medias>? certificateMedias =
        medias?.where((media) => media.title == 'certificate').toList();
    List<Medias>? photoMedias =
        medias?.where((media) => media.title == 'photos').toList();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(children: [
          _buildSectionTitle('Certifications'),
          const Spacer(),
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: const Color.fromARGB(255, 205, 222, 246),
              side: BorderSide(
                  width: 1.0, color: AppColors.primaryColor), // Outline border
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
            ),
            onPressed: () {
              if (_images.isEmpty && _files.isEmpty) {
                _getImageData("certificate", id).then((value) {});
              } else {
                _uploadedDocs("certificate", id).then((value) {});
              }
            },
            child: const Text(
              '+ Add New',
              style: TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontWeight: FontWeight.bold),
            ),
          )
        ]),
        const SizedBox(height: 8),
        certificateMedias?.length != 0
            ? ListView.builder(
                scrollDirection: Axis.vertical,
                itemCount: certificateMedias?.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return SizedBox(
                    height: 45,
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.picture_as_pdf,
                            color: Colors.red,
                            size: 32,
                          ),
                          Expanded(
                            child: Center(
                              child: Text(
                                certificateMedias?[index]
                                        .title
                                        ?.toUpperCase() ??
                                    "N/A",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(
                              Icons.close,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              )
            : const SizedBox(
                height: 48,
                child: Center(
                  child: Text(
                    'No certifices found',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
        const Divider(height: 32, color: Colors.grey),
        Row(children: [
          _buildSectionTitle('Photos'),
          const Spacer(),
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: const Color.fromARGB(255, 205, 222, 246),
              side: BorderSide(width: 1.0, color: AppColors.primaryColor),
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
            ),
            onPressed: () {
              if (_images.isEmpty && _files.isEmpty) {
                _getImageData("photos", id);
              } else {
                _uploadedDocs("photos", id);
              }
            },
            child: const Text(
              '+ Add New',
              style: TextStyle(
                fontSize: 12,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ]),
        const SizedBox(height: 8),
        photoMedias?.length != 0
            ? SizedBox(
                height: 150,
                width: double.infinity,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: photoMedias?.length ?? 0,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.all(0),
                      child: Stack(children: [
                        Container(
                            clipBehavior: Clip.hardEdge,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            height: 160,
                            child: Image.network(
                              photoMedias?[index].url ?? "",
                              fit: BoxFit.cover,
                            )),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () {
                              // Handle close button action
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Container(
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.red,
                                ),
                                padding: const EdgeInsets.all(4.0),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16.0, // Icon size
                                ),
                              ),
                            ),
                          ),
                        ),
                      ]),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(width: 8);
                  },
                ),
              )
            : const SizedBox(
                height: 48,
                child: Center(
                  child: Text(
                    'No photos found',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
        const Divider(
          height: 32,
          color: Colors.grey,
        ),
      ],
    );
  }

  _getImageData(
    String? doctype,
    int? id, {
    bool? profile = false,
  }) async {
    if (doctype != "photos") {
      await _pickFiles();
      if (_files.isNotEmpty) {
        _uploadedDocs(doctype, id);
      }
    } else {
      return showDialog(
          context: context,
          builder: (context) {
            var style = const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            );
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Wrap(
                children: [
                  ListTile(
                    leading: const Icon(
                      Icons.camera_alt,
                      color: Colors.black87,
                    ),
                    title: Text(
                      'Open Camera',
                      style: style,
                    ),
                    onTap: () async {
                      await _pickImageCamera();
                      Get.back(closeOverlays: true);
                      setState(() {});
                    },
                  ),
                  ListTile(
                    leading: const Icon(
                      Icons.image,
                      color: Colors.black87,
                    ),
                    title: Text(
                      'Image from Gallery',
                      style: style,
                    ),
                    onTap: () async {
                      await _pickImageGallery();
                      Get.back(closeOverlays: true);
                      setState(() {});
                    },
                  ),
                ],
              ),
            );
          }).then((val) {
        if ((_images.isNotEmpty || _files.isNotEmpty) && !profile!) {
          _uploadedDocs(doctype, id);
        }
      });
    }
  }

  _uploadedDocs(String? doctype, int? id) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: BlocConsumer<MyAccountCubit, MyAccountState>(
            listener: (context, state) {},
            builder: (context, state) {
              return SafeArea(
                child: Wrap(
                  children: <Widget>[
                    const Divider(),
                    if (_images.isNotEmpty) ...{
                      ListTile(
                        title: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Selected Images",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                Get.back(closeOverlays: true);
                                _getImageData(doctype, id);
                              },
                              icon: const Icon(
                                Icons.add_circle_outline_sharp,
                                color: AppColors.mainColor,
                              ),
                            ),
                          ],
                        ),
                        subtitle: SizedBox(
                          height: 150,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _images.length,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.all(13.0),
                                child: Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: kIsWeb
                                          ? Image.network(
                                              _images[index],
                                            )
                                          : Image.file(
                                              File(_images[index]),
                                            ),
                                    ),
                                    Positioned(
                                      top: -5,
                                      right: -10,
                                      child: Container(
                                        height: 25,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: AppColors.red),
                                        child: IconButton(
                                          onPressed: () async {
                                            _images.removeAt(index);
                                            Get.back(closeOverlays: true);
                                            if (_images.isNotEmpty) {
                                              _uploadedDocs(doctype, id);
                                            }
                                          },
                                          icon: const Icon(
                                            Icons.close,
                                            color: AppColors.white,
                                            size: 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    },
                    if (_files.isNotEmpty) ...{
                      ListTile(
                        title: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Selected Files",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                Get.back(closeOverlays: true);
                                _getImageData(doctype, id);
                              },
                              icon: const Icon(
                                Icons.add_circle_outline_sharp,
                                color: AppColors.mainColor,
                              ),
                            ),
                          ],
                        ),
                        subtitle: SizedBox(
                          height: 150,
                          child: ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            itemCount: _files.length,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.all(13.0),
                                child: Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.picture_as_pdf,
                                            color: Colors.red,
                                            size: 25,
                                          ),
                                          const SizedBox(height: 8),
                                          Expanded(
                                            child: Text(
                                                path.basename(_files[index])),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                      top: -5,
                                      right: -10,
                                      child: Container(
                                        height: 25,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: AppColors.red),
                                        child: IconButton(
                                          onPressed: () async {
                                            _files.removeAt(index);
                                            Get.back(closeOverlays: true);
                                            if (_files.isNotEmpty) {
                                              _uploadedDocs(
                                                doctype,
                                                id,
                                              );
                                            }
                                          },
                                          icon: const Icon(
                                            Icons.close,
                                            color: AppColors.white,
                                            size: 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    },
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: BaiButton(
                        onTap: () {
                          if ((_images.isEmpty && _files.isEmpty)) {
                            _images.clear();
                            _files.clear();
                            Get.back(closeOverlays: true);
                          } else if (state is UserprofileInitial) {
                            context
                                .read<UserProfileCubit>()
                                .uploadProfileItems(
                                  title: doctype,
                                  data: [..._images, ..._files],
                                  dataBytes: [..._imageBytes, ..._fileBytes],
                                  id: id,
                                )
                                .then(
                              (value) {
                                _images.clear();
                                _files.clear();
                                alert("Upload successful!");
                                Get.back(closeOverlays: true);
                              },
                            );
                          }
                        },
                        text: state is MyAccountLoading
                            ? "Uploading..."
                            : (_images.isEmpty && _files.isEmpty)
                                ? "Upload Success"
                                : "Upload",
                      ),
                    )
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  final List<String> _images = [];
  final List<String> _files = [];

  final List<Uint8List> _imageBytes = [];
  final List<Uint8List> _fileBytes = [];

  _pickImageGallery() async {
    final ImagePicker picker = ImagePicker();
    try {
      final List<XFile> image = await picker.pickMultiImage(imageQuality: 25);
      for (var e in image) {
        _images.add(e.path);
        _imageBytes.add(await e.readAsBytes());
      }
    } catch (e) {
      safePrint(e);
    }
  }

  _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom, allowedExtensions: ['pdf', 'doc', 'docx']);
    if (result != null) {
      if (kIsWeb) {
        _files.add(result.files.single.name);
        _fileBytes.add(result.files.single.bytes!);
      } else {
        _files.add(result.files.single.path!);
        _fileBytes.add(await File(result.files.single.path!).readAsBytes());
      }
      // File file = File(result.files.single.path!);
      // _files.add(file.path);
      // _fileBytes.add(await file.readAsBytes());
    } else {
      // User canceled the picker
    }
  }

  _pickImageCamera() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 25,
      );
      if (image != null) {
        _images.add(image.path);
        _imageBytes.add(await image.readAsBytes());
      }
    } catch (e) {
      safePrint(e);
    }
  }
}

class CategoryBlock extends StatelessWidget {
  final String categoryName;
  final String placeOfService;
  final String expiryDate;
  final List<String> availableAreas;
  final List<String> selectedAreas;
  final Function(List<String>) onAreasChanged;

  const CategoryBlock({
    Key? key,
    required this.categoryName,
    required this.placeOfService,
    required this.expiryDate,
    required this.availableAreas,
    required this.selectedAreas,
    required this.onAreasChanged,
  }) : super(key: key);

  void _showAreasBottomSheet(BuildContext context) {
    List<String> tempSelectedAreas = List.from(selectedAreas);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Edit Areas: $categoryName',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FilterMultiSelectDropdown(
              labelText: 'Areas',
              items: availableAreas,
              selectedValues: tempSelectedAreas,
              onChanged: (values) {
                tempSelectedAreas = values;
              },
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: BaiButton(
                borderRadius: 32,
                text: 'Save',
                onTap: () {
                  onAreasChanged(tempSelectedAreas);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade400),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  categoryName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Expiry Date: $expiryDate',
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Areas: ${selectedAreas.join(", ")}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.edit, size: 20),
                onPressed: () => _showAreasBottomSheet(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
