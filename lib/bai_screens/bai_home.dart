import 'dart:ui';

import 'package:connectone/bai_screens/approve_team_members.dart';
import 'package:connectone/bai_screens/project_details.dart';
import 'package:connectone/bai_screens/site.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/constants.dart';
import 'package:connectone/core/old_widgets/common/side_drawer.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/others.dart';
import 'package:connectone/old_blocs/home/<USER>';
import 'package:connectone/old_models/category_new.dart';
import 'package:connectone/old_screens/notifications_screen.dart';
import 'package:connectone/old_screens/offline_filters/offline_filter_screen.dart';
import 'package:connectone/old_screens/web_view_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:url_launcher/url_launcher.dart';

import '../bai_blocs/cubit/notification_count_cubit.dart';
import 'package:badges/badges.dart' as badge;

import '../core/utils/tools.dart';

class BaiHome extends StatefulWidget {
  const BaiHome({Key? key}) : super(key: key);

  @override
  State<BaiHome> createState() => _BaiHomeState();
}

class _BaiHomeState extends State<BaiHome> {
  NetworkController networkController = NetworkController();
  String org = '';

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey keyNotifs = GlobalKey();
  GlobalKey keyDrawer = GlobalKey();

  Future<void> setOrg() async {
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) {
      setState(() {
        org = networkController.organisationData?.organizationName ?? "";
      });
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(const InitializeHome());
    context.read<NotificationCountCubit>().notificationsCount();
    createTutorial();
    setOrg();
  }

  @override

  /// The main widget of the app.
  /// It contains the app bar, navigation drawer and the home screen.
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(org), // The title of the app bar is the organization name.
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.primaryColor,
          statusBarIconBrightness: Brightness.light,
        ),
        backgroundColor: AppColors.primaryColor,
        actions: [
          BlocBuilder<NotificationCountCubit, NotificationCountState>(
            builder: (context, state) {
              // This is the notification badge in the app bar.
              // It shows the number of unread notifications.
              if (state is NotificationCountLoaded) {
                var count = state.notificationsCountRes!.unreadCount;
                return badge.Badge(
                  key: keyNotifs,
                  badgeContent: Text(
                    count.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8.0,
                    ),
                  ),
                  position: badge.BadgePosition.topEnd(
                    top: 4,
                    end: 4,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.notifications),
                    padding: const EdgeInsets.only(top: 8),
                    onPressed: () {
                      // When the notification badge is pressed, navigate to the notifications screen.
                      Get.to(const NotificationsScreen())?.then(
                        (value) {
                          // When the notifications screen is closed, update the notification count.
                          context
                              .read<NotificationCountCubit>()
                              .notificationsCount();
                        },
                      );
                    },
                  ),
                );
              } else {
                // When the notification count is loading, show a loading indicator.
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text("Loading..."),
                  ),
                );
              }
            },
          ),
          // IconButton(
          //   onPressed: () {
          //     showTutorial();
          //   },
          //   icon: const Icon(
          //     Icons.help_center,
          //   ),
          // ),
          InfoHelp(
            onTap: () {
              showTutorial();
            },
          ),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<HomeBloc, HomeState>(
            listener: (context, state) {},
            builder: (context, state) {
              // When the home screen is loading, show a loading indicator.
              (state is HomeLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              if (state is HomeLoaded) {
                // When the home screen is loaded, show a grid view of the categories.
                state.categoryData.sort((a, b) {
                  int? aSequence = int.tryParse(a.sequence ?? '');
                  int? bSequence = int.tryParse(b.sequence ?? '');
                  return (aSequence ?? double.infinity.toInt())
                      .compareTo(bSequence ?? double.infinity.toInt());
                });
                return GridView.builder(
                  padding: const EdgeInsets.all(10.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 2.0,
                    mainAxisSpacing: 2.0,
                    childAspectRatio: 0.95,
                  ),
                  itemCount: state.categoryData.length,
                  itemBuilder: (context, index) {
                    var item = state.categoryData[index];
                    return OptionCard(
                      title: item.name ?? "",
                      imageUrl: item.image ?? "",
                      onTap: () async {
                        // When a category is pressed, check if it has children.
                        bool hasChildren = item.children?.isNotEmpty == true;
                        if (hasChildren) {
                          // If it has children, navigate to the category screen with the children.
                          Get.to(
                            BaiChildren(
                              orgName: item.name ?? "",
                              categoryData: item.children ?? [],
                            ),
                            preventDuplicates: false,
                          );
                        } else {
                          var userType = item.hcType;
                          var catType = item.categoryType;
                          //////////////////////////////////////////////////////
                          if (item.categoryType == "MATR") {
                            // material request
                            Get.to(const ProjectDetails(isMr: true));
                            return;
                          }
                          if (item.categoryType == "SERE") {
                            // service request
                            Get.to(const ProjectDetails(isMr: false));
                            return;
                          }
                          if (item.categoryType == "MYRQ" ||
                              item.name!.toLowerCase().contains("manage")) {
                            // my request
                            Get.to(
                              OfflineFilterScreen(
                                title: item.name ?? "Search",
                                userType: userType ?? "",
                                categoryType:
                                    DataMap.getValues(item.tileCode ?? "")
                                            ?.first ??
                                        "",
                                isAdmin: false,
                              ),
                            );
                            return;
                          }
                          if (item.categoryType == "ADMN") {
                            // no need
                          }
                          if (item.categoryType == "MADR") {
                            Get.to(
                              OfflineFilterScreen(
                                title: item.name ?? "Search",
                                userType: userType ?? "",
                                categoryType:
                                    DataMap.getValues(item.tileCode ?? "")
                                            ?.first ??
                                        "",
                                isAdmin: true,
                              ),
                            );
                            return;
                          }
                          if (item.categoryType == "TEAM") {
                            Get.to(const ApproveTeamMembers());
                            return;
                          }
                          if (item.categoryType == "SITE") {
                            Get.to(const Site());
                            return;
                          }
                          if (item.tileCode == "GINFO") {
                            var url = item.publicInsertPageLink ??
                                "https://drive.google.com/drive/folders/1VPkESvVrNmeqByYNCtDsdZKzAp4vjyWN?usp=sharing";
                            if (await canLaunchUrl(
                              Uri.parse(
                                url,
                              ),
                            )) {
                              await launchUrl(
                                Uri.parse(url),
                                mode: LaunchMode.externalApplication,
                              );
                            }
                            return;
                          }
                          if (item.tileCode == "RELST") {
                            Get.to(WebViewCover(
                              url: item.publicInsertPageLink ?? additionalUrl,
                            ));
                            return;
                          }
                          if (item.tileCode == "AICB") {
                            Get.to(WebViewCover(
                              url: item.publicInsertPageLink ?? additionalUrl,
                            ));
                            return;
                          }
                          if (item.tileCode == "DLRP") {
                            properAlert("This page is under construction.");
                            return;
                          }
                          //////////////////////////////////////////////////////
                        }
                      },
                    );
                  },
                );
              }
              return const SizedBox.shrink();
            }),
      ),
      drawer: SideDrawer(
        userName: userName,
      ),
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "keyNotifs",
        keyTarget: keyNotifs,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Tap here to view your notifications. You can also find them in the side menu. \n\nYou can see notifications about: \n\n1. Purchase Orders, \n2. Material Requests, \n3. Order Updates and more.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }
}

class BaiChildren extends StatefulWidget {
  const BaiChildren({
    Key? key,
    required this.orgName,
    required this.categoryData,
  }) : super(key: key);
  final String orgName;
  final List<CategoryNew> categoryData;

  @override
  State<BaiChildren> createState() => _BaiChildrenState();
}

class _BaiChildrenState extends State<BaiChildren> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.orgName),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: AppColors.primaryColor,
          statusBarIconBrightness: Brightness.light,
        ),
        backgroundColor: AppColors.primaryColor,
        actions: const [
          InfoHelp(url: "https://www.youtube.com/shorts/RVCXLT102Ag"),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<HomeBloc, HomeState>(
            listener: (context, state) {},
            builder: (context, state) {
              (state is HomeLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              if (state is HomeLoaded) {
                return GridView.builder(
                  padding: const EdgeInsets.all(10.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 2.0,
                    mainAxisSpacing: 2.0,
                    childAspectRatio: 0.95,
                  ),
                  itemCount: widget.categoryData.length,
                  itemBuilder: (context, index) {
                    var item = widget.categoryData[index];
                    return OptionCard(
                      title: item.name ?? "",
                      imageUrl: item.image ?? "",
                      onTap: () {
                        bool hasChildren = item.children?.isNotEmpty == true;

                        if (hasChildren) {
                          Get.to(
                            BaiChildren(
                                orgName: item.name ?? "",
                                categoryData: item.children ?? []),
                            preventDuplicates: false,
                          );
                        } else {
                          var userType = item.hcType;
                          var catType = item.categoryType;
                          //////////////////////////////////////////////////////
                          if (item.categoryType == "MATR") {
                            // material request
                            Get.to(const ProjectDetails(isMr: true));
                            return;
                          }
                          if (item.categoryType == "SERE") {
                            // service request
                            Get.to(const ProjectDetails(isMr: false));
                            return;
                          }
                          if (item.categoryType == "MYRQ") {
                            // my request
                            Get.to(
                              OfflineFilterScreen(
                                title: item.name ?? "Search",
                                userType: userType ?? "",
                                categoryType:
                                    DataMap.getValues(item.tileCode ?? "")
                                            ?.first ??
                                        "",
                                isAdmin: false,
                              ),
                            );
                            return;
                          }
                          if (item.categoryType == "ADMN") {
                            // no need
                          }
                          if (item.categoryType == "MADR") {
                            Get.to(
                              OfflineFilterScreen(
                                title: item.name ?? "Search",
                                userType: userType ?? "",
                                categoryType:
                                    DataMap.getValues(item.tileCode ?? "")
                                            ?.first ??
                                        "",
                                isAdmin: true,
                              ),
                            );
                            return;
                          }
                          if (item.categoryType == "TEAM") {
                            Get.to(const ApproveTeamMembers());
                            return;
                          }
                          if (item.categoryType == "SITE") {
                            Get.to(const Site());
                            return;
                          }
                          //////////////////////////////////////////////////////
                        }
                      },
                    );
                  },
                );
              }
              return const SizedBox.shrink();
            }),
      ),
    );
  }
}

class OptionCard extends StatelessWidget {
  final String title;
  final String imageUrl;
  final VoidCallback? onTap;

  const OptionCard({
    Key? key,
    required this.title,
    required this.imageUrl,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.hardEdge,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12.0)),
      ),
      elevation: 4.0,
      margin: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: onTap,
        splashColor: AppColors.primaryColor.withOpacity(0.5),
        highlightColor: AppColors.primaryColor.withOpacity(0.2),
        child: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              height: 200,
              child: BaiImage(
                url: imageUrl,
              ),
            ).withColorOverlay(),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                width: double.infinity,
                constraints: const BoxConstraints(minHeight: 50),
                color: AppColors.primaryColor.withOpacity(0.8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 3,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
