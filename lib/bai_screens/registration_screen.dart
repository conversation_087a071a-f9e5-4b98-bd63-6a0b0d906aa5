import 'package:connectone/bai_screens/registration_buyer.dart';
import 'package:connectone/bai_screens/registration_seller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../core/bai_widgets/help_info.dart';

class RegistrationScreen extends StatelessWidget {
  const RegistrationScreen({Key? key}) : super(key: key);

  @override

  /// Builds the registration screen which allows users to select
  /// if they want to register as a buyer or a seller.
  ///
  /// The screen contains two buttons. The first button is for
  /// buyers and the second button is for sellers.
  ///
  /// When the user clicks on a button, they are redirected to the
  /// registration screen for the selected type.
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        // Hide the app bar
        backgroundColor: AppColors.primaryColor,
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle(
          // Set the status bar color to the primary color
          statusBarColor: AppColors.primaryColor,
          // Set the status bar icon brightness to light
          statusBarIconBrightness: Brightness.light,
        ),
        actions: const [
          // Display the help icon in the app bar
          InfoHelp(
            url: '',
            name: '',
          )
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Display the icon for the app
              const Icon(
                Icons.account_balance,
                color: Colors.white,
                size: 48,
              ),
              const SizedBox(height: 8),
              // Display the title of the screen
              const Text(
                'Registration',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 72),
              // Display the buyer button
              RegistrationButton(
                text: 'Buyer \n(Builders, Contractors)',
                onPressed: () {
                  // Redirect the user to the buyer registration screen
                  Get.to(const RegistrationBuyerScreen());
                },
                route: 'assets/images/buyer_icon.png',
              ),
              const SizedBox(height: 24),
              // Display the seller button
              RegistrationButton(
                text: 'Seller\n(Material Providers, Vendors)',
                onPressed: () {
                  // Redirect the user to the seller registration screen
                  Get.to(const RegistrationSellerScreen());
                },
                route: 'assets/images/seller_icon.png',
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
}

class RegistrationButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final String route;

  const RegistrationButton({
    Key? key,
    required this.text,
    required this.onPressed,
    required this.route,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed, // Trigger the onPressed callback when tapped
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        // width: MediaQuery.of(context).size.width - 32,
        height: 96,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(width: 40),
            Image.asset(
              route,
              width: 72,
            ),
            const SizedBox(width: 40),
            Expanded(
              child: Text(
                text,
                style: const TextStyle(
                  // color: AppColors.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
        ),
      ),
    );
  }
}
