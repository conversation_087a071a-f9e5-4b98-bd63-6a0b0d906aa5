import 'dart:io';
import 'dart:ui';

import 'package:connectone/bai_blocs/purchases_bloc/purchases_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/get_projects_res.dart';
import 'package:connectone/bai_screens/add_new_project.dart';
import 'package:connectone/bai_screens/add_purchase_order.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class ProjectDetails extends StatefulWidget {
  const ProjectDetails({
    Key? key,
    this.orderGroupId,
    this.deliveryDate,
    this.category,
    required this.isMr,
    this.splitSiteId,
    this.splitGroupId,
    this.categoryId,
    this.splitGroupName,
  }) : super(key: key);

  final int? orderGroupId;
  final DateTime? deliveryDate;
  final String? category;
  final bool isMr;
  final int? splitSiteId;
  final int? splitGroupId;
  final int? categoryId;
  final String? splitGroupName;

  @override
  State<ProjectDetails> createState() => _ProjectDetailsState();
}

class _ProjectDetailsState extends State<ProjectDetails> {
  double lat = 0;
  double lng = 0;

  MapboxMap? mapboxMap;

  String? selectedProjectName;
  Project selectedProject = Project();

  void _onMapCreated(MapboxMap mapboxMap1) {
    mapboxMap = mapboxMap1;
  }

  @override
  void initState() {
    super.initState();
    createTutorial();
    getData();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Site Details!\n\nHere you can:\n\n• Select a site from the dropdown menu at the top\n• View complete site information including name, address and access details\n• See the site's exact location on the interactive map\n• Add a new site using the 'Add New Site' button if needed\n\nOnce you've selected a site, tap NEXT to proceed with your order.\n\nThe map is interactive - tap on it to open navigation in your preferred maps app.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  void getData() {
    context.read<PurchasesCubit>().getUnitsAndSiteAccess();
    context.read<PurchasesCubit>().getProjects();
  }

  void reset() {
    setState(() {
      selectedProjectName = null;
      selectedProject = Project();
    });
  }

  bool isSubmitted = false;

  getErrorText() {
    if (isSubmitted) {
      if (selectedProjectName == null) {
        return "Please select a site";
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  @override

  /// Builds the UI for the project details screen.
  ///
  /// The screen displays a dropdown to select a project, a button to add a new
  /// project, and a button to go to the next screen.
  ///
  /// The site name, site address, and site access are displayed in a card with
  /// an add button.
  ///
  /// The site location is displayed in a map widget. The user can tap on the
  /// map to go to the next screen.
  ///
  /// The site details are fetched from the server when the screen is loaded.
  /// The user can select a project from the dropdown. The project's site name,
  /// site address, and site access are displayed in the card.
  ///
  /// The site location is fetched from the server when the user selects a
  /// project. The site location is displayed in the map widget.
  ///
  /// The user can tap on the add button to go to the next screen.
  ///
  /// The screen is a stateful widget. The state is used to store the selected
  /// project and the site details. The state is updated when the user selects a
  /// project and when the user taps on the add button.
  ///
  /// The screen uses a Bloc to fetch the project details and to fetch the site
  /// location. The Bloc is provided by the [PurchasesCubit] class.
  ///
  /// The screen uses a [MapWidget] to display the site location. The [MapWidget]
  /// is provided by the [MapLauncher] class.
  ///
  /// The screen uses a [BaiButton] to display the button to go to the next
  /// screen. The [BaiButton] is provided by the [BaiWidgets] class.
  Widget build(BuildContext context) {
    // var designation = getDesignation().toLowerCase();
    var roleCode = getRoleLevel();
    bool isSiteInCharge = roleCode == 15;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Site Details'),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
          // IconButton(
          //   icon: const Icon(Icons.shopping_cart_outlined),
          //   onPressed: () {
          //     // Handle cart action
          //   },
          // ),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<PurchasesCubit, PurchasesState>(
          listener: (context, state) {
            if (state is ProjectDetailsError) {
              alert(state.error);
            }
            if (state is ProjectDetailsLoaded) {
              if (widget.orderGroupId != null && widget.splitSiteId != null) {
                // selectedProjectName = value;
                selectedProject = state.projects?.content?.firstWhere(
                        (element) => element.id == widget.splitSiteId) ??
                    Project();
                selectedProjectName =
                    "${selectedProject.projectName} - ${selectedProject.id}";
                BaiCart.project = selectedProject;
                BaiCart.siteAccess = selectedProject.siteAccess;
                mapboxMap?.setCamera(CameraOptions(
                  center: Point(
                      coordinates: Position(
                    selectedProject.address?.longitude ?? lng,
                    selectedProject.address?.latitude ?? lat,
                  )),
                  zoom: 12.0,
                ));
              }
            }
          },
          builder: (context, state) {
            (state is ProjectDetailsLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is ProjectDetailsLoaded) {
              return SizedBox(
                height: MediaQuery.of(context).size.height - 72,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: ListView(
                    children: [
                      const SizedBox(height: 12),
                      GestureDetector(
                        onTap: () {
                          if (widget.orderGroupId != null) {
                            properAlert(
                                "While splitting the MR, you cannot change the existing site. \n\nTap on the NEXT button to continue.");
                            return;
                          }
                        },
                        child: AbsorbPointer(
                          absorbing: widget.orderGroupId != null ? true : false,
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              //vishnu-commented
                              labelText: 'Select Site',
                              focusedBorder: const OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.black,
                                  width: 1,
                                ),
                              ),
                              enabledBorder: const OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.black,
                                  width: 1,
                                ),
                              ),
                              border: const OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.black,
                                  width: 1,
                                ),
                              ),
                              labelStyle: const TextStyle(color: Colors.black),
                              isDense: true,
                              errorText: getErrorText(),
                            ),
                            items: state.projects!.content!
                                .map((project) => DropdownMenuItem<String>(
                                      value:
                                          "${project.projectName} - ${project.id}",
                                      child: Text("${project.projectName}"),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedProjectName = value;
                                selectedProject = state.projects!.content!
                                    .firstWhere((element) =>
                                        "${element.projectName} - ${element.id}" ==
                                        selectedProjectName);
                                BaiCart.project = selectedProject;
                                BaiCart.siteAccess = selectedProject.siteAccess;
                                mapboxMap?.setCamera(CameraOptions(
                                  center: Point(
                                      coordinates: Position(
                                    selectedProject.address?.longitude ?? lng,
                                    selectedProject.address?.latitude ?? lat,
                                  )),
                                  zoom: 12.0,
                                ));
                              });
                              mapboxMap?.annotations
                                  .createPointAnnotationManager()
                                  .then((pointAnnotationManager) async {
                                final ByteData bytes = await rootBundle
                                    .load('assets/images/ic_marker.png');
                                final Uint8List marker =
                                    bytes.buffer.asUint8List();
                                var options = <PointAnnotationOptions>[];
                                if (selectedProject.address?.latitude != null) {
                                  options.add(PointAnnotationOptions(
                                    geometry: Point(
                                        coordinates: Position(
                                      selectedProject.address?.longitude ?? lng,
                                      selectedProject.address?.latitude ?? lat,
                                    )),
                                    image: marker,
                                    iconSize: Platform.isIOS ? 1 : 2,
                                  ));
                                }
                                pointAnnotationManager.createMulti(options);
                              });
                            },
                            value: selectedProjectName,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      BaiButton(
                        onTap: () {
                          if (selectedProjectName == null) {
                            setState(() {
                              isSubmitted = true;
                            });
                          } else {
                            Get.to(
                              AddPurchaseOrderScreen(
                                orderGroupId: widget.orderGroupId,
                                deliveryDate: widget.deliveryDate,
                                category: widget.category,
                                isMr: widget.isMr,
                                splitGroupId: widget.splitGroupId,
                                categoryId: widget.categoryId,
                                splitGroupName: widget.splitGroupName,
                              ),
                            );
                          }
                        },
                        text: "NEXT",
                      ),
                      const SizedBox(height: 20),
                      if (!isSiteInCharge && (widget.orderGroupId == null))
                        Row(
                          children: [
                            // const Expanded(child: SizedBox()),
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: AppColors.primaryColor),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.add,
                                      color: Colors.black,
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        Get.to(const AddNewProjectScreen())
                                            ?.then((value) async {
                                          reset();
                                          await Future.delayed(
                                                  const Duration(seconds: 2))
                                              .then((value) => context
                                                  .read<PurchasesCubit>()
                                                  .getProjects());
                                        });
                                      },
                                      child: const Text(
                                        //vishnu-commented
                                        "Add New Site",
                                        style: TextStyle(color: Colors.black),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 20),
                      const Text(
                        //vishnu-commented

                        'Site Name',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        //vishnu-commented

                        selectedProjectName != null
                            ? selectedProject.projectName ?? "-"
                            : "Please select a site",
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        //vishnu-commented

                        'Site Address',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        selectedProjectName != null
                            //vishnu-commented

                            ? "${selectedProject.address?.sellingAddressLine1 ?? "-"}\n${selectedProject.address?.city ?? "-"}\n${selectedProject.address?.state ?? "-"}\n${selectedProject.address?.country ?? "-"}"
                            : "Please select a site",
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        color: Colors.grey.shade200,
                        height: 200,
                        child: MapWidget(
                          key: const ValueKey("MapBoxWidget"),
                          onTapListener: (onTapListener) async {
                            final availableMaps =
                                await MapLauncher.isMapAvailable(
                                    MapType.google);
                            if (availableMaps == true) {
                              await MapLauncher.showMarker(
                                mapType: MapType.google,
                                coords: Coords(lat, lng),
                                title: "Location",
                              );
                            } else {
                              await MapLauncher.showMarker(
                                mapType: MapType.apple,
                                coords: Coords(lat, lng),
                                title: "Location",
                              );
                            }
                          },
                          onMapCreated: (onMapCreated) {
                            _onMapCreated(onMapCreated);

                            onMapCreated.gestures.updateSettings(
                              GesturesSettings(
                                scrollEnabled: true,
                                pinchToZoomEnabled: true,
                                quickZoomEnabled: true,
                                scrollMode: ScrollMode.HORIZONTAL_AND_VERTICAL,
                              ),
                            );

                            mapboxMap?.annotations
                                .createPointAnnotationManager()
                                .then((pointAnnotationManager) async {
                              final ByteData bytes = await rootBundle
                                  .load('assets/images/ic_marker.png');
                              final Uint8List marker =
                                  bytes.buffer.asUint8List();
                              var options = <PointAnnotationOptions>[];
                              if (selectedProject.address?.latitude != null) {
                                options.add(PointAnnotationOptions(
                                  geometry: Point(
                                      coordinates: Position(
                                    selectedProject.address?.longitude ?? lng,
                                    selectedProject.address?.latitude ?? lat,
                                  )),
                                  image: marker,
                                  iconSize: Platform.isIOS ? 1 : 2,
                                ));
                              }
                              pointAnnotationManager.createMulti(options);
                            });
                          },
                          // ignore: prefer_collection_literals
                          gestureRecognizers: Set()
                            ..add(Factory<PanGestureRecognizer>(
                                () => PanGestureRecognizer()))
                            ..add(Factory<ScaleGestureRecognizer>(
                                () => ScaleGestureRecognizer()))
                            ..add(Factory<TapGestureRecognizer>(
                                () => TapGestureRecognizer()))
                            ..add(Factory<OneSequenceGestureRecognizer>(
                                () => EagerGestureRecognizer())),
                          cameraOptions: selectedProject.address?.latitude !=
                                  null
                              ? CameraOptions(
                                  center: Point(
                                      coordinates: Position(
                                    selectedProject.address?.longitude ?? lng,
                                    selectedProject.address?.latitude ?? lat,
                                  )),
                                  zoom: 15.0,
                                )
                              : null,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Site Access',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        selectedProjectName != null
                            ? selectedProject.siteAccess ?? "-"
                            : "Please select a site",
                        //vishnu-commented

                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              );
            } else if (state is ProjectDetailsError) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: BaiButton(
                    onTap: () {
                      Get.to(const AddNewProjectScreen())?.then((value) async {
                        reset();
                        await Future.delayed(const Duration(seconds: 2)).then(
                            (value) =>
                                context.read<PurchasesCubit>().getProjects());
                      });
                    },
                    //vishnu-commented

                    text: "Create Site",
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
