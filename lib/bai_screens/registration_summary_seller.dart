import 'dart:ui';

import 'package:connectone/bai_blocs/seller_reg/cubit/seller_registration_cubit.dart';
import 'package:connectone/bai_models/add_category_req.dart';
import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_screens/bai_home.dart';
import 'package:connectone/bai_screens/registration_otp.dart';
import 'package:connectone/bai_screens/select_services.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/document_list_widget.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';
import 'profile_screen.dart';

class RegistrationSummarySeller extends StatefulWidget {
  final List<SelectedCategory> selectedCategories;
  final RegisterReq req;
  final List<PricingRes> pricing;
  final bool isMember;
  final bool isManufacturer;
  final bool fromProfile;

  const RegistrationSummarySeller({
    Key? key,
    required this.selectedCategories,
    required this.req,
    required this.pricing,
    required this.isMember,
    required this.isManufacturer,
    this.fromProfile = false,
  }) : super(key: key);

  @override
  State<RegistrationSummarySeller> createState() =>
      _RegistrationSummarySellerState();
}

class _RegistrationSummarySellerState extends State<RegistrationSummarySeller> {
  refreshState() async {
    await Future.delayed(const Duration(seconds: 1)).then((value) {
      setState(() {});
    });
  }

  String? selectedPaymentMethod;

  bool checkboxValue = false;

  @override
  void initState() {
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Seller Registration Summary!\n\nHere's what you need to know:\n\n1. Review your selected categories and their prices\n\n2. Check the total amount and applicable taxes\n\n3. Choose your preferred payment method\n\n4. Make sure to read and accept all terms and policies\n\n5. Verify all details before proceeding with registration\n\nClick Register to complete your seller account setup.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the UI for the registration summary page
  ///
  /// This page displays the selected categories with prices and the total price.
  /// It also displays a checkbox to accept the privacy policy, terms and conditions
  /// and refund & cancellation policies and a radio button to select the payment method.
  /// When the "Register" button is tapped, it calls the [registerSeller] method of the
  /// [SellerRegistrationCubit] with the selected categories and the selected payment method.
  Widget build(BuildContext context) {
    var items = widget.selectedCategories;
    bool isKerala = widget.req.state == "Kerala";
    return AppLoader(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Summary'),
          backgroundColor: AppColors.primaryColor,
          actions: [
            InfoHelp(
              key: key1,
              onTap: () {
                showTutorial();
              },
            )
          ],
        ),
        body: BlocConsumer<SellerRegistrationCubit, SellerRegistrationState>(
          listener: (context, state) {
            if (state is SellerRegistrationError) {
              if (state.message.contains("already registered")) {
                Get.off(RegistrationOTP(
                  user: widget.req.email ?? "",
                  isSeller: true,
                  sendOtpInitially: true,
                ));
              } else {
                properAlert(state.message);
              }
            }
            if (state is SellerRegistrationSuccess) {
              Get.off(RegistrationOTP(
                user: widget.req.email ?? "",
                isSeller: true,
                sendOtpInitially: true,
              ));
            }
            if (state is CategoriesAdded) {
              Get.offAll(const BaiHome());
              Get.to(const ProfileScreen());
            }
            if (state is CategoriesAddError) {
              properAlert(state.message);
            }
          },
          builder: (context, state) {
            (state is SellerRegistrationLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            return SizedBox(
              height: MediaQuery.of(context).size.height - 72,
              child: Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.only(right: 12),
                      itemCount: items.length,
                      itemBuilder: (context, index) {
                        var item = items[index];
                        return Row(
                          children: [
                            Checkbox(
                              value: true,
                              onChanged: (val) {},
                              fillColor: WidgetStateProperty.all(
                                  AppColors.primaryColor),
                              checkColor: Colors.white,
                            ),
                            Expanded(
                              child: Text(
                                item.categories ?? "",
                                style: const TextStyle(color: Colors.black),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              _getPrice(item.categories ?? "0")
                                  .toString()
                                  .toIndianCurrencyFormat(),
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 12,
                    ),
                    margin: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            const Text(
                              "Total",
                              style: TextStyle(
                                fontSize: 16,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              "${_getTotal()}".toIndianCurrencyFormat(),
                              style: const TextStyle(
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        if (isKerala)
                          Row(
                            children: [
                              const Text(
                                "CGST",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "${_getTotal() * 0.09}"
                                    .toIndianCurrencyFormat(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        if (isKerala) const SizedBox(height: 4),
                        if (isKerala)
                          Row(
                            children: [
                              const Text(
                                "SGST",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "${_getTotal() * 0.09}"
                                    .toIndianCurrencyFormat(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        if (!isKerala)
                          Row(
                            children: [
                              const Text(
                                "IGST",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                "${_getTotal() * 0.18}"
                                    .toIndianCurrencyFormat(),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Text(
                              "Grand Total: ",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              "${_getTotal() * 1.18}".toIndianCurrencyFormat(),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  DocumentListWidgetSeller(onAllChecked: (val) {
                    setState(() {
                      checkboxValue = val;
                    });
                  }),
                  const SizedBox(height: 8),
                  // Added Payment Method Selection
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Choose payment method:",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: RadioListTile<String>(
                                title: Text(
                                  'Cash/Cheque',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primaryColor,
                                  ),
                                ),
                                value: 'Cash/Cheque',
                                groupValue: selectedPaymentMethod,
                                onChanged: (value) {
                                  setState(() {
                                    selectedPaymentMethod = value;
                                  });
                                },
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                activeColor: AppColors.primaryColor,
                                visualDensity: const VisualDensity(
                                  horizontal: VisualDensity.minimumDensity,
                                  vertical: VisualDensity.minimumDensity,
                                ),
                              ),
                            ),
                            Expanded(
                              child: RadioListTile<String>(
                                title: Text('Online',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primaryColor,
                                    )),
                                value: 'Online',
                                groupValue: selectedPaymentMethod,
                                onChanged: (value) {
                                  setState(() {
                                    selectedPaymentMethod = value;
                                  });
                                },
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                activeColor: AppColors.primaryColor,
                                visualDensity: const VisualDensity(
                                  horizontal: VisualDensity.minimumDensity,
                                  vertical: VisualDensity.minimumDensity,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 12,
                    ),
                    child: BaiButton(
                      onTap: () {
                        if (checkboxValue == false) {
                          alert(
                              "Please accept the privacy policy, terms and conditions and refund & cancellation policies.");
                          return;
                        } else if (selectedPaymentMethod == null) {
                          alert("Please select a payment method.");
                          return;
                        } else {
                          var req = widget.req;
                          List<CategoriesList> categoriesList = [];
                          List<CategoriesAndArea> categoriesListNew = [];
                          for (var element in widget.selectedCategories) {
                            categoriesList.add(CategoriesList(
                              categoryId: element.id?.toInt(),
                              price: element.rate?.toInt(),
                            ));
                            categoriesListNew.add(CategoriesAndArea(
                              categoryId: element.id?.toInt(),
                              price: element.rate?.toInt(),
                              areaOfBusinessId: null,
                            ));
                          }
                          req.categoriesList = categoriesList;
                          req.modeOfPayment = selectedPaymentMethod;
                          req.totalPrice = (_getTotal() * 1.18).toDouble();
                          if (widget.fromProfile) {
                            context
                                .read<SellerRegistrationCubit>()
                                .addCategories(
                                  AddCategoryReq(
                                    categoriesAndAreas: categoriesListNew,
                                    totalPrice: (_getTotal() * 1.18).toInt(),
                                    modeOfPayment: selectedPaymentMethod,
                                  ),
                                );
                          } else {
                            context
                                .read<SellerRegistrationCubit>()
                                .registerSeller(req);
                          }
                        }
                      },
                      text: "Register",
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  num _getPrice(String category) {
    bool isFirstCategory = false;
    var index = 0;
    var selectedPricing = widget.pricing;
    var price = 0;

    widget.selectedCategories
        .sort((a, b) => (b.rate ?? 0).compareTo(a.rate ?? 0));

    for (var i = 0; i < selectedPricing.length; i++) {
      if (selectedPricing[i].categories == category) {
        index = i;
        break;
      }
    }

    isFirstCategory = widget.selectedCategories[0].categories == category;

    if (isFirstCategory) {
      if (widget.isManufacturer && widget.isMember) {
        price = selectedPricing[index].memberManufacturerRate1 ?? 0;
      } else if (widget.isManufacturer && !widget.isMember) {
        price = selectedPricing[index].manufacturerRate1 ?? 0;
      } else if (!widget.isManufacturer && widget.isMember) {
        price = selectedPricing[index].memberDealerRate1 ?? 0;
      } else {
        price = selectedPricing[index].dealerRate1 ?? 0;
      }
    } else {
      if (widget.isManufacturer && widget.isMember) {
        price = selectedPricing[index].memberManufacturerRate2 ?? 0;
      } else if (widget.isManufacturer && !widget.isMember) {
        price = selectedPricing[index].manufacturerRate2 ?? 0;
      } else if (!widget.isManufacturer && widget.isMember) {
        price = selectedPricing[index].memberDealerRate2 ?? 0;
      } else {
        price = selectedPricing[index].dealerRate2 ?? 0;
      }
    }

    for (var i = 0; i < widget.selectedCategories.length; i++) {
      if (widget.selectedCategories[i].categories == category) {
        widget.selectedCategories[i].rate = price;
        break;
      }
    }

    return price;
  }

  @override
  dispose() {
    super.dispose();
  }

  num _getTotal() {
    num sum = 0;
    for (var i = 0; i < widget.selectedCategories.length; i++) {
      sum += widget.selectedCategories[i].rate ?? 0;
    }
    return sum;
  }

  Map<String, dynamic> createPaymentOptions({
    required double amount,
    required String orderId,
    required String email,
    required String phone,
  }) {
    return {
      'key': 'rzp_test_3ipVrFfZAVlrUt',
      'amount': amount * 100, // Amount should be in paise
      'name': 'BAI Store',
      'description': 'Payment for registration',
      'order_id': orderId, // Include the order ID here
      'prefill': {
        'contact': phone,
        'email': email,
      },
    };
  }
}
