import 'dart:ui';

import 'package:connectone/bai_blocs/team_member/cubit/team_member_cubit.dart';
import 'package:connectone/bai_screens/add_employee_page.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/assign_to_new_member.dart';
import 'package:connectone/core/bai_widgets/team_member_card.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class ApproveTeamMembers extends StatefulWidget {
  const ApproveTeamMembers({Key? key}) : super(key: key);

  @override
  State<ApproveTeamMembers> createState() => _ApproveTeamMembersState();
}

class _ApproveTeamMembersState extends State<ApproveTeamMembers> {
  @override
  void initState() {
    super.initState();
    context.read<TeamMemberCubit>().getTeamMembers();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to Team Member Approval!\n\nHere's what you can do:\n\n1. Review pending team member requests in the list\n\n2. Tap the checkmark to approve a team member\n\n3. Tap the X to reject a team member\n\n4. Use the + button in the top right to add new team members\n\n5. Each card shows the member's details for easy review\n\nManage your team efficiently by approving or rejecting members as needed.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the UI for the [ApproveTeamMembers] screen.
  ///
  /// The screen displays a list of team members that need to be approved or
  /// rejected.
  ///
  /// The list is fetched from the server and then displayed in a [ListView].
  /// Each item in the list is a [MemberCard]. The [MemberCard] displays the
  /// team member's name, email, and a button to approve or reject the team
  /// member.
  ///
  /// When the user clicks the approve or reject button, the corresponding
  /// action is taken and the team member is removed from the list.
  ///
  /// If the list is empty, a message is displayed indicating that there is no
  /// data.
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('Approve Team Members'),
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddEmployeePage(),
                ),
              ).then((val) => context.read<TeamMemberCubit>().getTeamMembers());
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<TeamMemberCubit, TeamMemberState>(
          /// Listens to the state changes of the [TeamMemberCubit].
          ///
          /// If the state is [TeamMemberApproved] or [TeamMemberRejected], a
          /// message is displayed indicating that the team member has been
          /// approved or rejected.
          ///
          /// Then the [TeamMemberCubit] is asked to fetch the list of team
          /// members again.
          listener: (context, state) {
            if (state is TeamMemberApproved) {
              properAlert("Team member approved.");
              context.read<TeamMemberCubit>().getTeamMembers();
            } else if (state is TeamMemberRejected) {
              properAlert("Team member rejected.");
              context.read<TeamMemberCubit>().getTeamMembers();
            }
          },

          /// Builds the UI based on the state of the [TeamMemberCubit].
          ///
          /// If the state is [TeamMemberLoading], a loading animation is
          /// displayed.
          ///
          /// If the state is [TeamMemberLoaded], the list of team members is
          /// displayed.
          ///
          /// If the state is not [TeamMemberLoaded], a message is displayed
          /// indicating that there is no data.
          builder: (context, state) {
            if (state is TeamMemberLoading) {
              context.loaderOverlay.show();
            } else {
              context.loaderOverlay.hide();
            }

            if (state is TeamMemberLoaded) {
              var list = state.teamMembers;
              if (list.isEmpty) {
                return _buildNoDataView();
              }
              return ListView.builder(
                itemCount: list.length,
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  var member = list[index];
                  return MemberCard(
                    teamMember: list[index],
                    onApprove: () => context
                        .read<TeamMemberCubit>()
                        .approveTeamMember(list[index].id.toString()),
                    onReject: () => context
                        .read<TeamMemberCubit>()
                        .rejectTeamMember(list[index].id.toString()),
                    onDelete: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return AssignToNewMemberDialog(member: member);
                        },
                      );
                    },
                    onInactive: () => context
                        .read<TeamMemberCubit>()
                        .rejectTeamMember(list[index].id.toString()),
                  );
                },
              );
            }
            return _buildNoDataView();
          },
        ),
      ),
    );
  }

  Widget _buildNoDataView() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: const Center(
        child: Text(
          'No data found',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
