import 'dart:ui';

import 'package:connectone/bai_models/cat_sub_cat_res.dart';
import 'package:connectone/bai_screens/product_list.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../core/bai_widgets/help_info.dart';

class SubCatList extends StatefulWidget {
  const SubCatList({
    Key? key,
    required this.title,
    required this.subCategories,
    this.orderGroupId,
    this.deliveryDate,
    this.category,
    required this.isMr,
    this.splitGroupId,
    this.categoryId,
    this.splitGroupName,
  }) : super(key: key);

  final String title;
  final List<Subcategory> subCategories;
  final int? orderGroupId;
  final DateTime? deliveryDate;
  final String? category;
  final bool isMr;
  final int? splitGroupId;
  final int? categoryId;
  final String? splitGroupName;

  @override
  State<SubCatList> createState() => _SubCatListState();
}

class _SubCatListState extends State<SubCatList> {
  @override
  void initState() {
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Browse Categories\n\nHere you can explore different product categories.\n\nEach category shows:\n- Category icon\n- Category name\n\nTap on any category to view its products and start shopping.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override

  /// Builds the UI for the subcategory list page
  ///
  /// This page displays a list of subcategories. If a subcategory is tapped,
  /// it navigates to the ProductList page for that subcategory.
  Widget build(BuildContext context) {
    return Scaffold(
      // AppBar with title and help action
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppColors.primaryColor,
        actions: [
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          )
        ],
      ),
      // Main body with loader and list of subcategories
      body: AppLoader(
        child: SizedBox(
          height: MediaQuery.of(context).size.height - 72,
          child: widget.subCategories.isNotEmpty == true
              ? ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  itemBuilder: (context, index) {
                    // Get the subcategory at the current index
                    var item = widget.subCategories[index];
                    return ListTile(
                      // Display the name of the subcategory
                      title: Text(
                        item.name ?? "N/A",
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      // When the subcategory is tapped, navigate to the ProductList page
                      onTap: () {
                        if (widget.category != null) {
                          // Uncomment and modify the following block to add category splitting logic
                          // if (widget.category != item.name) {
                          //   alert(
                          //       "You are going to split an item of category ${widget.category}. Please select that category to proceed.");
                          //   return;
                          // }
                        }
                        Get.to(
                          ProductList(
                            title: item.name ?? "N/A",
                            categoryId: item.id ?? "",
                            orderGroupId: widget.orderGroupId,
                            deliveryDate: widget.deliveryDate,
                            isMr: widget.isMr,
                            splitGroupId: widget.splitGroupId,
                            cappCategoryId: widget.categoryId,
                            splitGroupName: widget.splitGroupName,
                          ),
                        );
                      },
                      // Leading image for the subcategory
                      leading: Container(
                        height: 48,
                        width: 48,
                        clipBehavior: Clip.hardEdge,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: BaiImage(url: item.image ?? ""),
                      ),
                    );
                  },
                  itemCount: widget.subCategories.length.toInt(),
                  // Separator between list items
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(height: 4);
                  },
                )
              : const Center(child: Text("No products found")),
        ),
      ),
    );
  }
}
