import 'package:connectone/bai_blocs/team_member/cubit/team_member_cubit.dart';
import 'package:connectone/bai_models/add_seller_req.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';

class AddEmployeePage extends StatefulWidget {
  const AddEmployeePage({Key? key}) : super(key: key);

  @override
  State<AddEmployeePage> createState() => _AddEmployeePageState();
}

class _AddEmployeePageState extends State<AddEmployeePage> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for the form fields
  final TextEditingController addressLineOneController =
      TextEditingController();
  final TextEditingController addressLineTwoController =
      TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController gstNumberController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController vendorNameController = TextEditingController();
  final TextEditingController clientIdController = TextEditingController();

  bool termsAndConditions = false;

  // Common style for text fields
  final InputDecoration _commonInputDecoration = const InputDecoration(
    border: OutlineInputBorder(borderSide: BorderSide(color: Colors.black)),
    focusedBorder:
        OutlineInputBorder(borderSide: BorderSide(color: Colors.black)),
    enabledBorder:
        OutlineInputBorder(borderSide: BorderSide(color: Colors.black)),
    filled: true,
    labelStyle: TextStyle(
      color: Colors.black,
      fontWeight: FontWeight.bold,
    ),
    fillColor: Colors.white,
    contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 10.0),
  );

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email';
    }
    return null;
  }

  String? _validateGST(String? value) {
    if (value == null || value.isEmpty) {
      return 'GST number is required';
    }
    final gstRegex = RegExp(
        r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}$');
    if (!gstRegex.hasMatch(value)) {
      return 'Enter a valid GST number';
    }
    return null;
  }

  String? _validateRequiredField(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Employee'),
        backgroundColor: AppColors.primaryColor,
      ),
      body: AppLoader(
        child: BlocConsumer<TeamMemberCubit, TeamMemberState>(
          listener: (context, state) {
            if (state is TeamMemberAdded) {
              // Show a success message
              alert("Employee added successfully.");
              Navigator.pop(context);
            }
            if (state is TeamMemberError) {
              alert("Error adding employee.");
            }
          },
          builder: (context, state) {
            state is TeamMemberLoading
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Address Line 1
                    TextFormField(
                      controller: addressLineOneController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'Address Line 1'),
                      validator: (value) =>
                          _validateRequiredField(value, 'Address Line 1'),
                    ),
                    const SizedBox(height: 12),
                    // Address Line 2
                    TextFormField(
                      controller: addressLineTwoController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'Address Line 2'),
                    ),
                    const SizedBox(height: 12),
                    // Email
                    TextFormField(
                      controller: emailController,
                      decoration:
                          _commonInputDecoration.copyWith(labelText: 'Email'),
                      keyboardType: TextInputType.emailAddress,
                      validator: _validateEmail,
                    ),
                    const SizedBox(height: 12),
                    // GST Number
                    TextFormField(
                      controller: gstNumberController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'GST Number'),
                      validator: _validateGST,
                    ),
                    const SizedBox(height: 12),
                    // Phone
                    TextFormField(
                      controller: phoneController,
                      decoration:
                          _commonInputDecoration.copyWith(labelText: 'Phone'),
                      keyboardType: TextInputType.phone,
                      validator: (value) =>
                          _validateRequiredField(value, 'Phone'),
                    ),
                    const SizedBox(height: 12),
                    // Name
                    TextFormField(
                      controller: nameController,
                      decoration:
                          _commonInputDecoration.copyWith(labelText: 'Name'),
                      validator: (value) =>
                          _validateRequiredField(value, 'Name'),
                    ),
                    const SizedBox(height: 12),
                    // Password
                    TextFormField(
                      controller: passwordController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'Password'),
                      obscureText: true,
                      validator: (value) =>
                          _validateRequiredField(value, 'Password'),
                    ),
                    const SizedBox(height: 12),
                    // State
                    TextFormField(
                      controller: stateController,
                      decoration:
                          _commonInputDecoration.copyWith(labelText: 'State'),
                      validator: (value) =>
                          _validateRequiredField(value, 'State'),
                    ),
                    const SizedBox(height: 12),
                    // City
                    TextFormField(
                      controller: cityController,
                      decoration:
                          _commonInputDecoration.copyWith(labelText: 'City'),
                      validator: (value) =>
                          _validateRequiredField(value, 'City'),
                    ),
                    const SizedBox(height: 12),
                    // Vendor Name
                    TextFormField(
                      controller: vendorNameController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'Vendor Name'),
                    ),
                    const SizedBox(height: 12),
                    // Client ID
                    TextFormField(
                      controller: clientIdController,
                      decoration: _commonInputDecoration.copyWith(
                          labelText: 'Client ID'),
                      validator: (value) =>
                          _validateRequiredField(value, 'Client ID'),
                    ),
                    const SizedBox(height: 12),
                    // Terms and Conditions
                    Row(
                      children: [
                        Checkbox(
                          value: termsAndConditions,
                          onChanged: (value) {
                            setState(() {
                              termsAndConditions = value ?? false;
                            });
                          },
                        ),
                        const Text('I Accept the Terms and Conditions'),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Submit Button
                    BaiButton(
                      onTap: () {
                        if (_formKey.currentState!.validate()) {
                          var req = AddSellerReq(
                            addressLineOne: addressLineOneController.text,
                            addressLineTwo: addressLineTwoController.text,
                            email: emailController.text,
                            gstNumber: gstNumberController.text,
                            phone: phoneController.text,
                            name: nameController.text,
                            password: passwordController.text,
                            state: stateController.text,
                            city: cityController.text,
                            vendorName: vendorNameController.text,
                            clientId: clientIdController.text,
                          );
                          context.read<TeamMemberCubit>().addNewSeller(req);
                        }
                      },
                      text: 'Submit',
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    addressLineOneController.dispose();
    addressLineTwoController.dispose();
    emailController.dispose();
    gstNumberController.dispose();
    phoneController.dispose();
    nameController.dispose();
    passwordController.dispose();
    stateController.dispose();
    cityController.dispose();
    vendorNameController.dispose();
    clientIdController.dispose();
    super.dispose();
  }
}
