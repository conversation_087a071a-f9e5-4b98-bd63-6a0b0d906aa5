import 'package:connectone/bai_blocs/site/cubit/site_cubit.dart';
import 'package:connectone/bai_screens/add_new_project.dart';
import 'package:connectone/bai_screens/site_details.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';

import '../core/bai_widgets/help_info.dart';

class Site extends StatefulWidget {
  const Site({Key? key}) : super(key: key);

  @override
  State<Site> createState() => _SiteState();
}

class _SiteState extends State<Site> {
  @override
  void initState() {
    super.initState();
    context.read<SiteCubit>().getSite();
  }

  @override

  /// Builds the site screen UI
  ///
  /// This widget displays a list of sites. Each site is shown in a card with project name and site access.
  /// The user can open site details by tapping the "Open" button.
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(), // Navigate back
        ),
        title: const Text('Site'), // Title of the screen
        backgroundColor: AppColors.primaryColor,
        actions: [
          // add icon
          IconButton(
            onPressed: () {
              Get.to(const AddNewProjectScreen())?.then((val) {
                context.read<SiteCubit>().getSite();
              });
            },
            icon: const Icon(Icons.add),
            color: Colors.white,
          ),
          const InfoHelp(
            url: '',
            name: '',
          ),
        ],
      ),
      body: AppLoader(
        child: BlocConsumer<SiteCubit, SiteState>(
          listener: (context, state) {
            // Handle state changes if needed
          },
          builder: (context, state) {
            // Show loader if the site is loading
            if (state is SiteLoading) {
              context.loaderOverlay.show();
            } else {
              context.loaderOverlay.hide();
            }

            // Display the site list when loaded
            if (state is SiteLoaded) {
              final siteList = state.siteList.content;

              // Check if site list is empty or null
              if (siteList == null || siteList.isEmpty) {
                return const Center(child: Text('No data'));
              }

              // Build a list of site cards
              return ListView.builder(
                itemCount: siteList.length,
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  final site = siteList[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Display project name
                          InfoRow(
                            label: 'Site Name',
                            value: site.projectName ?? "N/A",
                          ),
                          const SizedBox(height: 10),
                          InfoRow(
                            label: 'Address',
                            value:
                                "${site.address?.sellingAddressLine1}\n${site.address?.sellingAddressLine2}\n${site.address?.city}\n${site.address?.country}" ??
                                    "N/A",
                          ),
                          const SizedBox(height: 10),
                          // Display site access
                          InfoRow(
                            label: 'Site Access',
                            value: site.siteAccess ?? "N/A",
                          ),
                          const SizedBox(height: 10),
                          InfoRow(
                            label: 'Status',
                            value: site.projectStatus ?? "N/A",
                          ),

                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: BaiButton(
                                  onTap: () {
                                    Get.to(
                                      SiteDetails(
                                        projectId: site.id.toString(),
                                      ),
                                    ); // Navigate to site details
                                  },
                                  text: "Open",
                                  backgoundColor: AppColors.green,
                                  height: 40,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            }
            // Default case: Show no data message
            return const Center(child: Text('No data'));
          },
        ),
      ),
    );
  }
}

class InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const InfoRow({Key? key, required this.label, required this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label :',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
            child: Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold),
        )),
      ],
    );
  }
}
