import 'dart:math';
import 'dart:ui';

import 'package:connectone/bai_blocs/add_purchase_order/cubit/add_purchase_order_cubit.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/cat_sub_cat_res.dart';
import 'package:connectone/bai_models/cloud_search_res.dart';
import 'package:connectone/bai_models/test_search_res.dart';
import 'package:connectone/bai_screens/add_product.dart';
import 'package:connectone/bai_screens/check_out.dart';
import 'package:connectone/bai_screens/sub_cat_list.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/category_card.dart';
import 'package:connectone/core/bai_widgets/help_info.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

class AddPurchaseOrderScreen extends StatefulWidget {
  const AddPurchaseOrderScreen({
    Key? key,
    this.orderGroupId,
    this.deliveryDate,
    this.category,
    required this.isMr,
    this.splitGroupId,
    this.categoryId,
    this.splitGroupName,
  }) : super(key: key);

  final int? orderGroupId;
  final DateTime? deliveryDate;
  final String? category;
  final bool isMr;
  final int? splitGroupId;
  final int? categoryId;
  final String? splitGroupName;

  @override
  State<AddPurchaseOrderScreen> createState() => _AddPurchaseOrderScreenState();
}

class _AddPurchaseOrderScreenState extends State<AddPurchaseOrderScreen> {
  var suggestionsController = SuggestionsController<Hit>();

  List<CatSubCatRes> categories = [];

  @override
  void initState() {
    if (!BaiCart.hasSplitItems()) BaiCart.cartItems.clear();
    context.read<AddPurchaseOrderCubit>().getCatSubCat(widget.isMr);
    super.initState();
    createTutorial();
  }

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "Welcome to the Material Request screen!\n\nHere's how to use it:\n\n1. Use the search bar at the top to quickly find specific products\n\n2. Browse through categories below to explore available items\n\n3. Click the + button to add custom items not in the catalog\n\n4. Your cart icon shows the number of items added\n\n5. Click on any category to see its sub-categories and products\n\nSelect items you need and proceed to checkout when ready.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  int queryLength = 0;

  @override

  /// Builds the Add Purchase Order screen which allows users to add material requests and view categories.
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context); // Navigates back to the previous screen
          },
        ),
        title: Text(
            'Add ${widget.isMr ? "Material" : "Service"} Request'), // Title of the AppBar
        actions: [
          // Badge to show the number of items in the cart
          Badge(
            label: Text(BaiCart.cartItems.length.toString()),
            offset: const Offset(-0, 6),
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Get.to(CheckOut(
                  isMr: widget.isMr,
                  deliveryDate: widget.deliveryDate,
                ))?.then((val) {
                  setState(() {});
                }); // Navigates to the checkout screen
              },
            ),
          ),
          // Help information link
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          ),
        ],
      ),
      // Body with a loader overlay and BlocConsumer for state management
      body: AppLoader(
        child: BlocConsumer<AddPurchaseOrderCubit, AddPurchaseOrderState>(
          listener: (context, state) {},
          builder: (context, state) {
            // Show or hide the loader based on the loading state
            (state is AddPurchaseOrderLoading)
                ? context.loaderOverlay.show()
                : context.loaderOverlay.hide();
            if (state is AddPurchaseOrderLoaded) {
              categories = state.catSubCatRes; // Load categories from state
              return Padding(
                padding: const EdgeInsets.all(6),
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          // TypeAheadField for searching products
                          Expanded(
                            child: TypeAheadField<Content>(
                              suggestionsCallback: (search) async {
                                setState(() {
                                  queryLength = search.length;
                                });
                                if (search.isNotEmpty && search.length > 2) {
                                  var result =
                                      await NetworkController().cloudSearchTest(
                                    query: search.trim(),
                                    isMr: widget.isMr,
                                  );
                                  return result.content ?? [];
                                } else {
                                  return [];
                                }
                              },
                              loadingBuilder: (context) {
                                return const SizedBox(
                                  width: double.infinity,
                                  height: 32,
                                  child: Center(
                                    child: SizedBox(
                                      height: 16,
                                      width: 16,
                                      child: Center(
                                          child: CircularProgressIndicator(
                                              strokeWidth: 1)),
                                    ),
                                  ),
                                );
                              },
                              builder: (context, controller, focusNode) {
                                return TextField(
                                  controller: controller,
                                  focusNode: focusNode,
                                  autofocus: false,
                                  decoration: InputDecoration(
                                    labelText: 'Type here to search',
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.black,
                                        width: 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.black,
                                        width: 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    border: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: Colors.black,
                                        width: 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    labelStyle:
                                        const TextStyle(color: Colors.black),
                                    hintStyle:
                                        const TextStyle(color: Colors.black),
                                    isDense: true,
                                  ),
                                );
                              },
                              itemBuilder: (context, item) {
                                return Container(
                                  padding:
                                      const EdgeInsets.fromLTRB(12, 12, 2, 6),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "${item.name}",
                                          style: TextStyle(
                                            color: AppColors.primaryColor,
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          maxLines: 2,
                                        ),
                                      ),
                                      Icon(
                                        Icons.keyboard_arrow_right_rounded,
                                        size: 20,
                                        color: AppColors.primaryColor,
                                      ),
                                    ],
                                  ),
                                );
                              },
                              onSelected: (item) {
                                // Navigate to AddProductPage on item selection
                                if (!BaiCart.isCategoryAllowed(
                                  item.serviceId?.toInt() ?? 0,
                                  splitCategoryId: widget.categoryId,
                                )) {
                                  categoryAlert(
                                    message:
                                        "Your cart has items from a different category. Please create an MR with your current items or remove them before adding items from this category.",
                                    onGoToCart: () {
                                      Navigator.pop(context);
                                      Get.to(
                                        () => CheckOut(
                                          isMr: widget.isMr,
                                          deliveryDate: widget.deliveryDate,
                                        ),
                                      );
                                    },
                                  );

                                  return;
                                }
                                Get.to(AddProductPage(
                                  item: item,
                                  orderGroupId: widget.orderGroupId,
                                  deliveryDate: widget.deliveryDate,
                                  category: widget.category,
                                  isMr: widget.isMr,
                                  splitGroupId: widget.splitGroupId,
                                  categoryId: widget.categoryId,
                                  splitGroupName: widget.splitGroupName,
                                ))?.then((val) {
                                  setState(() {});
                                });
                              },
                              hideOnEmpty: queryLength < 1,
                            ),
                          ),
                          if (widget.deliveryDate == null)
                            const SizedBox(width: 8),
                          // Button to add a new product
                          if (widget.deliveryDate == null)
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.black),
                              ),
                              height: 50,
                              width: 40,
                              padding: const EdgeInsets.all(2),
                              child: IconButton(
                                icon: const Icon(Icons.add),
                                onPressed: () {
                                  if (!BaiCart.isCategoryAllowed(
                                    -3,
                                  )) {
                                    categoryAlert(
                                      message:
                                          "Your cart has items from a different category. Please create an MR with your current items or remove them before adding items from this category.",
                                      onGoToCart: () {
                                        Navigator.pop(context);
                                        Get.to(CheckOut(
                                          isMr: widget.isMr,
                                          deliveryDate: widget.deliveryDate,
                                        ));
                                      },
                                    );

                                    return;
                                  }
                                  Get.to(AddProductPage(
                                    item: Content(
                                      name:
                                          "New Product - ${Random().nextInt(1000)}",
                                      id: -3,
                                    ),
                                    orderGroupId: widget.orderGroupId,
                                    deliveryDate: widget.deliveryDate,
                                    isGeneral: true,
                                    category: widget.category,
                                    isMr: widget.isMr,
                                    splitGroupId: widget.splitGroupId,
                                    categoryId: widget.categoryId,
                                    splitGroupName: widget.splitGroupName,
                                  ));
                                },
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // GridView to display categories
                    Expanded(
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 2.0,
                          mainAxisSpacing: 2.0,
                          childAspectRatio: 0.95,
                        ),
                        itemCount: categories.length,
                        itemBuilder: (BuildContext context, int index) {
                          var item = categories[index];
                          return CategoryCard(
                            category: item.category ?? "",
                            onTap: () {
                              // Navigate to SubCatList on category tap
                              Get.to(
                                SubCatList(
                                  title: item.category ?? "N/A",
                                  subCategories: item.subcategory ?? [],
                                  orderGroupId: widget.orderGroupId,
                                  deliveryDate: widget.deliveryDate,
                                  category: widget.category,
                                  isMr: widget.isMr,
                                  splitGroupId: widget.splitGroupId,
                                  categoryId: widget.categoryId,
                                  splitGroupName: widget.splitGroupName,
                                ),
                              )?.then((val) {
                                setState(() {});
                              });
                            },
                            imageUrl: item.image ?? "",
                            title: item.category ?? "",
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            } else {
              return const SizedBox(); // Empty state
            }
          },
        ),
      ),
    );
  }
}
