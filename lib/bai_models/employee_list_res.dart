import 'dart:convert';

class EmployeeListRes {
  List<Datum>? data;
  int? status;
  String? statusDescription;

  EmployeeListRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory EmployeeListRes.fromRawJson(String str) =>
      EmployeeListRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmployeeListRes.fromJson(Map<String, dynamic> json) =>
      EmployeeListRes(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  int? id;
  int? projectId;
  int? employeeId;
  String? employeeName;
  String? projectName;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? employeeStatus;
  int? assignedByCustomerId;

  Datum({
    this.id,
    this.projectId,
    this.employeeId,
    this.employeeName,
    this.projectName,
    this.createdAt,
    this.updatedAt,
    this.employeeStatus,
    this.assignedByCustomerId,
  });

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        projectId: json["project_id"],
        employeeId: json["employee_id"],
        employeeName: json["employee_name"],
        projectName: json["project_name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        employeeStatus: json["employee_status"],
        assignedByCustomerId: json["assigned_by_customer_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "project_id": projectId,
        "employee_id": employeeId,
        "employee_name": employeeName,
        "project_name": projectName,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "employee_status": employeeStatus,
        "assigned_by_customer_id": assignedByCustomerId,
      };
}
