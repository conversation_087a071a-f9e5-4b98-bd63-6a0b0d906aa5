import 'dart:convert';

AssignProjectReq assignProjectReqFromJson(String str) =>
    AssignProjectReq.fromJson(json.decode(str));

String assignProjectReqToJson(AssignProjectReq data) =>
    json.encode(data.toJson());

class AssignProjectReq {
  int? projectId;
  int? customerId;

  AssignProjectReq({
    this.projectId,
    this.customerId,
  });

  factory AssignProjectReq.fromJson(Map<String, dynamic> json) =>
      AssignProjectReq(
        projectId: json["project_id"],
        customerId: json["customer_id"],
      );

  Map<String, dynamic> toJson() => {
        "project_id": projectId,
        "customer_id": customerId,
      };
}
