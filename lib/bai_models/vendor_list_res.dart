import 'dart:convert';

VendorList vendorListFromJson(String str) =>
    VendorList.fromJson(json.decode(str));

String vendorListToJson(VendorList data) => json.encode(data.toJson());

class VendorList {
  List<AlreadyAssignedVendor>? alreadyAssignedVendors;
  List<AssignVendor>? assignList;

  VendorList({
    this.alreadyAssignedVendors,
    this.assignList,
  });

  factory VendorList.fromJson(Map<String, dynamic> json) => VendorList(
        alreadyAssignedVendors: json["alreadyAssignedVendors"] == null
            ? []
            : List<AlreadyAssignedVendor>.from(json["alreadyAssignedVendors"]!
                .map((x) => AlreadyAssignedVendor.fromJson(x))),
        assignList: json["assignList"] == null
            ? []
            : List<AssignVendor>.from(
                json["assignList"]!.map((x) => AssignVendor.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "alreadyAssignedVendors": alreadyAssignedVendors == null
            ? []
            : List<dynamic>.from(
                alreadyAssignedVendors!.map((x) => x.toJson())),
        "assignList": assignList == null
            ? []
            : List<dynamic>.from(assignList!.map((x) => x.toJson())),
      };
}

class AlreadyAssignedVendor {
  int? id;
  dynamic customerId;
  int? vendorId;
  int? purchaseOrderId;
  int? insertedVendorId;
  dynamic insertedCustomerId;

  AlreadyAssignedVendor({
    this.id,
    this.customerId,
    this.vendorId,
    this.purchaseOrderId,
    this.insertedVendorId,
    this.insertedCustomerId,
  });

  factory AlreadyAssignedVendor.fromJson(Map<String, dynamic> json) =>
      AlreadyAssignedVendor(
        id: json["id"],
        customerId: json["customer_id"],
        vendorId: json["vendorId"],
        purchaseOrderId: json["purchase_order_id"],
        insertedVendorId: json["inserted_vendor_id"],
        insertedCustomerId: json["inserted_customer_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customer_id": customerId,
        "vendorId": vendorId,
        "purchase_order_id": purchaseOrderId,
        "inserted_vendor_id": insertedVendorId,
        "inserted_customer_id": insertedCustomerId,
      };
}

class AssignVendor {
  String? name;
  String? district;
  dynamic alreadyAssignedVendors;
  int? vendorId;
  int? customerId;
  bool? baiMember;
  String? gstFilingType;
  String? avgRating;

  AssignVendor({
    this.name,
    this.district,
    this.alreadyAssignedVendors,
    this.vendorId,
    this.customerId,
    this.baiMember,
    this.gstFilingType,
    this.avgRating,
  });

  factory AssignVendor.fromJson(Map<String, dynamic> json) => AssignVendor(
        name: json["name"],
        district: json["district"],
        alreadyAssignedVendors: json["alreadyAssignedVendors"],
        vendorId: json["vendor_id"],
        customerId: json["customer_id"],
        baiMember: json["bai_member"],
        gstFilingType: json["gst_filing_type"],
        avgRating: json["avg_rating"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "district": district,
        "alreadyAssignedVendors": alreadyAssignedVendors,
        "vendor_id": vendorId,
        "customer_id": customerId,
        "bai_member": baiMember,
        "gst_filing_type": gstFilingType,
        "avg_rating": avgRating,
      };
}
