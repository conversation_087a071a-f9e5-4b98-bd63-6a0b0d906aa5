// To parse this JSON data, do
//
//     final baiFilterRes = baiFilterResFromJson(jsonString);

import 'dart:convert';

BaiFilterRes baiFilterResFromJson(String str) =>
    BaiFilterRes.fromJson(json.decode(str));

String baiFilterResToJson(BaiFilterRes data) => json.encode(data.toJson());

class BaiFilterRes {
  List<Filter>? filters;

  BaiFilterRes({
    this.filters,
  });

  factory BaiFilterRes.fromJson(Map<String, dynamic> json) => BaiFilterRes(
        filters: json["filters"] == null
            ? []
            : List<Filter>.from(
                json["filters"]!.map((x) => Filter.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "filters": filters == null
            ? []
            : List<dynamic>.from(filters!.map((x) => x.toJson())),
      };
}

class Filter {
  String? filterName;
  List<Value>? values;
  String? type;
  int? sequence;
  String? filterDisplayName;
  int? minValue;
  int? step;
  int? maxValue;
  DateTime? minDate;
  DateTime? maxDate;

  Filter({
    this.filterName,
    this.values,
    this.type,
    this.sequence,
    this.filterDisplayName,
    this.minValue,
    this.step,
    this.maxValue,
    this.minDate,
    this.maxDate,
  });

  factory Filter.fromJson(Map<String, dynamic> json) => Filter(
        filterName: json["filter_name"],
        values: json["values"] == null
            ? []
            : List<Value>.from(json["values"]!.map((x) => Value.fromJson(x))),
        type: json["type"],
        sequence: json["sequence"],
        filterDisplayName: json["filter_display_name"],
        minValue: json["min_value"],
        step: json["step"],
        maxValue: json["max_value"],
        minDate:
            json["min_date"] == null ? null : DateTime.parse(json["min_date"]),
        maxDate:
            json["max_date"] == null ? null : DateTime.parse(json["max_date"]),
      );

  Map<String, dynamic> toJson() => {
        "filter_name": filterName,
        "values": values == null
            ? []
            : List<dynamic>.from(values!.map((x) => x.toJson())),
        "type": type,
        "sequence": sequence,
        "filter_display_name": filterDisplayName,
        "min_value": minValue,
        "step": step,
        "max_value": maxValue,
        "min_date":
            "${minDate!.year.toString().padLeft(4, '0')}-${minDate!.month.toString().padLeft(2, '0')}-${minDate!.day.toString().padLeft(2, '0')}",
        "max_date":
            "${maxDate!.year.toString().padLeft(4, '0')}-${maxDate!.month.toString().padLeft(2, '0')}-${maxDate!.day.toString().padLeft(2, '0')}",
      };
}

class Value {
  int? stockCount;
  String? name;
  int? id;

  Value({
    this.stockCount,
    this.name,
    this.id,
  });

  factory Value.fromJson(Map<String, dynamic> json) => Value(
        stockCount: json["stock_count"],
        name: json["name"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "stock_count": stockCount,
        "name": name,
        "id": id,
      };
}
