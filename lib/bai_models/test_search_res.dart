import 'dart:convert';

TestSearchRes testSearchResFromJson(String str) =>
    TestSearchRes.fromJson(json.decode(str));

String testSearchResToJson(TestSearchRes data) => json.encode(data.toJson());

class TestSearchRes {
  List<Content>? content;
  Pageable? pageable;
  bool? last;
  num? totalPages;
  num? totalElements;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  TestSearchRes({
    this.content,
    this.pageable,
    this.last,
    this.totalPages,
    this.totalElements,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  factory TestSearchRes.fromJson(Map<String, dynamic> json) => TestSearchRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalPages: json["total_pages"],
        totalElements: json["total_elements"],
        first: json["first"],
        numberOfElements: json["number_of_elements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "last": last,
        "total_pages": totalPages,
        "total_elements": totalElements,
        "first": first,
        "number_of_elements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  num? id;
  num? basePrice;
  String? pdctCd;
  String? name;
  String? image;
  bool? isAvailable;
  num? eta;
  num? price;
  num? tax;
  num? discount;
  num? itemTypeId;
  num? priorityId;
  String? vip;
  num? sequence;
  String? shortDescription;
  String? longDescription;
  num? addon;
  num? serviceItemFlag;
  num? capacity;
  num? rewardPoints;
  num? checkinCount;
  DateTime? createdAt;
  DateTime? modifiedAt;
  num? serviceId;
  num? productOfferingsId;
  num? itemId;
  num? approvedBy;
  String? priceDisplayType;
  num? kitchenGroupId;
  String? code;
  num? createdBy;
  num? itemRentalTypeId;
  num? unitQty;
  String? unitType;
  String? priceDisplayValue;
  String? auctionTypeCd;
  String? prmySellCd;
  String? auctYn;
  List<OptionGroup>? optionGroups;
  String? categoryName;

  Content({
    this.id,
    this.basePrice,
    this.pdctCd,
    this.name,
    this.image,
    this.isAvailable,
    this.eta,
    this.price,
    this.tax,
    this.discount,
    this.itemTypeId,
    this.priorityId,
    this.vip,
    this.sequence,
    this.shortDescription,
    this.longDescription,
    this.addon,
    this.serviceItemFlag,
    this.capacity,
    this.rewardPoints,
    this.checkinCount,
    this.createdAt,
    this.modifiedAt,
    this.serviceId,
    this.productOfferingsId,
    this.itemId,
    this.approvedBy,
    this.priceDisplayType,
    this.kitchenGroupId,
    this.code,
    this.createdBy,
    this.itemRentalTypeId,
    this.unitQty,
    this.unitType,
    this.priceDisplayValue,
    this.auctionTypeCd,
    this.prmySellCd,
    this.auctYn,
    this.optionGroups,
    this.categoryName,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        basePrice: json["base_price"],
        pdctCd: json["pdct_cd"],
        name: json["name"],
        image: json["image"],
        isAvailable: json["is_available"],
        eta: json["eta"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        itemTypeId: json["item_type_id"],
        priorityId: json["priority_id"],
        vip: json["vip"],
        sequence: json["sequence"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        addon: json["addon"],
        serviceItemFlag: json["service_item_flag"],
        capacity: json["capacity"],
        rewardPoints: json["reward_points"],
        checkinCount: json["checkin_count"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        serviceId: json["service_id"],
        productOfferingsId: json["product_offerings_id"],
        itemId: json["item_id"],
        approvedBy: json["approved_by"],
        priceDisplayType: json["price_display_type"],
        kitchenGroupId: json["kitchen_group_id"],
        code: json["code"],
        createdBy: json["created_by"],
        itemRentalTypeId: json["item_rental_type_id"],
        unitQty: json["unit_qty"],
        unitType: json["unit_type"],
        priceDisplayValue: json["price_display_value"],
        auctionTypeCd: json["auction_type_cd"],
        prmySellCd: json["prmy_sell_cd"],
        auctYn: json["auct_yn"],
        optionGroups: json["option_groups"] == null
            ? []
            : List<OptionGroup>.from(
                json["option_groups"]!.map((x) => OptionGroup.fromJson(x))),
        categoryName: json["category_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "base_price": basePrice,
        "pdct_cd": pdctCd,
        "name": name,
        "image": image,
        "is_available": isAvailable,
        "eta": eta,
        "price": price,
        "tax": tax,
        "discount": discount,
        "item_type_id": itemTypeId,
        "priority_id": priorityId,
        "vip": vip,
        "sequence": sequence,
        "short_description": shortDescription,
        "long_description": longDescription,
        "addon": addon,
        "service_item_flag": serviceItemFlag,
        "capacity": capacity,
        "reward_points": rewardPoints,
        "checkin_count": checkinCount,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "service_id": serviceId,
        "product_offerings_id": productOfferingsId,
        "item_id": itemId,
        "approved_by": approvedBy,
        "price_display_type": priceDisplayType,
        "kitchen_group_id": kitchenGroupId,
        "code": code,
        "created_by": createdBy,
        "item_rental_type_id": itemRentalTypeId,
        "unit_qty": unitQty,
        "unit_type": unitType,
        "price_display_value": priceDisplayValue,
        "auction_type_cd": auctionTypeCd,
        "prmy_sell_cd": prmySellCd,
        "auct_yn": auctYn,
        "option_groups": optionGroups == null
            ? []
            : List<dynamic>.from(optionGroups!.map((x) => x.toJson())),
      };
}

class OptionGroup {
  num? id;
  String? name;
  num? mvtItemId;
  bool? allowMultiSelect;
  List<Option>? options;

  OptionGroup({
    this.id,
    this.name,
    this.mvtItemId,
    this.allowMultiSelect,
    this.options,
  });

  factory OptionGroup.fromJson(Map<String, dynamic> json) => OptionGroup(
        id: json["id"],
        name: json["name"],
        mvtItemId: json["mvt_item_id"],
        allowMultiSelect: json["allow_multi_select"],
        options: json["options"] == null
            ? []
            : List<Option>.from(
                json["options"]!.map((x) => Option.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "mvt_item_id": mvtItemId,
        "allow_multi_select": allowMultiSelect,
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
      };
}

class Option {
  num? id;
  num? optionGroupId;
  String? name;
  num? price;
  bool? overrideItemPrice;

  Option({
    this.id,
    this.optionGroupId,
    this.name,
    this.price,
    this.overrideItemPrice,
  });

  factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        optionGroupId: json["option_group_id"],
        name: json["name"],
        price: json["price"],
        overrideItemPrice: json["override_item_price"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "option_group_id": optionGroupId,
        "name": name,
        "price": price,
        "override_item_price": overrideItemPrice,
      };
}

class Pageable {
  Sort? sort;
  num? pageSize;
  num? pageNumber;
  num? offset;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.pageSize,
    this.pageNumber,
    this.offset,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageSize: json["page_size"],
        pageNumber: json["page_number"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "page_size": pageSize,
        "page_number": pageNumber,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? unsorted;
  bool? sorted;
  bool? empty;

  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
