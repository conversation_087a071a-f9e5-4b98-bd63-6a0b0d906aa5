// To parse this JSON data, do
//
//     final registerReq = registerReqFromJson(jsonString);

import 'dart:convert';

RegisterReq registerReqFromJson(String str) =>
    RegisterReq.fromJson(json.decode(str));

String registerReqToJson(RegisterReq data) => json.encode(data.toJson());

class RegisterReq {
  String? name;
  String? vendorName;
  String? vendorId;
  String? email;
  String? password;
  String? addressLineOne;
  String? addressLineTwo;
  String? state;
  String? zip;
  String? city;
  bool? termsAndConditions;
  String? customerType;
  String? gstNumber;
  String? clientId;
  ShippingAddress? shippingAddress;
  String? loginType;
  String? location;
  String? designation;
  String? natureOfBusiness;
  String? typeOfGstFilling;
  bool? baiMember;
  // String? areaOfSupply;
  String? phone;
  String? countryCode;
  List<CategoriesList>? categoriesList;
  List<AreaOfSupply>? areaOfSupply;
  String? modeOfPayment;
  double? totalPrice;
  String? website;
  String? companyEmail;
  String? companyPhone;

  RegisterReq({
    this.name,
    this.vendorName,
    this.vendorId,
    this.email,
    this.password,
    this.addressLineOne,
    this.addressLineTwo,
    this.state,
    this.zip,
    this.city,
    this.termsAndConditions,
    this.customerType,
    this.gstNumber,
    this.clientId,
    this.shippingAddress,
    this.loginType,
    this.location,
    this.designation,
    this.natureOfBusiness,
    this.typeOfGstFilling,
    this.baiMember,
    this.areaOfSupply,
    this.categoriesList,
    this.phone,
    this.countryCode,
    this.modeOfPayment,
    this.totalPrice,
    this.website,
    this.companyEmail,
    this.companyPhone,
  });

  factory RegisterReq.fromJson(Map<String, dynamic> json) => RegisterReq(
        name: json["name"],
        vendorName: json["vendor_name"],
        vendorId: json["vendor_id"],
        email: json["email"],
        password: json["password"],
        addressLineOne: json["address_line_one"],
        addressLineTwo: json["address_line_two"],
        state: json["state"],
        zip: json["zip"],
        city: json["city"],
        termsAndConditions: json["terms_and_conditions"],
        customerType: json["customer_type"],
        gstNumber: json["gst_number"],
        clientId: json["client_id"],
        shippingAddress: json["shippingAddress"] == null
            ? null
            : ShippingAddress.fromJson(json["shippingAddress"]),
        loginType: json["login_type"],
        location: json["location"],
        designation: json["designation"],
        natureOfBusiness: json["nature_of_business"],
        typeOfGstFilling: json["type_of_gst_filling"],
        baiMember: json["bai_member"],
        // areaOfSupply: json["area_of_supply"],
        categoriesList: json["categories_list"] == null
            ? []
            : List<CategoriesList>.from(json["categories_list"]!
                .map((x) => CategoriesList.fromJson(x))),
        areaOfSupply: json["area_of_supply"] == null
            ? []
            : List<AreaOfSupply>.from(
                json["area_of_supply"]!.map((x) => AreaOfSupply.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "vendor_name": vendorName,
        "vendor_id": vendorId,
        "email": email,
        "password": password,
        "address_line_one": addressLineOne,
        "address_line_two": addressLineTwo,
        "state": state,
        "zip": zip,
        "city": city,
        "terms_and_conditions": termsAndConditions,
        "customer_type": customerType,
        "gst_number": gstNumber,
        "client_id": clientId,
        "shippingAddress": shippingAddress?.toJson(),
        "login_type": loginType,
        "location": location,
        "designation": designation,
        "nature_of_business": natureOfBusiness,
        "type_of_gst_filling": typeOfGstFilling,
        "bai_member": baiMember,
        // "area_of_supply": areaOfSupply,
        "phone": phone,
        "country_code": countryCode,
        "categories_list": categoriesList == null
            ? []
            : List<dynamic>.from(categoriesList!.map((x) => x.toJson())),
        "area_of_supply": areaOfSupply == null
            ? []
            : List<dynamic>.from(areaOfSupply!.map((x) => x.toJson())),
        "mode_of_payment": modeOfPayment,
        "total_price": totalPrice,
        "company_email": companyEmail,
        "company_phone": companyPhone,
      };
}

class CategoriesList {
  int? categoryId;
  int? price;

  CategoriesList({
    this.categoryId,
    this.price,
  });

  factory CategoriesList.fromJson(Map<String, dynamic> json) => CategoriesList(
        categoryId: json["category_id"],
        price: json["price"],
      );

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "price": price,
      };
}

class ShippingAddress {
  final String addressLine1;
  final String addressLine2;
  final String city;
  final String country;
  final String state;
  final String pincode;
  final double? latitude;
  final double? longitude;

  ShippingAddress({
    required this.addressLine1,
    required this.addressLine2,
    required this.city,
    required this.country,
    required this.state,
    required this.pincode,
    this.latitude,
    this.longitude,
  });

  // Factory method to create an instance from a JSON map
  factory ShippingAddress.fromJson(Map<String, dynamic> json) {
    return ShippingAddress(
      addressLine1: json['address_line1'],
      addressLine2: json['address_line2'],
      city: json['city'],
      country: json['country'],
      state: json['state'],
      pincode: json['pincode'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  // Method to convert an instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'address_line1': addressLine1,
      'address_line2': addressLine2,
      'city': city,
      'country': country,
      'state': state,
      'pincode': pincode,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class AreaOfSupply {
  int? id;
  String? area;
  DateTime? createdAt;
  String? createdBy;

  AreaOfSupply({
    this.id,
    this.area,
    this.createdAt,
    this.createdBy,
  });

  factory AreaOfSupply.fromJson(Map<String, dynamic> json) => AreaOfSupply(
        id: json["id"],
        area: json["area"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        createdBy: json["createdBy"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "area": area,
        "createdAt": createdAt?.toIso8601String(),
        "createdBy": createdBy,
      };
}
