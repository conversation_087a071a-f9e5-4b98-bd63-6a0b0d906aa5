class PurchaseOrderCategoriesRes {
  List<PurchaseOrderCategory>? categories;

  PurchaseOrderCategoriesRes({this.categories});

  factory PurchaseOrderCategoriesRes.fromJson(List<dynamic> parsedJson) {
    return PurchaseOrderCategoriesRes(
      categories: parsedJson
          .map((json) => PurchaseOrderCategory.fromJson(json))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categories': categories?.map((category) => category.toJson()).toList(),
    };
  }
}

class PurchaseOrderCategory {
  double? manufacturerPrice;
  double? normalPrice;
  String? name;
  String? id;

  PurchaseOrderCategory(
      {this.manufacturerPrice, this.normalPrice, this.name, this.id});

  factory PurchaseOrderCategory.fromJson(Map<String, dynamic> json) {
    return PurchaseOrderCategory(
      manufacturerPrice: json['manufacturer_price'],
      normalPrice: json['normal_price'],
      name: json['name'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'manufacturer_price': manufacturerPrice,
      'normal_price': normalPrice,
      'name': name,
      'id': id,
    };
  }
}
