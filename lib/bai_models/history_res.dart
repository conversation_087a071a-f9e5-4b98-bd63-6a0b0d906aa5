// To parse this JSON data, do
//
//     final historyRes = historyResFromJson(jsonString);

import 'dart:convert';

List<HistoryRes> historyResFromJson(String str) =>
    List<HistoryRes>.from(json.decode(str).map((x) => HistoryRes.fromJson(x)));

String historyResToJson(List<HistoryRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class HistoryRes {
  int? id;
  int? prchOrdrId;
  dynamic prchOrdrOffersId;
  String? statusCd;
  String? locationLat;
  String? locationLong;
  List<Media>? medias;
  dynamic eventName;
  int? assignedVendorsId;
  dynamic eventDatetime;
  int? eventCreatedCustomerId;
  String? statusName;
  DateTime? createdAt;
  String? description;
  String? eventCreatedCustomerName;
  String? eventCreatedCustomerPhone;
  String? title;
  num? mvtPrchOrdrHistoryId;
  bool? openQuoteScreen;
  bool? openNegoScreen;

  HistoryRes({
    this.id,
    this.prchOrdrId,
    this.prchOrdrOffersId,
    this.statusCd,
    this.locationLat,
    this.locationLong,
    this.medias,
    this.eventName,
    this.assignedVendorsId,
    this.eventDatetime,
    this.eventCreatedCustomerId,
    this.statusName,
    this.createdAt,
    this.description,
    this.eventCreatedCustomerName,
    this.eventCreatedCustomerPhone,
    this.title,
    this.mvtPrchOrdrHistoryId,
    this.openQuoteScreen,
    this.openNegoScreen,
  });

  factory HistoryRes.fromJson(Map<String, dynamic> json) => HistoryRes(
        id: json["id"],
        prchOrdrId: json["prchOrdrId"],
        prchOrdrOffersId: json["prchOrdrOffersId"],
        statusCd: json["statusCd"],
        locationLat: json["locationLat"],
        locationLong: json["locationLong"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        eventName: json["eventName"],
        assignedVendorsId: json["assignedVendorsId"],
        eventDatetime: json["eventDatetime"],
        eventCreatedCustomerId: json["eventCreatedCustomerId"],
        statusName: json["statusName"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        description: json["description"],
        eventCreatedCustomerName: json["eventCreatedCustomerName"],
        eventCreatedCustomerPhone: json["eventCreatedCustomerPhone"],
        title: json["title"],
        mvtPrchOrdrHistoryId: json["mvtPrchOrdrHistoryId"],
        openQuoteScreen: json["openQuoteScreen"],
        openNegoScreen: json["openNegoScreen"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "prchOrdrId": prchOrdrId,
        "prchOrdrOffersId": prchOrdrOffersId,
        "statusCd": statusCd,
        "locationLat": locationLat,
        "locationLong": locationLong,
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "eventName": eventName,
        "assignedVendorsId": assignedVendorsId,
        "eventDatetime": eventDatetime,
        "eventCreatedCustomerId": eventCreatedCustomerId,
        "statusName": statusName,
        "createdAt": createdAt?.toIso8601String(),
        "description": description,
        "eventCreatedCustomerName": eventCreatedCustomerName,
        "eventCreatedCustomerPhone": eventCreatedCustomerPhone,
        "mvtPrchOrdrHistoryId": mvtPrchOrdrHistoryId,
        "openQuoteScreen": openQuoteScreen,
        "openNegoScreen": openNegoScreen,
      };
}

class Media {
  dynamic mediaTitle;
  dynamic previewUrl;
  String? url;

  Media({
    this.mediaTitle,
    this.previewUrl,
    this.url,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        mediaTitle: json["mediaTitle"],
        previewUrl: json["previewUrl"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "mediaTitle": mediaTitle,
        "previewUrl": previewUrl,
        "url": url,
      };
}
