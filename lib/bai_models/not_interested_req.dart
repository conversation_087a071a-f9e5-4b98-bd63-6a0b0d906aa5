import 'dart:convert';

class NotInterestedReq {
  List<int>? prchOrdrIds;
  int? vendorId;
  String? statusCd;
  int? orderGrpId;

  NotInterestedReq({
    this.prchOrdrIds,
    this.vendorId,
    this.statusCd,
    this.orderGrpId,
  });

  factory NotInterestedReq.fromRawJson(String str) =>
      NotInterestedReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory NotInterestedReq.fromJson(Map<String, dynamic> json) =>
      NotInterestedReq(
        prchOrdrIds: json["prchOrdrIds"] == null
            ? []
            : List<int>.from(json["prchOrdrIds"]!.map((x) => x)),
        vendorId: json["vendorId"],
        statusCd: json["statusCd"],
        orderGrpId: json["orderGrpId"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrIds": prchOrdrIds == null
            ? []
            : List<dynamic>.from(prchOrdrIds!.map((x) => x)),
        "vendorId": vendorId,
        "statusCd": statusCd,
        "orderGrpId": orderGrpId,
      };
}
