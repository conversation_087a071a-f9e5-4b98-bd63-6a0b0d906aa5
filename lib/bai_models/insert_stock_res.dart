// To parse this JSON data, do
//
//     final insertStockRes = insertStockResFromJson(jsonString);

import 'dart:convert';

InsertStockRes insertStockResFromJson(String str) =>
    InsertStockRes.fromJson(json.decode(str));

String insertStockResToJson(InsertStockRes data) => json.encode(data.toJson());

class InsertStockRes {
  List<Order>? orders;

  InsertStockRes({
    this.orders,
  });

  factory InsertStockRes.fromJson(Map<String, dynamic> json) => InsertStockRes(
        orders: json["orders"] == null
            ? []
            : List<Order>.from(json["orders"]!.map((x) => Order.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "orders": orders == null
            ? []
            : List<dynamic>.from(orders!.map((x) => x.toJson())),
      };
}

class Order {
  int? id;
  String? productName;
  String? orderGrpName;

  Order({
    this.id,
    this.productName,
    this.orderGrpName,
  });

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        id: json["id"],
        productName: json["productName"],
        orderGrpName: json["orderGrpName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "productName": productName,
        "orderGrpName": orderGrpName,
      };
}
