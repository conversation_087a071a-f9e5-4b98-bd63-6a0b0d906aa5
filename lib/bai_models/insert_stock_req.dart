import 'dart:convert';
import 'package:intl/intl.dart';

InsertStockReq insertStockReqFromJson(String str) =>
    InsertStockReq.fromJson(json.decode(str));

String insertStockReqToJson(InsertStockReq data) => json.encode(data.toJson());

class InsertStockReq {
  int? projectId;
  int? orderGroupId;
  int? orderGrpNo;
  int? prchOrdrId;
  String? projectName;
  Address? projectAddress;
  String? siteAccess;
  String? roadAccess;
  DateTime? deliveryDate;
  int? customerId;
  Address? shippingAddress;
  List<PoStockItem>? poStockItems;

  InsertStockReq({
    this.projectId,
    this.orderGroupId,
    this.orderGrpNo,
    this.prchOrdrId,
    this.projectName,
    this.projectAddress,
    this.siteAccess,
    this.roadAccess,
    this.deliveryDate,
    this.customerId,
    this.shippingAddress,
    this.poStockItems,
  });

  factory InsertStockReq.fromJson(Map<String, dynamic> json) => InsertStockReq(
        projectId: json["projectId"],
        orderGroupId: json["orderGroupId"],
        orderGrpNo: json["orderGrpNo"],
        prchOrdrId: json["prchOrdrId"],
        projectName: json["projectName"],
        projectAddress: json["projectAddress"] == null
            ? null
            : Address.fromJson(json["projectAddress"]),
        siteAccess: json["siteAccess"],
        roadAccess: json["roadAccess"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        customerId: json["customerId"],
        shippingAddress: json["shippingAddress"] == null
            ? null
            : Address.fromJson(json["shippingAddress"]),
        poStockItems: json["poStockItems"] == null
            ? []
            : List<PoStockItem>.from(
                json["poStockItems"].map((x) => PoStockItem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "projectId": projectId,
        "orderGroupId": orderGroupId,
        "orderGrpNo": orderGrpNo,
        "prchOrdrId": prchOrdrId,
        "projectName": projectName,
        "projectAddress": projectAddress?.toJson(),
        "siteAccess": siteAccess,
        "roadAccess": roadAccess,
        "deliveryDate": deliveryDate == null
            ? null
            : DateFormat('yyyy-MM-dd HH:mm:ss').format(deliveryDate!),
        "customerId": customerId,
        "shippingAddress": shippingAddress?.toJson(),
        "poStockItems": poStockItems == null
            ? []
            : List<dynamic>.from(poStockItems!.map((x) => x.toJson())),
      };
}

class PoStockItem {
  int? mvtItemId;
  int? orderGroupId;
  String? mvtItemName;
  int? quantity;
  int? price;
  int? unitId;
  String? instructions;
  String? status;
  List<Media>? medias;
  List<Variant>? variants;
  int? cappCategoriesId;
  String? categoryName;
  int? prchOrdrSplitId;

  PoStockItem({
    this.mvtItemId,
    this.orderGroupId,
    this.mvtItemName,
    this.quantity,
    this.price,
    this.unitId,
    this.instructions,
    this.status,
    this.medias,
    this.variants,
    this.cappCategoriesId,
    this.categoryName,
    this.prchOrdrSplitId,
  });

  factory PoStockItem.fromJson(Map<String, dynamic> json) => PoStockItem(
        mvtItemId: json["mvtItemId"],
        orderGroupId: json["orderGroupId"],
        mvtItemName: json["mvtItemName"],
        quantity: json["quantity"],
        price: json["price"],
        unitId: json["unitId"],
        instructions: json["instructions"],
        cappCategoriesId: json["cappCategoriesId"],
        status: json["status"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"].map((x) => Media.fromJson(x))),
        variants: json["variants"] == null
            ? []
            : List<Variant>.from(
                json["variants"].map((x) => Variant.fromJson(x))),
        prchOrdrSplitId: json["prchOrdrSplitId"],
      );

  Map<String, dynamic> toJson() => {
        "mvtItemId": mvtItemId,
        "orderGroupId": orderGroupId,
        "mvtItemName": mvtItemName,
        "quantity": quantity,
        "price": price,
        "unitId": unitId,
        "instructions": instructions,
        "status": status,
        "cappCategoriesId": cappCategoriesId,
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "variants": variants == null
            ? []
            : List<dynamic>.from(variants!.map((x) => x.toJson())),
        "prchOrdrSplitId": prchOrdrSplitId,
      };
}

class Media {
  String? previewUrl;
  String? url;
  int? sequence;

  Media({
    this.previewUrl,
    this.url,
    this.sequence,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        previewUrl: json["previewUrl"],
        url: json["url"],
        sequence: json["sequence"],
      );

  Map<String, dynamic> toJson() => {
        "previewUrl": previewUrl,
        "url": url,
        "sequence": sequence,
      };
}

class Variant {
  int? optionGroupId;
  String? optionGroupName;
  int? optionId;
  String? optionName;
  bool? offerCheckBoolean;

  Variant({
    this.optionGroupId,
    this.optionGroupName,
    this.optionId,
    this.optionName,
    this.offerCheckBoolean,
  });

  factory Variant.fromJson(Map<String, dynamic> json) => Variant(
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionId: json["optionId"],
        optionName: json["optionName"],
        offerCheckBoolean: json["offerCheckBoolean"],
      );

  Map<String, dynamic> toJson() => {
        "optionGroupId": optionGroupId,
        "optionGroupName": optionGroupName,
        "optionId": optionId,
        "optionName": optionName,
        "offerCheckBoolean": offerCheckBoolean,
      };
}

class Address {
  String? addressLine1;
  String? addressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;
  double? latitude;
  double? longitude;
  double? cityLan;
  double? cityLong;

  Address({
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLan,
    this.cityLong,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude:
            json["latitude"] != null ? double.parse(json["latitude"]) : null,
        longitude:
            json["longitude"] != null ? double.parse(json["longitude"]) : null,
        cityLan:
            json["city_lan"] != null ? double.parse(json["city_lan"]) : null,
        cityLong:
            json["city_long"] != null ? double.parse(json["city_long"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude?.toString(),
        "longitude": longitude?.toString(),
        "city_lan": cityLan?.toString(),
        "city_long": cityLong?.toString(),
      };
}

// import 'dart:convert';

// InsertStockReq insertStockReqFromJson(String str) => InsertStockReq.fromJson(json.decode(str));

// String insertStockReqToJson(InsertStockReq data) => json.encode(data.toJson());

// class InsertStockReq {
//   int? projectId;
//   String? projectName;
//   Address? projectAddress;
//   String? siteAccess;
//   String? roadAccess;
//   String? deliveryDate;
//   int? customerId;
//   Address? shippingAddress;
//   List<PoStockItem>? poStockItems;

//   InsertStockReq({
//     this.projectId,
//     this.projectName,
//     this.projectAddress,
//     this.siteAccess,
//     this.roadAccess,
//     this.deliveryDate,
//     this.customerId,
//     this.shippingAddress,
//     this.poStockItems,
//   });

//   factory InsertStockReq.fromJson(Map<String, dynamic> json) => InsertStockReq(
//         projectId: json["projectId"],
//         projectName: json["projectName"],
//         projectAddress: json["projectAddress"] == null ? null : Address.fromJson(json["projectAddress"]),
//         siteAccess: json["siteAccess"],
//         roadAccess: json["roadAccess"],
//         deliveryDate: json["deliveryDate"],
//         customerId: json["customerId"],
//         shippingAddress: json["shippingAddress"] == null ? null : Address.fromJson(json["shippingAddress"]),
//         poStockItems: json["poStockItems"] == null ? [] : List<PoStockItem>.from(json["poStockItems"]!.map((x) => PoStockItem.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "projectId": projectId,
//         "projectName": projectName,
//         "projectAddress": projectAddress?.toJson(),
//         "siteAccess": siteAccess,
//         "roadAccess": roadAccess,
//         "deliveryDate": deliveryDate,
//         "customerId": customerId,
//         "shippingAddress": shippingAddress?.toJson(),
//         "poStockItems": poStockItems == null ? [] : List<dynamic>.from(poStockItems!.map((x) => x.toJson())),
//       };
// }

// class PoStockItem {
//   int? mvtItemId;
//   String? mvtItemName;
//   int? quantity;
//   int? price;
//   int? unitId;
//   String? instructions;
//   String? status;
//   int? stockId;
//   List<Media>? medias;

//   PoStockItem({
//     this.mvtItemId,
//     this.mvtItemName,
//     this.quantity,
//     this.price,
//     this.unitId,
//     this.instructions,
//     this.status,
//     this.stockId,
//     this.medias,
//   });

//   factory PoStockItem.fromJson(Map<String, dynamic> json) => PoStockItem(
//         mvtItemId: json["mvtItemId"],
//         mvtItemName: json["mvtItemName"],
//         quantity: json["quantity"],
//         price: json["price"],
//         unitId: json["unitId"],
//         instructions: json["instructions"],
//         status: json["status"],
//         stockId: json["stockId"],
//         medias: json["medias"] == null ? [] : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "mvtItemId": mvtItemId,
//         "mvtItemName": mvtItemName,
//         "quantity": quantity,
//         "price": price,
//         "unitId": unitId,
//         "instructions": instructions,
//         "status": status,
//         "stockId": stockId,
//         "medias": medias == null ? [] : List<dynamic>.from(medias!.map((x) => x.toJson())),
//       };
// }

// class Media {
//   String? previewUrl;
//   String? url;
//   int? sequence;

//   Media({
//     this.previewUrl,
//     this.url,
//     this.sequence,
//   });

//   factory Media.fromJson(Map<String, dynamic> json) => Media(
//         previewUrl: json["previewUrl"],
//         url: json["url"],
//         sequence: json["sequence"],
//       );

//   Map<String, dynamic> toJson() => {
//         "previewUrl": previewUrl,
//         "url": url,
//         "sequence": sequence,
//       };
// }

// class Address {
//   String? sellingAddressLine1;
//   String? sellingAddressLine2;
//   String? city;
//   String? country;
//   String? state;
//   String? pincode;
//   double? latitude;
//   double? longitude;
//   double? cityLan;
//   double? cityLong;

//   Address({
//     this.sellingAddressLine1,
//     this.sellingAddressLine2,
//     this.city,
//     this.country,
//     this.state,
//     this.pincode,
//     this.latitude,
//     this.longitude,
//     this.cityLan,
//     this.cityLong,
//   });

//   factory Address.fromJson(Map<String, dynamic> json) => Address(
//         sellingAddressLine1: json["sellingAddressLine1"],
//         sellingAddressLine2: json["sellingAddressLine2"],
//         city: json["city"],
//         country: json["country"],
//         state: json["state"],
//         pincode: json["pincode"],
//         latitude: json["latitude"]?.toDouble(),
//         longitude: json["longitude"]?.toDouble(),
//         cityLan: json["cityLan"]?.toDouble(),
//         cityLong: json["cityLong"]?.toDouble(),
//       );

//   Map<String, dynamic> toJson() => {
//         "sellingAddressLine1": sellingAddressLine1,
//         "sellingAddressLine2": sellingAddressLine2,
//         "city": city,
//         "country": country,
//         "state": state,
//         "pincode": pincode,
//         "latitude": latitude,
//         "longitude": longitude,
//         "cityLan": cityLan,
//         "cityLong": cityLong,
//       };
// }
