// // To parse this JSON data, do
// //
// //     final baiProductsRes = baiProductsResFromJson(jsonString);

// import 'dart:convert';

// BaiProductsRes baiProductsResFromJson(String str) => BaiProductsRes.fromJson(json.decode(str));

// String baiProductsResToJson(BaiProductsRes data) => json.encode(data.toJson());

// class BaiProductsRes {
//   List<Content>? content;
//   Pageable? pageable;
//   bool? last;
//   num? totalElements;
//   num? totalPages;
//   num? numberOfElements;
//   bool? first;
//   Sort? sort;
//   num? size;
//   num? number;
//   bool? empty;

//   BaiProductsRes({
//     this.content,
//     this.pageable,
//     this.last,
//     this.totalElements,
//     this.totalPages,
//     this.numberOfElements,
//     this.first,
//     this.sort,
//     this.size,
//     this.number,
//     this.empty,
//   });

//   factory BaiProductsRes.fromJson(Map<String, dynamic> json) => BaiProductsRes(
//         content: json["content"] == null ? [] : List<Content>.from(json["content"]!.map((x) => Content.fromJson(x))),
//         pageable: json["pageable"] == null ? null : Pageable.fromJson(json["pageable"]),
//         last: json["last"],
//         totalElements: json["totalElements"],
//         totalPages: json["totalPages"],
//         numberOfElements: json["numberOfElements"],
//         first: json["first"],
//         sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
//         size: json["size"],
//         number: json["number"],
//         empty: json["empty"],
//       );

//   Map<String, dynamic> toJson() => {
//         "content": content == null ? [] : List<dynamic>.from(content!.map((x) => x.toJson())),
//         "pageable": pageable?.toJson(),
//         "last": last,
//         "totalElements": totalElements,
//         "totalPages": totalPages,
//         "numberOfElements": numberOfElements,
//         "first": first,
//         "sort": sort?.toJson(),
//         "size": size,
//         "number": number,
//         "empty": empty,
//       };
// }

// class Content {
//   num? id;
//   dynamic projectName;
//   String? projectId;
//   String? productCode;
//   String? auctionTypeCode;
//   num? bidIncrementValue;
//   num? serviceId;
//   num? highestBidAmount;
//   num? itemId;
//   String? itemName;
//   num? amount;
//   String? lotNo;
//   String? priceRange;
//   String? grade;
//   dynamic quality;
//   num? expectedPrice;
//   DateTime? deliveryDate;
//   DateTime? biddingDate;
//   num? quantity;
//   num? unitId;
//   String? unitShortName;
//   String? unitName;
//   num? customerId;
//   num? buyerCustomerId;
//   num? startingPrice;
//   String? instructions;
//   String? favouriteYn;
//   String? image;
//   String? image1Thumbnail;
//   String? image1;
//   String? image2Thumbnail;
//   String? image2;
//   String? image3Thumbnail;
//   String? image3;
//   String? image4Thumbnail;
//   String? image4;
//   String? image5Thumbnail;
//   String? image5;
//   num? stockStatusId;
//   num? insertTransactionId;
//   dynamic updateTransactionId;
//   DateTime? createdAt;
//   DateTime? modifiedAt;
//   String? addressLine1;
//   String? addressLine2;
//   String? pickupCity;
//   String? state;
//   String? country;
//   String? pincode;
//   num? lat;
//   num? lon;
//   num? cityLat;
//   num? cityLong;
//   num? vendorId;
//   String? name;
//   num? roomId;
//   num? stockId;
//   String? stockStatusName;
//   String? stockStatusCd;
//   dynamic auctionCloseTs;
//   num? buyBidPrice;
//   num? averageRating;
//   num? countOfRatings;
//   num? uniqueViews;
//   num? totalViews;
//   String? activeYn;
//   String? auctionYn;
//   num? categoryId;
//   String? productDescription;
//   String? primarySellCode;

//   Content({
//     this.id,
//     this.projectName,
//     this.projectId,
//     this.productCode,
//     this.auctionTypeCode,
//     this.bidIncrementValue,
//     this.serviceId,
//     this.highestBidAmount,
//     this.itemId,
//     this.itemName,
//     this.amount,
//     this.lotNo,
//     this.priceRange,
//     this.grade,
//     this.quality,
//     this.expectedPrice,
//     this.deliveryDate,
//     this.biddingDate,
//     this.quantity,
//     this.unitId,
//     this.unitShortName,
//     this.unitName,
//     this.customerId,
//     this.buyerCustomerId,
//     this.startingPrice,
//     this.favouriteYn,
//     this.image,
//     this.image1Thumbnail,
//     this.image1,
//     this.image2Thumbnail,
//     this.image2,
//     this.image3Thumbnail,
//     this.image3,
//     this.image4Thumbnail,
//     this.image4,
//     this.image5Thumbnail,
//     this.image5,
//     this.stockStatusId,
//     this.insertTransactionId,
//     this.updateTransactionId,
//     this.createdAt,
//     this.modifiedAt,
//     this.addressLine1,
//     this.addressLine2,
//     this.pickupCity,
//     this.state,
//     this.country,
//     this.pincode,
//     this.lat,
//     this.lon,
//     this.cityLat,
//     this.cityLong,
//     this.vendorId,
//     this.name,
//     this.roomId,
//     this.stockId,
//     this.stockStatusName,
//     this.stockStatusCd,
//     this.auctionCloseTs,
//     this.buyBidPrice,
//     this.averageRating,
//     this.countOfRatings,
//     this.uniqueViews,
//     this.totalViews,
//     this.activeYn,
//     this.auctionYn,
//     this.categoryId,
//     this.productDescription,
//     this.primarySellCode,
//     this.instructions,
//   });

//   factory Content.fromJson(Map<String, dynamic> json) => Content(
//         id: json["id"],
//         projectName: json["projectName"],
//         projectId: json["projectId"],
//         productCode: json["productCode"],
//         auctionTypeCode: json["auctionTypeCode"],
//         bidIncrementValue: json["bidIncrementValue"]?.toDouble(),
//         serviceId: json["serviceId"],
//         highestBidAmount: json["highestBidAmount"],
//         itemId: json["itemId"],
//         instructions: json["instructions"],
//         itemName: json["itemName"],
//         amount: json["amount"],
//         lotNo: json["lotNo"],
//         priceRange: json["priceRange"],
//         grade: json["grade"],
//         quality: json["quality"],
//         expectedPrice: json["expectedPrice"],
//         deliveryDate: json["deliveryDate"] == null ? null : DateTime.parse(json["deliveryDate"]),
//         biddingDate: json["biddingDate"] == null ? null : DateTime.parse(json["biddingDate"]),
//         quantity: json["quantity"],
//         unitId: json["unitId"],
//         unitShortName: json["unitShortName"],
//         unitName: json["unitName"],
//         customerId: json["customerId"],
//         buyerCustomerId: json["buyerCustomerId"],
//         startingPrice: json["startingPrice"],
//         favouriteYn: json["favouriteYN"],
//         image: json["image"],
//         image1Thumbnail: json["image1Thumbnail"],
//         image1: json["image1"],
//         image2Thumbnail: json["image2Thumbnail"],
//         image2: json["image2"],
//         image3Thumbnail: json["image3Thumbnail"],
//         image3: json["image3"],
//         image4Thumbnail: json["image4Thumbnail"],
//         image4: json["image4"],
//         image5Thumbnail: json["image5Thumbnail"],
//         image5: json["image5"],
//         stockStatusId: json["stockStatusId"],
//         insertTransactionId: json["insertTransactionId"],
//         updateTransactionId: json["updateTransactionId"],
//         createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
//         modifiedAt: json["modifiedAt"] == null ? null : DateTime.parse(json["modifiedAt"]),
//         addressLine1: json["addressLine1"],
//         addressLine2: json["addressLine2"],
//         pickupCity: json["pickupCity"],
//         state: json["state"],
//         country: json["country"],
//         pincode: json["pincode"],
//         lat: json["lat"]?.toDouble(),
//         lon: json["lon"]?.toDouble(),
//         cityLat: json["cityLat"]?.toDouble(),
//         cityLong: json["cityLong"]?.toDouble(),
//         vendorId: json["vendorId"],
//         name: json["name"],
//         roomId: json["roomId"],
//         stockId: json["stockId"],
//         stockStatusName: json["stockStatusName"],
//         stockStatusCd: json["stockStatusCd"],
//         auctionCloseTs: json["auctionCloseTs"],
//         buyBidPrice: json["buyBidPrice"],
//         averageRating: json["averageRating"],
//         countOfRatings: json["countOfRatings"],
//         uniqueViews: json["uniqueViews"],
//         totalViews: json["totalViews"],
//         activeYn: json["activeYn"],
//         auctionYn: json["auctionYn"],
//         categoryId: json["categoryId"],
//         productDescription: json["productDescription"],
//         primarySellCode: json["primarySellCode"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "projectName": projectName,
//         "projectId": projectId,
//         "productCode": productCode,
//         "auctionTypeCode": auctionTypeCode,
//         "bidIncrementValue": bidIncrementValue,
//         "serviceId": serviceId,
//         "highestBidAmount": highestBidAmount,
//         "itemId": itemId,
//         "itemName": itemName,
//         "amount": amount,
//         "lotNo": lotNo,
//         "priceRange": priceRange,
//         "grade": grade,
//         "quality": quality,
//         "expectedPrice": expectedPrice,
//         "deliveryDate":
//             "${deliveryDate!.year.toString().padLeft(4, '0')}-${deliveryDate!.month.toString().padLeft(2, '0')}-${deliveryDate!.day.toString().padLeft(2, '0')}",
//         "biddingDate":
//             "${biddingDate!.year.toString().padLeft(4, '0')}-${biddingDate!.month.toString().padLeft(2, '0')}-${biddingDate!.day.toString().padLeft(2, '0')}",
//         "quantity": quantity,
//         "unitId": unitId,
//         "unitShortName": unitShortName,
//         "unitName": unitName,
//         "customerId": customerId,
//         "buyerCustomerId": buyerCustomerId,
//         "startingPrice": startingPrice,
//         "favouriteYN": favouriteYn,
//         "image": image,
//         "image1Thumbnail": image1Thumbnail,
//         "image1": image1,
//         "image2Thumbnail": image2Thumbnail,
//         "image2": image2,
//         "image3Thumbnail": image3Thumbnail,
//         "image3": image3,
//         "image4Thumbnail": image4Thumbnail,
//         "image4": image4,
//         "image5Thumbnail": image5Thumbnail,
//         "image5": image5,
//         "stockStatusId": stockStatusId,
//         "insertTransactionId": insertTransactionId,
//         "updateTransactionId": updateTransactionId,
//         "createdAt":
//             "${createdAt!.year.toString().padLeft(4, '0')}-${createdAt!.month.toString().padLeft(2, '0')}-${createdAt!.day.toString().padLeft(2, '0')}",
//         "modifiedAt":
//             "${modifiedAt!.year.toString().padLeft(4, '0')}-${modifiedAt!.month.toString().padLeft(2, '0')}-${modifiedAt!.day.toString().padLeft(2, '0')}",
//         "addressLine1": addressLine1,
//         "addressLine2": addressLine2,
//         "pickupCity": pickupCity,
//         "state": state,
//         "country": country,
//         "pincode": pincode,
//         "lat": lat,
//         "lon": lon,
//         "cityLat": cityLat,
//         "cityLong": cityLong,
//         "vendorId": vendorId,
//         "name": name,
//         "roomId": roomId,
//         "stockId": stockId,
//         "stockStatusName": stockStatusName,
//         "stockStatusCd": stockStatusCd,
//         "auctionCloseTs": auctionCloseTs,
//         "buyBidPrice": buyBidPrice,
//         "averageRating": averageRating,
//         "countOfRatings": countOfRatings,
//         "uniqueViews": uniqueViews,
//         "totalViews": totalViews,
//         "activeYn": activeYn,
//         "auctionYn": auctionYn,
//         "categoryId": categoryId,
//         "productDescription": productDescription,
//         "primarySellCode": primarySellCode,
//         "instructions": instructions,
//       };
// }

// class Pageable {
//   Sort? sort;
//   num? pageNumber;
//   num? pageSize;
//   num? offset;
//   bool? paged;
//   bool? unpaged;

//   Pageable({
//     this.sort,
//     this.pageNumber,
//     this.pageSize,
//     this.offset,
//     this.paged,
//     this.unpaged,
//   });

//   factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
//         sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
//         pageNumber: json["pageNumber"],
//         pageSize: json["pageSize"],
//         offset: json["offset"],
//         paged: json["paged"],
//         unpaged: json["unpaged"],
//       );

//   Map<String, dynamic> toJson() => {
//         "sort": sort?.toJson(),
//         "pageNumber": pageNumber,
//         "pageSize": pageSize,
//         "offset": offset,
//         "paged": paged,
//         "unpaged": unpaged,
//       };
// }

// class Sort {
//   bool? unsorted;
//   bool? sorted;
//   bool? empty;

//   Sort({
//     this.unsorted,
//     this.sorted,
//     this.empty,
//   });

//   factory Sort.fromJson(Map<String, dynamic> json) => Sort(
//         unsorted: json["unsorted"],
//         sorted: json["sorted"],
//         empty: json["empty"],
//       );

//   Map<String, dynamic> toJson() => {
//         "unsorted": unsorted,
//         "sorted": sorted,
//         "empty": empty,
//       };
// }

// To parse this JSON data, do
//
//     final baiProductRes = baiProductResFromJson(jsonString);

import 'dart:convert';

BaiProductsRes baiProductResFromJson(String str) =>
    BaiProductsRes.fromJson(json.decode(str));

String baiProductResToJson(BaiProductsRes data) => json.encode(data.toJson());

class BaiProductsRes {
  List<Content>? content;
  Pageable? pageable;
  bool? last;
  num? totalElements;
  num? totalPages;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? number;
  num? size;
  bool? empty;

  BaiProductsRes({
    this.content,
    this.pageable,
    this.last,
    this.totalElements,
    this.totalPages,
    this.first,
    this.numberOfElements,
    this.sort,
    this.number,
    this.size,
    this.empty,
  });

  factory BaiProductsRes.fromJson(Map<String, dynamic> json) => BaiProductsRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        number: json["number"],
        size: json["size"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "last": last,
        "totalElements": totalElements,
        "totalPages": totalPages,
        "first": first,
        "numberOfElements": numberOfElements,
        "sort": sort?.toJson(),
        "number": number,
        "size": size,
        "empty": empty,
      };
}

class Content {
  num? prchOrdrId;
  String? statusCd;
  num? projectId;
  String? projectName;
  num? projectAddressId;
  String? projectAddressLine1;
  String? projectAddressLine2;
  String? projectCity;
  String? projectCountry;
  String? projectState;
  String? projectPincode;
  num? projectLatitude;
  num? projectLongitude;
  num? projectCityLan;
  num? projectCityLong;
  String? siteAccess;
  String? roadAccess;
  DateTime? deliveryDate;
  num? customerId;
  num? deliveryAddressId;
  String? deliveryAddressLine1;
  String? deliveryAddressLine2;
  String? deliveryCity;
  String? deliveryCountry;
  String? deliveryState;
  String? deliveryPincode;
  num? deliveryLatitude;
  num? deliveryLongitude;
  num? deliveryCityLan;
  num? deliveryCityLong;
  num? mvtItemId;
  String? mvtItemName;
  num? quantity;
  dynamic price;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  String? variant1OptionIds;
  String? variant1Options;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  String? variant2OptionIds;
  String? variant2Options;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  String? variant3OptionIds;
  String? variant3Options;
  num? optionGroupId;
  String? optionGroupName;
  num? optionId;
  String? optionName;
  DateTime? createdAt;
  DateTime? updatedAt;
  num? vendorId;
  String? image1;
  String? image2;
  String? image3;
  String? image4;
  String? image5;
  String? averageRating;
  String? instructions;
  String? cappCategoriesName;
  num? cappCategoriesId;
  String? customerName;
  String? customerPhone;
  String? vendorName;
  num? vendorRating;
  String? buyerVendorName;
  String? buyerCustomerName;
  String? buyerCustomerPhone;
  num? offerPrice;
  num? orderGroupId;
  String? orderGroupName;
  String? statusName;
  bool? isRead;
  num? sellerCustomerId;
  num? sellerVendorId;
  String? sellerVendorName;
  String? mvtItemImage;
  String? createdBy;
  bool? isMr;
  num? orderGrpNo;
  String? buyerNegoYN;
  String? sellerNegoYN;
  String? prchOrdrSplitName;
  num? prchOrdrSplitId;
  String? mrStatusButtonColor;

  Content({
    this.prchOrdrId,
    this.statusCd,
    this.projectId,
    this.projectName,
    this.projectAddressId,
    this.projectAddressLine1,
    this.projectAddressLine2,
    this.projectCity,
    this.projectCountry,
    this.projectState,
    this.projectPincode,
    this.projectLatitude,
    this.projectLongitude,
    this.projectCityLan,
    this.projectCityLong,
    this.siteAccess,
    this.roadAccess,
    this.deliveryDate,
    this.customerId,
    this.deliveryAddressId,
    this.deliveryAddressLine1,
    this.deliveryAddressLine2,
    this.deliveryCity,
    this.deliveryCountry,
    this.deliveryState,
    this.deliveryPincode,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.deliveryCityLan,
    this.deliveryCityLong,
    this.mvtItemId,
    this.mvtItemName,
    this.quantity,
    this.price,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant1OptionIds,
    this.variant1Options,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant2OptionIds,
    this.variant2Options,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.variant3OptionIds,
    this.variant3Options,
    this.optionGroupId,
    this.optionGroupName,
    this.optionId,
    this.optionName,
    this.createdAt,
    this.updatedAt,
    this.vendorId,
    this.instructions,
    this.image1,
    this.image2,
    this.image3,
    this.image4,
    this.image5,
    this.averageRating,
    this.cappCategoriesName,
    this.cappCategoriesId,
    this.customerName,
    this.customerPhone,
    this.vendorName,
    this.vendorRating,
    this.buyerVendorName,
    this.buyerCustomerName,
    this.buyerCustomerPhone,
    this.offerPrice,
    this.orderGroupId,
    this.orderGroupName,
    this.statusName,
    this.isRead,
    this.sellerCustomerId,
    this.sellerVendorId,
    this.sellerVendorName,
    this.mvtItemImage,
    this.createdBy,
    this.isMr,
    this.orderGrpNo,
    this.buyerNegoYN,
    this.sellerNegoYN,
    this.prchOrdrSplitName,
    this.prchOrdrSplitId,
    this.mrStatusButtonColor,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        prchOrdrId: json["prchOrdrId"],
        statusCd: json["statusCd"],
        projectId: json["projectId"],
        projectName: json["projectName"],
        projectAddressId: json["projectAddressId"],
        projectAddressLine1: json["projectAddressLine1"],
        projectAddressLine2: json["projectAddressLine2"],
        projectCity: json["projectCity"],
        projectCountry: json["projectCountry"],
        projectState: json["projectState"],
        projectPincode: json["projectPincode"],
        projectLatitude: json["projectLatitude"],
        projectLongitude: json["projectLongitude"],
        projectCityLan: json["projectCityLan"],
        projectCityLong: json["projectCityLong"],
        siteAccess: json["siteAccess"],
        roadAccess: json["roadAccess"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        customerId: json["customerId"],
        deliveryAddressId: json["deliveryAddressId"],
        deliveryAddressLine1: json["deliveryAddressLine1"],
        deliveryAddressLine2: json["deliveryAddressLine2"],
        deliveryCity: json["deliveryCity"],
        deliveryCountry: json["deliveryCountry"],
        deliveryState: json["deliveryState"],
        deliveryPincode: json["deliveryPincode"],
        deliveryLatitude: json["deliveryLatitude"],
        deliveryLongitude: json["deliveryLongitude"],
        deliveryCityLan: json["deliveryCityLan"],
        deliveryCityLong: json["deliveryCityLong"],
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        quantity: json["quantity"],
        price: json["price"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant1OptionIds: json["variant1OptionIds"],
        variant1Options: json["variant1Options"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant2OptionIds: json["variant2OptionIds"],
        variant2Options: json["variant2Options"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        variant3OptionIds: json["variant3OptionIds"],
        variant3Options: json["variant3Options"],
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionId: json["optionId"],
        optionName: json["optionName"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        vendorId: json["vendorId"],
        instructions: json["instructions"],
        image1: json["image1"],
        image2: json["image2"],
        image3: json["image3"],
        image4: json["image4"],
        image5: json["image5"],
        averageRating: json["averageRating"],
        cappCategoriesName: json["cappCategoriesName"],
        cappCategoriesId: json["cappCategoriesId"],
        customerName: json["customerName"],
        customerPhone: json["customerPhone"],
        vendorName: json["vendorName"],
        vendorRating: json["vendorRating"],
        buyerVendorName: json["buyerVendorName"],
        buyerCustomerName: json["buyerCustomerName"],
        buyerCustomerPhone: json["buyerCustomerPhone"],
        offerPrice: json["offerPrice"],
        orderGroupId: json["orderGroupId"],
        orderGroupName: json["orderGroupName"],
        statusName: json["statusName"],
        isRead: json["isRead"],
        sellerCustomerId: json["sellerCustomerId"],
        sellerVendorId: json["sellerVendorId"],
        sellerVendorName: json["sellerVendorName"],
        mvtItemImage: json["mvtItemImage"],
        createdBy: json["createdBy"],
        isMr: json["isMr"],
        orderGrpNo: json["orderGrpNo"],
        buyerNegoYN: json["buyerNegoYN"],
        sellerNegoYN: json["sellerNegoYN"],
        prchOrdrSplitName: json["prchOrdrSplitName"],
        prchOrdrSplitId: json["prchOrdrSplitId"],
        mrStatusButtonColor: json["mrStatusButtonColor"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrId": prchOrdrId,
        "statusCd": statusCd,
        "projectId": projectId,
        "projectName": projectName,
        "projectAddressId": projectAddressId,
        "projectAddressLine1": projectAddressLine1,
        "projectAddressLine2": projectAddressLine2,
        "projectCity": projectCity,
        "projectCountry": projectCountry,
        "projectState": projectState,
        "projectPincode": projectPincode,
        "projectLatitude": projectLatitude,
        "projectLongitude": projectLongitude,
        "projectCityLan": projectCityLan,
        "projectCityLong": projectCityLong,
        "siteAccess": siteAccess,
        "roadAccess": roadAccess,
        "deliveryDate": deliveryDate?.toIso8601String(),
        "customerId": customerId,
        "deliveryAddressId": deliveryAddressId,
        "deliveryAddressLine1": deliveryAddressLine1,
        "deliveryAddressLine2": deliveryAddressLine2,
        "deliveryCity": deliveryCity,
        "deliveryCountry": deliveryCountry,
        "deliveryState": deliveryState,
        "deliveryPincode": deliveryPincode,
        "deliveryLatitude": deliveryLatitude,
        "deliveryLongitude": deliveryLongitude,
        "deliveryCityLan": deliveryCityLan,
        "deliveryCityLong": deliveryCityLong,
        "mvtItemId": mvtItemId,
        "mvtItemName": mvtItemName,
        "quantity": quantity,
        "price": price,
        "variant1OptionGroupId": variant1OptionGroupId,
        "variant1OptionGroupName": variant1OptionGroupName,
        "variant1OptionIds": variant1OptionIds,
        "variant1Options": variant1Options,
        "variant2OptionGroupId": variant2OptionGroupId,
        "variant2OptionGroupName": variant2OptionGroupName,
        "variant2OptionIds": variant2OptionIds,
        "variant2Options": variant2Options,
        "variant3OptionGroupId": variant3OptionGroupId,
        "variant3OptionGroupName": variant3OptionGroupName,
        "variant3OptionIds": variant3OptionIds,
        "variant3Options": variant3Options,
        "optionGroupId": optionGroupId,
        "optionGroupName": optionGroupName,
        "optionId": optionId,
        "optionName": optionName,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "vendorId": vendorId,
        "orderGroupId": orderGroupId,
        "orderGroupName": orderGroupName,
        "statusName": statusName,
        "isRead": isRead,
        "sellerCustomerId": sellerCustomerId,
        "sellerVendorId": sellerVendorId,
        "sellerVendorName": sellerVendorName,
        "mvtItemImage": mvtItemImage,
        "createdBy": createdBy,
        "isMr": isMr,
        "orderGrpNo": orderGrpNo,
        "buyerNegoYN": buyerNegoYN,
        "sellerNegoYN": sellerNegoYN,
        "prchOrdrSplitName": prchOrdrSplitName,
        "prchOrdrSplitId": prchOrdrSplitId,
        "mrStatusButtonColor": mrStatusButtonColor,
      };
}

class Pageable {
  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? sorted;
  bool? unsorted;
  bool? empty;

  Sort({
    this.sorted,
    this.unsorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        sorted: json["sorted"],
        unsorted: json["unsorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "sorted": sorted,
        "unsorted": unsorted,
        "empty": empty,
      };
}
