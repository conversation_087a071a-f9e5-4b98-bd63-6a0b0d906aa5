import 'dart:convert';

OffersFilterRes offersFilterResFromJson(String str) =>
    OffersFilterRes.fromJson(json.decode(str));

String offersFilterResToJson(OffersFilterRes data) =>
    json.encode(data.toJson());

class OffersFilterRes {
  List<String>? variant1OptionGroupName;
  List<String>? variant1OptionNames;
  List<String>? variant2OptionGroupName;
  List<String>? variant2OptionNames;
  List<String>? variant3OptionGroupName;
  List<String>? variant3OptionNames;

  OffersFilterRes({
    this.variant1OptionGroupName,
    this.variant1OptionNames,
    this.variant2OptionGroupName,
    this.variant2OptionNames,
    this.variant3OptionGroupName,
    this.variant3OptionNames,
  });

  factory OffersFilterRes.fromJson(Map<String, dynamic> json) =>
      OffersFilterRes(
        variant1OptionGroupName: _parseList(json["variant1OptionGroupName"]),
        variant1OptionNames: _parseList(json["variant1OptionNames"]),
        variant2OptionGroupName: _parseList(json["variant2OptionGroupName"]),
        variant2OptionNames: _parseList(json["variant2OptionNames"]),
        variant3OptionGroupName: _parseList(json["variant3OptionGroupName"]),
        variant3OptionNames: _parseList(json["variant3OptionNames"]),
      );

  static List<String> _parseList(dynamic jsonList) {
    if (jsonList == null) return [];
    if (jsonList is List) {
      return jsonList
          .map((x) => x as String? ?? '')
          .toList()
          .where((item) => item.isNotEmpty)
          .toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() => {
        "variant1OptionGroupName": _toJsonList(variant1OptionGroupName),
        "variant1OptionNames": _toJsonList(variant1OptionNames),
        "variant2OptionGroupName": _toJsonList(variant2OptionGroupName),
        "variant2OptionNames": _toJsonList(variant2OptionNames),
        "variant3OptionGroupName": _toJsonList(variant3OptionGroupName),
        "variant3OptionNames": _toJsonList(variant3OptionNames),
      };

  static List<dynamic> _toJsonList(List<String>? list) {
    if (list == null || list.isEmpty) return [];
    return list.map((x) => x.isNotEmpty ? x : null).toList();
  }
}
