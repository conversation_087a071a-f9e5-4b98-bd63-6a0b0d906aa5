import 'dart:convert';

class AddSellerReq {
  String? customerType;
  String? addressLineOne;
  String? addressLineTwo;
  String? email;
  String? gstNumber;
  String? phone;
  String? countryCode;
  String? name;
  String? password;
  String? state;
  String? city;
  bool? termsAndConditions;
  String? vendorName;
  String? zip;
  String? clientId;
  ShippingAddress? shippingAddress;
  String? loginType;
  Docs? docs;

  AddSellerReq({
    this.customerType,
    this.addressLineOne,
    this.addressLineTwo,
    this.email,
    this.gstNumber,
    this.phone,
    this.countryCode,
    this.name,
    this.password,
    this.state,
    this.city,
    this.termsAndConditions,
    this.vendorName,
    this.zip,
    this.clientId,
    this.shippingAddress,
    this.loginType,
    this.docs,
  });

  factory AddSellerReq.fromRawJson(String str) =>
      AddSellerReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddSellerReq.fromJson(Map<String, dynamic> json) => AddSellerReq(
        customerType: json["customer_type"],
        addressLineOne: json["address_line_one"],
        addressLineTwo: json["address_line_two"],
        email: json["email"],
        gstNumber: json["gst_number"],
        phone: json["phone"],
        countryCode: json["country_code"],
        name: json["name"],
        password: json["password"],
        state: json["state"],
        city: json["city"],
        termsAndConditions: json["terms_and_conditions"],
        vendorName: json["vendor_name"],
        zip: json["zip"],
        clientId: json["client_id"],
        shippingAddress: json["shippingAddress"] == null
            ? null
            : ShippingAddress.fromJson(json["shippingAddress"]),
        loginType: json["login_type"],
        docs: json["docs"] == null ? null : Docs.fromJson(json["docs"]),
      );

  Map<String, dynamic> toJson() => {
        "customer_type": customerType,
        "address_line_one": addressLineOne,
        "address_line_two": addressLineTwo,
        "email": email,
        "gst_number": gstNumber,
        "phone": phone,
        "country_code": countryCode,
        "name": name,
        "password": password,
        "state": state,
        "city": city,
        "terms_and_conditions": termsAndConditions,
        "vendor_name": vendorName,
        "zip": zip,
        "client_id": clientId,
        "shippingAddress": shippingAddress?.toJson(),
        "login_type": loginType,
        "docs": docs?.toJson(),
      };
}

class Docs {
  String? docId;
  String? docType;

  Docs({
    this.docId,
    this.docType,
  });

  factory Docs.fromRawJson(String str) => Docs.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Docs.fromJson(Map<String, dynamic> json) => Docs(
        docId: json["doc_id"],
        docType: json["doc_type"],
      );

  Map<String, dynamic> toJson() => {
        "doc_id": docId,
        "doc_type": docType,
      };
}

class ShippingAddress {
  String? addressLine1;
  String? addressLine2;
  String? state;
  String? zip;
  String? city;

  ShippingAddress({
    this.addressLine1,
    this.addressLine2,
    this.state,
    this.zip,
    this.city,
  });

  factory ShippingAddress.fromRawJson(String str) =>
      ShippingAddress.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ShippingAddress.fromJson(Map<String, dynamic> json) =>
      ShippingAddress(
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        state: json["state"],
        zip: json["zip"],
        city: json["city"],
      );

  Map<String, dynamic> toJson() => {
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "state": state,
        "zip": zip,
        "city": city,
      };
}
