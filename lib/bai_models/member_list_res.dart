class MemberListItem {
  final String phone;
  final String name;
  final String id;
  final String designation;
  final DateTime dateOfRegistration;
  final String email;

  MemberListItem({
    required this.phone,
    required this.name,
    required this.id,
    required this.designation,
    required this.dateOfRegistration,
    required this.email,
  });

  factory MemberListItem.fromJson(Map<String, dynamic> json) {
    return MemberListItem(
      phone: json['phone'] as String,
      name: json['name'] as String,
      id: json['id'] as String,
      designation: json['designation'] as String,
      dateOfRegistration:
          DateTime.parse(json['date_of_registration'] as String),
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'name': name,
      'id': id,
      'designation': designation,
      'date_of_registration': dateOfRegistration.toIso8601String(),
      'email': email,
    };
  }
}

List<MemberListItem> memberListFromJson(List<dynamic> jsonList) {
  return jsonList.map((json) => MemberListItem.fromJson(json)).toList();
}
