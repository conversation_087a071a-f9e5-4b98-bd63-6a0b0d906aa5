import 'dart:convert';

class AllowEditRes {
  Data? data;
  int? status;
  String? statusDescription;

  AllowEditRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory AllowEditRes.fromRawJson(String str) =>
      AllowEditRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AllowEditRes.fromJson(Map<String, dynamic> json) => AllowEditRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  bool? isDeliveryDateDifferent;

  Data({
    this.isDeliveryDateDifferent,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        isDeliveryDateDifferent: json["is_delivery_date_different"],
      );

  Map<String, dynamic> toJson() => {
        "is_delivery_date_different": isDeliveryDateDifferent,
      };
}
