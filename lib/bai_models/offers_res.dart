// class BuyerOffersRes {
//   List<Offer>? offers;

//   BuyerOffersRes({this.offers});

//   factory BuyerOffersRes.fromJson(List<dynamic> json) {
//     return BuyerOffersRes(
//       offers: json.map((offer) => Offer.fromJson(offer)).toList(),
//     );
//   }

//   List<dynamic> toJson() {
//     return offers?.map((offer) => offer.toJson()).toList() ?? [];
//   }
// }

class BuyerOffersRes {
  List<Offer>? offers;
  String? mrStatus;

  BuyerOffersRes({
    this.offers,
    this.mrStatus,
  });

  factory BuyerOffersRes.fromJson(Map<String, dynamic> json) => BuyerOffersRes(
        offers: json["prchOrdrOffersDto"] == null
            ? []
            : List<Offer>.from(
                json["prchOrdrOffersDto"]!.map((x) => Offer.fromJson(x))),
        mrStatus: json["mrStatus"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrOffersDto": offers == null
            ? []
            : List<dynamic>.from(offers!.map((x) => x.toJson())),
        "mrStatus": mrStatus,
      };
}

class Offer {
  num? id;
  num? mvtItemId;
  String? mvtItemName;
  num? optionGroupId;
  String? optionGroupName;
  String? optionName;
  num? optionId;
  num? offerPrice;
  num? negotiatedPrice;
  String? negotiationStatus;
  String? negotiationStatusName;
  num? customerId;
  num? vendorId;
  num? prchOrdrId;
  String? statusCd;
  String? statusName;
  String? vendorName;
  String? vendorCity;
  num? variant1OptionId;
  String? variant1OptionName;
  num? variant2OptionId;
  String? variant2OptionName;
  num? variant3OptionId;
  String? variant3OptionName;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  String? vendorPhone;
  String? customerName;
  String? customerPhone;
  String? remarks;
  num? quantity;
  DateTime? createdAt;
  bool? baiMember;
  String? typeOfGstFiling;
  num? rating;
  List<Media>? medias;
  List<NegotiationHistory>? negotiationHistory;
  bool? quantityEdit;
  num? gst;

  Offer({
    this.id,
    this.mvtItemId,
    this.mvtItemName,
    this.optionGroupId,
    this.optionGroupName,
    this.optionName,
    this.optionId,
    this.offerPrice,
    this.customerId,
    this.vendorId,
    this.prchOrdrId,
    this.statusCd,
    this.statusName,
    this.vendorName,
    this.vendorCity,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.vendorPhone,
    this.customerName,
    this.customerPhone,
    this.remarks,
    this.quantity,
    this.createdAt,
    this.baiMember,
    this.typeOfGstFiling,
    this.rating,
    this.medias,
    this.negotiationHistory,
    this.negotiatedPrice,
    this.negotiationStatus,
    this.negotiationStatusName,
    this.quantityEdit,
    this.gst,
  });

  factory Offer.fromJson(Map<String, dynamic> json) {
    return Offer(
      id: json['id'],
      mvtItemId: json['mvt_item_id'],
      mvtItemName: json['mvt_item_name'],
      optionGroupId: json['option_group_id'],
      optionGroupName: json['option_group_name'],
      optionName: json['option_name'],
      optionId: json['option_id'],
      offerPrice: json['offer_price'],
      customerId: json['customer_id'],
      vendorId: json['vendor_id'],
      prchOrdrId: json['prch_ordr_id'],
      statusCd: json['status_cd'],
      statusName: json['status_name'],
      vendorName: json['vendor_name'],
      vendorCity: json['vendor_city'],
      variant1OptionId: json['variant_1_option_id'],
      variant1OptionName: json['variant_1_option_name'],
      variant2OptionId: json['variant_2_option_id'],
      variant2OptionName: json['variant_2_option_name'],
      variant3OptionId: json['variant_3_option_id'],
      variant3OptionName: json['variant_3_option_name'],
      variant1OptionGroupId: json['variant_1_option_group_id'],
      variant1OptionGroupName: json['variant_1_option_group_name'],
      variant2OptionGroupId: json['variant_2_option_group_id'],
      variant2OptionGroupName: json['variant_2_option_group_name'],
      variant3OptionGroupId: json['variant_3_option_group_id'],
      variant3OptionGroupName: json['variant_3_option_group_name'],
      vendorPhone: json['vendor_phone'],
      customerName: json['customer_name'],
      customerPhone: json['customer_phone'],
      remarks: json['remarks'],
      quantity: json['quantity'],
      createdAt: DateTime.parse(json['created_at']),
      baiMember: json['bai_member'],
      typeOfGstFiling: json['type_of_gst_filing'],
      rating: json['rating'],
      medias: json["medias"] == null
          ? []
          : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
      negotiationHistory: json["negotiation_history"] == null
          ? []
          : List<NegotiationHistory>.from(json["negotiation_history"]!
              .map((x) => NegotiationHistory.fromJson(x))),
      negotiatedPrice: json['negotiated_price'],
      negotiationStatus: json['negotiation_status'],
      negotiationStatusName: json['negotiation_status_name'],
      quantityEdit: json['quantity_edit'],
      gst: json['gst'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mvt_item_id': mvtItemId,
      'mvt_item_name': mvtItemName,
      'option_group_id': optionGroupId,
      'option_group_name': optionGroupName,
      'option_name': optionName,
      'option_id': optionId,
      'offer_price': offerPrice,
      'customer_id': customerId,
      'vendor_id': vendorId,
      'prch_ordr_id': prchOrdrId,
      'status_cd': statusCd,
      'status_name': statusName,
      'vendor_name': vendorName,
      'vendor_city': vendorCity,
      'variant_1_option_id': variant1OptionId,
      'variant_1_option_name': variant1OptionName,
      'variant_2_option_id': variant2OptionId,
      'variant_2_option_name': variant2OptionName,
      'variant_3_option_id': variant3OptionId,
      'variant_3_option_name': variant3OptionName,
      'variant_1_option_group_id': variant1OptionGroupId,
      'variant_1_option_group_name': variant1OptionGroupName,
      'variant_2_option_group_id': variant2OptionGroupId,
      'variant_2_option_group_name': variant2OptionGroupName,
      'variant_3_option_group_id': variant3OptionGroupId,
      'variant_3_option_group_name': variant3OptionGroupName,
      'vendor_phone': vendorPhone,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'remarks': remarks,
      'negotiation_status': negotiationStatus,
      'negotiation_status_name': negotiationStatusName,
      "medias": medias == null
          ? []
          : List<dynamic>.from(medias!.map((x) => x.toJson())),
      "negotiation_history": negotiationHistory == null
          ? []
          : List<dynamic>.from(negotiationHistory!.map((x) => x.toJson())),
      "quantity_edit": quantityEdit,
      "gst": gst,
    };
  }
}

class Media {
  String? title;
  String? url;
  String? previewUrl;

  Media({
    this.title,
    this.url,
    this.previewUrl,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        title: json["title"],
        url: json["url"],
        previewUrl: json["previewUrl"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "url": url,
        "previewUrl": previewUrl,
      };
}

class NegotiationHistory {
  int? id;
  int? purchaseOrderOffersId;
  int? purchaseOrderId;
  num? offerPrice;
  int? customerId;
  int? vendorId;
  String? customerType;
  String? statusName;
  DateTime? createdAt;
  num? negotiatedPrice;
  String? statusCode;
  String? remarks;
  List<Media>? medias;
  String? companyName;
  num? quantity;

  NegotiationHistory({
    this.id,
    this.purchaseOrderOffersId,
    this.purchaseOrderId,
    this.offerPrice,
    this.customerId,
    this.vendorId,
    this.customerType,
    this.statusName,
    this.createdAt,
    this.negotiatedPrice,
    this.statusCode,
    this.remarks,
    this.medias,
    this.companyName,
    this.quantity,
  });

  factory NegotiationHistory.fromJson(Map<String, dynamic> json) =>
      NegotiationHistory(
        id: json["id"],
        purchaseOrderOffersId: json["purchase_order_offers_id"],
        purchaseOrderId: json["purchase_order_id"],
        offerPrice: json["offer_price"],
        customerId: json["customer_id"],
        vendorId: json["vendor_id"],
        customerType: json["customer_type"],
        statusName: json["status_name"],
        createdAt: json["created_at"],
        negotiatedPrice: json["negotiated_price"],
        statusCode: json["status_code"],
        remarks: json["remarks"],
        companyName: json["company_name"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "purchase_order_offers_id": purchaseOrderOffersId,
        "purchase_order_id": purchaseOrderId,
        "offer_price": offerPrice,
        "customer_id": customerId,
        "vendor_id": vendorId,
        "customer_type": customerType,
        "status_name": statusName,
        "created_at": createdAt,
        "negotiated_price": negotiatedPrice,
        "status_code": statusCode,
        "remarks": remarks,
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "quantity": quantity,
      };
}
