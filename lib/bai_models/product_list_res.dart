import 'dart:convert';

ProductListRes productListResFromJson(String str) =>
    ProductListRes.fromJson(json.decode(str));

String productListResToJson(ProductListRes data) => json.encode(data.toJson());

class ProductListRes {
  List<Content>? content;
  Pageable? pageable;
  bool? last;
  num? totalElements;
  num? totalPages;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  ProductListRes({
    this.content,
    this.pageable,
    this.last,
    this.totalElements,
    this.totalPages,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  factory ProductListRes.fromJson(Map<String, dynamic> json) => ProductListRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "last": last,
        "totalElements": totalElements,
        "totalPages": totalPages,
        "first": first,
        "numberOfElements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  num? id;
  String? eta;
  String? image;
  String? name;
  num? price;
  num? tax;
  num? discount;
  num? serviceItemFlag;
  num? rewardPoints;
  String? auctionTypeCd;
  String? pdctCd;
  String? prmySellCd;
  String? auctYn;
  Service? service;

  Content({
    this.id,
    this.eta,
    this.image,
    this.name,
    this.price,
    this.tax,
    this.discount,
    this.serviceItemFlag,
    this.rewardPoints,
    this.auctionTypeCd,
    this.pdctCd,
    this.prmySellCd,
    this.auctYn,
    this.service,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        eta: json["eta"],
        image: json["image"],
        name: json["name"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        serviceItemFlag: json["serviceItemFlag"],
        rewardPoints: json["rewardPoints"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        prmySellCd: json["prmySellCd"],
        auctYn: json["auctYn"],
        service:
            json["service"] == null ? null : Service.fromJson(json["service"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "eta": eta,
        "image": image,
        "name": name,
        "price": price,
        "tax": tax,
        "discount": discount,
        "serviceItemFlag": serviceItemFlag,
        "rewardPoints": rewardPoints,
        "auctionTypeCd": auctionTypeCd,
        "pdctCd": pdctCd,
        "prmySellCd": prmySellCd,
        "auctYn": auctYn,
        "service": service?.toJson(),
      };
}

class Service {
  num? id;
  String? name;

  Service({
    this.id,
    this.name,
  });

  factory Service.fromJson(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Pageable {
  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? unsorted;
  bool? sorted;
  bool? empty;

  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
