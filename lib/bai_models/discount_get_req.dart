import 'dart:convert';

class DiscountGetReq {
  int? ordrGrpId;
  int? sellerId;
  int? categoryId;
  List<int>? offerIds;

  DiscountGetReq({
    this.ordrGrpId,
    this.sellerId,
    this.categoryId,
    this.offerIds,
  });

  factory DiscountGetReq.fromRawJson(String str) =>
      DiscountGetReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DiscountGetReq.fromJson(Map<String, dynamic> json) => DiscountGetReq(
        ordrGrpId: json["ordrGrpId"],
        sellerId: json["sellerId"],
        categoryId: json["categoryId"],
        offerIds: json["offerIds"] == null
            ? []
            : List<int>.from(json["offerIds"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "ordrGrpId": ordrGrpId,
        "sellerId": sellerId,
        "categoryId": categoryId,
        "offerIds":
            offerIds == null ? [] : List<dynamic>.from(offerIds!.map((x) => x)),
      };
}
