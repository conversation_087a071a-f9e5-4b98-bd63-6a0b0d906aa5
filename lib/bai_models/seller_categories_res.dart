class SellerCategoresRes {
  List<Content>? content;

  SellerCategoresRes({this.content});

  SellerCategoresRes.fromJson(Map<String, dynamic> json) {
    if (json['content'] != null) {
      content = <Content>[];
      json['content'].forEach((v) {
        content!.add(Content.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (content != null) {
      data['content'] = content!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Content {
  String? id;
  dynamic parentId;
  String? name;
  dynamic link;
  dynamic shortServiceName;
  dynamic code;
  String? hasChild;
  String? image;
  dynamic isAvailable;
  int? sequence;
  dynamic categoryType;
  dynamic shortDescription;
  dynamic longDescription;
  String? primaryPhone;
  dynamic icon;
  dynamic timeText;
  String? avgRating;
  String? totalRating;
  int? reservationTypeId;
  dynamic hcType;
  dynamic categoryData;
  dynamic categoryFilter;
  dynamic children;
  dynamic publicInsertPageLink;
  dynamic categoryId;
  String? group;
  int? dealerRate1;
  int? manufacturerRate1;
  int? memberDealerRate1;
  int? memberManufacturerRate1;
  int? dealerRate2;
  int? manufacturerRate2;
  int? memberDealerRate2;
  int? memberManufacturerRate2;
  bool? allowInRegistration;
  bool? removeInCategories;

  Content(
      {this.id,
      this.parentId,
      this.name,
      this.link,
      this.shortServiceName,
      this.code,
      this.hasChild,
      this.image,
      this.isAvailable,
      this.sequence,
      this.categoryType,
      this.shortDescription,
      this.longDescription,
      this.primaryPhone,
      this.icon,
      this.timeText,
      this.avgRating,
      this.totalRating,
      this.reservationTypeId,
      this.hcType,
      this.categoryData,
      this.categoryFilter,
      this.children,
      this.publicInsertPageLink,
      this.categoryId,
      this.group,
      this.dealerRate1,
      this.manufacturerRate1,
      this.memberDealerRate1,
      this.memberManufacturerRate1,
      this.dealerRate2,
      this.manufacturerRate2,
      this.memberDealerRate2,
      this.memberManufacturerRate2,
      this.allowInRegistration,
      this.removeInCategories});

  Content.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parent_id'];
    name = json['name'];
    link = json['link'];
    shortServiceName = json['short_service_name'];
    code = json['code'];
    hasChild = json['has_child'];
    image = json['image'];
    isAvailable = json['is_available'];
    sequence = json['sequence'];
    categoryType = json['category_type'];
    shortDescription = json['short_description'];
    longDescription = json['long_description'];
    primaryPhone = json['primary_phone'];
    icon = json['icon'];
    timeText = json['time_text'];
    avgRating = json['avg_rating'];
    totalRating = json['total_rating'];
    reservationTypeId = json['reservation_type_id'];
    hcType = json['hc_type'];
    categoryData = json['category_data'];
    categoryFilter = json['category_filter'];
    children = json['children'];
    publicInsertPageLink = json['public_insert_page_link'];
    categoryId = json['category_id'];
    group = json['group'];
    dealerRate1 = json['dealer_rate1'];
    manufacturerRate1 = json['manufacturer_rate1'];
    memberDealerRate1 = json['member_dealer_rate1'];
    memberManufacturerRate1 = json['member_manufacturer_rate1'];
    dealerRate2 = json['dealer_rate2'];
    manufacturerRate2 = json['manufacturer_rate2'];
    memberDealerRate2 = json['member_dealer_rate2'];
    memberManufacturerRate2 = json['member_manufacturer_rate2'];
    allowInRegistration = json['allow_in_registration'];
    removeInCategories = json['remove_in_categories'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['parent_id'] = parentId;
    data['name'] = name;
    data['link'] = link;
    data['short_service_name'] = shortServiceName;
    data['code'] = code;
    data['has_child'] = hasChild;
    data['image'] = image;
    data['is_available'] = isAvailable;
    data['sequence'] = sequence;
    data['category_type'] = categoryType;
    data['short_description'] = shortDescription;
    data['long_description'] = longDescription;
    data['primary_phone'] = primaryPhone;
    data['icon'] = icon;
    data['time_text'] = timeText;
    data['avg_rating'] = avgRating;
    data['total_rating'] = totalRating;
    data['reservation_type_id'] = reservationTypeId;
    data['hc_type'] = hcType;
    data['category_data'] = categoryData;
    data['category_filter'] = categoryFilter;
    data['children'] = children;
    data['public_insert_page_link'] = publicInsertPageLink;
    data['category_id'] = categoryId;
    data['group'] = group;
    data['dealer_rate1'] = dealerRate1;
    data['manufacturer_rate1'] = manufacturerRate1;
    data['member_dealer_rate1'] = memberDealerRate1;
    data['member_manufacturer_rate1'] = memberManufacturerRate1;
    data['dealer_rate2'] = dealerRate2;
    data['manufacturer_rate2'] = manufacturerRate2;
    data['member_dealer_rate2'] = memberDealerRate2;
    data['member_manufacturer_rate2'] = memberManufacturerRate2;
    data['allow_in_registration'] = allowInRegistration;
    data['remove_in_categories'] = removeInCategories;
    return data;
  }
}
