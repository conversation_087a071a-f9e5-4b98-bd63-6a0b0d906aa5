// To parse this JSON data, do
//
//     final changeStatusReq = changeStatusReqFromJson(jsonString);

import 'dart:convert';

ChangeStatusReq changeStatusReqFromJson(String str) =>
    ChangeStatusReq.fromJson(json.decode(str));

String changeStatusReqToJson(ChangeStatusReq data) =>
    json.encode(data.toJson());

class ChangeStatusReq {
  String? statusCd;
  String? locationLat;
  String? locationLong;
  String? eventName;
  int? assignedVendorsId;
  List<Media>? medias;
  String? comments;
  int? orderGrpId;
  List<int>? prchOrdrId;

  ChangeStatusReq({
    this.prchOrdrId,
    this.statusCd,
    this.locationLat,
    this.locationLong,
    this.eventName,
    this.assignedVendorsId,
    this.medias,
    this.comments,
    this.orderGrpId,
  });

  factory ChangeStatusReq.fromJson(Map<String, dynamic> json) =>
      ChangeStatusReq(
        prchOrdrId: json["prchOrdrId"] == null
            ? []
            : List<int>.from(json["prchOrdrId"]!.map((x) => x)),
        statusCd: json["statusCd"],
        locationLat: json["locationLat"],
        locationLong: json["locationLong"],
        eventName: json["eventName"],
        assignedVendorsId: json["assignedVendorsId"],
        comments: json["comments"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        orderGrpId: json["orderGrpId"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrId": prchOrdrId == null
            ? []
            : List<dynamic>.from(prchOrdrId!.map((x) => x)),
        "statusCd": statusCd,
        "locationLat": locationLat,
        "locationLong": locationLong,
        "eventName": eventName,
        "assignedVendorsId": assignedVendorsId,
        "comments": comments,
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "orderGrpId": orderGrpId,
      };
}

class Media {
  String? mediaTitle;
  String? previewUrl;
  String? url;

  Media({
    this.mediaTitle,
    this.previewUrl,
    this.url,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        mediaTitle: json["mediaTitle"],
        previewUrl: json["previewUrl"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "mediaTitle": mediaTitle,
        "previewUrl": previewUrl,
        "url": url,
      };
}
