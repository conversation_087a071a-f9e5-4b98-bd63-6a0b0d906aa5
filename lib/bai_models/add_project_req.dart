// To parse this JSON data, do
//
//     final addProjectReq = addProjectReqFromJson(jsonString);

import 'dart:convert';

AddProjectReq addProjectReqFromJson(String str) =>
    AddProjectReq.fromJson(json.decode(str));

String addProjectReqToJson(AddProjectReq data) => json.encode(data.toJson());

class AddProjectReq {
  String? projectName;
  String? siteAccess;
  int? customerOrgsId;
  String? createdBy;
  String? customerName;
  Address? address;
  int? areaOfBusinessId;

  AddProjectReq({
    this.projectName,
    this.siteAccess,
    this.customerOrgsId,
    this.createdBy,
    this.customerName,
    this.address,
    this.areaOfBusinessId,
  });

  factory AddProjectReq.fromJson(Map<String, dynamic> json) => AddProjectReq(
        projectName: json["project_name"],
        siteAccess: json["site_access"],
        customerOrgsId: json["customer_orgs_id"],
        createdBy: json["created_by"],
        customerName: json["customer_name"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        areaOfBusinessId: json["area_of_business_id"],
      );

  Map<String, dynamic> toJson() => {
        "project_name": projectName,
        "site_access": siteAccess,
        "customer_orgs_id": customerOrgsId,
        "created_by": createdBy,
        "customer_name": customerName,
        "address": address?.toJson(),
        "area_of_business_id": areaOfBusinessId,
      };
}

class Address {
  String? addressLine1;
  dynamic addressLine2;
  String? city;
  String? state;
  String? country;
  dynamic pincode;
  double? latitude;
  double? longitude;

  Address({
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.country,
    this.pincode,
    this.latitude,
    this.longitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        addressLine1: json["selling_address_line_1"],
        addressLine2: json["selling_address_line_2"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "selling_address_line1": addressLine1,
        "selling_address_line2": addressLine2,
        "city": city,
        "state": state,
        "country": country,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
      };
}
