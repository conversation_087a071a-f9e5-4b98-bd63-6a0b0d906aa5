class NatureOfBusinessRes {
  List<NatureOfBusiness>? natureOfBusiness;

  NatureOfBusinessRes({this.natureOfBusiness});

  factory NatureOfBusinessRes.fromJson(List<dynamic> json) {
    return NatureOfBusinessRes(
      natureOfBusiness: json.map((e) => NatureOfBusiness.fromJson(e)).toList(),
    );
  }

  List<dynamic> toJson() {
    return natureOfBusiness!.map((e) => e.toJson()).toList();
  }
}

class NatureOfBusiness {
  String? name;
  String? id;

  NatureOfBusiness({this.name, this.id});

  factory NatureOfBusiness.fromJson(Map<String, dynamic> json) {
    return NatureOfBusiness(
      name: json['name'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'id': id,
    };
  }
}
