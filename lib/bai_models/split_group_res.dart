// Model class for the split group response
class SplitGroupRes {
  final Data data;
  final int status;
  final String statusDescription;

  SplitGroupRes({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  factory SplitGroupRes.fromJson(Map<String, dynamic> json) {
    return SplitGroupRes(
      data: Data.fromJson(json['data']),
      status: json['status'],
      statusDescription: json['status_description'],
    );
  }
}

class Data {
  final int id;
  final String name;
  final int prchOrdrId;
  final String createdAt;
  final String updatedAt;
  final int createdCustomerId;
  final dynamic updatedCustomerId;

  Data({
    required this.id,
    required this.name,
    required this.prchOrdrId,
    required this.createdAt,
    required this.updatedAt,
    required this.createdCustomerId,
    this.updatedCustomerId,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      id: json['id'],
      name: json['name'],
      prchOrdrId: json['prchOrdrId'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
      createdCustomerId: json['createdCustomerId'],
      updatedCustomerId: json['updatedCustomerId'],
    );
  }
}
