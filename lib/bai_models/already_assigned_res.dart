import 'dart:convert';

List<AlreadyAssignedRes> alreadyAssignedResFromJson(String str) =>
    List<AlreadyAssignedRes>.from(
        json.decode(str).map((x) => AlreadyAssignedRes.fromJson(x)));

String alreadyAssignedResToJson(List<AlreadyAssignedRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AlreadyAssignedRes {
  num? id;
  num? customerId;
  num? purchaseOrderId;
  List<Vendor>? vendors;
  num? insertedCustomerId;
  num? insertedVendorId;

  AlreadyAssignedRes({
    this.id,
    this.customerId,
    this.purchaseOrderId,
    this.vendors,
    this.insertedCustomerId,
    this.insertedVendorId,
  });

  factory AlreadyAssignedRes.fromJson(Map<String, dynamic> json) =>
      AlreadyAssignedRes(
        id: json["id"],
        customerId: json["customerId"],
        purchaseOrderId: json["purchaseOrderId"],
        vendors: json["vendors"] == null
            ? []
            : List<Vendor>.from(
                json["vendors"]!.map((x) => Vendor.fromJson(x))),
        insertedCustomerId: json["insertedCustomerId"],
        insertedVendorId: json["insertedVendorId"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerId": customerId,
        "purchaseOrderId": purchaseOrderId,
        "vendors": vendors == null
            ? []
            : List<dynamic>.from(vendors!.map((x) => x.toJson())),
        "insertedCustomerId": insertedCustomerId,
        "insertedVendorId": insertedVendorId,
      };
}

class Vendor {
  num? id;
  String? name;
  bool? subscriptionRequired;
  dynamic subscriptionStartedAt;
  dynamic subscriptionEndsOn;
  num? subscriptionAmount;
  num? subscriptionAmountPending;
  num? subscriptionAmountPaid;
  dynamic subscriptionAmountPaidOn;
  dynamic website;
  String? email;
  dynamic rating;
  dynamic address;
  dynamic addressLine1;
  dynamic addressLine2;
  dynamic primaryPhone;
  dynamic city;
  String? state;
  dynamic country;
  dynamic addressLat;
  dynamic addressLong;
  dynamic secondaryPhone;
  dynamic primaryPhoneCountryCode;
  dynamic secondaryPhoneCountryCode;
  dynamic officeNumber;
  dynamic officeNumberCc;
  dynamic homeNumber;
  dynamic homeNumberCc;
  dynamic landlineNumber;
  dynamic landlineNumberCc;
  dynamic whatsappNumber;
  dynamic whatsappNumberCc;
  dynamic fullAddressFromGmaps;
  dynamic shortDescription;
  dynamic longDescription;
  dynamic caption;
  dynamic sequence;
  dynamic totalRating;
  String? mondayFromTime;
  String? mondayToTime;
  String? mondayHoliday;
  String? tuesdayFromTime;
  String? tuesdayToTime;
  String? tuesdayHoliday;
  String? wednesdayFromTime;
  String? wednesdayToTime;
  String? wednesdayHoliday;
  String? thursdayFromTime;
  String? thursdayToTime;
  String? thursdayHoliday;
  String? fridayFromTime;
  String? fridayToTime;
  String? fridayHoliday;
  String? saturdayFromTime;
  String? saturdayToTime;
  String? saturdayHoliday;
  dynamic sundayFromTime;
  dynamic sundayToTime;
  String? sundayHoliday;
  dynamic defaultRouteId;
  dynamic defaultProductOfferingId;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic subdomain;
  dynamic adminPortalUrl;
  num? approvedBy;
  dynamic approvedAt;
  bool? active;
  dynamic vendorCategoryId;
  dynamic abn;
  String? approvedYn;
  DateTime? acceptedTermsAndConditionsAt;
  dynamic profileComplete;
  dynamic referredBy;
  dynamic image;
  dynamic imageTitle;
  dynamic encryptIv;
  num? isAvailable;
  dynamic defaultRoomId;
  dynamic productListBanner;
  String? gstNumber;
  dynamic shippingAddress;

  Vendor({
    this.id,
    this.name,
    this.subscriptionRequired,
    this.subscriptionStartedAt,
    this.subscriptionEndsOn,
    this.subscriptionAmount,
    this.subscriptionAmountPending,
    this.subscriptionAmountPaid,
    this.subscriptionAmountPaidOn,
    this.website,
    this.email,
    this.rating,
    this.address,
    this.addressLine1,
    this.addressLine2,
    this.primaryPhone,
    this.city,
    this.state,
    this.country,
    this.addressLat,
    this.addressLong,
    this.secondaryPhone,
    this.primaryPhoneCountryCode,
    this.secondaryPhoneCountryCode,
    this.officeNumber,
    this.officeNumberCc,
    this.homeNumber,
    this.homeNumberCc,
    this.landlineNumber,
    this.landlineNumberCc,
    this.whatsappNumber,
    this.whatsappNumberCc,
    this.fullAddressFromGmaps,
    this.shortDescription,
    this.longDescription,
    this.caption,
    this.sequence,
    this.totalRating,
    this.mondayFromTime,
    this.mondayToTime,
    this.mondayHoliday,
    this.tuesdayFromTime,
    this.tuesdayToTime,
    this.tuesdayHoliday,
    this.wednesdayFromTime,
    this.wednesdayToTime,
    this.wednesdayHoliday,
    this.thursdayFromTime,
    this.thursdayToTime,
    this.thursdayHoliday,
    this.fridayFromTime,
    this.fridayToTime,
    this.fridayHoliday,
    this.saturdayFromTime,
    this.saturdayToTime,
    this.saturdayHoliday,
    this.sundayFromTime,
    this.sundayToTime,
    this.sundayHoliday,
    this.defaultRouteId,
    this.defaultProductOfferingId,
    this.createdAt,
    this.modifiedAt,
    this.subdomain,
    this.adminPortalUrl,
    this.approvedBy,
    this.approvedAt,
    this.active,
    this.vendorCategoryId,
    this.abn,
    this.approvedYn,
    this.acceptedTermsAndConditionsAt,
    this.profileComplete,
    this.referredBy,
    this.image,
    this.imageTitle,
    this.encryptIv,
    this.isAvailable,
    this.defaultRoomId,
    this.productListBanner,
    this.gstNumber,
    this.shippingAddress,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        id: json["id"],
        name: json["name"],
        subscriptionRequired: json["subscriptionRequired"],
        subscriptionStartedAt: json["subscriptionStartedAt"],
        subscriptionEndsOn: json["subscriptionEndsOn"],
        subscriptionAmount: json["subscriptionAmount"],
        subscriptionAmountPending: json["subscriptionAmountPending"],
        subscriptionAmountPaid: json["subscriptionAmountPaid"],
        subscriptionAmountPaidOn: json["subscriptionAmountPaidOn"],
        website: json["website"],
        email: json["email"],
        rating: json["rating"],
        address: json["address"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        primaryPhone: json["primary_phone"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        addressLat: json["address_lat"],
        addressLong: json["address_long"],
        secondaryPhone: json["secondary_phone"],
        primaryPhoneCountryCode: json["primary_phone_country_code"],
        secondaryPhoneCountryCode: json["secondary_phone_country_code"],
        officeNumber: json["office_number"],
        officeNumberCc: json["office_number_cc"],
        homeNumber: json["home_number"],
        homeNumberCc: json["home_number_cc"],
        landlineNumber: json["landline_number"],
        landlineNumberCc: json["landline_number_cc"],
        whatsappNumber: json["whatsapp_number"],
        whatsappNumberCc: json["whatsapp_number_cc"],
        fullAddressFromGmaps: json["full_address_from_gmaps"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        caption: json["caption"],
        sequence: json["sequence"],
        totalRating: json["total_rating"],
        mondayFromTime: json["monday_from_time"],
        mondayToTime: json["monday_to_time"],
        mondayHoliday: json["monday_holiday"],
        tuesdayFromTime: json["tuesday_from_time"],
        tuesdayToTime: json["tuesday_to_time"],
        tuesdayHoliday: json["tuesday_holiday"],
        wednesdayFromTime: json["wednesday_from_time"],
        wednesdayToTime: json["wednesday_to_time"],
        wednesdayHoliday: json["wednesday_holiday"],
        thursdayFromTime: json["thursday_from_time"],
        thursdayToTime: json["thursday_to_time"],
        thursdayHoliday: json["thursday_holiday"],
        fridayFromTime: json["friday_from_time"],
        fridayToTime: json["friday_to_time"],
        fridayHoliday: json["friday_holiday"],
        saturdayFromTime: json["saturday_from_time"],
        saturdayToTime: json["saturday_to_time"],
        saturdayHoliday: json["saturday_holiday"],
        sundayFromTime: json["sunday_from_time"],
        sundayToTime: json["sunday_to_time"],
        sundayHoliday: json["sunday_holiday"],
        defaultRouteId: json["default_route_id"],
        defaultProductOfferingId: json["default_product_offering_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        subdomain: json["subdomain"],
        adminPortalUrl: json["admin_portal_url"],
        approvedBy: json["approved_by"],
        approvedAt: json["approved_at"],
        active: json["active"],
        vendorCategoryId: json["vendor_category_id"],
        abn: json["abn"],
        approvedYn: json["approved_YN"],
        acceptedTermsAndConditionsAt:
            json["accepted_terms_and_conditions_at"] == null
                ? null
                : DateTime.parse(json["accepted_terms_and_conditions_at"]),
        profileComplete: json["profile_complete"],
        referredBy: json["referred_by"],
        image: json["image"],
        imageTitle: json["image_title"],
        encryptIv: json["encrypt_iv"],
        isAvailable: json["is_available"],
        defaultRoomId: json["default_room_id"],
        productListBanner: json["product_list_banner"],
        gstNumber: json["gst_number"],
        shippingAddress: json["shippingAddress"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "subscriptionRequired": subscriptionRequired,
        "subscriptionStartedAt": subscriptionStartedAt,
        "subscriptionEndsOn": subscriptionEndsOn,
        "subscriptionAmount": subscriptionAmount,
        "subscriptionAmountPending": subscriptionAmountPending,
        "subscriptionAmountPaid": subscriptionAmountPaid,
        "subscriptionAmountPaidOn": subscriptionAmountPaidOn,
        "website": website,
        "email": email,
        "rating": rating,
        "address": address,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "primary_phone": primaryPhone,
        "city": city,
        "state": state,
        "country": country,
        "address_lat": addressLat,
        "address_long": addressLong,
        "secondary_phone": secondaryPhone,
        "primary_phone_country_code": primaryPhoneCountryCode,
        "secondary_phone_country_code": secondaryPhoneCountryCode,
        "office_number": officeNumber,
        "office_number_cc": officeNumberCc,
        "home_number": homeNumber,
        "home_number_cc": homeNumberCc,
        "landline_number": landlineNumber,
        "landline_number_cc": landlineNumberCc,
        "whatsapp_number": whatsappNumber,
        "whatsapp_number_cc": whatsappNumberCc,
        "full_address_from_gmaps": fullAddressFromGmaps,
        "short_description": shortDescription,
        "long_description": longDescription,
        "caption": caption,
        "sequence": sequence,
        "total_rating": totalRating,
        "monday_from_time": mondayFromTime,
        "monday_to_time": mondayToTime,
        "monday_holiday": mondayHoliday,
        "tuesday_from_time": tuesdayFromTime,
        "tuesday_to_time": tuesdayToTime,
        "tuesday_holiday": tuesdayHoliday,
        "wednesday_from_time": wednesdayFromTime,
        "wednesday_to_time": wednesdayToTime,
        "wednesday_holiday": wednesdayHoliday,
        "thursday_from_time": thursdayFromTime,
        "thursday_to_time": thursdayToTime,
        "thursday_holiday": thursdayHoliday,
        "friday_from_time": fridayFromTime,
        "friday_to_time": fridayToTime,
        "friday_holiday": fridayHoliday,
        "saturday_from_time": saturdayFromTime,
        "saturday_to_time": saturdayToTime,
        "saturday_holiday": saturdayHoliday,
        "sunday_from_time": sundayFromTime,
        "sunday_to_time": sundayToTime,
        "sunday_holiday": sundayHoliday,
        "default_route_id": defaultRouteId,
        "default_product_offering_id": defaultProductOfferingId,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "subdomain": subdomain,
        "admin_portal_url": adminPortalUrl,
        "approved_by": approvedBy,
        "approved_at": approvedAt,
        "active": active,
        "vendor_category_id": vendorCategoryId,
        "abn": abn,
        "approved_YN": approvedYn,
        "accepted_terms_and_conditions_at":
            acceptedTermsAndConditionsAt?.toIso8601String(),
        "profile_complete": profileComplete,
        "referred_by": referredBy,
        "image": image,
        "image_title": imageTitle,
        "encrypt_iv": encryptIv,
        "is_available": isAvailable,
        "default_room_id": defaultRoomId,
        "product_list_banner": productListBanner,
        "gst_number": gstNumber,
        "shippingAddress": shippingAddress,
      };
}
