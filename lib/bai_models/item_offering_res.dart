import 'dart:convert';

ItemOfferingRes itemOfferingResFromJson(String str) =>
    ItemOfferingRes.fromJson(json.decode(str));

String itemOfferingResToJson(ItemOfferingRes data) =>
    json.encode(data.toJson());

class ItemOfferingRes {
  Data? data;
  num? status; // Changed from int? to num?
  String? statusDescription;

  ItemOfferingRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory ItemOfferingRes.fromJson(Map<String, dynamic> json) =>
      ItemOfferingRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  num? id; // Changed from int? to num?
  num? basePrice; // Changed from int? to num?
  String? pdctCd;
  String? name;
  String? image;
  bool? isAvailable;
  num? eta; // Changed from int? to num?
  num? price; // Changed from int? to num?
  num? tax; // Changed from int? to num?
  num? discount; // Changed from int? to num?
  num? itemTypeId; // Changed from int? to num?
  num? priorityId; // Changed from int? to num?
  String? vip;
  num? sequence; // Changed from int? to num?
  String? shortDescription;
  String? longDescription;
  num? addon; // Changed from int? to num?
  num? serviceItemFlag; // Changed from int? to num?
  num? capacity; // Changed from int? to num?
  num? rewardPoints; // Changed from int? to num?
  num? checkinCount; // Changed from int? to num?
  DateTime? createdAt;
  DateTime? modifiedAt;
  num? serviceId; // Changed from int? to num?
  num? productOfferingsId; // Changed from int? to num?
  num? itemId; // Changed from int? to num?
  num? approvedBy; // Changed from int? to num?
  String? priceDisplayType;
  num? kitchenGroupId; // Changed from int? to num?
  String? code;
  num? createdBy; // Changed from int? to num?
  num? itemRentalTypeId; // Changed from int? to num?
  num? unitQty; // Changed from int? to num?
  String? unitType;
  String? priceDisplayValue;
  String? auctionTypeCd;
  String? prmySellCd;
  String? auctYn;
  num? purchaseOrderProductCategoryId; // Changed from int? to num?
  num? optionId; // Changed from int? to num?
  List<OptionGroup>? optionGroups;

  Data({
    this.id,
    this.basePrice,
    this.pdctCd,
    this.name,
    this.image,
    this.isAvailable,
    this.eta,
    this.price,
    this.tax,
    this.discount,
    this.itemTypeId,
    this.priorityId,
    this.vip,
    this.sequence,
    this.shortDescription,
    this.longDescription,
    this.addon,
    this.serviceItemFlag,
    this.capacity,
    this.rewardPoints,
    this.checkinCount,
    this.createdAt,
    this.modifiedAt,
    this.serviceId,
    this.productOfferingsId,
    this.itemId,
    this.approvedBy,
    this.priceDisplayType,
    this.kitchenGroupId,
    this.code,
    this.createdBy,
    this.itemRentalTypeId,
    this.unitQty,
    this.unitType,
    this.priceDisplayValue,
    this.auctionTypeCd,
    this.prmySellCd,
    this.auctYn,
    this.purchaseOrderProductCategoryId,
    this.optionId,
    this.optionGroups,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        basePrice: json["base_price"],
        pdctCd: json["pdct_cd"],
        name: json["name"],
        image: json["image"],
        isAvailable: json["is_available"],
        eta: json["eta"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        itemTypeId: json["item_type_id"],
        priorityId: json["priority_id"],
        vip: json["vip"],
        sequence: json["sequence"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        addon: json["addon"],
        serviceItemFlag: json["service_item_flag"],
        capacity: json["capacity"],
        rewardPoints: json["reward_points"],
        checkinCount: json["checkin_count"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        serviceId: json["service_id"],
        productOfferingsId: json["product_offerings_id"],
        itemId: json["item_id"],
        approvedBy: json["approved_by"],
        priceDisplayType: json["price_display_type"],
        kitchenGroupId: json["kitchen_group_id"],
        code: json["code"],
        createdBy: json["created_by"],
        itemRentalTypeId: json["item_rental_type_id"],
        unitQty: json["unit_qty"],
        unitType: json["unit_type"],
        priceDisplayValue: json["price_display_value"],
        auctionTypeCd: json["auction_type_cd"],
        prmySellCd: json["prmy_sell_cd"],
        auctYn: json["auct_yn"],
        purchaseOrderProductCategoryId:
            json["purchase_order_product_category_id"],
        optionId: json["option_id"],
        optionGroups: json["option_groups"] == null
            ? []
            : List<OptionGroup>.from(
                json["option_groups"]!.map((x) => OptionGroup.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "base_price": basePrice,
        "pdct_cd": pdctCd,
        "name": name,
        "image": image,
        "is_available": isAvailable,
        "eta": eta,
        "price": price,
        "tax": tax,
        "discount": discount,
        "item_type_id": itemTypeId,
        "priority_id": priorityId,
        "vip": vip,
        "sequence": sequence,
        "short_description": shortDescription,
        "long_description": longDescription,
        "addon": addon,
        "service_item_flag": serviceItemFlag,
        "capacity": capacity,
        "reward_points": rewardPoints,
        "checkin_count": checkinCount,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "service_id": serviceId,
        "product_offerings_id": productOfferingsId,
        "item_id": itemId,
        "approved_by": approvedBy,
        "price_display_type": priceDisplayType,
        "kitchen_group_id": kitchenGroupId,
        "code": code,
        "created_by": createdBy,
        "item_rental_type_id": itemRentalTypeId,
        "unit_qty": unitQty,
        "unit_type": unitType,
        "price_display_value": priceDisplayValue,
        "auction_type_cd": auctionTypeCd,
        "prmy_sell_cd": prmySellCd,
        "auct_yn": auctYn,
        "purchase_order_product_category_id": purchaseOrderProductCategoryId,
        "option_id": optionId,
        "option_groups": optionGroups == null
            ? []
            : List<dynamic>.from(optionGroups!.map((x) => x.toJson())),
      };
}

class OptionGroup {
  num? id; // Changed from int? to num?
  String? name;
  num? mvtItemId; // Changed from int? to num?
  bool? allowMultiSelect;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<Option>? options;
  String? optionType;
  String? valueType;

  OptionGroup({
    this.id,
    this.name,
    this.mvtItemId,
    this.allowMultiSelect,
    this.createdAt,
    this.updatedAt,
    this.options,
    this.optionType,
    this.valueType,
  });

  factory OptionGroup.fromJson(Map<String, dynamic> json) => OptionGroup(
        id: json["id"],
        name: json["name"],
        mvtItemId: json["mvt_item_id"],
        allowMultiSelect: json["allow_multi_select"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        options: json["options"] == null
            ? []
            : List<Option>.from(
                json["options"]!.map((x) => Option.fromJson(x))),
        optionType: json["option_type"],
        valueType: json["value_type"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "mvt_item_id": mvtItemId,
        "allow_multi_select": allowMultiSelect,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
        "option_type": optionType,
        "value_type": valueType,
      };
}

class Option {
  num? id; // Changed from int? to num?
  num? optionGroupId; // Changed from int? to num?
  String? name;
  DateTime? createdAt;
  num? price; // Changed from int? to num?
  bool? overrideItemPrice;

  Option({
    this.id,
    this.optionGroupId,
    this.name,
    this.createdAt,
    this.price,
    this.overrideItemPrice,
  });

  factory Option.fromJson(Map<String, dynamic> json) => Option(
        id: json["id"],
        optionGroupId: json["option_group_id"],
        name: json["name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        price: json["price"],
        overrideItemPrice: json["override_item_price"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "option_group_id": optionGroupId,
        "name": name,
        "created_at": createdAt?.toIso8601String(),
        "price": price,
        "override_item_price": overrideItemPrice,
      };
}
