// To parse this JSON data, do
//
//     final siteRes = siteResFromJson(jsonString);

import 'dart:convert';

SiteRes siteResFromJson(String str) => SiteRes.fromJson(json.decode(str));

String siteResToJson(SiteRes data) => json.encode(data.toJson());

class SiteRes {
  List<Content>? content;
  Pageable? pageable;
  int? totalElements;
  int? totalPages;
  bool? last;
  int? size;
  int? number;
  Sort? sort;
  int? numberOfElements;
  bool? first;
  bool? empty;

  SiteRes({
    this.content,
    this.pageable,
    this.totalElements,
    this.totalPages,
    this.last,
    this.size,
    this.number,
    this.sort,
    this.numberOfElements,
    this.first,
    this.empty,
  });

  factory SiteRes.fromJson(Map<String, dynamic> json) => SiteRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        last: json["last"],
        size: json["size"],
        number: json["number"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        numberOfElements: json["numberOfElements"],
        first: json["first"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "totalElements": totalElements,
        "totalPages": totalPages,
        "last": last,
        "size": size,
        "number": number,
        "sort": sort?.toJson(),
        "numberOfElements": numberOfElements,
        "first": first,
        "empty": empty,
      };
}

class Content {
  int? id;
  Address? address;
  String? projectName;
  int? addressId;
  String? siteAccess;
  int? customerOrgsId;
  String? createdBy;
  String? customerName;
  String? projectStatus;

  Content({
    this.id,
    this.address,
    this.projectName,
    this.addressId,
    this.siteAccess,
    this.customerOrgsId,
    this.createdBy,
    this.customerName,
    this.projectStatus,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        projectName: json["project_name"],
        addressId: json["address_id"],
        siteAccess: json["site_access"],
        customerOrgsId: json["customer_orgs_id"],
        createdBy: json["created_by"],
        customerName: json["customer_name"],
        projectStatus: json["project_status"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "address": address?.toJson(),
        "project_name": projectName,
        "address_id": addressId,
        "site_access": siteAccess,
        "customer_orgs_id": customerOrgsId,
        "created_by": createdBy,
        "customer_name": customerName,
        "project_status": projectStatus,
      };
}

class Address {
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;
  double? latitude;
  double? longitude;
  dynamic cityLatitude;
  dynamic cityLongitude;

  Address({
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLatitude,
    this.cityLongitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        sellingAddressLine1: json["selling_address_line1"],
        sellingAddressLine2: json["selling_address_line2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        cityLatitude: json["city_latitude"],
        cityLongitude: json["city_longitude"],
      );

  Map<String, dynamic> toJson() => {
        "selling_address_line1": sellingAddressLine1,
        "selling_address_line2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
        "city_latitude": cityLatitude,
        "city_longitude": cityLongitude,
      };
}

class Pageable {
  Sort? sort;
  int? offset;
  int? pageSize;
  int? pageNumber;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.offset,
    this.pageSize,
    this.pageNumber,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        offset: json["offset"],
        pageSize: json["pageSize"],
        pageNumber: json["pageNumber"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "offset": offset,
        "pageSize": pageSize,
        "pageNumber": pageNumber,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? unsorted;
  bool? sorted;
  bool? empty;

  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
