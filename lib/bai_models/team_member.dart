// To parse this JSON data, do
//
//     final teamMember = teamMemberFromJson(jsonString);

import 'dart:convert';

List<TeamMember> teamMemberFromJson(String str) =>
    List<TeamMember>.from(json.decode(str).map((x) => TeamMember.fromJson(x)));

String teamMemberToJson(List<TeamMember> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TeamMember {
  num? id;
  String? userId;
  String? name;
  dynamic lastName;
  dynamic username;
  String? password;
  String? email;
  String? phone;
  dynamic officeNumber;
  dynamic landlineNumber;
  String? addressLine1;
  String? addressLine2;
  dynamic address;
  dynamic apartment;
  dynamic homeNumber;
  dynamic comments;
  dynamic city;
  String? state;
  String? countryCode;
  dynamic officeNumberCc;
  dynamic homeNumberCc;
  dynamic landlineNumberCc;
  String? vip;
  dynamic bidMaxAmount;
  dynamic genderId;
  bool? profileComplete;
  dynamic country;
  String? zip;
  dynamic lat;
  dynamic lon;
  num? rewardsPoints;
  dynamic fsGlobalAppUserId;
  dynamic deviseId;
  bool? verified;
  dynamic referredBy;
  num? vendorId;
  String? customerType;
  bool? indirectCustomer;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic lastVerifiedAt;
  List<dynamic>? lstAccess;
  bool? deletedFlag;
  DateTime? lastLoggedIn;
  dynamic van;
  dynamic vanBankName;
  dynamic bankAccountHolderName;
  dynamic bankIfscCode;
  dynamic bankAccountNo;
  String? customerGroupCd;
  num? userRoleId;
  num? orgId;
  String? currentLocation;
  String? designation;
  bool? isRealPhone;
  bool? isRealEmail;
  bool? isTestUser;
  dynamic createdFrom;
  dynamic vendor;
  String? customerApprovalStatusCd;
  num? customerApprovedBy;
  DateTime? customerApprovedAt;
  String? uvendorName;
  String? uaddressLine1;
  String? uaddressLine2;
  String? ustate;
  String? uphone;
  String? uemail;
  String? uname;

  TeamMember({
    this.id,
    this.userId,
    this.name,
    this.lastName,
    this.username,
    this.password,
    this.email,
    this.phone,
    this.officeNumber,
    this.landlineNumber,
    this.addressLine1,
    this.addressLine2,
    this.address,
    this.apartment,
    this.homeNumber,
    this.comments,
    this.city,
    this.state,
    this.countryCode,
    this.officeNumberCc,
    this.homeNumberCc,
    this.landlineNumberCc,
    this.vip,
    this.bidMaxAmount,
    this.genderId,
    this.profileComplete,
    this.country,
    this.zip,
    this.lat,
    this.lon,
    this.rewardsPoints,
    this.fsGlobalAppUserId,
    this.deviseId,
    this.verified,
    this.referredBy,
    this.vendorId,
    this.customerType,
    this.indirectCustomer,
    this.createdAt,
    this.modifiedAt,
    this.lastVerifiedAt,
    this.lstAccess,
    this.deletedFlag,
    this.lastLoggedIn,
    this.van,
    this.vanBankName,
    this.bankAccountHolderName,
    this.bankIfscCode,
    this.bankAccountNo,
    this.customerGroupCd,
    this.userRoleId,
    this.orgId,
    this.currentLocation,
    this.designation,
    this.isRealPhone,
    this.isRealEmail,
    this.isTestUser,
    this.createdFrom,
    this.vendor,
    this.customerApprovalStatusCd,
    this.customerApprovedBy,
    this.customerApprovedAt,
    this.uvendorName,
    this.uaddressLine1,
    this.uaddressLine2,
    this.ustate,
    this.uphone,
    this.uemail,
    this.uname,
  });

  factory TeamMember.fromJson(Map<String, dynamic> json) => TeamMember(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        lastName: json["last_name"],
        username: json["username"],
        password: json["password"],
        email: json["email"],
        phone: json["phone"],
        officeNumber: json["office_number"],
        landlineNumber: json["landline_number"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        address: json["address"],
        apartment: json["apartment"],
        homeNumber: json["home_number"],
        comments: json["comments"],
        city: json["city"],
        state: json["state"],
        countryCode: json["country_code"],
        officeNumberCc: json["office_number_cc"],
        homeNumberCc: json["home_number_cc"],
        landlineNumberCc: json["landline_number_cc"],
        vip: json["vip"],
        bidMaxAmount: json["bidMaxAmount"],
        genderId: json["gender_id"],
        profileComplete: json["profile_complete"],
        country: json["country"],
        zip: json["zip"],
        lat: json["lat"],
        lon: json["lon"],
        rewardsPoints: json["rewards_points"],
        fsGlobalAppUserId: json["fs_global_app_user_id"],
        deviseId: json["devise_id"],
        verified: json["verified"],
        referredBy: json["referred_by"],
        vendorId: json["vendorId"],
        customerType: json["customerType"],
        indirectCustomer: json["indirect_customer"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        lastVerifiedAt: json["last_verified_at"],
        lstAccess: json["lstAccess"] == null
            ? []
            : List<dynamic>.from(json["lstAccess"]!.map((x) => x)),
        deletedFlag: json["deleted_flag"],
        lastLoggedIn: json["last_logged_in"] == null
            ? null
            : DateTime.parse(json["last_logged_in"]),
        van: json["van"],
        vanBankName: json["vanBankName"],
        bankAccountHolderName: json["bankAccountHolderName"],
        bankIfscCode: json["bankIfscCode"],
        bankAccountNo: json["bankAccountNo"],
        customerGroupCd: json["customerGroupCd"],
        userRoleId: json["userRoleId"],
        orgId: json["orgId"],
        currentLocation: json["current_location"],
        designation: json["designation"],
        isRealPhone: json["isRealPhone"],
        isRealEmail: json["isRealEmail"],
        isTestUser: json["isTestUser"],
        createdFrom: json["createdFrom"],
        vendor: json["vendor"],
        customerApprovalStatusCd: json["customerApprovalStatusCd"],
        customerApprovedBy: json["customerApprovedBy"],
        customerApprovedAt: json["customerApprovedAt"] == null
            ? null
            : DateTime.parse(json["customerApprovedAt"]),
        uvendorName: json["uvendorName"],
        uaddressLine1: json["uaddressLine1"],
        uaddressLine2: json["uaddressLine2"],
        ustate: json["ustate"],
        uphone: json["uphone"],
        uemail: json["uemail"],
        uname: json["uname"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "last_name": lastName,
        "username": username,
        "password": password,
        "email": email,
        "phone": phone,
        "office_number": officeNumber,
        "landline_number": landlineNumber,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "address": address,
        "apartment": apartment,
        "home_number": homeNumber,
        "comments": comments,
        "city": city,
        "state": state,
        "country_code": countryCode,
        "office_number_cc": officeNumberCc,
        "home_number_cc": homeNumberCc,
        "landline_number_cc": landlineNumberCc,
        "vip": vip,
        "bidMaxAmount": bidMaxAmount,
        "gender_id": genderId,
        "profile_complete": profileComplete,
        "country": country,
        "zip": zip,
        "lat": lat,
        "lon": lon,
        "rewards_points": rewardsPoints,
        "fs_global_app_user_id": fsGlobalAppUserId,
        "devise_id": deviseId,
        "verified": verified,
        "referred_by": referredBy,
        "vendorId": vendorId,
        "customerType": customerType,
        "indirect_customer": indirectCustomer,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "last_verified_at": lastVerifiedAt,
        "lstAccess": lstAccess == null
            ? []
            : List<dynamic>.from(lstAccess!.map((x) => x)),
        "deleted_flag": deletedFlag,
        "last_logged_in": lastLoggedIn?.toIso8601String(),
        "van": van,
        "vanBankName": vanBankName,
        "bankAccountHolderName": bankAccountHolderName,
        "bankIfscCode": bankIfscCode,
        "bankAccountNo": bankAccountNo,
        "customerGroupCd": customerGroupCd,
        "userRoleId": userRoleId,
        "orgId": orgId,
        "current_location": currentLocation,
        "designation": designation,
        "isRealPhone": isRealPhone,
        "isRealEmail": isRealEmail,
        "isTestUser": isTestUser,
        "createdFrom": createdFrom,
        "vendor": vendor,
        "customerApprovalStatusCd": customerApprovalStatusCd,
        "customerApprovedBy": customerApprovedBy,
        "customerApprovedAt": customerApprovedAt?.toIso8601String(),
        "uvendorName": uvendorName,
        "uaddressLine1": uaddressLine1,
        "uaddressLine2": uaddressLine2,
        "ustate": ustate,
        "uphone": uphone,
        "uemail": uemail,
        "uname": uname,
      };
}
