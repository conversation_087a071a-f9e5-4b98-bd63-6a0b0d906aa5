// To parse this JSON data, do
//
//     final requestTypes = requestTypesFromJson(jsonString);

import 'dart:convert';

List<RequestTypes> requestTypesFromJson(String str) => List<RequestTypes>.from(
    json.decode(str).map((x) => RequestTypes.fromJson(x)));

String requestTypesToJson(List<RequestTypes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class RequestTypes {
  int? id;
  String? statusCode;
  String? statusName;
  bool? isActive;
  DateTime? startDate;
  DateTime? expiryDate;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? colourCode;

  RequestTypes({
    this.id,
    this.statusCode,
    this.statusName,
    this.isActive,
    this.startDate,
    this.expiryDate,
    this.createdAt,
    this.updatedAt,
    this.colourCode,
  });

  factory RequestTypes.fromJson(Map<String, dynamic> json) => RequestTypes(
        id: json["id"],
        statusCode: json["status_code"],
        statusName: json["status_name"],
        isActive: json["is_active"],
        startDate: json["start_date"] == null
            ? null
            : DateTime.parse(json["start_date"]),
        expiryDate: json["expiry_date"] == null
            ? null
            : DateTime.parse(json["expiry_date"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        colourCode: json["colour_code"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "status_code": statusCode,
        "status_name": statusName,
        "is_active": isActive,
        "start_date": startDate?.toIso8601String(),
        "expiry_date": expiryDate?.toIso8601String(),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "colour_code": colourCode,
      };
}
