import 'dart:convert';

InviteStatusRes inviteStatusResFromJson(String str) =>
    InviteStatusRes.fromJson(json.decode(str));

String inviteStatusResToJson(InviteStatusRes data) =>
    json.encode(data.toJson());

class InviteStatusRes {
  num? totalInvite;
  num? totalQuoteReceived;
  List<VendorDetail>? vendorDetails;

  InviteStatusRes({
    this.totalInvite,
    this.totalQuoteReceived,
    this.vendorDetails,
  });

  factory InviteStatusRes.fromJson(Map<String, dynamic> json) =>
      InviteStatusRes(
        totalInvite: json["total_invite"],
        totalQuoteReceived: json["total_quote_received"],
        vendorDetails: json["vendor_details"] == null
            ? []
            : List<VendorDetail>.from(
                json["vendor_details"]!.map((x) => VendorDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_invite": totalInvite,
        "total_quote_received": totalQuoteReceived,
        "vendor_details": vendorDetails == null
            ? []
            : List<dynamic>.from(vendorDetails!.map((x) => x.toJson())),
      };
}

class VendorDetail {
  num? vendorId;
  String? vendorName;
  String? phoneNo;
  num? noOfViews;
  String? status;
  bool? isRemind;

  VendorDetail({
    this.vendorId,
    this.vendorName,
    this.phoneNo,
    this.noOfViews,
    this.status,
    this.isRemind,
  });

  factory VendorDetail.fromJson(Map<String, dynamic> json) => VendorDetail(
        vendorId: json["vendor_id"],
        vendorName: json["vendor_name"],
        phoneNo: json["phone_no"],
        noOfViews: json["no_of_views"],
        status: json["status"],
        isRemind: json["is_remind"],
      );

  Map<String, dynamic> toJson() => {
        "vendor_id": vendorId,
        "vendor_name": vendorName,
        "phone_no": phoneNo,
        "no_of_views": noOfViews,
        "status": status,
        "is_remind": isRemind,
      };
}
