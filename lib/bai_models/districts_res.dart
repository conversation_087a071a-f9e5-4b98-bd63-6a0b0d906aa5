class DistrictsRes {
  List<District>? districts;

  DistrictsRes({this.districts});

  factory DistrictsRes.fromJson(List<dynamic> json) {
    return DistrictsRes(
      districts: json.map((e) => District.fromJson(e)).toList(),
    );
  }

  List<dynamic> toJson() {
    return districts!.map((e) => e.toJson()).toList();
  }
}

class District {
  String? name;
  String? id;

  District({this.name, this.id});

  factory District.fromJson(Map<String, dynamic> json) {
    return District(
      name: json['name'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'id': id,
    };
  }
}
