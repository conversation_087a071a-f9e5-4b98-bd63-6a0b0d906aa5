class CustomerReviewsRes {
  int? totalPages;
  int? currentPage;
  List<Content>? content;
  int? totalElements;

  CustomerReviewsRes(
      {this.totalPages, this.currentPage, this.content, this.totalElements});

  CustomerReviewsRes.fromJson(Map<String, dynamic> json) {
    totalPages = json['totalPages'];
    currentPage = json['currentPage'];
    if (json['content'] != null) {
      content = <Content>[];
      json['content'].forEach((v) {
        content!.add(Content.fromJson(v));
      });
    }
    totalElements = json['totalElements'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalPages'] = totalPages;
    data['currentPage'] = currentPage;
    if (content != null) {
      data['content'] = content!.map((v) => v.toJson()).toList();
    }
    data['totalElements'] = totalElements;
    return data;
  }
}

class Content {
  int? id;
  dynamic stockId;
  int? helpfulCount;
  bool? verifiedPurchase;
  String? comment;
  int? rating;
  String? createdAt;
  String? customerName;
  String? customersCompanyName;
  bool? reported;

  Content(
      {this.id,
      this.stockId,
      this.helpfulCount,
      this.verifiedPurchase,
      this.comment,
      this.rating,
      this.createdAt,
      this.customerName,
      this.customersCompanyName,
      this.reported});

  Content.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stockId = json['stockId'];
    helpfulCount = json['helpfulCount'];
    verifiedPurchase = json['verifiedPurchase'];
    comment = json['comment'];
    rating = json['rating'];
    createdAt = json['createdAt'];
    customerName = json['customerName'];
    customersCompanyName = json['customersCompanyName'];
    reported = json['reported'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['stockId'] = stockId;
    data['helpfulCount'] = helpfulCount;
    data['verifiedPurchase'] = verifiedPurchase;
    data['comment'] = comment;
    data['rating'] = rating;
    data['createdAt'] = createdAt;
    data['customerName'] = customerName;
    data['customersCompanyName'] = customersCompanyName;
    data['reported'] = reported;
    return data;
  }
}
