import 'dart:convert';

RatingVendorListRes ratingVendorListResFromJson(String str) =>
    RatingVendorListRes.fromJson(json.decode(str));

String ratingVendorListResToJson(RatingVendorListRes data) =>
    json.encode(data.toJson());

class RatingVendorListRes {
  List<Datum>? data;
  int? status;
  String? statusDescription;

  RatingVendorListRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory RatingVendorListRes.fromJson(Map<String, dynamic> json) =>
      RatingVendorListRes(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  int? vendorId;
  String? vendorName;
  String? rating;
  String? review;

  Datum({
    this.vendorId,
    this.vendorName,
    this.rating,
    this.review,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        vendorId: json["vendorId"],
        vendorName: json["vendorName"],
        rating: json["rating"],
        review: json["review"],
      );

  Map<String, dynamic> toJson() => {
        "vendorId": vendorId,
        "vendorName": vendorName,
        "rating": rating,
        "review": review,
      };
}
