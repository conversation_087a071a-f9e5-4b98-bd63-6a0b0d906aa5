import 'dart:convert';

class ReportReq {
  int? prchOrdrId;
  int? customerId;
  String? description;

  ReportReq({
    this.prchOrdrId,
    this.customerId,
    this.description,
  });

  factory ReportReq.fromRawJson(String str) =>
      ReportReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ReportReq.fromJson(Map<String, dynamic> json) => ReportReq(
        prchOrdrId: json["prchOrdrId"],
        customerId: json["customerId"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "prchOrdrId": prchOrdrId,
        "customerId": customerId,
        "description": description,
      };
}
