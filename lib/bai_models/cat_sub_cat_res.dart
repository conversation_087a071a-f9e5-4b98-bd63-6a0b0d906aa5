import 'dart:convert';

List<CatSubCatRes> catSubCatResFromJson(String str) => List<CatSubCatRes>.from(
    json.decode(str).map((x) => CatSubCatRes.fromJson(x)));

String catSubCatResToJson(List<CatSubCatRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CatSubCatRes {
  String? category;
  String? image;
  List<Subcategory>? subcategory;

  CatSubCatRes({
    this.category,
    this.image,
    this.subcategory,
  });

  factory CatSubCatRes.fromJson(Map<String, dynamic> json) => CatSubCatRes(
        category: json["category"],
        image: json["image"],
        subcategory: json["subcategory"] == null
            ? []
            : List<Subcategory>.from(
                json["subcategory"]!.map((x) => Subcategory.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "category": category,
        "image": image,
        "subcategory": subcategory == null
            ? []
            : List<dynamic>.from(subcategory!.map((x) => x.toJson())),
      };
}

class Subcategory {
  String? id;
  String? name;
  String? image;
  List<Product>? product;

  Subcategory({
    this.id,
    this.name,
    this.image,
    this.product,
  });

  factory Subcategory.fromJson(Map<String, dynamic> json) => Subcategory(
        id: json["id"],
        name: json["name"],
        image: json["image"],
        product: json["product"] == null
            ? []
            : List<Product>.from(
                json["product"]!.map((x) => Product.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "product": product == null
            ? []
            : List<dynamic>.from(product!.map((x) => x.toJson())),
      };
}

class Product {
  num? id;
  String? eta;
  String? image;
  String? name;
  num? price;
  num? tax;
  num? discount;
  num? serviceItemFlag;
  num? rewardPoints;
  String? auctionTypeCd;
  String? pdctCd;
  String? prmySellCd;
  String? auctYn;
  Service? service;

  Product({
    this.id,
    this.eta,
    this.image,
    this.name,
    this.price,
    this.tax,
    this.discount,
    this.serviceItemFlag,
    this.rewardPoints,
    this.auctionTypeCd,
    this.pdctCd,
    this.prmySellCd,
    this.auctYn,
    this.service,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        eta: json["eta"],
        image: json["image"],
        name: json["name"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        serviceItemFlag: json["serviceItemFlag"],
        rewardPoints: json["rewardPoints"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        prmySellCd: json["prmySellCd"],
        auctYn: json["auctYn"],
        service:
            json["service"] == null ? null : Service.fromJson(json["service"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "eta": eta,
        "image": image,
        "name": name,
        "price": price,
        "tax": tax,
        "discount": discount,
        "serviceItemFlag": serviceItemFlag,
        "rewardPoints": rewardPoints,
        "auctionTypeCd": auctionTypeCd,
        "pdctCd": pdctCd,
        "prmySellCd": prmySellCd,
        "auctYn": auctYn,
        "service": service?.toJson(),
      };
}

class Service {
  num? id;
  String? name;

  Service({
    this.id,
    this.name,
  });

  factory Service.fromJson(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
