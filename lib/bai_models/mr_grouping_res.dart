import 'dart:convert';

List<MrGroupingRes> mrGroupingResFromJson(String str) =>
    List<MrGroupingRes>.from(
        json.decode(str).map((x) => MrGroupingRes.fromJson(x)));

String mrGroupingResToJson(List<MrGroupingRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class MrGroupingRes {
  num? id;
  num? customerId;
  num? vendorId;
  num? mvtItemId;
  String? mvtItemName;
  dynamic price;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deliveryDate;
  num? projectId;
  String? siteAccess;
  String? projectName;
  String? roadAccess;
  num? quantity;
  num? projectAddressId;
  num? deliveryAddressId;
  dynamic createdCustomerDesignation;
  String? statusCd;
  num? cappCategoriesId;
  String? cappCategoriesName;
  String? vendorName;
  String? instructions;
  String? customerName;
  String? customerPhone;
  num? vendorRating;
  dynamic siteInChargeId;
  num? orderGroupId;
  String? orderGroupName;

  MrGroupingRes({
    this.id,
    this.customerId,
    this.vendorId,
    this.mvtItemId,
    this.mvtItemName,
    this.price,
    this.createdAt,
    this.updatedAt,
    this.deliveryDate,
    this.projectId,
    this.siteAccess,
    this.projectName,
    this.roadAccess,
    this.quantity,
    this.projectAddressId,
    this.deliveryAddressId,
    this.createdCustomerDesignation,
    this.statusCd,
    this.cappCategoriesId,
    this.cappCategoriesName,
    this.vendorName,
    this.instructions,
    this.customerName,
    this.customerPhone,
    this.vendorRating,
    this.siteInChargeId,
    this.orderGroupId,
    this.orderGroupName,
  });

  factory MrGroupingRes.fromJson(Map<String, dynamic> json) => MrGroupingRes(
        id: json["id"],
        customerId: json["customerId"],
        vendorId: json["vendorId"],
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        price: json["price"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        projectId: json["projectId"],
        siteAccess: json["siteAccess"],
        projectName: json["projectName"],
        roadAccess: json["roadAccess"],
        quantity: json["quantity"],
        projectAddressId: json["projectAddressId"],
        deliveryAddressId: json["deliveryAddressId"],
        createdCustomerDesignation: json["createdCustomerDesignation"],
        statusCd: json["statusCd"],
        cappCategoriesId: json["cappCategoriesId"],
        cappCategoriesName: json["cappCategoriesName"],
        vendorName: json["vendorName"],
        instructions: json["instructions"],
        customerName: json["customerName"],
        customerPhone: json["customerPhone"],
        vendorRating: json["vendorRating"],
        siteInChargeId: json["siteInChargeId"],
        orderGroupId: json["orderGroupId"],
        orderGroupName: json["orderGroupName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerId": customerId,
        "vendorId": vendorId,
        "mvtItemId": mvtItemId,
        "mvtItemName": mvtItemName,
        "price": price,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "deliveryDate": deliveryDate?.toIso8601String(),
        "projectId": projectId,
        "siteAccess": siteAccess,
        "projectName": projectName,
        "roadAccess": roadAccess,
        "quantity": quantity,
        "projectAddressId": projectAddressId,
        "deliveryAddressId": deliveryAddressId,
        "createdCustomerDesignation": createdCustomerDesignation,
        "statusCd": statusCd,
        "cappCategoriesId": cappCategoriesId,
        "cappCategoriesName": cappCategoriesName,
        "vendorName": vendorName,
        "instructions": instructions,
        "customerName": customerName,
        "customerPhone": customerPhone,
        "vendorRating": vendorRating,
        "siteInChargeId": siteInChargeId,
        "orderGroupId": orderGroupId,
        "orderGroupName": orderGroupName,
      };
}
