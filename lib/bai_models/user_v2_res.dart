import 'dart:convert';

UserV2Res userV2ResFromJson(String str) => UserV2Res.fromJson(json.decode(str));

String userV2ResToJson(UserV2Res data) => json.encode(data.toJson());

class UserV2Res {
  num? status;
  String? statusDescription;
  List<Datum>? data;

  UserV2Res({
    this.status,
    this.statusDescription,
    this.data,
  });

  factory UserV2Res.fromJson(Map<String, dynamic> json) => UserV2Res(
        status: json["status"],
        statusDescription: json["status_description"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "status_description": statusDescription,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  num? id;
  String? userId;
  String? name;
  dynamic lastName;
  dynamic username;
  String? password;
  String? email;
  String? phone;
  dynamic officeNumber;
  dynamic landlineNumber;
  String? addressLine1;
  String? addressLine2;
  dynamic address;
  dynamic apartment;
  dynamic homeNumber;
  dynamic comments;
  dynamic city;
  String? state;
  String? countryCode;
  dynamic officeNumberCc;
  dynamic homeNumberCc;
  dynamic landlineNumberCc;
  String? vip;
  num? bidMaxAmount;
  num? genderId;
  bool? profileComplete;
  dynamic country;
  String? zip;
  dynamic lat;
  dynamic lon;
  num? rewardsPoints;
  dynamic fsGlobalAppUserId;
  dynamic deviseId;
  bool? verified;
  dynamic referredBy;
  num? vendorId;
  String? customerType;
  bool? indirectCustomer;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic lastVerifiedAt;
  List<dynamic>? lstAccess;
  bool? deletedFlag;
  DateTime? lastLoggedIn;
  String? van;
  String? vanBankName;
  String? bankAccountHolderName;
  String? bankIfscCode;
  String? bankAccountNo;
  String? customerGroupCd;
  num? userRoleId;
  num? orgId;
  String? currentLocation;
  bool? isRealPhone;
  bool? isRealEmail;
  bool? isTestUser;
  dynamic createdFrom;
  Vendor? vendor;
  String? uemail;
  String? uvendorName;
  String? uaddressLine2;
  String? ustate;
  String? uname;
  String? uphone;
  String? uaddressLine1;
  String? designation;
  String? customerApprovalStatusCd;
  num? userLevel;

  Datum({
    this.id,
    this.userId,
    this.name,
    this.lastName,
    this.username,
    this.password,
    this.email,
    this.phone,
    this.officeNumber,
    this.landlineNumber,
    this.addressLine1,
    this.addressLine2,
    this.address,
    this.apartment,
    this.homeNumber,
    this.comments,
    this.city,
    this.state,
    this.countryCode,
    this.officeNumberCc,
    this.homeNumberCc,
    this.landlineNumberCc,
    this.vip,
    this.bidMaxAmount,
    this.genderId,
    this.profileComplete,
    this.country,
    this.zip,
    this.lat,
    this.lon,
    this.rewardsPoints,
    this.fsGlobalAppUserId,
    this.deviseId,
    this.verified,
    this.referredBy,
    this.vendorId,
    this.customerType,
    this.indirectCustomer,
    this.createdAt,
    this.modifiedAt,
    this.lastVerifiedAt,
    this.lstAccess,
    this.deletedFlag,
    this.lastLoggedIn,
    this.van,
    this.vanBankName,
    this.bankAccountHolderName,
    this.bankIfscCode,
    this.bankAccountNo,
    this.customerGroupCd,
    this.userRoleId,
    this.orgId,
    this.currentLocation,
    this.isRealPhone,
    this.isRealEmail,
    this.isTestUser,
    this.createdFrom,
    this.vendor,
    this.uemail,
    this.uvendorName,
    this.uaddressLine2,
    this.ustate,
    this.uname,
    this.uphone,
    this.uaddressLine1,
    this.designation,
    this.customerApprovalStatusCd,
    this.userLevel,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        lastName: json["last_name"],
        username: json["username"],
        password: json["password"],
        email: json["email"],
        phone: json["phone"],
        officeNumber: json["office_number"],
        landlineNumber: json["landline_number"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        address: json["address"],
        apartment: json["apartment"],
        homeNumber: json["home_number"],
        comments: json["comments"],
        city: json["city"],
        state: json["state"],
        countryCode: json["country_code"],
        officeNumberCc: json["office_number_cc"],
        homeNumberCc: json["home_number_cc"],
        landlineNumberCc: json["landline_number_cc"],
        vip: json["vip"],
        bidMaxAmount: json["bidMaxAmount"],
        genderId: json["gender_id"],
        profileComplete: json["profile_complete"],
        country: json["country"],
        zip: json["zip"],
        lat: json["lat"],
        lon: json["lon"],
        rewardsPoints: json["rewards_points"],
        fsGlobalAppUserId: json["fs_global_app_user_id"],
        deviseId: json["devise_id"],
        verified: json["verified"],
        referredBy: json["referred_by"],
        vendorId: json["vendorId"],
        customerType: json["customerType"],
        indirectCustomer: json["indirect_customer"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        lastVerifiedAt: json["last_verified_at"],
        lstAccess: json["lstAccess"] == null
            ? []
            : List<dynamic>.from(json["lstAccess"]!.map((x) => x)),
        deletedFlag: json["deleted_flag"],
        lastLoggedIn: json["last_logged_in"] == null
            ? null
            : DateTime.parse(json["last_logged_in"]),
        van: json["van"],
        vanBankName: json["vanBankName"],
        bankAccountHolderName: json["bankAccountHolderName"],
        bankIfscCode: json["bankIfscCode"],
        bankAccountNo: json["bankAccountNo"],
        customerGroupCd: json["customerGroupCd"],
        userRoleId: json["userRoleId"],
        orgId: json["orgId"],
        currentLocation: json["current_location"],
        isRealPhone: json["isRealPhone"],
        isRealEmail: json["isRealEmail"],
        isTestUser: json["isTestUser"],
        createdFrom: json["createdFrom"],
        vendor: json["vendor"] == null ? null : Vendor.fromJson(json["vendor"]),
        uemail: json["uemail"],
        uvendorName: json["uvendorName"],
        uaddressLine2: json["uaddressLine2"],
        ustate: json["ustate"],
        uname: json["uname"],
        uphone: json["uphone"],
        uaddressLine1: json["uaddressLine1"],
        designation: json["designation"],
        customerApprovalStatusCd: json["customerApprovalStatusCd"],
        userLevel: json["userLevel"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "last_name": lastName,
        "username": username,
        "password": password,
        "email": email,
        "phone": phone,
        "office_number": officeNumber,
        "landline_number": landlineNumber,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "address": address,
        "apartment": apartment,
        "home_number": homeNumber,
        "comments": comments,
        "city": city,
        "state": state,
        "country_code": countryCode,
        "office_number_cc": officeNumberCc,
        "home_number_cc": homeNumberCc,
        "landline_number_cc": landlineNumberCc,
        "vip": vip,
        "bidMaxAmount": bidMaxAmount,
        "gender_id": genderId,
        "profile_complete": profileComplete,
        "country": country,
        "zip": zip,
        "lat": lat,
        "lon": lon,
        "rewards_points": rewardsPoints,
        "fs_global_app_user_id": fsGlobalAppUserId,
        "devise_id": deviseId,
        "verified": verified,
        "referred_by": referredBy,
        "vendorId": vendorId,
        "customerType": customerType,
        "indirect_customer": indirectCustomer,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "last_verified_at": lastVerifiedAt,
        "lstAccess": lstAccess == null
            ? []
            : List<dynamic>.from(lstAccess!.map((x) => x)),
        "deleted_flag": deletedFlag,
        "last_logged_in": lastLoggedIn?.toIso8601String(),
        "van": van,
        "vanBankName": vanBankName,
        "bankAccountHolderName": bankAccountHolderName,
        "bankIfscCode": bankIfscCode,
        "bankAccountNo": bankAccountNo,
        "customerGroupCd": customerGroupCd,
        "userRoleId": userRoleId,
        "orgId": orgId,
        "current_location": currentLocation,
        "isRealPhone": isRealPhone,
        "isRealEmail": isRealEmail,
        "isTestUser": isTestUser,
        "createdFrom": createdFrom,
        "vendor": vendor?.toJson(),
        "uemail": uemail,
        "uvendorName": uvendorName,
        "uaddressLine2": uaddressLine2,
        "ustate": ustate,
        "uname": uname,
        "uphone": uphone,
        "uaddressLine1": uaddressLine1,
        "designation": designation,
        "userLevel": userLevel,
      };
}

class Vendor {
  num? id;
  String? name;
  bool? subscriptionRequired;
  dynamic subscriptionStartedAt;
  dynamic subscriptionEndsOn;
  num? subscriptionAmount;
  num? subscriptionAmountPending;
  num? subscriptionAmountPaid;
  dynamic subscriptionAmountPaidOn;
  String? website;
  String? email;
  dynamic rating;
  dynamic address;
  String? addressLine1;
  String? addressLine2;
  String? primaryPhone;
  dynamic city;
  String? state;
  dynamic country;
  dynamic addressLat;
  dynamic addressLong;
  String? secondaryPhone;
  String? primaryPhoneCountryCode;
  String? secondaryPhoneCountryCode;
  dynamic officeNumber;
  dynamic officeNumberCc;
  dynamic homeNumber;
  dynamic homeNumberCc;
  dynamic landlineNumber;
  dynamic landlineNumberCc;
  dynamic whatsappNumber;
  dynamic whatsappNumberCc;
  dynamic fullAddressFromGmaps;
  String? shortDescription;
  String? longDescription;
  String? caption;
  dynamic sequence;
  dynamic totalRating;
  String? mondayFromTime;
  String? mondayToTime;
  String? mondayHoliday;
  String? tuesdayFromTime;
  String? tuesdayToTime;
  String? tuesdayHoliday;
  String? wednesdayFromTime;
  String? wednesdayToTime;
  String? wednesdayHoliday;
  String? thursdayFromTime;
  String? thursdayToTime;
  String? thursdayHoliday;
  String? fridayFromTime;
  String? fridayToTime;
  String? fridayHoliday;
  String? saturdayFromTime;
  String? saturdayToTime;
  String? saturdayHoliday;
  String? sundayFromTime;
  String? sundayToTime;
  String? sundayHoliday;
  dynamic defaultRouteId;
  dynamic defaultProductOfferingId;
  DateTime? createdAt;
  DateTime? modifiedAt;
  String? subdomain;
  String? adminPortalUrl;
  num? approvedBy;
  DateTime? approvedAt;
  bool? active;
  dynamic vendorCategoryId;
  dynamic abn;
  String? approvedYn;
  DateTime? acceptedTermsAndConditionsAt;
  dynamic profileComplete;
  dynamic referredBy;
  String? image;
  String? imageTitle;
  String? encryptIv;
  num? isAvailable;
  dynamic defaultRoomId;
  String? productListBanner;
  String? gstNumber;
  dynamic shippingAddress;

  Vendor({
    this.id,
    this.name,
    this.subscriptionRequired,
    this.subscriptionStartedAt,
    this.subscriptionEndsOn,
    this.subscriptionAmount,
    this.subscriptionAmountPending,
    this.subscriptionAmountPaid,
    this.subscriptionAmountPaidOn,
    this.website,
    this.email,
    this.rating,
    this.address,
    this.addressLine1,
    this.addressLine2,
    this.primaryPhone,
    this.city,
    this.state,
    this.country,
    this.addressLat,
    this.addressLong,
    this.secondaryPhone,
    this.primaryPhoneCountryCode,
    this.secondaryPhoneCountryCode,
    this.officeNumber,
    this.officeNumberCc,
    this.homeNumber,
    this.homeNumberCc,
    this.landlineNumber,
    this.landlineNumberCc,
    this.whatsappNumber,
    this.whatsappNumberCc,
    this.fullAddressFromGmaps,
    this.shortDescription,
    this.longDescription,
    this.caption,
    this.sequence,
    this.totalRating,
    this.mondayFromTime,
    this.mondayToTime,
    this.mondayHoliday,
    this.tuesdayFromTime,
    this.tuesdayToTime,
    this.tuesdayHoliday,
    this.wednesdayFromTime,
    this.wednesdayToTime,
    this.wednesdayHoliday,
    this.thursdayFromTime,
    this.thursdayToTime,
    this.thursdayHoliday,
    this.fridayFromTime,
    this.fridayToTime,
    this.fridayHoliday,
    this.saturdayFromTime,
    this.saturdayToTime,
    this.saturdayHoliday,
    this.sundayFromTime,
    this.sundayToTime,
    this.sundayHoliday,
    this.defaultRouteId,
    this.defaultProductOfferingId,
    this.createdAt,
    this.modifiedAt,
    this.subdomain,
    this.adminPortalUrl,
    this.approvedBy,
    this.approvedAt,
    this.active,
    this.vendorCategoryId,
    this.abn,
    this.approvedYn,
    this.acceptedTermsAndConditionsAt,
    this.profileComplete,
    this.referredBy,
    this.image,
    this.imageTitle,
    this.encryptIv,
    this.isAvailable,
    this.defaultRoomId,
    this.productListBanner,
    this.gstNumber,
    this.shippingAddress,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        id: json["id"],
        name: json["name"],
        subscriptionRequired: json["subscriptionRequired"],
        subscriptionStartedAt: json["subscriptionStartedAt"],
        subscriptionEndsOn: json["subscriptionEndsOn"],
        subscriptionAmount: json["subscriptionAmount"],
        subscriptionAmountPending: json["subscriptionAmountPending"],
        subscriptionAmountPaid: json["subscriptionAmountPaid"],
        subscriptionAmountPaidOn: json["subscriptionAmountPaidOn"],
        website: json["website"],
        email: json["email"],
        rating: json["rating"],
        address: json["address"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        primaryPhone: json["primary_phone"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        addressLat: json["address_lat"],
        addressLong: json["address_long"],
        secondaryPhone: json["secondary_phone"],
        primaryPhoneCountryCode: json["primary_phone_country_code"],
        secondaryPhoneCountryCode: json["secondary_phone_country_code"],
        officeNumber: json["office_number"],
        officeNumberCc: json["office_number_cc"],
        homeNumber: json["home_number"],
        homeNumberCc: json["home_number_cc"],
        landlineNumber: json["landline_number"],
        landlineNumberCc: json["landline_number_cc"],
        whatsappNumber: json["whatsapp_number"],
        whatsappNumberCc: json["whatsapp_number_cc"],
        fullAddressFromGmaps: json["full_address_from_gmaps"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        caption: json["caption"],
        sequence: json["sequence"],
        totalRating: json["total_rating"],
        mondayFromTime: json["monday_from_time"],
        mondayToTime: json["monday_to_time"],
        mondayHoliday: json["monday_holiday"],
        tuesdayFromTime: json["tuesday_from_time"],
        tuesdayToTime: json["tuesday_to_time"],
        tuesdayHoliday: json["tuesday_holiday"],
        wednesdayFromTime: json["wednesday_from_time"],
        wednesdayToTime: json["wednesday_to_time"],
        wednesdayHoliday: json["wednesday_holiday"],
        thursdayFromTime: json["thursday_from_time"],
        thursdayToTime: json["thursday_to_time"],
        thursdayHoliday: json["thursday_holiday"],
        fridayFromTime: json["friday_from_time"],
        fridayToTime: json["friday_to_time"],
        fridayHoliday: json["friday_holiday"],
        saturdayFromTime: json["saturday_from_time"],
        saturdayToTime: json["saturday_to_time"],
        saturdayHoliday: json["saturday_holiday"],
        sundayFromTime: json["sunday_from_time"],
        sundayToTime: json["sunday_to_time"],
        sundayHoliday: json["sunday_holiday"],
        defaultRouteId: json["default_route_id"],
        defaultProductOfferingId: json["default_product_offering_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        subdomain: json["subdomain"],
        adminPortalUrl: json["admin_portal_url"],
        approvedBy: json["approved_by"],
        approvedAt: json["approved_at"] == null
            ? null
            : DateTime.parse(json["approved_at"]),
        active: json["active"],
        vendorCategoryId: json["vendor_category_id"],
        abn: json["abn"],
        approvedYn: json["approved_YN"],
        acceptedTermsAndConditionsAt:
            json["accepted_terms_and_conditions_at"] == null
                ? null
                : DateTime.parse(json["accepted_terms_and_conditions_at"]),
        profileComplete: json["profile_complete"],
        referredBy: json["referred_by"],
        image: json["image"],
        imageTitle: json["image_title"],
        encryptIv: json["encrypt_iv"],
        isAvailable: json["is_available"],
        defaultRoomId: json["default_room_id"],
        productListBanner: json["product_list_banner"],
        gstNumber: json["gst_number"],
        shippingAddress: json["shippingAddress"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "subscriptionRequired": subscriptionRequired,
        "subscriptionStartedAt": subscriptionStartedAt,
        "subscriptionEndsOn": subscriptionEndsOn,
        "subscriptionAmount": subscriptionAmount,
        "subscriptionAmountPending": subscriptionAmountPending,
        "subscriptionAmountPaid": subscriptionAmountPaid,
        "subscriptionAmountPaidOn": subscriptionAmountPaidOn,
        "website": website,
        "email": email,
        "rating": rating,
        "address": address,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "primary_phone": primaryPhone,
        "city": city,
        "state": state,
        "country": country,
        "address_lat": addressLat,
        "address_long": addressLong,
        "secondary_phone": secondaryPhone,
        "primary_phone_country_code": primaryPhoneCountryCode,
        "secondary_phone_country_code": secondaryPhoneCountryCode,
        "office_number": officeNumber,
        "office_number_cc": officeNumberCc,
        "home_number": homeNumber,
        "home_number_cc": homeNumberCc,
        "landline_number": landlineNumber,
        "landline_number_cc": landlineNumberCc,
        "whatsapp_number": whatsappNumber,
        "whatsapp_number_cc": whatsappNumberCc,
        "full_address_from_gmaps": fullAddressFromGmaps,
        "short_description": shortDescription,
        "long_description": longDescription,
        "caption": caption,
        "sequence": sequence,
        "total_rating": totalRating,
        "monday_from_time": mondayFromTime,
        "monday_to_time": mondayToTime,
        "monday_holiday": mondayHoliday,
        "tuesday_from_time": tuesdayFromTime,
        "tuesday_to_time": tuesdayToTime,
        "tuesday_holiday": tuesdayHoliday,
        "wednesday_from_time": wednesdayFromTime,
        "wednesday_to_time": wednesdayToTime,
        "wednesday_holiday": wednesdayHoliday,
        "thursday_from_time": thursdayFromTime,
        "thursday_to_time": thursdayToTime,
        "thursday_holiday": thursdayHoliday,
        "friday_from_time": fridayFromTime,
        "friday_to_time": fridayToTime,
        "friday_holiday": fridayHoliday,
        "saturday_from_time": saturdayFromTime,
        "saturday_to_time": saturdayToTime,
        "saturday_holiday": saturdayHoliday,
        "sunday_from_time": sundayFromTime,
        "sunday_to_time": sundayToTime,
        "sunday_holiday": sundayHoliday,
        "default_route_id": defaultRouteId,
        "default_product_offering_id": defaultProductOfferingId,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "subdomain": subdomain,
        "admin_portal_url": adminPortalUrl,
        "approved_by": approvedBy,
        "approved_at": approvedAt?.toIso8601String(),
        "active": active,
        "vendor_category_id": vendorCategoryId,
        "abn": abn,
        "approved_YN": approvedYn,
        "accepted_terms_and_conditions_at":
            acceptedTermsAndConditionsAt?.toIso8601String(),
        "profile_complete": profileComplete,
        "referred_by": referredBy,
        "image": image,
        "image_title": imageTitle,
        "encrypt_iv": encryptIv,
        "is_available": isAvailable,
        "default_room_id": defaultRoomId,
        "product_list_banner": productListBanner,
        "gst_number": gstNumber,
        "shippingAddress": shippingAddress,
      };
}
