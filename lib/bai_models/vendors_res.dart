class VendorsRes {
  List<Vendor>? vendors;

  VendorsRes({this.vendors});

  factory VendorsRes.fromJson(List<dynamic> json) {
    return VendorsRes(
      vendors: json.map((e) => Vendor.fromJson(e)).toList(),
    );
  }

  List<dynamic> toJson() {
    return vendors!.map((e) => e.toJson()).toList();
  }
}

class Vendor {
  String? name;
  String? gstNo;
  String? id;

  Vendor({this.name, this.gstNo, this.id});

  factory Vendor.fromJson(Map<String, dynamic> json) {
    return Vendor(
      name: json['name'],
      gstNo: json['gstNo'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'gstNo': gstNo,
      'id': id,
    };
  }
}
