// // To parse this JSON data, do
// //
// //     final negotiationReq = negotiationReqFromJson(jsonString);

// import 'dart:convert';

// NegotiationReq negotiationReqFromJson(String str) =>
//     NegotiationReq.fromJson(json.decode(str));

// String negotiationReqToJson(NegotiationReq data) => json.encode(data.toJson());

// class NegotiationReq {
//   int? negotiatedPrice;
//   String? negotiationBuyrComment;
//   String? title;
//   String? url;
//   String? previewUrl;
//   String? docType;

//   NegotiationReq({
//     this.negotiatedPrice,
//     this.negotiationBuyrComment,
//     this.title,
//     this.url,
//     this.previewUrl,
//     this.docType,
//   });

//   factory NegotiationReq.fromJson(Map<String, dynamic> json) => NegotiationReq(
//         negotiatedPrice: json["negotiated_price"],
//         negotiationBuyrComment: json["negotiation_buyr_comment"],
//         title: json["title"],
//         url: json["url"],
//         previewUrl: json["preview_url"],
//         docType: json["doc_type"],
//       );

//   Map<String, dynamic> toJson() => {
//         "negotiated_price": negotiatedPrice,
//         "negotiation_buyr_comment": negotiationBuyrComment,
//         "title": title,
//         "url": url,
//         "preview_url": previewUrl,
//         "doc_type": docType,
//       };
// }

// To parse this JSON data, do
//
//     final negotiationReq = negotiationReqFromJson(jsonString);

import 'dart:convert';

NegotiationReq negotiationReqFromJson(String str) =>
    NegotiationReq.fromJson(json.decode(str));

String negotiationReqToJson(NegotiationReq data) => json.encode(data.toJson());

class NegotiationReq {
  int? negotiatedPrice;
  String? negotiationBuyrComment;
  String? negotiationMbmrComment;
  List<MediaRequest>? mediaRequests;
  num quantity;

  NegotiationReq({
    this.negotiatedPrice,
    this.negotiationBuyrComment,
    this.negotiationMbmrComment,
    this.mediaRequests,
    required this.quantity,
  });

  factory NegotiationReq.fromJson(Map<String, dynamic> json) => NegotiationReq(
        negotiatedPrice: json["negotiated_price"],
        negotiationBuyrComment: json["negotiation_buyr_comment"],
        negotiationMbmrComment: json["negotiation_mbmr_comment"],
        mediaRequests: json["media_requests"] == null
            ? []
            : List<MediaRequest>.from(
                json["media_requests"]!.map((x) => MediaRequest.fromJson(x))),
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "negotiated_price": negotiatedPrice,
        "negotiation_buyr_comment": negotiationBuyrComment,
        "negotiation_mbmr_comment": negotiationMbmrComment,
        "media_requests": mediaRequests == null
            ? []
            : List<dynamic>.from(mediaRequests!.map((x) => x.toJson())),
        "quantity": quantity,
      };
}

class MediaRequest {
  String? mediaTitle;
  String? url;
  String? previewUrl;

  MediaRequest({
    this.mediaTitle,
    this.url,
    this.previewUrl,
  });

  factory MediaRequest.fromJson(Map<String, dynamic> json) => MediaRequest(
        mediaTitle: json["media_title"],
        url: json["url"],
        previewUrl: json["preview_url"],
      );

  Map<String, dynamic> toJson() => {
        "media_title": mediaTitle,
        "url": url,
        "preview_url": previewUrl,
      };
}
