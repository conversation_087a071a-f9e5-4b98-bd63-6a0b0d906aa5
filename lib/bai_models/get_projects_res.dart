// To parse this JSON data, do
//
//     final getProjectsRes = getProjectsResFromJson(jsonString);

import 'dart:convert';

GetProjectsRes getProjectsResFromJson(String str) =>
    GetProjectsRes.fromJson(json.decode(str));

String getProjectsResToJson(GetProjectsRes data) => json.encode(data.toJson());

class GetProjectsRes {
  List<Project>? content;

  GetProjectsRes({
    this.content,
  });

  factory GetProjectsRes.fromJson(Map<String, dynamic> json) => GetProjectsRes(
        content: json["content"] == null
            ? []
            : List<Project>.from(
                json["content"]!.map((x) => Project.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
      };
}

class Project {
  int? id;
  Address? address;
  String? projectName;
  int? addressId;
  String? siteAccess;
  int? customerOrgsId;
  String? createdBy;
  String? customerName;

  Project({
    this.id,
    this.address,
    this.projectName,
    this.addressId,
    this.siteAccess,
    this.customerOrgsId,
    this.createdBy,
    this.customerName,
  });

  factory Project.fromJson(Map<String, dynamic> json) => Project(
        id: json["id"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        projectName: json["project_name"],
        addressId: json["address_id"],
        siteAccess: json["site_access"],
        customerOrgsId: json["customer_orgs_id"],
        createdBy: json["created_by"],
        customerName: json["customer_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "address": address?.toJson(),
        "project_name": projectName,
        "address_id": addressId,
        "site_access": siteAccess,
        "customer_orgs_id": customerOrgsId,
        "created_by": createdBy,
        "customer_name": customerName,
      };
}

class Address {
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;
  double? latitude;
  double? longitude;
  dynamic cityLatitude;
  dynamic cityLongitude;

  Address({
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLatitude,
    this.cityLongitude,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        sellingAddressLine1: json["selling_address_line1"],
        sellingAddressLine2: json["selling_address_line2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        cityLatitude: json["city_latitude"],
        cityLongitude: json["city_longitude"],
      );

  Map<String, dynamic> toJson() => {
        "selling_address_line1": sellingAddressLine1,
        "selling_address_line2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
        "city_latitude": cityLatitude,
        "city_longitude": cityLongitude,
      };
}
