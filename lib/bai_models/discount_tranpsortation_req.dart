// To parse this JSON data, do
//
//     final discountReq = discountReqFromJson(jsonString);

import 'dart:convert';

DiscountReq discountReqFromJson(String str) =>
    DiscountReq.fromJson(json.decode(str));

String discountReqToJson(DiscountReq data) => json.encode(data.toJson());

class DiscountReq {
  int? ordrGrpId;
  int? sellerId;
  double? discount;
  double? transportationCharges;
  double? loading;
  int categoryId;
  double? discountPercent;
  List<int>? offerIds;

  DiscountReq({
    this.ordrGrpId,
    this.sellerId,
    this.discount,
    this.transportationCharges,
    this.loading,
    required this.categoryId,
    this.discountPercent,
    this.offerIds,
  });

  factory DiscountReq.fromJson(Map<String, dynamic> json) => DiscountReq(
        ordrGrpId: json["ordrGrpId"],
        sellerId: json["sellerId"],
        discount: json["discount"],
        transportationCharges: json["transportationCharges"],
        loading: json["loading"],
        categoryId: json["categoryId"],
        discountPercent: json["discountPercent"],
        offerIds: json["offerId"],
      );

  Map<String, dynamic> toJson() => {
        "ordrGrpId": ordrGrpId,
        "sellerId": sellerId,
        "discount": discount,
        "transportationCharges": transportationCharges,
        "loading": loading,
        "categoryId": categoryId,
        "discountPercent": discountPercent,
        "offerIds":
            offerIds == null ? [] : List<dynamic>.from(offerIds!.map((x) => x)),
      };
}
