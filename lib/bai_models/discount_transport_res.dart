// To parse this JSON data, do
//
//     final discountTransportRes = discountTransportResFromJson(jsonString);

import 'dart:convert';

DiscountTransportRes discountTransportResFromJson(String str) =>
    DiscountTransportRes.fromJson(json.decode(str));

String discountTransportResToJson(DiscountTransportRes data) =>
    json.encode(data.toJson());

class DiscountTransportRes {
  Data? data;
  int? status;
  String? statusDescription;

  DiscountTransportRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory DiscountTransportRes.fromJson(Map<String, dynamic> json) =>
      DiscountTransportRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  int? id;
  int? ordrGrpId;
  int? sellerId;
  num? discount;
  num? transportationCharges;
  num? loadingCharges;
  num? noOfProducts;
  num? totalQuantity;
  num? totalPrice;
  num? loading;
  num? discountPercent;

  Data({
    this.id,
    this.ordrGrpId,
    this.sellerId,
    this.discount,
    this.transportationCharges,
    this.loadingCharges,
    this.noOfProducts,
    this.totalQuantity,
    this.totalPrice,
    this.loading,
    this.discountPercent,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        ordrGrpId: json["ordrGrpId"],
        sellerId: json["sellerId"],
        discount: json["discount"],
        transportationCharges: json["transportationCharges"],
        loadingCharges: json["loadingCharges"],
        noOfProducts: json["noOfProducts"],
        totalQuantity: json["totalQuantity"],
        totalPrice: json["totalPrice"],
        loading: json["loading"],
        discountPercent: json["discountPercent"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "ordrGrpId": ordrGrpId,
        "sellerId": sellerId,
        "discount": discount,
        "transportationCharges": transportationCharges,
        "loadingCharges": loadingCharges,
        "noOfProducts": noOfProducts,
        "totalQuantity": totalQuantity,
        "totalPrice": totalPrice,
        "discountPercent": discountPercent,
        "loading": loading,
      };
}
