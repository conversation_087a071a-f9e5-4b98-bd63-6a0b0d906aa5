// To parse this JSON data, do
//
//     final editProfileReq = editProfileReqFromJson(jsonString);

import 'dart:convert';

EditProfileReq editProfileReqFromJson(String str) =>
    EditProfileReq.fromJson(json.decode(str));

String editProfileReqTo<PERSON>son(EditProfileReq data) => json.encode(data.toJson());

class EditProfileReq {
  String? website;
  String? title;
  String? linkPrimaryKeyValue;
  String? linkPrimaryKeyName;
  String? url;
  String? previewUrl;
  String? docType;
  String? name;
  String? email;
  Address? address;
  String? businessEmail;
  String? businessPhone;
  String? gstType;

  EditProfileReq({
    this.website,
    this.title,
    this.linkPrimaryKeyValue,
    this.linkPrimaryKeyName,
    this.url,
    this.previewUrl,
    this.docType,
    this.name,
    this.email,
    this.address,
    this.businessEmail,
    this.businessPhone,
    this.gstType,
  });

  factory EditProfileReq.fromJson(Map<String, dynamic> json) => EditProfileReq(
        website: json["website"],
        title: json["title"],
        linkPrimaryKeyValue: json["linkPrimaryKeyValue"],
        linkPrimaryKeyName: json["linkPrimaryKeyName"],
        url: json["url"],
        previewUrl: json["previewUrl"],
        docType: json["docType"],
        name: json["name"],
        email: json["email"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
      );

  Map<String, dynamic> toJson() => {
        "website": website,
        "title": title,
        "linkPrimaryKeyValue": linkPrimaryKeyValue,
        "linkPrimaryKeyName": linkPrimaryKeyName,
        "url": url,
        "previewUrl": previewUrl,
        "docType": docType,
        "name": name,
        "email": email,
        "address": address?.toJson(),
        "companyEmail": businessEmail,
        "companyPhone": businessPhone,
        "gstFillingType": gstType,
      };
}

class Address {
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;

  Address({
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        sellingAddressLine1: json["selling_address_line1"],
        sellingAddressLine2: json["selling_address_line2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
      );

  Map<String, dynamic> toJson() => {
        "selling_address_line1": sellingAddressLine1,
        "selling_address_line2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
      };
}
