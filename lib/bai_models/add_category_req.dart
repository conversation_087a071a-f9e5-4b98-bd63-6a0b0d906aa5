import 'dart:convert';

class AddCategoryReq {
  List<CategoriesAndArea>? categoriesAndAreas;
  int? totalPrice;
  String? modeOfPayment;

  AddCategoryReq({
    this.categoriesAndAreas,
    this.totalPrice,
    this.modeOfPayment,
  });

  factory AddCategoryReq.fromRawJson(String str) =>
      AddCategoryReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddCategoryReq.fromJson(Map<String, dynamic> json) => AddCategoryReq(
        categoriesAndAreas: json["categories_and_areas"] == null
            ? []
            : List<CategoriesAndArea>.from(json["categories_and_areas"]!
                .map((x) => CategoriesAndArea.fromJson(x))),
        totalPrice: json["total_price"],
        modeOfPayment: json["mode_of_payment"],
      );

  Map<String, dynamic> toJson() => {
        "categories_and_areas": categoriesAndAreas == null
            ? []
            : List<dynamic>.from(categoriesAndAreas!.map((x) => x.toJson())),
        "total_price": totalPrice,
        "mode_of_payment": modeOfPayment,
      };
}

class CategoriesAndArea {
  dynamic areaOfBusinessId;
  int? categoryId;
  int? price;

  CategoriesAndArea({
    this.areaOfBusinessId,
    this.categoryId,
    this.price,
  });

  factory CategoriesAndArea.fromRawJson(String str) =>
      CategoriesAndArea.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CategoriesAndArea.fromJson(Map<String, dynamic> json) =>
      CategoriesAndArea(
        areaOfBusinessId: json["areaOfBusinessId"],
        categoryId: json["categoryId"],
        price: json["price"],
      );

  Map<String, dynamic> toJson() => {
        "areaOfBusinessId": areaOfBusinessId,
        "categoryId": categoryId,
        "price": price,
      };
}
