// To parse this JSON data, do
//
//     final customersListRes = customersListResFromJson(jsonString);

import 'dart:convert';

List<CustomersListRes> customersListResFromJson(String str) =>
    List<CustomersListRes>.from(
        json.decode(str).map((x) => CustomersListRes.fromJson(x)));

String customersListResToJson(List<CustomersListRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CustomersListRes {
  String? phone;
  String? name;
  String? id;
  String? designation;
  String? email;

  CustomersListRes({
    this.phone,
    this.name,
    this.id,
    this.designation,
    this.email,
  });

  factory CustomersListRes.fromJson(Map<String, dynamic> json) =>
      CustomersListRes(
        phone: json["phone"],
        name: json["name"],
        id: json["id"],
        designation: json["designation"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "phone": phone,
        "name": name,
        "id": id,
        "designation": designation,
        "email": email,
      };
}
