import 'dart:convert';

List<ProductItem> productItemFromJson(String str) => List<ProductItem>.from(
    json.decode(str).map((x) => ProductItem.fromJson(x)));

String productItemToJson(List<ProductItem> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ProductItem {
  num? id;
  String? eta;
  String? image;
  String? name;
  num? price;
  num? tax;
  num? discount;
  num? serviceItemFlag;
  num? rewardPoints;
  String? auctionTypeCd;
  String? pdctCd;
  String? prmySellCd;
  String? auctYn;
  Service? service;

  ProductItem({
    this.id,
    this.eta,
    this.image,
    this.name,
    this.price,
    this.tax,
    this.discount,
    this.serviceItemFlag,
    this.rewardPoints,
    this.auctionTypeCd,
    this.pdctCd,
    this.prmySellCd,
    this.auctYn,
    this.service,
  });

  factory ProductItem.fromJson(Map<String, dynamic> json) => ProductItem(
        id: json["id"],
        eta: json["eta"],
        image: json["image"],
        name: json["name"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        serviceItemFlag: json["serviceItemFlag"],
        rewardPoints: json["rewardPoints"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        prmySellCd: json["prmySellCd"],
        auctYn: json["auctYn"],
        service:
            json["service"] == null ? null : Service.fromJson(json["service"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "eta": eta,
        "image": image,
        "name": name,
        "price": price,
        "tax": tax,
        "discount": discount,
        "serviceItemFlag": serviceItemFlag,
        "rewardPoints": rewardPoints,
        "auctionTypeCd": auctionTypeCd,
        "pdctCd": pdctCd,
        "prmySellCd": prmySellCd,
        "auctYn": auctYn,
        "service": service?.toJson(),
      };
}

class Service {
  num? id;
  String? name;

  Service({
    this.id,
    this.name,
  });

  factory Service.fromJson(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
