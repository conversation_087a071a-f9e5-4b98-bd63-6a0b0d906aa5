class UploadMediaRes {
  int? status;
  String? statusDescription;
  String? fileUrl;
  String? previewFileUrl;

  UploadMediaRes({
    this.status,
    this.statusDescription,
    this.fileUrl,
    this.previewFileUrl,
  });

  factory UploadMediaRes.fromJson(Map<String, dynamic> json) {
    return UploadMediaRes(
      status: json['status'],
      statusDescription: json['status_description'],
      fileUrl: json['fileUrl'],
      previewFileUrl: json['previewFileUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'status_description': statusDescription,
      'fileUrl': fileUrl,
      'previewFileUrl': previewFileUrl,
    };
  }
}
