import 'dart:convert';

class EditAreaReq {
  int? categoryId;
  List<int>? newAreaOfBusinessId;

  EditAreaReq({
    this.categoryId,
    this.newAreaOfBusinessId,
  });

  factory EditAreaReq.fromRawJson(String str) =>
      EditAreaReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EditAreaReq.fromJson(Map<String, dynamic> json) => EditAreaReq(
        categoryId: json["categoryId"],
        newAreaOfBusinessId: json["newAreaOfBusinessId"] == null
            ? []
            : List<int>.from(json["newAreaOfBusinessId"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "categoryId": categoryId,
        "newAreaOfBusinessId": newAreaOfBusinessId == null
            ? []
            : List<dynamic>.from(newAreaOfBusinessId!.map((x) => x)),
      };
}
