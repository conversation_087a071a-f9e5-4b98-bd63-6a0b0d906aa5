import 'dart:convert';

class UserProfileV2Res {
  Customer? data;
  int? status;
  String? statusDescription;

  UserProfileV2Res({this.data, this.status, this.statusDescription});

  UserProfileV2Res.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Customer.fromJson(json['data']) : null;
    status = json['status'];
    statusDescription = json['status_description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status'] = status;
    data['status_description'] = statusDescription;
    return data;
  }
}

class Customer {
  int? id;
  String? name;
  String? primaryPhone;
  dynamic secondaryPhone;
  String? primaryPhoneCountryCode;
  dynamic secondaryPhoneCountryCode;
  String? email;
  dynamic address;
  String? addressLine1;
  String? addressLine2;
  int? totalOrder;
  double? totalAmount;
  int? totalReviews;
  double? averageRating;
  List<Medias>? medias;
  String? website;
  bool? baiMember;
  String? typeOfGstFiling;
  String? companyEmail;
  String? companyPhone;
  List<RegisteredCategoryDatum>? registeredCategoryData;
  String? areaOfSupply;

  Customer({
    this.id,
    this.name,
    this.primaryPhone,
    this.secondaryPhone,
    this.primaryPhoneCountryCode,
    this.secondaryPhoneCountryCode,
    this.email,
    this.address,
    this.addressLine1,
    this.addressLine2,
    this.totalOrder,
    this.totalReviews,
    this.averageRating,
    this.medias,
    this.website,
    this.baiMember,
    this.typeOfGstFiling,
    this.registeredCategoryData,
    this.areaOfSupply,
  });

  Customer.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    primaryPhone = json['primaryPhone'];
    secondaryPhone = json['secondaryPhone'];
    primaryPhoneCountryCode = json['primaryPhoneCountryCode'];
    secondaryPhoneCountryCode = json['secondaryPhoneCountryCode'];
    email = json['email'];
    address = json['address'];
    addressLine1 = json['addressLine1'];
    addressLine2 = json['addressLine2'];
    totalOrder = json['totalOrder'];
    totalReviews = json['totalReviews'];
    totalAmount = json['totalAmount'];
    averageRating = json['averageRating'];
    if (json['medias'] != null) {
      medias = <Medias>[];
      json['medias'].forEach((v) {
        medias!.add(Medias.fromJson(v));
      });
    }
    website = json['website'];
    baiMember = json['baiMember'];
    typeOfGstFiling = json['typeOfGstFiling'];
    companyEmail = json['companyEmail'];
    companyPhone = json['companyPhone'];
    registeredCategoryData = json["registeredCategoryData"] == null
        ? []
        : List<RegisteredCategoryDatum>.from(json["registeredCategoryData"]!
            .map((x) => RegisteredCategoryDatum.fromJson(x)));
    areaOfSupply = json["areaOfSupply"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['primaryPhone'] = primaryPhone;
    data['secondaryPhone'] = secondaryPhone;
    data['primaryPhoneCountryCode'] = primaryPhoneCountryCode;
    data['secondaryPhoneCountryCode'] = secondaryPhoneCountryCode;
    data['email'] = email;
    data['address'] = address;
    data['addressLine1'] = addressLine1;
    data['addressLine2'] = addressLine2;
    data['totalOrder'] = totalOrder;
    data['totalReviews'] = totalReviews;
    data['totalAmount'] = totalAmount;
    data['averageRating'] = averageRating;
    if (medias != null) {
      data['medias'] = medias!.map((v) => v.toJson()).toList();
    }
    data['website'] = website;
    data['baiMember'] = baiMember;
    data['typeOfGstFiling'] = typeOfGstFiling;
    data['companyEmail'] = companyEmail;
    data['companyPhone'] = companyPhone;

    return data;
  }
}

class Medias {
  dynamic linkPrimaryKeyName;
  dynamic linkPrimaryKeyValue;
  dynamic docType;
  dynamic previewUrl;
  dynamic url;
  String? title;

  Medias(
      {this.linkPrimaryKeyName,
      this.linkPrimaryKeyValue,
      this.docType,
      this.previewUrl,
      this.url,
      this.title});

  Medias.fromJson(Map<String, dynamic> json) {
    linkPrimaryKeyName = json['linkPrimaryKeyName'];
    linkPrimaryKeyValue = json['linkPrimaryKeyValue'];
    docType = json['docType'];
    previewUrl = json['previewUrl'];
    url = json['url'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['linkPrimaryKeyName'] = linkPrimaryKeyName;
    data['linkPrimaryKeyValue'] = linkPrimaryKeyValue;
    data['docType'] = docType;
    data['previewUrl'] = previewUrl;
    data['url'] = url;
    data['title'] = title;

    return data;
  }
}

class RegisteredCategoryDatum {
  String? categoryName;
  int? categoryId;
  String? price;
  DateTime? regDate;
  DateTime? expDate;
  List<String>? area;

  RegisteredCategoryDatum({
    this.categoryName,
    this.categoryId,
    this.price,
    this.regDate,
    this.expDate,
    this.area,
  });

  factory RegisteredCategoryDatum.fromRawJson(String str) =>
      RegisteredCategoryDatum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RegisteredCategoryDatum.fromJson(Map<String, dynamic> json) =>
      RegisteredCategoryDatum(
        categoryName: json["categoryName"],
        categoryId: json["categoryId"],
        price: json["price"],
        regDate:
            json["regDate"] == null ? null : DateTime.parse(json["regDate"]),
        expDate:
            json["expDate"] == null ? null : DateTime.parse(json["expDate"]),
        area: json["area"] == null
            ? []
            : List<String>.from(json["area"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "categoryName": categoryName,
        "categoryId": categoryId,
        "price": price,
        "regDate": regDate?.toIso8601String(),
        "expDate": expDate?.toIso8601String(),
        "area": area == null ? [] : List<dynamic>.from(area!.map((x) => x)),
      };
}
