// To parse this JSON data, do
//
//     final baiOfflineRes = baiOfflineResFromJson(jsonString);

import 'dart:convert';

BaiOfflineRes baiOfflineResFromJson(String str) =>
    BaiOfflineRes.fromJson(json.decode(str));

String baiOfflineResToJson(BaiOfflineRes data) => json.encode(data.toJson());

class BaiOfflineRes {
  List<Content>? content;
  Pageable? pageable;
  bool? last;
  num? totalElements;
  num? totalPages;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  BaiOfflineRes({
    this.content,
    this.pageable,
    this.last,
    this.totalElements,
    this.totalPages,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  factory BaiOfflineRes.fromJson(Map<String, dynamic> json) => BaiOfflineRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"].map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "last": last,
        "totalElements": totalElements,
        "totalPages": totalPages,
        "first": first,
        "numberOfElements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  num? id;
  dynamic projectName;
  dynamic projectId;
  String? productCode;
  String? auctionTypeCode;
  num? bidIncrementValue;
  num? serviceId;
  num? highestBidAmount;
  num? itemId;
  String? itemName;
  num? amount;
  String? lotNo;
  String? priceRange;
  String? grade;
  dynamic quality;
  num? expectedPrice;
  DateTime? deliveryDate;
  DateTime? biddingDate;
  num? quantity;
  num? unitId;
  String? unitName;
  String? unitShortName;
  num? customerId;
  num? buyerCustomerId;
  num? startingPrice;
  String? favouriteYn;
  String? image;
  String? image1Thumbnail;
  String? image1;
  dynamic image2Thumbnail;
  dynamic image2;
  dynamic image3Thumbnail;
  dynamic image3;
  dynamic image4Thumbnail;
  dynamic image4;
  dynamic image5Thumbnail;
  dynamic image5;
  num? stockStatusId;
  num? insertTransactionId;
  dynamic updateTransactionId;
  DateTime? createdAt;
  DateTime? modifiedAt;
  String? addressLine1;
  String? addressLine2;
  String? pickupCity;
  String? state;
  String? country;
  String? pincode;
  num? lat;
  num? lon;
  num? cityLat;
  num? cityLong;
  num? vendorId;
  String? name;
  num? roomId;
  num? stockId;
  String? stockStatusName;
  String? stockStatusCd;
  dynamic auctionCloseTs;
  num? buyBidPrice;
  num? averageRating;
  num? countOfRatings;
  num? uniqueViews;
  num? totalViews;
  String? activeYn;
  String? auctionYn;
  num? categoryId;
  String? productDescription;
  String? primarySellCode;

  Content({
    this.id,
    this.projectName,
    this.projectId,
    this.productCode,
    this.auctionTypeCode,
    this.bidIncrementValue,
    this.serviceId,
    this.highestBidAmount,
    this.itemId,
    this.itemName,
    this.amount,
    this.lotNo,
    this.priceRange,
    this.grade,
    this.quality,
    this.expectedPrice,
    this.deliveryDate,
    this.biddingDate,
    this.quantity,
    this.unitId,
    this.unitName,
    this.unitShortName,
    this.customerId,
    this.buyerCustomerId,
    this.startingPrice,
    this.favouriteYn,
    this.image,
    this.image1Thumbnail,
    this.image1,
    this.image2Thumbnail,
    this.image2,
    this.image3Thumbnail,
    this.image3,
    this.image4Thumbnail,
    this.image4,
    this.image5Thumbnail,
    this.image5,
    this.stockStatusId,
    this.insertTransactionId,
    this.updateTransactionId,
    this.createdAt,
    this.modifiedAt,
    this.addressLine1,
    this.addressLine2,
    this.pickupCity,
    this.state,
    this.country,
    this.pincode,
    this.lat,
    this.lon,
    this.cityLat,
    this.cityLong,
    this.vendorId,
    this.name,
    this.roomId,
    this.stockId,
    this.stockStatusName,
    this.stockStatusCd,
    this.auctionCloseTs,
    this.buyBidPrice,
    this.averageRating,
    this.countOfRatings,
    this.uniqueViews,
    this.totalViews,
    this.activeYn,
    this.auctionYn,
    this.categoryId,
    this.productDescription,
    this.primarySellCode,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        projectName: json["projectName"],
        projectId: json["projectId"],
        productCode: json["productCode"],
        auctionTypeCode: json["auctionTypeCode"],
        bidIncrementValue: json["bidIncrementValue"]?.toDouble(),
        serviceId: json["serviceId"],
        highestBidAmount: json["highestBidAmount"],
        itemId: json["itemId"],
        itemName: json["itemName"],
        amount: json["amount"],
        lotNo: json["lotNo"],
        priceRange: json["priceRange"],
        grade: json["grade"],
        quality: json["quality"],
        expectedPrice: json["expectedPrice"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        biddingDate: json["biddingDate"] == null
            ? null
            : DateTime.parse(json["biddingDate"]),
        quantity: json["quantity"],
        unitId: json["unitId"],
        unitName: json["unitName"],
        unitShortName: json["unitShortName"],
        customerId: json["customerId"],
        buyerCustomerId: json["buyerCustomerId"],
        startingPrice: json["startingPrice"],
        favouriteYn: json["favouriteYN"],
        image: json["image"],
        image1Thumbnail: json["image1Thumbnail"],
        image1: json["image1"],
        image2Thumbnail: json["image2Thumbnail"],
        image2: json["image2"],
        image3Thumbnail: json["image3Thumbnail"],
        image3: json["image3"],
        image4Thumbnail: json["image4Thumbnail"],
        image4: json["image4"],
        image5Thumbnail: json["image5Thumbnail"],
        image5: json["image5"],
        stockStatusId: json["stockStatusId"],
        insertTransactionId: json["insertTransactionId"],
        updateTransactionId: json["updateTransactionId"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        modifiedAt: json["modifiedAt"] == null
            ? null
            : DateTime.parse(json["modifiedAt"]),
        addressLine1: json["addressLine1"],
        addressLine2: json["addressLine2"],
        pickupCity: json["pickupCity"],
        state: json["state"],
        country: json["country"],
        pincode: json["pincode"],
        lat: json["lat"]?.toDouble(),
        lon: json["lon"]?.toDouble(),
        cityLat: json["cityLat"]?.toDouble(),
        cityLong: json["cityLong"]?.toDouble(),
        vendorId: json["vendorId"],
        name: json["name"],
        roomId: json["roomId"],
        stockId: json["stockId"],
        stockStatusName: json["stockStatusName"],
        stockStatusCd: json["stockStatusCd"],
        auctionCloseTs: json["auctionCloseTs"],
        buyBidPrice: json["buyBidPrice"],
        averageRating: json["averageRating"],
        countOfRatings: json["countOfRatings"],
        uniqueViews: json["uniqueViews"],
        totalViews: json["totalViews"],
        activeYn: json["activeYn"],
        auctionYn: json["auctionYn"],
        categoryId: json["categoryId"],
        productDescription: json["productDescription"],
        primarySellCode: json["primarySellCode"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "projectName": projectName,
        "projectId": projectId,
        "productCode": productCode,
        "auctionTypeCode": auctionTypeCode,
        "bidIncrementValue": bidIncrementValue,
        "serviceId": serviceId,
        "highestBidAmount": highestBidAmount,
        "itemId": itemId,
        "itemName": itemName,
        "amount": amount,
        "lotNo": lotNo,
        "priceRange": priceRange,
        "grade": grade,
        "quality": quality,
        "expectedPrice": expectedPrice,
        "deliveryDate":
            "${deliveryDate!.year.toString().padLeft(4, '0')}-${deliveryDate!.month.toString().padLeft(2, '0')}-${deliveryDate!.day.toString().padLeft(2, '0')}",
        "biddingDate":
            "${biddingDate!.year.toString().padLeft(4, '0')}-${biddingDate!.month.toString().padLeft(2, '0')}-${biddingDate!.day.toString().padLeft(2, '0')}",
        "quantity": quantity,
        "unitId": unitId,
        "unitName": unitName,
        "unitShortName": unitShortName,
        "customerId": customerId,
        "buyerCustomerId": buyerCustomerId,
        "startingPrice": startingPrice,
        "favouriteYN": favouriteYn,
        "image": image,
        "image1Thumbnail": image1Thumbnail,
        "image1": image1,
        "image2Thumbnail": image2Thumbnail,
        "image2": image2,
        "image3Thumbnail": image3Thumbnail,
        "image3": image3,
        "image4Thumbnail": image4Thumbnail,
        "image4": image4,
        "image5Thumbnail": image5Thumbnail,
        "image5": image5,
        "stockStatusId": stockStatusId,
        "insertTransactionId": insertTransactionId,
        "updateTransactionId": updateTransactionId,
        "createdAt":
            "${createdAt!.year.toString().padLeft(4, '0')}-${createdAt!.month.toString().padLeft(2, '0')}-${createdAt!.day.toString().padLeft(2, '0')}",
        "modifiedAt":
            "${modifiedAt!.year.toString().padLeft(4, '0')}-${modifiedAt!.month.toString().padLeft(2, '0')}-${modifiedAt!.day.toString().padLeft(2, '0')}",
        "addressLine1": addressLine1,
        "addressLine2": addressLine2,
        "pickupCity": pickupCity,
        "state": state,
        "country": country,
        "pincode": pincode,
        "lat": lat,
        "lon": lon,
        "cityLat": cityLat,
        "cityLong": cityLong,
        "vendorId": vendorId,
        "name": name,
        "roomId": roomId,
        "stockId": stockId,
        "stockStatusName": stockStatusName,
        "stockStatusCd": stockStatusCd,
        "auctionCloseTs": auctionCloseTs,
        "buyBidPrice": buyBidPrice,
        "averageRating": averageRating,
        "countOfRatings": countOfRatings,
        "uniqueViews": uniqueViews,
        "totalViews": totalViews,
        "activeYn": activeYn,
        "auctionYn": auctionYn,
        "categoryId": categoryId,
        "productDescription": productDescription,
        "primarySellCode": primarySellCode,
      };
}

class Pageable {
  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? unsorted;
  bool? sorted;
  bool? empty;

  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
