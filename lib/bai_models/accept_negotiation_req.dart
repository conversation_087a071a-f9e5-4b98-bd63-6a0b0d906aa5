// To parse this JSON data, do
//
//     final acceptNegotiationReq = acceptNegotiationReqFromJson(jsonString);

import 'dart:convert';

AcceptNegotiationReq acceptNegotiationReqFromJson(String str) =>
    AcceptNegotiationReq.fromJson(json.decode(str));

String acceptNegotiationReqToJson(AcceptNegotiationReq data) =>
    json.encode(data.toJson());

class AcceptNegotiationReq {
  String? negotiationMmbrComment;
  String? negotiationStatus;
  List<MediaRequest>? mediaRequests;

  AcceptNegotiationReq({
    this.negotiationMmbrComment,
    this.negotiationStatus,
    this.mediaRequests,
  });

  factory AcceptNegotiationReq.fromJson(Map<String, dynamic> json) =>
      AcceptNegotiationReq(
        negotiationMmbrComment: json["negotiation_mmbr_comment"],
        negotiationStatus: json["negotiation_status"],
        mediaRequests: json["media_requests"] == null
            ? []
            : List<MediaRequest>.from(
                json["media_requests"]!.map((x) => MediaRequest.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "negotiation_mbmr_comment": negotiationMmbrComment,
        "negotiation_status": negotiationStatus,
        "media_requests": mediaRequests == null
            ? []
            : List<dynamic>.from(mediaRequests!.map((x) => x.toJson())),
      };
}

class MediaRequest {
  String? mediaTitle;
  String? url;
  String? previewUrl;

  MediaRequest({
    this.mediaTitle,
    this.url,
    this.previewUrl,
  });

  factory MediaRequest.fromJson(Map<String, dynamic> json) => MediaRequest(
        mediaTitle: json["media_title"],
        url: json["url"],
        previewUrl: json["preview_url"],
      );

  Map<String, dynamic> toJson() => {
        "media_title": mediaTitle,
        "url": url,
        "preview_url": previewUrl,
      };
}
