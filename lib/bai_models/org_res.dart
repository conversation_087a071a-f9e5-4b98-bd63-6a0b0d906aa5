class Org {
  final int? orgId;
  final String? orgName;
  final String? orgCode;
  final int? customerId;
  final String? customerName;
  final String? customerPhone;

  Org({
    this.orgId,
    this.orgName,
    this.orgCode,
    this.customerId,
    this.customerName,
    this.customerPhone,
  });

  factory Org.fromJson(Map<String, dynamic> json) {
    return Org(
      orgId: json['org_id'],
      orgName: json['org_name'],
      orgCode: json['org_code'],
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      customerPhone: json['customer_phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'org_id': orgId,
      'org_name': orgName,
      'org_code': orgCode,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
    };
  }
}
