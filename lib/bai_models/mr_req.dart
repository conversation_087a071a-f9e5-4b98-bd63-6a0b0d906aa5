// To parse this JSON data, do
//
//     final mrReq = mrReqFromJson(jsonString);

import 'dart:convert';

MrReq mrReqFromJson(String str) => MrReq.fromJson(json.decode(str));

String mrReqToJson(MrReq data) => json.encode(data.toJson());

class MrReq {
  int? id;
  int? customerId;
  int? vendorId;
  int? mvtItemId;
  String? mvtItemName;
  double? price;
  DateTime? deliveryDate;
  int? projectId;
  String? siteAccess;
  String? projectName;
  String? roadAccess;
  int? quantity;
  int? projectAddressId;
  int? deliveryAddressId;
  String? statusCd;
  String? instructions;
  String? customerName;
  String? customerPhone;
  List<Variant>? variants;
  List<OptionsValue>? optionsValues;
  List<Media>? medias;
  bool? deleteAllMedias;
  String? cappCategoriesId;
  String? cappCategoriesName;

  MrReq({
    this.id,
    this.customerId,
    this.vendorId,
    this.mvtItemId,
    this.mvtItemName,
    this.price,
    this.deliveryDate,
    this.projectId,
    this.siteAccess,
    this.projectName,
    this.roadAccess,
    this.quantity,
    this.projectAddressId,
    this.deliveryAddressId,
    this.statusCd,
    this.instructions,
    this.customerName,
    this.customerPhone,
    this.variants,
    this.optionsValues,
    this.medias,
    this.deleteAllMedias,
    this.cappCategoriesId,
    this.cappCategoriesName,
  });

  factory MrReq.fromJson(Map<String, dynamic> json) => MrReq(
        id: json["id"],
        customerId: json["customerId"],
        vendorId: json["vendorId"],
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        price: json["price"]?.toDouble(),
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        projectId: json["projectId"],
        siteAccess: json["siteAccess"],
        projectName: json["projectName"],
        roadAccess: json["roadAccess"],
        quantity: json["quantity"],
        projectAddressId: json["projectAddressId"],
        deliveryAddressId: json["deliveryAddressId"],
        statusCd: json["statusCd"],
        instructions: json["instructions"],
        customerName: json["customerName"],
        customerPhone: json["customerPhone"],
        variants: json["variants"] == null
            ? []
            : List<Variant>.from(
                json["variants"]!.map((x) => Variant.fromJson(x))),
        optionsValues: json["optionsValues"] == null
            ? []
            : List<OptionsValue>.from(
                json["optionsValues"]!.map((x) => OptionsValue.fromJson(x))),
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        deleteAllMedias: json["deleteAllMedias"],
        cappCategoriesId: json["cappCategoriesId"],
        cappCategoriesName: json["cappCategoriesName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerId": customerId,
        "vendorId": vendorId,
        "mvtItemId": mvtItemId,
        "mvtItemName": mvtItemName,
        "price": price,
        "deliveryDate": deliveryDate?.toIso8601String(),
        "projectId": projectId,
        "siteAccess": siteAccess,
        "projectName": projectName,
        "roadAccess": roadAccess,
        "quantity": quantity,
        "projectAddressId": projectAddressId,
        "deliveryAddressId": deliveryAddressId,
        "statusCd": statusCd,
        "instructions": instructions,
        "customerName": customerName,
        "customerPhone": customerPhone,
        "variants": variants == null
            ? []
            : List<dynamic>.from(variants!.map((x) => x.toJson())),
        "optionsValues": optionsValues == null
            ? []
            : List<dynamic>.from(optionsValues!.map((x) => x.toJson())),
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "deleteAllMedias": deleteAllMedias,
        "cappCategoriesId": cappCategoriesId,
        "cappCategoriesName": cappCategoriesName,
      };
}

class Media {
  int? id;
  String? previewUrl;
  String? url;
  int? sequence;
  String? title;
  String? docType;
  bool? delete;

  Media({
    this.id,
    this.previewUrl,
    this.url,
    this.sequence,
    this.title,
    this.docType,
    this.delete,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        id: json["id"],
        previewUrl: json["previewUrl"],
        url: json["url"],
        sequence: json["sequence"],
        title: json["title"],
        docType: json["docType"],
        delete: json["delete"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "previewUrl": previewUrl,
        "url": url,
        "sequence": sequence,
        "title": title,
        "docType": docType,
        "delete": delete,
      };
}

class OptionsValue {
  int? id;
  int? optionGroupId;
  String? optionGroupName;
  int? optionId;
  String? optionName;
  bool? offerCheckBoolean;
  bool? delete;

  OptionsValue({
    this.id,
    this.optionGroupId,
    this.optionGroupName,
    this.optionId,
    this.optionName,
    this.offerCheckBoolean,
    this.delete,
  });

  factory OptionsValue.fromJson(Map<String, dynamic> json) => OptionsValue(
        id: json["id"],
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionId: json["optionId"],
        optionName: json["optionName"],
        offerCheckBoolean: json["offerCheckBoolean"],
        delete: json["delete"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "optionGroupId": optionGroupId,
        "optionGroupName": optionGroupName,
        "optionId": optionId,
        "optionName": optionName,
        "offerCheckBoolean": offerCheckBoolean,
        "delete": delete,
      };
}

class Variant {
  int? id;
  int? variant1OptionId;
  String? variant1OptionName;
  int? variant2OptionId;
  String? variant2OptionName;
  String? soldStatus;
  double? price;
  bool? delete;

  Variant({
    this.id,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.soldStatus,
    this.price,
    this.delete,
  });

  factory Variant.fromJson(Map<String, dynamic> json) => Variant(
        id: json["id"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        soldStatus: json["soldStatus"],
        price: json["price"]?.toDouble(),
        delete: json["delete"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "variant1OptionId": variant1OptionId,
        "variant1OptionName": variant1OptionName,
        "variant2OptionId": variant2OptionId,
        "variant2OptionName": variant2OptionName,
        "soldStatus": soldStatus,
        "price": price,
        "delete": delete,
      };
}
