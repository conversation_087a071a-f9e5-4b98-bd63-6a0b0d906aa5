import 'dart:convert';

SearchCategoryRes searchCategoryResFromJson(String str) =>
    SearchCategoryRes.fromJson(json.decode(str));

String searchCategoryResToJson(SearchCategoryRes data) =>
    json.encode(data.toJson());

class SearchCategoryRes {
  List<Content>? content;
  Pageable? pageable;
  num? totalPages;
  num? totalElements;
  bool? last;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  SearchCategoryRes({
    this.content,
    this.pageable,
    this.totalPages,
    this.totalElements,
    this.last,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  factory SearchCategoryRes.fromJson(Map<String, dynamic> json) =>
      SearchCategoryRes(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        totalPages: json["total_pages"],
        totalElements: json["total_elements"],
        last: json["last"],
        first: json["first"],
        numberOfElements: json["number_of_elements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "total_pages": totalPages,
        "total_elements": totalElements,
        "last": last,
        "first": first,
        "number_of_elements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  String? id;
  String? name;

  Content({
    this.id,
    this.name,
  });

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Pageable {
  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["page_number"],
        pageSize: json["page_size"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "page_number": pageNumber,
        "page_size": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  bool? unsorted;
  bool? sorted;
  bool? empty;

  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
