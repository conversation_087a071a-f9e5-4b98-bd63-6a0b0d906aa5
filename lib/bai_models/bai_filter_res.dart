// To parse this JSON data, do
//
//     final baiFilterRes = baiFilterResFromJson(jsonString);

import 'dart:convert';

BaiFilterRes baiFilterResFromJson(String str) =>
    BaiFilterRes.fromJson(json.decode(str));

String baiFilterResToJson(BaiFilterRes data) => json.encode(data.toJson());

class BaiFilterRes {
  List<Filter>? filters;
  List<Summary>? summary;

  BaiFilterRes({
    this.filters,
    this.summary,
  });

  factory BaiFilterRes.fromJson(Map<String, dynamic> json) => BaiFilterRes(
        filters: json["filters"] == null
            ? []
            : List<Filter>.from(
                json["filters"]!.map((x) => Filter.fromJson(x))),
        summary: json["summary"] == null
            ? []
            : List<Summary>.from(
                json["summary"]!.map((x) => Summary.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "filters": filters == null
            ? []
            : List<dynamic>.from(filters!.map((x) => x.toJson())),
        "summary": summary == null
            ? []
            : List<dynamic>.from(summary!.map((x) => x.toJson())),
      };
}

class Filter {
  String? filterName;
  String? filterDisplayName;
  int? sequence;
  String? type;
  List<Value>? values;

  Filter({
    this.filterName,
    this.filterDisplayName,
    this.sequence,
    this.type,
    this.values,
  });

  factory Filter.fromJson(Map<String, dynamic> json) => Filter(
        filterName: json["filterName"],
        filterDisplayName: json["filterDisplayName"],
        sequence: json["sequence"],
        type: json["type"],
        values: json["values"] == null
            ? []
            : List<Value>.from(json["values"]!.map((x) => Value.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "filterName": filterName,
        "filterDisplayName": filterDisplayName,
        "sequence": sequence,
        "type": type,
        "values": values == null
            ? []
            : List<dynamic>.from(values!.map((x) => x.toJson())),
      };
}

class Value {
  String? name;
  int? stockCount;
  int? id;

  Value({
    this.name,
    this.stockCount,
    this.id,
  });

  factory Value.fromJson(Map<String, dynamic> json) => Value(
        name: json["name"],
        stockCount: json["stockCount"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "stockCount": stockCount,
        "id": id,
      };
}

class Summary {
  Fields? fields;
  int? stockCount;

  Summary({
    this.fields,
    this.stockCount,
  });

  factory Summary.fromJson(Map<String, dynamic> json) => Summary(
        fields: json["fields"] == null ? null : Fields.fromJson(json["fields"]),
        stockCount: json["stock_count"],
      );

  Map<String, dynamic> toJson() => {
        "fields": fields?.toJson(),
        "stock_count": stockCount,
      };
}

class Fields {
  // String? projectName;
  // String? mvtItemName;
  // String? vendorName;
  // String? cappCategoriesName;

  num? projectId;
  String? projectIdName;
  num? cappCategoriesId;
  String? cappCategoriesIdName;
  num? vendorId;
  String? vendorIdName;
  num? mvtItemId;
  String? mvtItemIdName;
  DateTime? createdAt;
  DateTime? deliveryDate;

  Fields({
    // this.projectName,
    // this.mvtItemName,
    // this.vendorName,
    // this.cappCategoriesName,
    required this.projectId,
    required this.projectIdName,
    required this.cappCategoriesId,
    required this.cappCategoriesIdName,
    required this.vendorId,
    required this.vendorIdName,
    required this.mvtItemId,
    required this.mvtItemIdName,
    this.createdAt,
    this.deliveryDate,
  });

  factory Fields.fromJson(Map<String, dynamic> json) => Fields(
        // projectName: json["projectName"],
        // mvtItemName: json["mvtItemName"],
        // vendorName: json["vendorName"],
        // cappCategoriesName: json["cappCategoriesName"],
        projectId: json["projectId"],
        projectIdName: json["projectIdName"],
        cappCategoriesId: json["cappCategoriesId"],
        cappCategoriesIdName: json["cappCategoriesIdName"],
        vendorId: json["vendorId"],
        vendorIdName: json["vendorIdName"],
        mvtItemId: json["mvtItemId"],
        mvtItemIdName: json["mvtItemIdName"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        deliveryDate: json["delivery_date"] == null
            ? null
            : DateTime.parse(json["delivery_date"]),
      );

  Map<String, dynamic> toJson() => {
        // "projectName": projectName,
        // "mvtItemName": mvtItemName,
        // "vendorName": vendorName,
        // "cappCategoriesName": cappCategoriesName,
        "projectId": projectId,
        "projectIdName": projectIdName,
        "cappCategoriesId": cappCategoriesId,
        "cappCategoriesIdName": cappCategoriesIdName,
        "vendorId": vendorId,
        "vendorIdName": vendorIdName,
        "mvtItemId": mvtItemId,
        "mvtItemIdName": mvtItemIdName,
        "created_at": createdAt?.toIso8601String(),
        "delivery_date": deliveryDate?.toIso8601String(),
      };
}
