import 'dart:convert';

class ViewOfferReq {
  List<String> variant1OptionGroupName;
  List<String> variant1OptionNames;
  List<String> variant2OptionGroupName;
  List<String> variant2OptionNames;
  List<String> variant3OptionGroupName;
  List<String> variant3OptionNames;

  ViewOfferReq({
    required this.variant1OptionGroupName,
    required this.variant1OptionNames,
    required this.variant2OptionGroupName,
    required this.variant2OptionNames,
    required this.variant3OptionGroupName,
    required this.variant3OptionNames,
  });

  Map<String, dynamic> toMap() {
    return {
      'variant1OptionGroupName': variant1OptionGroupName,
      'variant1OptionNames': variant1OptionNames,
      'variant2OptionGroupName': variant2OptionGroupName,
      'variant2OptionNames': variant2OptionNames,
      'variant3OptionGroupName': variant3OptionGroupName,
      'variant3OptionNames': variant3OptionNames,
    };
  }

  String toJson() {
    return json.encode(toMap());
  }

  factory ViewOfferReq.fromMap(Map<String, dynamic> map) {
    return ViewOfferReq(
      variant1OptionGroupName:
          List<String>.from(map['variant1OptionGroupName']),
      variant1OptionNames: List<String>.from(map['variant1OptionNames']),
      variant2OptionGroupName:
          List<String>.from(map['variant2OptionGroupName']),
      variant2OptionNames: List<String>.from(map['variant2OptionNames']),
      variant3OptionGroupName:
          List<String>.from(map['variant3OptionGroupName']),
      variant3OptionNames: List<String>.from(map['variant3OptionNames']),
    );
  }

  factory ViewOfferReq.fromJson(String jsonString) {
    final Map<String, dynamic> map = json.decode(jsonString);
    return ViewOfferReq.fromMap(map);
  }
}
