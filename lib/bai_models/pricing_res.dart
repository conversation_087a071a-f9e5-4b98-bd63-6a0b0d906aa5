// To parse this JSON data, do
//
//     final pricingRes = pricingResFromJson(jsonString);

import 'dart:convert';

List<PricingRes> pricingResFromJson(String str) =>
    List<PricingRes>.from(json.decode(str).map((x) => PricingRes.fromJson(x)));

String pricingResToJson(List<PricingRes> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PricingRes {
  int? categoryId;
  String? group;
  String? categories;
  int? dealerRate1;
  int? manufacturerRate1;
  int? memberDealerRate1;
  int? memberManufacturerRate1;
  int? dealerRate2;
  int? manufacturerRate2;
  int? memberDealerRate2;
  int? memberManufacturerRate2;
  bool? allowInRegistration;

  PricingRes({
    this.categoryId,
    this.group,
    this.categories,
    this.dealerRate1,
    this.manufacturerRate1,
    this.memberDealerRate1,
    this.memberManufacturerRate1,
    this.dealerRate2,
    this.manufacturerRate2,
    this.memberDealerRate2,
    this.memberManufacturerRate2,
    this.allowInRegistration,
  });

  factory PricingRes.fromJson(Map<String, dynamic> json) => PricingRes(
        categoryId: json["category_id"],
        group: json["group"],
        categories: json["categories"],
        dealerRate1: json["dealer_rate1"],
        manufacturerRate1: json["manufacturer_rate1"],
        memberDealerRate1: json["member_dealer_rate1"],
        memberManufacturerRate1: json["member_manufacturer_rate1"],
        dealerRate2: json["dealer_rate2"],
        manufacturerRate2: json["manufacturer_rate2"],
        memberDealerRate2: json["member_dealer_rate2"],
        memberManufacturerRate2: json["member_manufacturer_rate2"],
        allowInRegistration: json["allow_in_registration"],
      );

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "group": group,
        "categories": categories,
        "dealer_rate1": dealerRate1,
        "manufacturer_rate1": manufacturerRate1,
        "member_dealer_rate1": memberDealerRate1,
        "member_manufacturer_rate1": memberManufacturerRate1,
        "dealer_rate2": dealerRate2,
        "manufacturer_rate2": manufacturerRate2,
        "member_dealer_rate2": memberDealerRate2,
        "member_manufacturer_rate2": memberManufacturerRate2,
        "allow_in_registration": allowInRegistration,
      };
}
