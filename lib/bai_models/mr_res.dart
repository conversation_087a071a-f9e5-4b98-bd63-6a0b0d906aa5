// To parse this JSON data, do
//
//     final mrRes = mrResFromJson(jsonString);

import 'dart:convert';

MrRes mrResFromJson(String str) => MrRes.fromJson(json.decode(str));

String mrResToJson(MrRes data) => json.encode(data.toJson());

class MrRes {
  PrchOrdr? prchOrdr;
  List<Variant>? variants;
  List<OptionsValue>? optionsValues;
  List<Media>? medias;
  Address? projectAddress;
  Address? deliveryAddress;
  Vendor? vendor;
  Customer? customer;
  ItemOffering? itemOffering;

  MrRes({
    this.prchOrdr,
    this.variants,
    this.optionsValues,
    this.medias,
    this.projectAddress,
    this.deliveryAddress,
    this.vendor,
    this.customer,
    this.itemOffering,
  });

  factory MrRes.fromJson(Map<String, dynamic> json) => MrRes(
        prchOrdr: json["prchOrdr"] == null
            ? null
            : PrchOrdr.fromJson(json["prchOrdr"]),
        variants: json["variants"] == null
            ? []
            : List<Variant>.from(
                json["variants"]!.map((x) => Variant.fromJson(x))),
        optionsValues: json["optionsValues"] == null
            ? []
            : List<OptionsValue>.from(
                json["optionsValues"]!.map((x) => OptionsValue.fromJson(x))),
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        projectAddress: json["projectAddress"] == null
            ? null
            : Address.fromJson(json["projectAddress"]),
        deliveryAddress: json["deliveryAddress"] == null
            ? null
            : Address.fromJson(json["deliveryAddress"]),
        vendor: json["vendor"] == null ? null : Vendor.fromJson(json["vendor"]),
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        itemOffering: json["itemOffering"] == null
            ? null
            : ItemOffering.fromJson(json["itemOffering"]),
      );

  Map<String, dynamic> toJson() => {
        "prchOrdr": prchOrdr?.toJson(),
        "variants": variants == null
            ? []
            : List<dynamic>.from(variants!.map((x) => x.toJson())),
        "optionsValues": optionsValues == null
            ? []
            : List<dynamic>.from(optionsValues!.map((x) => x.toJson())),
        "medias": medias == null
            ? []
            : List<dynamic>.from(medias!.map((x) => x.toJson())),
        "projectAddress": projectAddress?.toJson(),
        "deliveryAddress": deliveryAddress?.toJson(),
        "vendor": vendor?.toJson(),
        "customer": customer?.toJson(),
        "itemOffering": itemOffering?.toJson(),
      };
}

class Customer {
  num? id;
  String? userId;
  String? name;
  dynamic lastName;
  dynamic username;
  String? password;
  String? email;
  String? phone;
  dynamic officeNumber;
  dynamic landlineNumber;
  String? addressLine1;
  String? addressLine2;
  dynamic address;
  dynamic apartment;
  dynamic homeNumber;
  dynamic comments;
  dynamic city;
  String? state;
  String? countryCode;
  dynamic officeNumberCc;
  dynamic homeNumberCc;
  dynamic landlineNumberCc;
  String? vip;
  num? bidMaxAmount;
  num? genderId;
  bool? profileComplete;
  dynamic country;
  String? zip;
  dynamic lat;
  dynamic lon;
  num? rewardsPoints;
  dynamic fsGlobalAppUserId;
  dynamic deviseId;
  bool? verified;
  dynamic referredBy;
  num? vendorId;
  String? customerType;
  bool? indirectCustomer;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic lastVerifiedAt;
  List<dynamic>? lstAccess;
  bool? deletedFlag;
  DateTime? lastLoggedIn;
  dynamic van;
  dynamic vanBankName;
  dynamic bankAccountHolderName;
  dynamic bankIfscCode;
  dynamic bankAccountNo;
  String? customerGroupCd;
  num? userRoleId;
  num? orgId;
  String? currentLocation;
  String? designation;
  bool? isRealPhone;
  bool? isRealEmail;
  bool? isTestUser;
  dynamic createdFrom;
  dynamic vendor;
  String? customerApprovalStatusCd;
  num? customerApprovedBy;
  DateTime? customerApprovedAt;
  String? ustate;
  String? uname;
  String? uphone;
  String? uemail;
  String? uvendorName;
  String? uaddressLine1;
  String? uaddressLine2;

  Customer({
    this.id,
    this.userId,
    this.name,
    this.lastName,
    this.username,
    this.password,
    this.email,
    this.phone,
    this.officeNumber,
    this.landlineNumber,
    this.addressLine1,
    this.addressLine2,
    this.address,
    this.apartment,
    this.homeNumber,
    this.comments,
    this.city,
    this.state,
    this.countryCode,
    this.officeNumberCc,
    this.homeNumberCc,
    this.landlineNumberCc,
    this.vip,
    this.bidMaxAmount,
    this.genderId,
    this.profileComplete,
    this.country,
    this.zip,
    this.lat,
    this.lon,
    this.rewardsPoints,
    this.fsGlobalAppUserId,
    this.deviseId,
    this.verified,
    this.referredBy,
    this.vendorId,
    this.customerType,
    this.indirectCustomer,
    this.createdAt,
    this.modifiedAt,
    this.lastVerifiedAt,
    this.lstAccess,
    this.deletedFlag,
    this.lastLoggedIn,
    this.van,
    this.vanBankName,
    this.bankAccountHolderName,
    this.bankIfscCode,
    this.bankAccountNo,
    this.customerGroupCd,
    this.userRoleId,
    this.orgId,
    this.currentLocation,
    this.designation,
    this.isRealPhone,
    this.isRealEmail,
    this.isTestUser,
    this.createdFrom,
    this.vendor,
    this.customerApprovalStatusCd,
    this.customerApprovedBy,
    this.customerApprovedAt,
    this.ustate,
    this.uname,
    this.uphone,
    this.uemail,
    this.uvendorName,
    this.uaddressLine1,
    this.uaddressLine2,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        lastName: json["last_name"],
        username: json["username"],
        password: json["password"],
        email: json["email"],
        phone: json["phone"],
        officeNumber: json["office_number"],
        landlineNumber: json["landline_number"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        address: json["address"],
        apartment: json["apartment"],
        homeNumber: json["home_number"],
        comments: json["comments"],
        city: json["city"],
        state: json["state"],
        countryCode: json["country_code"],
        officeNumberCc: json["office_number_cc"],
        homeNumberCc: json["home_number_cc"],
        landlineNumberCc: json["landline_number_cc"],
        vip: json["vip"],
        bidMaxAmount: json["bidMaxAmount"],
        genderId: json["gender_id"],
        profileComplete: json["profile_complete"],
        country: json["country"],
        zip: json["zip"],
        lat: json["lat"],
        lon: json["lon"],
        rewardsPoints: json["rewards_points"],
        fsGlobalAppUserId: json["fs_global_app_user_id"],
        deviseId: json["devise_id"],
        verified: json["verified"],
        referredBy: json["referred_by"],
        vendorId: json["vendorId"],
        customerType: json["customerType"],
        indirectCustomer: json["indirect_customer"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        lastVerifiedAt: json["last_verified_at"],
        lstAccess: json["lstAccess"] == null
            ? []
            : List<dynamic>.from(json["lstAccess"]!.map((x) => x)),
        deletedFlag: json["deleted_flag"],
        lastLoggedIn: json["last_logged_in"] == null
            ? null
            : DateTime.parse(json["last_logged_in"]),
        van: json["van"],
        vanBankName: json["vanBankName"],
        bankAccountHolderName: json["bankAccountHolderName"],
        bankIfscCode: json["bankIfscCode"],
        bankAccountNo: json["bankAccountNo"],
        customerGroupCd: json["customerGroupCd"],
        userRoleId: json["userRoleId"],
        orgId: json["orgId"],
        currentLocation: json["current_location"],
        designation: json["designation"],
        isRealPhone: json["isRealPhone"],
        isRealEmail: json["isRealEmail"],
        isTestUser: json["isTestUser"],
        createdFrom: json["createdFrom"],
        vendor: json["vendor"],
        customerApprovalStatusCd: json["customerApprovalStatusCd"],
        customerApprovedBy: json["customerApprovedBy"],
        customerApprovedAt: json["customerApprovedAt"] == null
            ? null
            : DateTime.parse(json["customerApprovedAt"]),
        ustate: json["ustate"],
        uname: json["uname"],
        uphone: json["uphone"],
        uemail: json["uemail"],
        uvendorName: json["uvendorName"],
        uaddressLine1: json["uaddressLine1"],
        uaddressLine2: json["uaddressLine2"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "last_name": lastName,
        "username": username,
        "password": password,
        "email": email,
        "phone": phone,
        "office_number": officeNumber,
        "landline_number": landlineNumber,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "address": address,
        "apartment": apartment,
        "home_number": homeNumber,
        "comments": comments,
        "city": city,
        "state": state,
        "country_code": countryCode,
        "office_number_cc": officeNumberCc,
        "home_number_cc": homeNumberCc,
        "landline_number_cc": landlineNumberCc,
        "vip": vip,
        "bidMaxAmount": bidMaxAmount,
        "gender_id": genderId,
        "profile_complete": profileComplete,
        "country": country,
        "zip": zip,
        "lat": lat,
        "lon": lon,
        "rewards_points": rewardsPoints,
        "fs_global_app_user_id": fsGlobalAppUserId,
        "devise_id": deviseId,
        "verified": verified,
        "referred_by": referredBy,
        "vendorId": vendorId,
        "customerType": customerType,
        "indirect_customer": indirectCustomer,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "last_verified_at": lastVerifiedAt,
        "lstAccess": lstAccess == null
            ? []
            : List<dynamic>.from(lstAccess!.map((x) => x)),
        "deleted_flag": deletedFlag,
        "last_logged_in": lastLoggedIn?.toIso8601String(),
        "van": van,
        "vanBankName": vanBankName,
        "bankAccountHolderName": bankAccountHolderName,
        "bankIfscCode": bankIfscCode,
        "bankAccountNo": bankAccountNo,
        "customerGroupCd": customerGroupCd,
        "userRoleId": userRoleId,
        "orgId": orgId,
        "current_location": currentLocation,
        "designation": designation,
        "isRealPhone": isRealPhone,
        "isRealEmail": isRealEmail,
        "isTestUser": isTestUser,
        "createdFrom": createdFrom,
        "vendor": vendor,
        "customerApprovalStatusCd": customerApprovalStatusCd,
        "customerApprovedBy": customerApprovedBy,
        "customerApprovedAt": customerApprovedAt?.toIso8601String(),
        "ustate": ustate,
        "uname": uname,
        "uphone": uphone,
        "uemail": uemail,
        "uvendorName": uvendorName,
        "uaddressLine1": uaddressLine1,
        "uaddressLine2": uaddressLine2,
      };
}

class Address {
  num? id;
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;
  num? latitude;
  num? longitude;
  num? cityLan;
  num? cityLong;
  DateTime? createdAt;

  Address({
    this.id,
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLan,
    this.cityLong,
    this.createdAt,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        id: json["id"],
        sellingAddressLine1: json["sellingAddressLine1"],
        sellingAddressLine2: json["sellingAddressLine2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        cityLan: json["cityLan"]?.toDouble(),
        cityLong: json["cityLong"]?.toDouble(),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "sellingAddressLine1": sellingAddressLine1,
        "sellingAddressLine2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
        "cityLan": cityLan,
        "cityLong": cityLong,
        "createdAt": createdAt?.toIso8601String(),
      };
}

class ItemOffering {
  num? id;
  String? eta;
  String? image;
  String? name;
  num? price;
  num? tax;
  num? discount;
  num? serviceItemFlag;
  num? rewardPoints;
  String? auctionTypeCd;
  String? pdctCd;
  String? prmySellCd;
  String? auctYn;
  Service? service;

  ItemOffering({
    this.id,
    this.eta,
    this.image,
    this.name,
    this.price,
    this.tax,
    this.discount,
    this.serviceItemFlag,
    this.rewardPoints,
    this.auctionTypeCd,
    this.pdctCd,
    this.prmySellCd,
    this.auctYn,
    this.service,
  });

  factory ItemOffering.fromJson(Map<String, dynamic> json) => ItemOffering(
        id: json["id"],
        eta: json["eta"],
        image: json["image"],
        name: json["name"],
        price: json["price"],
        tax: json["tax"],
        discount: json["discount"],
        serviceItemFlag: json["serviceItemFlag"],
        rewardPoints: json["rewardPoints"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        prmySellCd: json["prmySellCd"],
        auctYn: json["auctYn"],
        service:
            json["service"] == null ? null : Service.fromJson(json["service"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "eta": eta,
        "image": image,
        "name": name,
        "price": price,
        "tax": tax,
        "discount": discount,
        "serviceItemFlag": serviceItemFlag,
        "rewardPoints": rewardPoints,
        "auctionTypeCd": auctionTypeCd,
        "pdctCd": pdctCd,
        "prmySellCd": prmySellCd,
        "auctYn": auctYn,
        "service": service?.toJson(),
      };
}

class Service {
  num? id;
  String? name;

  Service({
    this.id,
    this.name,
  });

  factory Service.fromJson(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Media {
  num? id;
  String? linkPrimaryKeyName;
  String? linkPrimaryKeyValue;
  dynamic previewUrl;
  String? url;
  num? sequence;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic title;
  dynamic docType;

  Media({
    this.id,
    this.linkPrimaryKeyName,
    this.linkPrimaryKeyValue,
    this.previewUrl,
    this.url,
    this.sequence,
    this.createdAt,
    this.modifiedAt,
    this.title,
    this.docType,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        id: json["id"],
        linkPrimaryKeyName: json["linkPrimaryKeyName"],
        linkPrimaryKeyValue: json["linkPrimaryKeyValue"],
        previewUrl: json["previewUrl"],
        url: json["url"],
        sequence: json["sequence"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        modifiedAt: json["modifiedAt"] == null
            ? null
            : DateTime.parse(json["modifiedAt"]),
        title: json["title"],
        docType: json["docType"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "linkPrimaryKeyName": linkPrimaryKeyName,
        "linkPrimaryKeyValue": linkPrimaryKeyValue,
        "previewUrl": previewUrl,
        "url": url,
        "sequence": sequence,
        "createdAt": createdAt?.toIso8601String(),
        "modifiedAt": modifiedAt?.toIso8601String(),
        "title": title,
        "docType": docType,
      };
}

class OptionsValue {
  num? id;
  num? prchOrdrId;
  num? optionGroupId;
  String? optionGroupName;
  num? optionId;
  String? optionName;
  bool? offerCheckBoolean;
  DateTime? createdAt;
  DateTime? updatedAt;

  OptionsValue({
    this.id,
    this.prchOrdrId,
    this.optionGroupId,
    this.optionGroupName,
    this.optionId,
    this.optionName,
    this.offerCheckBoolean,
    this.createdAt,
    this.updatedAt,
  });

  factory OptionsValue.fromJson(Map<String, dynamic> json) => OptionsValue(
        id: json["id"],
        prchOrdrId: json["prchOrdrId"],
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionId: json["optionId"],
        optionName: json["optionName"],
        offerCheckBoolean: json["offerCheckBoolean"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "prchOrdrId": prchOrdrId,
        "optionGroupId": optionGroupId,
        "optionGroupName": optionGroupName,
        "optionId": optionId,
        "optionName": optionName,
        "offerCheckBoolean": offerCheckBoolean,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}

class PrchOrdr {
  num? id;
  num? customerId;
  num? vendorId;
  num? mvtItemId;
  String? mvtItemName;
  dynamic price;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deliveryDate;
  num? projectId;
  String? siteAccess;
  String? projectName;
  String? roadAccess;
  num? quantity;
  num? projectAddressId;
  num? deliveryAddressId;
  dynamic createdCustomerDesignation;
  String? statusCd;
  num? cappCategoriesId;
  String? cappCategoriesName;
  String? vendorName;
  dynamic instructions;
  dynamic customerName;
  dynamic customerPhone;
  num? vendorRating;

  PrchOrdr({
    this.id,
    this.customerId,
    this.vendorId,
    this.mvtItemId,
    this.mvtItemName,
    this.price,
    this.createdAt,
    this.updatedAt,
    this.deliveryDate,
    this.projectId,
    this.siteAccess,
    this.projectName,
    this.roadAccess,
    this.quantity,
    this.projectAddressId,
    this.deliveryAddressId,
    this.createdCustomerDesignation,
    this.statusCd,
    this.cappCategoriesId,
    this.cappCategoriesName,
    this.vendorName,
    this.instructions,
    this.customerName,
    this.customerPhone,
    this.vendorRating,
  });

  factory PrchOrdr.fromJson(Map<String, dynamic> json) => PrchOrdr(
        id: json["id"],
        customerId: json["customerId"],
        vendorId: json["vendorId"],
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        price: json["price"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        projectId: json["projectId"],
        siteAccess: json["siteAccess"],
        projectName: json["projectName"],
        roadAccess: json["roadAccess"],
        quantity: json["quantity"],
        projectAddressId: json["projectAddressId"],
        deliveryAddressId: json["deliveryAddressId"],
        createdCustomerDesignation: json["createdCustomerDesignation"],
        statusCd: json["statusCd"],
        cappCategoriesId: json["cappCategoriesId"],
        cappCategoriesName: json["cappCategoriesName"],
        vendorName: json["vendorName"],
        instructions: json["instructions"],
        customerName: json["customerName"],
        customerPhone: json["customerPhone"],
        vendorRating: json["vendorRating"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerId": customerId,
        "vendorId": vendorId,
        "mvtItemId": mvtItemId,
        "mvtItemName": mvtItemName,
        "price": price,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "deliveryDate": deliveryDate?.toIso8601String(),
        "projectId": projectId,
        "siteAccess": siteAccess,
        "projectName": projectName,
        "roadAccess": roadAccess,
        "quantity": quantity,
        "projectAddressId": projectAddressId,
        "deliveryAddressId": deliveryAddressId,
        "createdCustomerDesignation": createdCustomerDesignation,
        "statusCd": statusCd,
        "cappCategoriesId": cappCategoriesId,
        "cappCategoriesName": cappCategoriesName,
        "vendorName": vendorName,
        "instructions": instructions,
        "customerName": customerName,
        "customerPhone": customerPhone,
        "vendorRating": vendorRating,
      };
}

class Variant {
  num? id;
  num? prchOrdrId;
  num? variant1OptionId;
  String? variant1OptionName;
  num? variant2OptionId;
  String? variant2OptionName;
  num? variant3OptionId;
  String? variant3OptionName;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  String? soldStatus;
  dynamic price;

  Variant({
    this.id,
    this.prchOrdrId,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.soldStatus,
    this.price,
  });

  factory Variant.fromJson(Map<String, dynamic> json) => Variant(
        id: json["id"],
        prchOrdrId: json["prchOrdrId"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        variant3OptionId: json["variant3OptionId"],
        variant3OptionName: json["variant3OptionName"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        soldStatus: json["soldStatus"],
        price: json["price"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "prchOrdrId": prchOrdrId,
        "variant1OptionId": variant1OptionId,
        "variant1OptionName": variant1OptionName,
        "variant2OptionId": variant2OptionId,
        "variant2OptionName": variant2OptionName,
        "variant3OptionId": variant3OptionId,
        "variant3OptionName": variant3OptionName,
        "variant1OptionGroupId": variant1OptionGroupId,
        "variant1OptionGroupName": variant1OptionGroupName,
        "variant2OptionGroupId": variant2OptionGroupId,
        "variant2OptionGroupName": variant2OptionGroupName,
        "variant3OptionGroupId": variant3OptionGroupId,
        "variant3OptionGroupName": variant3OptionGroupName,
        "soldStatus": soldStatus,
        "price": price,
      };
}

class Vendor {
  num? id;
  String? name;
  bool? subscriptionRequired;
  dynamic subscriptionStartedAt;
  dynamic subscriptionEndsOn;
  dynamic subscriptionAmount;
  dynamic subscriptionAmountPending;
  num? subscriptionAmountPaid;
  dynamic subscriptionAmountPaidOn;
  dynamic website;
  String? email;
  String? rating;
  dynamic address;
  String? addressLine1;
  String? addressLine2;
  String? primaryPhone;
  dynamic city;
  String? state;
  dynamic country;
  dynamic addressLat;
  dynamic addressLong;
  dynamic secondaryPhone;
  String? primaryPhoneCountryCode;
  dynamic secondaryPhoneCountryCode;
  dynamic officeNumber;
  dynamic officeNumberCc;
  dynamic homeNumber;
  dynamic homeNumberCc;
  dynamic landlineNumber;
  dynamic landlineNumberCc;
  dynamic whatsappNumber;
  dynamic whatsappNumberCc;
  dynamic fullAddressFromGmaps;
  dynamic shortDescription;
  dynamic longDescription;
  dynamic caption;
  dynamic sequence;
  dynamic totalRating;
  String? mondayFromTime;
  String? mondayToTime;
  String? mondayHoliday;
  String? tuesdayFromTime;
  String? tuesdayToTime;
  String? tuesdayHoliday;
  String? wednesdayFromTime;
  String? wednesdayToTime;
  String? wednesdayHoliday;
  String? thursdayFromTime;
  String? thursdayToTime;
  String? thursdayHoliday;
  String? fridayFromTime;
  String? fridayToTime;
  String? fridayHoliday;
  String? saturdayFromTime;
  String? saturdayToTime;
  String? saturdayHoliday;
  dynamic sundayFromTime;
  dynamic sundayToTime;
  String? sundayHoliday;
  dynamic defaultRouteId;
  dynamic defaultProductOfferingId;
  DateTime? createdAt;
  DateTime? modifiedAt;
  dynamic subdomain;
  dynamic adminPortalUrl;
  num? approvedBy;
  DateTime? approvedAt;
  bool? active;
  dynamic vendorCategoryId;
  dynamic abn;
  String? approvedYn;
  DateTime? acceptedTermsAndConditionsAt;
  dynamic profileComplete;
  dynamic referredBy;
  dynamic image;
  dynamic imageTitle;
  dynamic encryptIv;
  num? isAvailable;
  dynamic defaultRoomId;
  dynamic productListBanner;
  String? gstNumber;
  Address? shippingAddress;

  Vendor({
    this.id,
    this.name,
    this.subscriptionRequired,
    this.subscriptionStartedAt,
    this.subscriptionEndsOn,
    this.subscriptionAmount,
    this.subscriptionAmountPending,
    this.subscriptionAmountPaid,
    this.subscriptionAmountPaidOn,
    this.website,
    this.email,
    this.rating,
    this.address,
    this.addressLine1,
    this.addressLine2,
    this.primaryPhone,
    this.city,
    this.state,
    this.country,
    this.addressLat,
    this.addressLong,
    this.secondaryPhone,
    this.primaryPhoneCountryCode,
    this.secondaryPhoneCountryCode,
    this.officeNumber,
    this.officeNumberCc,
    this.homeNumber,
    this.homeNumberCc,
    this.landlineNumber,
    this.landlineNumberCc,
    this.whatsappNumber,
    this.whatsappNumberCc,
    this.fullAddressFromGmaps,
    this.shortDescription,
    this.longDescription,
    this.caption,
    this.sequence,
    this.totalRating,
    this.mondayFromTime,
    this.mondayToTime,
    this.mondayHoliday,
    this.tuesdayFromTime,
    this.tuesdayToTime,
    this.tuesdayHoliday,
    this.wednesdayFromTime,
    this.wednesdayToTime,
    this.wednesdayHoliday,
    this.thursdayFromTime,
    this.thursdayToTime,
    this.thursdayHoliday,
    this.fridayFromTime,
    this.fridayToTime,
    this.fridayHoliday,
    this.saturdayFromTime,
    this.saturdayToTime,
    this.saturdayHoliday,
    this.sundayFromTime,
    this.sundayToTime,
    this.sundayHoliday,
    this.defaultRouteId,
    this.defaultProductOfferingId,
    this.createdAt,
    this.modifiedAt,
    this.subdomain,
    this.adminPortalUrl,
    this.approvedBy,
    this.approvedAt,
    this.active,
    this.vendorCategoryId,
    this.abn,
    this.approvedYn,
    this.acceptedTermsAndConditionsAt,
    this.profileComplete,
    this.referredBy,
    this.image,
    this.imageTitle,
    this.encryptIv,
    this.isAvailable,
    this.defaultRoomId,
    this.productListBanner,
    this.gstNumber,
    this.shippingAddress,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        id: json["id"],
        name: json["name"],
        subscriptionRequired: json["subscriptionRequired"],
        subscriptionStartedAt: json["subscriptionStartedAt"],
        subscriptionEndsOn: json["subscriptionEndsOn"],
        subscriptionAmount: json["subscriptionAmount"],
        subscriptionAmountPending: json["subscriptionAmountPending"],
        subscriptionAmountPaid: json["subscriptionAmountPaid"],
        subscriptionAmountPaidOn: json["subscriptionAmountPaidOn"],
        website: json["website"],
        email: json["email"],
        rating: json["rating"],
        address: json["address"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        primaryPhone: json["primary_phone"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        addressLat: json["address_lat"],
        addressLong: json["address_long"],
        secondaryPhone: json["secondary_phone"],
        primaryPhoneCountryCode: json["primary_phone_country_code"],
        secondaryPhoneCountryCode: json["secondary_phone_country_code"],
        officeNumber: json["office_number"],
        officeNumberCc: json["office_number_cc"],
        homeNumber: json["home_number"],
        homeNumberCc: json["home_number_cc"],
        landlineNumber: json["landline_number"],
        landlineNumberCc: json["landline_number_cc"],
        whatsappNumber: json["whatsapp_number"],
        whatsappNumberCc: json["whatsapp_number_cc"],
        fullAddressFromGmaps: json["full_address_from_gmaps"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        caption: json["caption"],
        sequence: json["sequence"],
        totalRating: json["total_rating"],
        mondayFromTime: json["monday_from_time"],
        mondayToTime: json["monday_to_time"],
        mondayHoliday: json["monday_holiday"],
        tuesdayFromTime: json["tuesday_from_time"],
        tuesdayToTime: json["tuesday_to_time"],
        tuesdayHoliday: json["tuesday_holiday"],
        wednesdayFromTime: json["wednesday_from_time"],
        wednesdayToTime: json["wednesday_to_time"],
        wednesdayHoliday: json["wednesday_holiday"],
        thursdayFromTime: json["thursday_from_time"],
        thursdayToTime: json["thursday_to_time"],
        thursdayHoliday: json["thursday_holiday"],
        fridayFromTime: json["friday_from_time"],
        fridayToTime: json["friday_to_time"],
        fridayHoliday: json["friday_holiday"],
        saturdayFromTime: json["saturday_from_time"],
        saturdayToTime: json["saturday_to_time"],
        saturdayHoliday: json["saturday_holiday"],
        sundayFromTime: json["sunday_from_time"],
        sundayToTime: json["sunday_to_time"],
        sundayHoliday: json["sunday_holiday"],
        defaultRouteId: json["default_route_id"],
        defaultProductOfferingId: json["default_product_offering_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        modifiedAt: json["modified_at"] == null
            ? null
            : DateTime.parse(json["modified_at"]),
        subdomain: json["subdomain"],
        adminPortalUrl: json["admin_portal_url"],
        approvedBy: json["approved_by"],
        approvedAt: json["approved_at"] == null
            ? null
            : DateTime.parse(json["approved_at"]),
        active: json["active"],
        vendorCategoryId: json["vendor_category_id"],
        abn: json["abn"],
        approvedYn: json["approved_YN"],
        acceptedTermsAndConditionsAt:
            json["accepted_terms_and_conditions_at"] == null
                ? null
                : DateTime.parse(json["accepted_terms_and_conditions_at"]),
        profileComplete: json["profile_complete"],
        referredBy: json["referred_by"],
        image: json["image"],
        imageTitle: json["image_title"],
        encryptIv: json["encrypt_iv"],
        isAvailable: json["is_available"],
        defaultRoomId: json["default_room_id"],
        productListBanner: json["product_list_banner"],
        gstNumber: json["gst_number"],
        shippingAddress: json["shippingAddress"] == null
            ? null
            : Address.fromJson(json["shippingAddress"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "subscriptionRequired": subscriptionRequired,
        "subscriptionStartedAt": subscriptionStartedAt,
        "subscriptionEndsOn": subscriptionEndsOn,
        "subscriptionAmount": subscriptionAmount,
        "subscriptionAmountPending": subscriptionAmountPending,
        "subscriptionAmountPaid": subscriptionAmountPaid,
        "subscriptionAmountPaidOn": subscriptionAmountPaidOn,
        "website": website,
        "email": email,
        "rating": rating,
        "address": address,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "primary_phone": primaryPhone,
        "city": city,
        "state": state,
        "country": country,
        "address_lat": addressLat,
        "address_long": addressLong,
        "secondary_phone": secondaryPhone,
        "primary_phone_country_code": primaryPhoneCountryCode,
        "secondary_phone_country_code": secondaryPhoneCountryCode,
        "office_number": officeNumber,
        "office_number_cc": officeNumberCc,
        "home_number": homeNumber,
        "home_number_cc": homeNumberCc,
        "landline_number": landlineNumber,
        "landline_number_cc": landlineNumberCc,
        "whatsapp_number": whatsappNumber,
        "whatsapp_number_cc": whatsappNumberCc,
        "full_address_from_gmaps": fullAddressFromGmaps,
        "short_description": shortDescription,
        "long_description": longDescription,
        "caption": caption,
        "sequence": sequence,
        "total_rating": totalRating,
        "monday_from_time": mondayFromTime,
        "monday_to_time": mondayToTime,
        "monday_holiday": mondayHoliday,
        "tuesday_from_time": tuesdayFromTime,
        "tuesday_to_time": tuesdayToTime,
        "tuesday_holiday": tuesdayHoliday,
        "wednesday_from_time": wednesdayFromTime,
        "wednesday_to_time": wednesdayToTime,
        "wednesday_holiday": wednesdayHoliday,
        "thursday_from_time": thursdayFromTime,
        "thursday_to_time": thursdayToTime,
        "thursday_holiday": thursdayHoliday,
        "friday_from_time": fridayFromTime,
        "friday_to_time": fridayToTime,
        "friday_holiday": fridayHoliday,
        "saturday_from_time": saturdayFromTime,
        "saturday_to_time": saturdayToTime,
        "saturday_holiday": saturdayHoliday,
        "sunday_from_time": sundayFromTime,
        "sunday_to_time": sundayToTime,
        "sunday_holiday": sundayHoliday,
        "default_route_id": defaultRouteId,
        "default_product_offering_id": defaultProductOfferingId,
        "created_at": createdAt?.toIso8601String(),
        "modified_at": modifiedAt?.toIso8601String(),
        "subdomain": subdomain,
        "admin_portal_url": adminPortalUrl,
        "approved_by": approvedBy,
        "approved_at": approvedAt?.toIso8601String(),
        "active": active,
        "vendor_category_id": vendorCategoryId,
        "abn": abn,
        "approved_YN": approvedYn,
        "accepted_terms_and_conditions_at":
            acceptedTermsAndConditionsAt?.toIso8601String(),
        "profile_complete": profileComplete,
        "referred_by": referredBy,
        "image": image,
        "image_title": imageTitle,
        "encrypt_iv": encryptIv,
        "is_available": isAvailable,
        "default_room_id": defaultRoomId,
        "product_list_banner": productListBanner,
        "gst_number": gstNumber,
        "shippingAddress": shippingAddress?.toJson(),
      };
}
