// To parse this JSON data, do
//
//     final unitsRes = unitsResFromJson(jsonString);

import 'dart:convert';

UnitsRes unitsResFromJson(String str) => UnitsRes.fromJson(json.decode(str));

String unitsResToJson(UnitsRes data) => json.encode(data.toJson());

class UnitsRes {
  List<Datum>? data;
  int? status;
  String? statusDescription;

  UnitsRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory UnitsRes.fromJson(Map<String, dynamic> json) => UnitsRes(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  int? id;
  String? unitTypeCode;
  String? name;
  String? shortName;

  Datum({
    this.id,
    this.unitTypeCode,
    this.name,
    this.shortName,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        unitTypeCode: json["unit_type_code"],
        name: json["name"],
        shortName: json["short_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "unit_type_code": unitTypeCode,
        "name": name,
        "short_name": shortName,
      };
}
