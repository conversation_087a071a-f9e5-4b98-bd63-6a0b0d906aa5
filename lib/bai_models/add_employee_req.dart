import 'dart:convert';

class AddEmployeeReq {
  int? projectId;
  List<int>? employeeIds;
  int? assignedByCustomerId;

  AddEmployeeReq({
    this.projectId,
    this.employeeIds,
    this.assignedByCustomerId,
  });

  factory AddEmployeeReq.fromRawJson(String str) =>
      AddEmployeeReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AddEmployeeReq.fromJson(Map<String, dynamic> json) => AddEmployeeReq(
        projectId: json["projectId"],
        employeeIds: json["employeeIds"] == null
            ? []
            : List<int>.from(json["employeeIds"]!.map((x) => x)),
        assignedByCustomerId: json["assignedByCustomerId"],
      );

  Map<String, dynamic> toJson() => {
        "projectId": projectId,
        "employeeIds": employeeIds == null
            ? []
            : List<dynamic>.from(employeeIds!.map((x) => x)),
        "assignedByCustomerId": assignedByCustomerId,
      };
}
