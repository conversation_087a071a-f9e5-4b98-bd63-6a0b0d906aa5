import 'dart:convert';

ApprovePoReq approvePoReqFromJson(String str) =>
    ApprovePoReq.fromJson(json.decode(str));

String approvePoReqToJson(ApprovePoReq data) => json.encode(data.toJson());

class ApprovePoReq {
  List<int>? prchOrdrOfferIds;

  ApprovePoReq({
    this.prchOrdrOfferIds,
  });

  factory ApprovePoReq.fromJson(Map<String, dynamic> json) => ApprovePoReq(
        prchOrdrOfferIds: json["prch_ordr_offer_ids"] == null
            ? []
            : List<int>.from(json["prch_ordr_offer_ids"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "prch_ordr_offer_ids": prchOrdrOfferIds == null
            ? []
            : List<dynamic>.from(prchOrdrOfferIds!.map((x) => x)),
      };
}
