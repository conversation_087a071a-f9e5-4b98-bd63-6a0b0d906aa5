class AreaOfBusinessRes {
  List<AreaOfBusiness>? areas;

  AreaOfBusinessRes({this.areas});

  factory AreaOfBusinessRes.fromJson(List<dynamic> parsedJson) {
    return AreaOfBusinessRes(
      areas: parsedJson.map((json) => AreaOfBusiness.fromJson(json)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'areas': areas?.map((area) => area.toJson()).toList(),
    };
  }
}

class AreaOfBusiness {
  String? name;
  String? id;

  AreaOfBusiness({this.name, this.id});

  factory AreaOfBusiness.fromJson(Map<String, dynamic> json) {
    return AreaOfBusiness(
      name: json['name'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'id': id,
    };
  }
}
