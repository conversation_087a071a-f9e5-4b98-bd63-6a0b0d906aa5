// To parse this JSON data, do
//
//     final cloudSearchRes = cloudSearchResFromJson(jsonString);

import 'dart:convert';

CloudSearchRes cloudSearchResFromJson(String str) =>
    CloudSearchRes.fromJson(json.decode(str));

String cloudSearchResToJson(CloudSearchRes data) => json.encode(data.toJson());

class CloudSearchRes {
  Status? status;
  Hits? hits;

  CloudSearchRes({
    this.status,
    this.hits,
  });

  factory CloudSearchRes.fromJson(Map<String, dynamic> json) => CloudSearchRes(
        status: json["status"] == null ? null : Status.fromJson(json["status"]),
        hits: json["hits"] == null ? null : Hits.fromJson(json["hits"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status?.toJson(),
        "hits": hits?.toJson(),
      };
}

class Hits {
  int? found;
  int? start;
  List<Hit>? hit;

  Hits({
    this.found,
    this.start,
    this.hit,
  });

  factory Hits.fromJson(Map<String, dynamic> json) => Hits(
        found: json["found"],
        start: json["start"],
        hit: json["hit"] == null
            ? []
            : List<Hit>.from(json["hit"]!.map((x) => Hit.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "found": found,
        "start": start,
        "hit":
            hit == null ? [] : List<dynamic>.from(hit!.map((x) => x.toJson())),
      };
}

class Hit {
  String? id;
  Fields? fields;

  Hit({
    this.id,
    this.fields,
  });

  factory Hit.fromJson(Map<String, dynamic> json) => Hit(
        id: json["id"],
        fields: json["fields"] == null ? null : Fields.fromJson(json["fields"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "fields": fields?.toJson(),
      };
}

class Fields {
  String? item;
  String? unit;
  String? material;
  String? roadAccess;
  String? categoryId;
  String? unitId;
  String? brand;
  String? category;

  Fields({
    this.item,
    this.unit,
    this.material,
    this.roadAccess,
    this.categoryId,
    this.unitId,
    this.brand,
    this.category,
  });

  factory Fields.fromJson(Map<String, dynamic> json) => Fields(
        item: json["item"],
        unit: json["unit"],
        material: json["material"],
        roadAccess: json["road_access"],
        categoryId: json["category_id"],
        unitId: json["unit_id"],
        brand: json["brand"],
        category: json["category"],
      );

  Map<String, dynamic> toJson() => {
        "item": item,
        "unit": unit,
        "material": material,
        "road_access": roadAccess,
        "category_id": categoryId,
        "brand": brand,
        "category": category,
      };
}

class Status {
  String? rid;
  int? timeMs;

  Status({
    this.rid,
    this.timeMs,
  });

  factory Status.fromJson(Map<String, dynamic> json) => Status(
        rid: json["rid"],
        timeMs: json["time-ms"],
      );

  Map<String, dynamic> toJson() => {
        "rid": rid,
        "time-ms": timeMs,
      };
}
