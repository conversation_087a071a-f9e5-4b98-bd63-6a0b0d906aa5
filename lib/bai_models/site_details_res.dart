import 'dart:convert';

class SiteDetailsRes {
  int? id;
  Address? address;
  String? projectName;
  int? addressId;
  String? siteAccess;
  int? customerOrgsId;
  String? createdBy;
  String? customerName;
  String? projectStatus;
  List<EmployeeDetail>? employeeDetails;

  SiteDetailsRes({
    this.id,
    this.address,
    this.projectName,
    this.addressId,
    this.siteAccess,
    this.customerOrgsId,
    this.createdBy,
    this.customerName,
    this.projectStatus,
    this.employeeDetails,
  });

  factory SiteDetailsRes.fromRawJson(String str) =>
      SiteDetailsRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SiteDetailsRes.fromJson(Map<String, dynamic> json) => SiteDetailsRes(
        id: json["id"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        projectName: json["project_name"],
        addressId: json["address_id"],
        siteAccess: json["site_access"],
        customerOrgsId: json["customer_orgs_id"],
        createdBy: json["created_by"],
        customerName: json["customer_name"],
        projectStatus: json["project_status"],
        employeeDetails: json["employee_details"] == null
            ? []
            : List<EmployeeDetail>.from(json["employee_details"]!
                .map((x) => EmployeeDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "address": address?.toJson(),
        "project_name": projectName,
        "address_id": addressId,
        "site_access": siteAccess,
        "customer_orgs_id": customerOrgsId,
        "created_by": createdBy,
        "customer_name": customerName,
        "project_status": projectStatus,
        "employee_details": employeeDetails == null
            ? []
            : List<dynamic>.from(employeeDetails!.map((x) => x.toJson())),
      };
}

class Address {
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  String? state;
  String? pincode;
  double? latitude;
  double? longitude;
  double? cityLatitude;
  double? cityLongitude;

  Address({
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLatitude,
    this.cityLongitude,
  });

  factory Address.fromRawJson(String str) => Address.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        sellingAddressLine1: json["selling_address_line1"],
        sellingAddressLine2: json["selling_address_line2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        cityLatitude: json["city_latitude"]?.toDouble(),
        cityLongitude: json["city_longitude"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "selling_address_line1": sellingAddressLine1,
        "selling_address_line2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
        "city_latitude": cityLatitude,
        "city_longitude": cityLongitude,
      };
}

class EmployeeDetail {
  String? designation;
  String? employeeName;
  String? employeeId;
  String? phoneNo;
  String? employeeStatus;

  EmployeeDetail({
    this.designation,
    this.employeeName,
    this.employeeId,
    this.phoneNo,
    this.employeeStatus,
  });

  factory EmployeeDetail.fromRawJson(String str) =>
      EmployeeDetail.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmployeeDetail.fromJson(Map<String, dynamic> json) => EmployeeDetail(
        designation: json["designation"],
        employeeName: json["employee_name"],
        employeeId: json["employee_id"],
        phoneNo: json["phone_no"],
        employeeStatus: json["employee_status"],
      );

  Map<String, dynamic> toJson() => {
        "designation": designation,
        "employee_name": employeeName,
        "employee_id": employeeId,
        "phone_no": phoneNo,
        "employee_status": employeeStatus,
      };
}
