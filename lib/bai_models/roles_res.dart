class RolesRes {
  String? name;
  String? id;

  RolesRes({this.name, this.id});

  // Factory method to create a RolesRes object from JSON
  factory RolesRes.fromJson(Map<String, dynamic> json) {
    return RolesRes(
      name: json['name'] as String?,
      id: json['id'] as String?,
    );
  }

  // Method to convert a RolesRes object to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'id': id,
    };
  }
}
