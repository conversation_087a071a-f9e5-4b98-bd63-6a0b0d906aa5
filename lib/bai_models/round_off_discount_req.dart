import 'dart:convert';

class RoundOffDiscountReq {
  int? discountRoundOff;
  List<int>? offerIds;

  RoundOffDiscountReq({
    this.discountRoundOff,
    this.offerIds,
  });

  factory RoundOffDiscountReq.fromRawJson(String str) =>
      RoundOffDiscountReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RoundOffDiscountReq.fromJson(Map<String, dynamic> json) =>
      RoundOffDiscountReq(
        discountRoundOff: json["discountRoundOff"],
        offerIds: json["offerIds"] == null
            ? []
            : List<int>.from(json["offerIds"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "discountRoundOff": discountRoundOff,
        "offerIds":
            offerIds == null ? [] : List<dynamic>.from(offerIds!.map((x) => x)),
      };
}
