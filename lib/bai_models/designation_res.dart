class DesignationsRes {
  List<Designation>? designations;

  DesignationsRes({this.designations});

  factory DesignationsRes.fromJson(List<dynamic> json) {
    return DesignationsRes(
      designations: json.map((e) => Designation.fromJson(e)).toList(),
    );
  }

  List<dynamic> toJson() {
    return designations!.map((e) => e.toJson()).toList();
  }
}

class Designation {
  String? name;
  String? id;

  Designation({this.name, this.id});

  factory Designation.fromJson(Map<String, dynamic> json) {
    return Designation(
      name: json['name'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'id': id,
    };
  }
}
