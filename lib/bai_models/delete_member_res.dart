import 'dart:convert';

class DeleteMemberRes {
  int? id;
  String? rowType;
  int? rowId;
  bool? recoverable;
  DateTime? createdAt;
  DateTime? modifiedAt;
  int? createdCustomerId;
  String? createdCustomerType;
  String? createdCustomerName;
  String? rowName;

  DeleteMemberRes({
    this.id,
    this.rowType,
    this.rowId,
    this.recoverable,
    this.createdAt,
    this.modifiedAt,
    this.createdCustomerId,
    this.createdCustomerType,
    this.createdCustomerName,
    this.rowName,
  });

  factory DeleteMemberRes.fromRawJson(String str) =>
      DeleteMemberRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory DeleteMemberRes.fromJson(Map<String, dynamic> json) =>
      DeleteMemberRes(
        id: json["id"],
        rowType: json["rowType"],
        rowId: json["rowId"],
        recoverable: json["recoverable"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        modifiedAt: json["modifiedAt"] == null
            ? null
            : DateTime.parse(json["modifiedAt"]),
        createdCustomerId: json["createdCustomerId"],
        createdCustomerType: json["createdCustomerType"],
        createdCustomerName: json["createdCustomerName"],
        rowName: json["rowName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "rowType": rowType,
        "rowId": rowId,
        "recoverable": recoverable,
        "createdAt": createdAt?.toIso8601String(),
        "modifiedAt": modifiedAt?.toIso8601String(),
        "createdCustomerId": createdCustomerId,
        "createdCustomerType": createdCustomerType,
        "createdCustomerName": createdCustomerName,
        "rowName": rowName,
      };
}
