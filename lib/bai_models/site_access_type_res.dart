class SiteAccessType {
  String? value;
  String? key;

  SiteAccessType({this.value, this.key});

  factory SiteAccessType.fromJson(Map<String, dynamic> json) {
    return SiteAccessType(
      value: json['value'],
      key: json['key'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'key': key,
    };
  }
}

class SiteAccessTypeList {
  List<SiteAccessType>? roadAccessTypes;

  SiteAccessTypeList({this.roadAccessTypes});

  factory SiteAccessTypeList.fromJson(List<dynamic> json) {
    return SiteAccessTypeList(
      roadAccessTypes: json.map((e) => SiteAccessType.fromJson(e)).toList(),
    );
  }

  List<dynamic> toJson() {
    return roadAccessTypes?.map((e) => e.toJson()).toList() ?? [];
  }
}
