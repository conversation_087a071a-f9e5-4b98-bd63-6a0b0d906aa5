// To parse this JSON data, do
//
//     final roundOffDiscountRes = roundOffDiscountResFromJson(jsonString);

import 'dart:convert';

RoundOffDiscountRes roundOffDiscountResFromJson(String str) =>
    RoundOffDiscountRes.fromJson(json.decode(str));

String roundOffDiscountResToJson(RoundOffDiscountRes data) =>
    json.encode(data.toJson());

class RoundOffDiscountRes {
  Data? data;
  int? status;
  String? statusDescription;

  RoundOffDiscountRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory RoundOffDiscountRes.fromJson(Map<String, dynamic> json) =>
      RoundOffDiscountRes(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  double? discountRoundOff;

  Data({
    this.discountRoundOff,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        discountRoundOff: json["discountRoundOff"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "discountRoundOff": discountRoundOff,
      };
}
