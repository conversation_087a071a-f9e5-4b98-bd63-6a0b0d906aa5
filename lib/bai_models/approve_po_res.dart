// import 'dart:convert';

// ApprovePoRes approvePoResFromJson(String str) =>
//     ApprovePoRes.fromJson(json.decode(str));

// String approvePoResToJson(ApprovePoRes data) => json.encode(data.toJson());

// class ApprovePoRes {
//   Data? data;
//   num? status;
//   String? statusDescription;

//   ApprovePoRes({
//     this.data,
//     this.status,
//     this.statusDescription,
//   });

//   factory ApprovePoRes.fromJson(Map<String, dynamic> json) => ApprovePoRes(
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//         status: json["status"],
//         statusDescription: json["status_description"],
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//         "status": status,
//         "status_description": statusDescription,
//       };
// }

// class Data {
//   num? id;
//   num? prchOrdrId;
//   String? statusCd;
//   num? projectId;
//   String? projectName;
//   num? projectAddressId;
//   String? projectAddressLine1;
//   String? projectAddressLine2;
//   String? projectCity;
//   String? projectCountry;
//   String? projectState;
//   String? projectPincode;
//   num? projectLatitude;
//   num? projectLongitude;
//   num? projectCityLan;
//   num? projectCityLong;
//   String? siteAccess;
//   String? roadAccess;
//   DateTime? deliveryDate;
//   num? buyerCustomerId;
//   num? buyerVendorId;
//   num? deliveryAddressId;
//   String? deliveryAddressLine1;
//   String? deliveryAddressLine2;
//   String? deliveryCity;
//   String? deliveryCountry;
//   String? deliveryState;
//   String? deliveryPincode;
//   num? deliveryLatitude;
//   num? deliveryLongitude;
//   num? deliveryCityLan;
//   num? deliveryCityLong;
//   num? mvtItemId;
//   String? mvtItemName;
//   num? quantity;
//   dynamic price;
//   dynamic cappCategoriesId;
//   dynamic cappCategoriesName;
//   dynamic vendorName;
//   dynamic image1Thumbnail;
//   dynamic image1;
//   dynamic image2Thumbnail;
//   dynamic image2;
//   dynamic image3Thumbnail;
//   dynamic image3;
//   dynamic image4Thumbnail;
//   dynamic image4;
//   dynamic image5Thumbnail;
//   dynamic image5;
//   num? sellerCustomerId;
//   num? sellerVendorId;
//   num? optionGroupId;
//   String? optionGroupName;
//   String? optionName;
//   num? optionId;
//   num? offerPrice;
//   num? variant1OptionId;
//   String? variant1OptionName;
//   num? variant2OptionId;
//   String? variant2OptionName;
//   num? variant3OptionId;
//   String? variant3OptionName;
//   num? variant1OptionGroupId;
//   String? variant1OptionGroupName;
//   num? variant2OptionGroupId;
//   String? variant2OptionGroupName;
//   num? variant3OptionGroupId;
//   String? variant3OptionGroupName;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   String? sellerCustomerName;
//   String? sellerVendorName;
//   String? sellerCustomerPhone;
//   String? buyerCustomerName;
//   String? buyerVendorName;
//   String? buyerCustomerPhone;
//   num? sellerVendorRating;
//   num? buyerVendorRating;
//   dynamic instructions;
//   String? confirmedOfferRemarks;
//   num? confirmedOfferQuantity;

//   Data({
//     this.id,
//     this.prchOrdrId,
//     this.statusCd,
//     this.projectId,
//     this.projectName,
//     this.projectAddressId,
//     this.projectAddressLine1,
//     this.projectAddressLine2,
//     this.projectCity,
//     this.projectCountry,
//     this.projectState,
//     this.projectPincode,
//     this.projectLatitude,
//     this.projectLongitude,
//     this.projectCityLan,
//     this.projectCityLong,
//     this.siteAccess,
//     this.roadAccess,
//     this.deliveryDate,
//     this.buyerCustomerId,
//     this.buyerVendorId,
//     this.deliveryAddressId,
//     this.deliveryAddressLine1,
//     this.deliveryAddressLine2,
//     this.deliveryCity,
//     this.deliveryCountry,
//     this.deliveryState,
//     this.deliveryPincode,
//     this.deliveryLatitude,
//     this.deliveryLongitude,
//     this.deliveryCityLan,
//     this.deliveryCityLong,
//     this.mvtItemId,
//     this.mvtItemName,
//     this.quantity,
//     this.price,
//     this.cappCategoriesId,
//     this.cappCategoriesName,
//     this.vendorName,
//     this.image1Thumbnail,
//     this.image1,
//     this.image2Thumbnail,
//     this.image2,
//     this.image3Thumbnail,
//     this.image3,
//     this.image4Thumbnail,
//     this.image4,
//     this.image5Thumbnail,
//     this.image5,
//     this.sellerCustomerId,
//     this.sellerVendorId,
//     this.optionGroupId,
//     this.optionGroupName,
//     this.optionName,
//     this.optionId,
//     this.offerPrice,
//     this.variant1OptionId,
//     this.variant1OptionName,
//     this.variant2OptionId,
//     this.variant2OptionName,
//     this.variant3OptionId,
//     this.variant3OptionName,
//     this.variant1OptionGroupId,
//     this.variant1OptionGroupName,
//     this.variant2OptionGroupId,
//     this.variant2OptionGroupName,
//     this.variant3OptionGroupId,
//     this.variant3OptionGroupName,
//     this.createdAt,
//     this.updatedAt,
//     this.sellerCustomerName,
//     this.sellerVendorName,
//     this.sellerCustomerPhone,
//     this.buyerCustomerName,
//     this.buyerVendorName,
//     this.buyerCustomerPhone,
//     this.sellerVendorRating,
//     this.buyerVendorRating,
//     this.instructions,
//     this.confirmedOfferRemarks,
//     this.confirmedOfferQuantity,
//   });

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         id: json["id"],
//         prchOrdrId: json["prchOrdrId"],
//         statusCd: json["statusCd"],
//         projectId: json["projectId"],
//         projectName: json["projectName"],
//         projectAddressId: json["projectAddressId"],
//         projectAddressLine1: json["projectAddressLine1"],
//         projectAddressLine2: json["projectAddressLine2"],
//         projectCity: json["projectCity"],
//         projectCountry: json["projectCountry"],
//         projectState: json["projectState"],
//         projectPincode: json["projectPincode"],
//         projectLatitude: json["projectLatitude"]?.toDouble(),
//         projectLongitude: json["projectLongitude"]?.toDouble(),
//         projectCityLan: json["projectCityLan"]?.toDouble(),
//         projectCityLong: json["projectCityLong"]?.toDouble(),
//         siteAccess: json["siteAccess"],
//         roadAccess: json["roadAccess"],
//         deliveryDate: json["deliveryDate"] == null
//             ? null
//             : DateTime.parse(json["deliveryDate"]),
//         buyerCustomerId: json["buyerCustomerId"],
//         buyerVendorId: json["buyerVendorId"],
//         deliveryAddressId: json["deliveryAddressId"],
//         deliveryAddressLine1: json["deliveryAddressLine1"],
//         deliveryAddressLine2: json["deliveryAddressLine2"],
//         deliveryCity: json["deliveryCity"],
//         deliveryCountry: json["deliveryCountry"],
//         deliveryState: json["deliveryState"],
//         deliveryPincode: json["deliveryPincode"],
//         deliveryLatitude: json["deliveryLatitude"]?.toDouble(),
//         deliveryLongitude: json["deliveryLongitude"]?.toDouble(),
//         deliveryCityLan: json["deliveryCityLan"]?.toDouble(),
//         deliveryCityLong: json["deliveryCityLong"]?.toDouble(),
//         mvtItemId: json["mvtItemId"],
//         mvtItemName: json["mvtItemName"],
//         quantity: json["quantity"],
//         price: json["price"],
//         cappCategoriesId: json["cappCategoriesId"],
//         cappCategoriesName: json["cappCategoriesName"],
//         vendorName: json["vendorName"],
//         image1Thumbnail: json["image1Thumbnail"],
//         image1: json["image1"],
//         image2Thumbnail: json["image2Thumbnail"],
//         image2: json["image2"],
//         image3Thumbnail: json["image3Thumbnail"],
//         image3: json["image3"],
//         image4Thumbnail: json["image4Thumbnail"],
//         image4: json["image4"],
//         image5Thumbnail: json["image5Thumbnail"],
//         image5: json["image5"],
//         sellerCustomerId: json["sellerCustomerId"],
//         sellerVendorId: json["sellerVendorId"],
//         optionGroupId: json["optionGroupId"],
//         optionGroupName: json["optionGroupName"],
//         optionName: json["optionName"],
//         optionId: json["optionId"],
//         offerPrice: json["offerPrice"],
//         variant1OptionId: json["variant1OptionId"],
//         variant1OptionName: json["variant1OptionName"],
//         variant2OptionId: json["variant2OptionId"],
//         variant2OptionName: json["variant2OptionName"],
//         variant3OptionId: json["variant3OptionId"],
//         variant3OptionName: json["variant3OptionName"],
//         variant1OptionGroupId: json["variant1OptionGroupId"],
//         variant1OptionGroupName: json["variant1OptionGroupName"],
//         variant2OptionGroupId: json["variant2OptionGroupId"],
//         variant2OptionGroupName: json["variant2OptionGroupName"],
//         variant3OptionGroupId: json["variant3OptionGroupId"],
//         variant3OptionGroupName: json["variant3OptionGroupName"],
//         createdAt: json["createdAt"] == null
//             ? null
//             : DateTime.parse(json["createdAt"]),
//         updatedAt: json["updatedAt"] == null
//             ? null
//             : DateTime.parse(json["updatedAt"]),
//         sellerCustomerName: json["sellerCustomerName"],
//         sellerVendorName: json["sellerVendorName"],
//         sellerCustomerPhone: json["sellerCustomerPhone"],
//         buyerCustomerName: json["buyerCustomerName"],
//         buyerVendorName: json["buyerVendorName"],
//         buyerCustomerPhone: json["buyerCustomerPhone"],
//         sellerVendorRating: json["sellerVendorRating"],
//         buyerVendorRating: json["buyerVendorRating"],
//         instructions: json["instructions"],
//         confirmedOfferRemarks: json["confirmedOfferRemarks"],
//         confirmedOfferQuantity: json["confirmedOfferQuantity"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "prchOrdrId": prchOrdrId,
//         "statusCd": statusCd,
//         "projectId": projectId,
//         "projectName": projectName,
//         "projectAddressId": projectAddressId,
//         "projectAddressLine1": projectAddressLine1,
//         "projectAddressLine2": projectAddressLine2,
//         "projectCity": projectCity,
//         "projectCountry": projectCountry,
//         "projectState": projectState,
//         "projectPincode": projectPincode,
//         "projectLatitude": projectLatitude,
//         "projectLongitude": projectLongitude,
//         "projectCityLan": projectCityLan,
//         "projectCityLong": projectCityLong,
//         "siteAccess": siteAccess,
//         "roadAccess": roadAccess,
//         "deliveryDate": deliveryDate?.toIso8601String(),
//         "buyerCustomerId": buyerCustomerId,
//         "buyerVendorId": buyerVendorId,
//         "deliveryAddressId": deliveryAddressId,
//         "deliveryAddressLine1": deliveryAddressLine1,
//         "deliveryAddressLine2": deliveryAddressLine2,
//         "deliveryCity": deliveryCity,
//         "deliveryCountry": deliveryCountry,
//         "deliveryState": deliveryState,
//         "deliveryPincode": deliveryPincode,
//         "deliveryLatitude": deliveryLatitude,
//         "deliveryLongitude": deliveryLongitude,
//         "deliveryCityLan": deliveryCityLan,
//         "deliveryCityLong": deliveryCityLong,
//         "mvtItemId": mvtItemId,
//         "mvtItemName": mvtItemName,
//         "quantity": quantity,
//         "price": price,
//         "cappCategoriesId": cappCategoriesId,
//         "cappCategoriesName": cappCategoriesName,
//         "vendorName": vendorName,
//         "image1Thumbnail": image1Thumbnail,
//         "image1": image1,
//         "image2Thumbnail": image2Thumbnail,
//         "image2": image2,
//         "image3Thumbnail": image3Thumbnail,
//         "image3": image3,
//         "image4Thumbnail": image4Thumbnail,
//         "image4": image4,
//         "image5Thumbnail": image5Thumbnail,
//         "image5": image5,
//         "sellerCustomerId": sellerCustomerId,
//         "sellerVendorId": sellerVendorId,
//         "optionGroupId": optionGroupId,
//         "optionGroupName": optionGroupName,
//         "optionName": optionName,
//         "optionId": optionId,
//         "offerPrice": offerPrice,
//         "variant1OptionId": variant1OptionId,
//         "variant1OptionName": variant1OptionName,
//         "variant2OptionId": variant2OptionId,
//         "variant2OptionName": variant2OptionName,
//         "variant3OptionId": variant3OptionId,
//         "variant3OptionName": variant3OptionName,
//         "variant1OptionGroupId": variant1OptionGroupId,
//         "variant1OptionGroupName": variant1OptionGroupName,
//         "variant2OptionGroupId": variant2OptionGroupId,
//         "variant2OptionGroupName": variant2OptionGroupName,
//         "variant3OptionGroupId": variant3OptionGroupId,
//         "variant3OptionGroupName": variant3OptionGroupName,
//         "createdAt": createdAt?.toIso8601String(),
//         "updatedAt": updatedAt?.toIso8601String(),
//         "sellerCustomerName": sellerCustomerName,
//         "sellerVendorName": sellerVendorName,
//         "sellerCustomerPhone": sellerCustomerPhone,
//         "buyerCustomerName": buyerCustomerName,
//         "buyerVendorName": buyerVendorName,
//         "buyerCustomerPhone": buyerCustomerPhone,
//         "sellerVendorRating": sellerVendorRating,
//         "buyerVendorRating": buyerVendorRating,
//         "instructions": instructions,
//         "confirmedOfferRemarks": confirmedOfferRemarks,
//         "confirmedOfferQuantity": confirmedOfferQuantity,
//       };
// }

// To parse this JSON data, do
//
//     final approvePoRes = approvePoResFromJson(jsonString);

import 'dart:convert';

ApprovePoRes approvePoResFromJson(String str) =>
    ApprovePoRes.fromJson(json.decode(str));

String approvePoResToJson(ApprovePoRes data) => json.encode(data.toJson());

class ApprovePoRes {
  List<Datum>? data;
  num? status;
  String? statusDescription;

  ApprovePoRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory ApprovePoRes.fromJson(Map<String, dynamic> json) => ApprovePoRes(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  num? id;
  num? prchOrdrId;
  String? statusCd;
  num? projectId;
  String? projectName;
  num? projectAddressId;
  String? projectAddressLine1;
  String? projectAddressLine2;
  String? projectCity;
  String? projectCountry;
  String? projectState;
  String? projectPincode;
  num? projectLatitude;
  num? projectLongitude;
  num? projectCityLan;
  num? projectCityLong;
  String? siteAccess;
  String? roadAccess;
  DateTime? deliveryDate;
  num? buyerCustomerId;
  num? buyerVendorId;
  num? deliveryAddressId;
  String? deliveryAddressLine1;
  String? deliveryAddressLine2;
  String? deliveryCity;
  String? deliveryCountry;
  String? deliveryState;
  String? deliveryPincode;
  num? deliveryLatitude;
  num? deliveryLongitude;
  num? deliveryCityLan;
  num? deliveryCityLong;
  num? mvtItemId;
  String? mvtItemName;
  num? quantity;
  dynamic price;
  dynamic cappCategoriesId;
  dynamic cappCategoriesName;
  dynamic vendorName;
  dynamic image1Thumbnail;
  dynamic image1;
  dynamic image2Thumbnail;
  dynamic image2;
  dynamic image3Thumbnail;
  dynamic image3;
  dynamic image4Thumbnail;
  dynamic image4;
  dynamic image5Thumbnail;
  dynamic image5;
  num? sellerCustomerId;
  num? sellerVendorId;
  num? optionGroupId;
  String? optionGroupName;
  String? optionName;
  num? optionId;
  num? offerPrice;
  num? variant1OptionId;
  String? variant1OptionName;
  num? variant2OptionId;
  String? variant2OptionName;
  num? variant3OptionId;
  String? variant3OptionName;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? sellerCustomerName;
  String? sellerVendorName;
  String? sellerCustomerPhone;
  String? buyerCustomerName;
  String? buyerVendorName;
  String? buyerCustomerPhone;
  num? sellerVendorRating;
  num? buyerVendorRating;
  dynamic instructions;
  String? confirmedOfferRemarks;
  num? confirmedOfferQuantity;

  Datum({
    this.id,
    this.prchOrdrId,
    this.statusCd,
    this.projectId,
    this.projectName,
    this.projectAddressId,
    this.projectAddressLine1,
    this.projectAddressLine2,
    this.projectCity,
    this.projectCountry,
    this.projectState,
    this.projectPincode,
    this.projectLatitude,
    this.projectLongitude,
    this.projectCityLan,
    this.projectCityLong,
    this.siteAccess,
    this.roadAccess,
    this.deliveryDate,
    this.buyerCustomerId,
    this.buyerVendorId,
    this.deliveryAddressId,
    this.deliveryAddressLine1,
    this.deliveryAddressLine2,
    this.deliveryCity,
    this.deliveryCountry,
    this.deliveryState,
    this.deliveryPincode,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.deliveryCityLan,
    this.deliveryCityLong,
    this.mvtItemId,
    this.mvtItemName,
    this.quantity,
    this.price,
    this.cappCategoriesId,
    this.cappCategoriesName,
    this.vendorName,
    this.image1Thumbnail,
    this.image1,
    this.image2Thumbnail,
    this.image2,
    this.image3Thumbnail,
    this.image3,
    this.image4Thumbnail,
    this.image4,
    this.image5Thumbnail,
    this.image5,
    this.sellerCustomerId,
    this.sellerVendorId,
    this.optionGroupId,
    this.optionGroupName,
    this.optionName,
    this.optionId,
    this.offerPrice,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.createdAt,
    this.updatedAt,
    this.sellerCustomerName,
    this.sellerVendorName,
    this.sellerCustomerPhone,
    this.buyerCustomerName,
    this.buyerVendorName,
    this.buyerCustomerPhone,
    this.sellerVendorRating,
    this.buyerVendorRating,
    this.instructions,
    this.confirmedOfferRemarks,
    this.confirmedOfferQuantity,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        prchOrdrId: json["prchOrdrId"],
        statusCd: json["statusCd"],
        projectId: json["projectId"],
        projectName: json["projectName"],
        projectAddressId: json["projectAddressId"],
        projectAddressLine1: json["projectAddressLine1"],
        projectAddressLine2: json["projectAddressLine2"],
        projectCity: json["projectCity"],
        projectCountry: json["projectCountry"],
        projectState: json["projectState"],
        projectPincode: json["projectPincode"],
        projectLatitude: json["projectLatitude"]?.toDouble(),
        projectLongitude: json["projectLongitude"]?.toDouble(),
        projectCityLan: json["projectCityLan"]?.toDouble(),
        projectCityLong: json["projectCityLong"]?.toDouble(),
        siteAccess: json["siteAccess"],
        roadAccess: json["roadAccess"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        buyerCustomerId: json["buyerCustomerId"],
        buyerVendorId: json["buyerVendorId"],
        deliveryAddressId: json["deliveryAddressId"],
        deliveryAddressLine1: json["deliveryAddressLine1"],
        deliveryAddressLine2: json["deliveryAddressLine2"],
        deliveryCity: json["deliveryCity"],
        deliveryCountry: json["deliveryCountry"],
        deliveryState: json["deliveryState"],
        deliveryPincode: json["deliveryPincode"],
        deliveryLatitude: json["deliveryLatitude"]?.toDouble(),
        deliveryLongitude: json["deliveryLongitude"]?.toDouble(),
        deliveryCityLan: json["deliveryCityLan"]?.toDouble(),
        deliveryCityLong: json["deliveryCityLong"]?.toDouble(),
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        quantity: json["quantity"],
        price: json["price"],
        cappCategoriesId: json["cappCategoriesId"],
        cappCategoriesName: json["cappCategoriesName"],
        vendorName: json["vendorName"],
        image1Thumbnail: json["image1Thumbnail"],
        image1: json["image1"],
        image2Thumbnail: json["image2Thumbnail"],
        image2: json["image2"],
        image3Thumbnail: json["image3Thumbnail"],
        image3: json["image3"],
        image4Thumbnail: json["image4Thumbnail"],
        image4: json["image4"],
        image5Thumbnail: json["image5Thumbnail"],
        image5: json["image5"],
        sellerCustomerId: json["sellerCustomerId"],
        sellerVendorId: json["sellerVendorId"],
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionName: json["optionName"],
        optionId: json["optionId"],
        offerPrice: json["offerPrice"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        variant3OptionId: json["variant3OptionId"],
        variant3OptionName: json["variant3OptionName"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        sellerCustomerName: json["sellerCustomerName"],
        sellerVendorName: json["sellerVendorName"],
        sellerCustomerPhone: json["sellerCustomerPhone"],
        buyerCustomerName: json["buyerCustomerName"],
        buyerVendorName: json["buyerVendorName"],
        buyerCustomerPhone: json["buyerCustomerPhone"],
        sellerVendorRating: json["sellerVendorRating"],
        buyerVendorRating: json["buyerVendorRating"],
        instructions: json["instructions"],
        confirmedOfferRemarks: json["confirmedOfferRemarks"],
        confirmedOfferQuantity: json["confirmedOfferQuantity"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "prchOrdrId": prchOrdrId,
        "statusCd": statusCd,
        "projectId": projectId,
        "projectName": projectName,
        "projectAddressId": projectAddressId,
        "projectAddressLine1": projectAddressLine1,
        "projectAddressLine2": projectAddressLine2,
        "projectCity": projectCity,
        "projectCountry": projectCountry,
        "projectState": projectState,
        "projectPincode": projectPincode,
        "projectLatitude": projectLatitude,
        "projectLongitude": projectLongitude,
        "projectCityLan": projectCityLan,
        "projectCityLong": projectCityLong,
        "siteAccess": siteAccess,
        "roadAccess": roadAccess,
        "deliveryDate": deliveryDate?.toIso8601String(),
        "buyerCustomerId": buyerCustomerId,
        "buyerVendorId": buyerVendorId,
        "deliveryAddressId": deliveryAddressId,
        "deliveryAddressLine1": deliveryAddressLine1,
        "deliveryAddressLine2": deliveryAddressLine2,
        "deliveryCity": deliveryCity,
        "deliveryCountry": deliveryCountry,
        "deliveryState": deliveryState,
        "deliveryPincode": deliveryPincode,
        "deliveryLatitude": deliveryLatitude,
        "deliveryLongitude": deliveryLongitude,
        "deliveryCityLan": deliveryCityLan,
        "deliveryCityLong": deliveryCityLong,
        "mvtItemId": mvtItemId,
        "mvtItemName": mvtItemName,
        "quantity": quantity,
        "price": price,
        "cappCategoriesId": cappCategoriesId,
        "cappCategoriesName": cappCategoriesName,
        "vendorName": vendorName,
        "image1Thumbnail": image1Thumbnail,
        "image1": image1,
        "image2Thumbnail": image2Thumbnail,
        "image2": image2,
        "image3Thumbnail": image3Thumbnail,
        "image3": image3,
        "image4Thumbnail": image4Thumbnail,
        "image4": image4,
        "image5Thumbnail": image5Thumbnail,
        "image5": image5,
        "sellerCustomerId": sellerCustomerId,
        "sellerVendorId": sellerVendorId,
        "optionGroupId": optionGroupId,
        "optionGroupName": optionGroupName,
        "optionName": optionName,
        "optionId": optionId,
        "offerPrice": offerPrice,
        "variant1OptionId": variant1OptionId,
        "variant1OptionName": variant1OptionName,
        "variant2OptionId": variant2OptionId,
        "variant2OptionName": variant2OptionName,
        "variant3OptionId": variant3OptionId,
        "variant3OptionName": variant3OptionName,
        "variant1OptionGroupId": variant1OptionGroupId,
        "variant1OptionGroupName": variant1OptionGroupName,
        "variant2OptionGroupId": variant2OptionGroupId,
        "variant2OptionGroupName": variant2OptionGroupName,
        "variant3OptionGroupId": variant3OptionGroupId,
        "variant3OptionGroupName": variant3OptionGroupName,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "sellerCustomerName": sellerCustomerName,
        "sellerVendorName": sellerVendorName,
        "sellerCustomerPhone": sellerCustomerPhone,
        "buyerCustomerName": buyerCustomerName,
        "buyerVendorName": buyerVendorName,
        "buyerCustomerPhone": buyerCustomerPhone,
        "sellerVendorRating": sellerVendorRating,
        "buyerVendorRating": buyerVendorRating,
        "instructions": instructions,
        "confirmedOfferRemarks": confirmedOfferRemarks,
        "confirmedOfferQuantity": confirmedOfferQuantity,
      };
}
