class RazorpayOrderIdRes {
  final num id;
  final String? customerId;
  final String? stockId;
  final num amount;
  final String orderId;
  final String? vendorId;
  final String primaryKeyName;
  final num primaryKeyValue;
  final String event;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  RazorpayOrderIdRes({
    required this.id,
    this.customerId,
    this.stockId,
    required this.amount,
    required this.orderId,
    this.vendorId,
    required this.primaryKeyName,
    required this.primaryKeyValue,
    required this.event,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RazorpayOrderIdRes.fromJson(Map<String, dynamic> json) {
    return RazorpayOrderIdRes(
      id: json['id'],
      customerId: json['customerId'],
      stockId: json['stockId'],
      amount: json['amount'],
      orderId: json['orderId'],
      vendorId: json['vendorId'],
      primaryKeyName: json['primaryKeyName'],
      primaryKeyValue: json['primaryKeyValue'],
      event: json['event'],
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}
