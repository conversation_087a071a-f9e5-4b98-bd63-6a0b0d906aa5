import 'package:razorpay_flutter/razorpay_flutter.dart';

class AppPayment {
  final Razorpay _razorpay = Razorpay();

  // Callbacks
  void Function(PaymentSuccessResponse)? onSuccess;
  void Function(PaymentFailureResponse)? onError;
  void Function(ExternalWalletResponse)? onExternalWallet;

  AppPayment({this.onSuccess, this.onError, this.onExternalWallet}) {
    init();
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    // Handle payment success
    if (onSuccess != null) {
      onSuccess!(response);
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    // Handle payment error
    if (onError != null) {
      onError!(response);
    }
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet
    if (onExternalWallet != null) {
      onExternalWallet!(response);
    }
  }

  void init() {
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void pay(double amount, String orderId, String email, String phone) {
    _razorpay.open(createPaymentOptions(
      amount: amount,
      orderId: orderId,
      email: email,
      phone: phone,
    ));
  }

  Map<String, dynamic> createPaymentOptions({
    required double amount,
    required String orderId,
    required String email,
    required String phone,
  }) {
    return {
      'key': 'rzp_test_3ipVrFfZAVlrUt',
      'amount': amount * 100, // Amount should be in paise
      'name': 'bai Store',
      'description': 'Payment for registration',
      'order_id': orderId, // Include the order ID here
      'prefill': {
        'contact': phone,
        'email': email,
      },
    };
  }

  void dispose() {
    _razorpay.clear();
  }
}
