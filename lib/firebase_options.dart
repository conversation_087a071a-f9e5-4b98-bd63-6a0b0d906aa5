// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBdgqAJHEjQ7p662Wc_69uPk6R6tIuBoak',
    appId: '1:77306820736:android:f43b705823918514938e65',
    messagingSenderId: '77306820736',
    projectId: 'connectone-prod',
    databaseURL:
        'https://connectone-prod-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'connectone-prod.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD2OmTvP1xakB2malEWnd0dE29nMBY2ccQ',
    appId: '1:77306820736:ios:86634e82fc35e1c0938e65',
    messagingSenderId: '77306820736',
    projectId: 'connectone-prod',
    databaseURL:
        'https://connectone-prod-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'connectone-prod.firebasestorage.app',
    iosBundleId: 'bai.store.app',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAFwqyHHhUjA6ylpjejyXv9bflae1jPGKQ',
    appId: '1:77306820736:web:33e54d67e4a565f0938e65',
    messagingSenderId: '77306820736',
    projectId: 'connectone-prod',
    authDomain: 'connectone-prod.firebaseapp.com',
    databaseURL:
        'https://connectone-prod-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'connectone-prod.firebasestorage.app',
    measurementId: 'G-VSJNTLBX3S',
  );
}
