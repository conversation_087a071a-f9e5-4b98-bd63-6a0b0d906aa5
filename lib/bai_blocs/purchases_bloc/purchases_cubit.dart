import 'package:bloc/bloc.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/add_project_req.dart';
import 'package:connectone/bai_models/get_projects_res.dart';
import 'package:connectone/bai_models/org_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:equatable/equatable.dart';

part 'purchases_state.dart';

class PurchasesCubit extends Cubit<PurchasesState> {
  PurchasesCubit() : super(PurchasesInitial());
  var api = NetworkController();

  Future<void> getProjects() async {
    emit(ProjectDetailsLoading());
    try {
      var res = await api.getProjectList();
      emit(ProjectDetailsLoaded(projects: res));
    } catch (e) {
      emit(ProjectDetailsError(error: e.toString()));
    }
  }

  Future<void> getUnitsAndSiteAccess() async {
    try {
      var siteAccesses = await api.getSiteAccess();
      if ((siteAccesses.roadAccessTypes ?? []).isNotEmpty) {
        siteAccesses.roadAccessTypes
            ?.removeWhere((accessType) => accessType.key?.isEmpty ?? true);
      }
      BaiCart.siteAccessTypeList = siteAccesses;
    } catch (e) {
      safePrint('Error fetching site access: $e');
    }
  }

  Future<void> getOrgIds() async {
    emit(NewProjectLoading());
    try {
      var res = await api.getOrgIds();
      emit(NewProjectLoaded(orgs: res));
    } catch (e) {
      emit(NewProjectInitial());
    }
  }

  void addNewProject(AddProjectReq req) {
    emit(NewProjectLoading());
    try {
      var res = api.addNewProject(req: req);
      emit(NewProjectAdded());
    } catch (e) {
      emit(const NewProjectLoaded());
    }
  }
}
