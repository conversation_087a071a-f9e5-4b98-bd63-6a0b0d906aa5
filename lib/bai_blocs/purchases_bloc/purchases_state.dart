part of 'purchases_cubit.dart';

class PurchasesState extends Equatable {
  const PurchasesState();

  @override
  List<Object> get props => [];
}

class PurchasesInitial extends PurchasesState {}

class ProjectDetailsInitial extends PurchasesState {}

class ProjectDetailsLoading extends PurchasesState {}

class ProjectDetailsError extends PurchasesState {
  final String error;

  const ProjectDetailsError({required this.error});
}

class ProjectDetailsLoaded extends PurchasesState {
  final GetProjectsRes? projects;
  const ProjectDetailsLoaded({this.projects});
}

class NewProjectInitial extends PurchasesState {}

class NewProjectLoading extends PurchasesState {}

class NewProjectLoaded extends PurchasesState {
  final List<Org>? orgs;
  const NewProjectLoaded({this.orgs});
}

class NewProjectAdded extends PurchasesState {}
