import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/assign_req.dart';
import 'package:connectone/bai_models/districts_res.dart';
import 'package:connectone/bai_models/vendor_list_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'assign_state.dart';

class AssignCubit extends Cubit<AssignState> {
  AssignCubit() : super(AssignInitial());
  var api = NetworkController();

  getVendors({
    String name = '',
    String district = '',
    required String prchOrdrSplitId,
    required DateTime deliveryDate,
  }) async {
    emit(AssignLoading());
    try {
      var vendorList = await api.getVendorListV2(
        name: name,
        district: district,
        prchOrdrSplitId: prchOrdrSplitId,
        deliveryDate: deliveryDate,
      );
      var districts = await api.getBaiCentres();
      emit(AssignLoaded(
        vendorList: vendorList,
        districts: districts,
      ));
    } catch (e) {
      emit(AssignError(error: e.toString()));
    }
  }

  getVendorsNew({
    String name = '',
    String district = '',
    required String prchOrdrSplitId,
    required DateTime deliveryDate,
  }) async {
    var previousState = state as AssignLoaded;
    emit(AssignLoading());
    try {
      var vendorList = await api.getVendorListV2(
        name: name,
        district: district,
        prchOrdrSplitId: prchOrdrSplitId,
        deliveryDate: deliveryDate,
      );
      emit(previousState.copyWith(vendorList: vendorList));
    } catch (e) {
      emit(AssignError(error: e.toString()));
    }
  }

  assignVendor(AssignReq req) async {
    emit(AssignLoading());
    try {
      var response = await api.assignVendor(req);
      emit(AssignSuccess());
    } catch (e) {
      emit(AssignError(error: e.toString()));
    }
  }
}
