part of 'assign_cubit.dart';

class AssignState extends Equatable {
  const AssignState();

  @override
  List<Object> get props => [];
}

class AssignInitial extends AssignState {}

class AssignLoading extends AssignState {}

class AssignError extends AssignState {
  final String error;

  const AssignError({required this.error});
}

class AssignSuccess extends AssignState {}

class AssignLoaded extends AssignState {
  final VendorList vendorList;
  final DistrictsRes? districts;
  // final List<AlreadyAssignedRes>? alreadyAssignedList;
  const AssignLoaded({required this.vendorList, this.districts});

  AssignLoaded copyWith({
    VendorList? vendorList,
    DistrictsRes? districts,
  }) {
    return AssignLoaded(
      vendorList: vendorList ?? this.vendorList,
      districts: districts ?? this.districts,
    );
  }
}
