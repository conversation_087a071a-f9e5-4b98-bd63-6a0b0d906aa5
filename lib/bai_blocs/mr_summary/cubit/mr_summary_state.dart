part of 'mr_summary_cubit.dart';

class MrSummaryState extends Equatable {
  const MrSummaryState();

  @override
  List<Object> get props => [];
}

class MrSummaryInitial extends MrSummaryState {}

class MrSummaryLoading extends MrSummaryState {}

class MrSummaryLoaded extends MrSummaryState {
  DeliveryDatesRes deliveryDates;
  MrSummaryLoaded(this.deliveryDates);
}

class MrSummaryError extends MrSummaryState {
  String error;
  MrSummaryError(this.error);
}
