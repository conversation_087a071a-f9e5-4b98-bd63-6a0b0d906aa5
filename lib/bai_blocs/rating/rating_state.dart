part of 'rating_cubit.dart';

class RatingState extends Equatable {
  const RatingState();

  @override
  List<Object> get props => [];
}

class RatingInitial extends RatingState {}

class RatingLoading extends RatingState {}

class RatingLoaded extends RatingState {
  final RatingVendorListRes vendorListRes;

  const RatingLoaded(this.vendorListRes);
}

class RatingError extends RatingState {
  final String error;

  const RatingError(this.error);
}

class RatingSuccess extends RatingState {
  final String message;

  const RatingSuccess(this.message);
}
