import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/rating_vendor_list_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/feedback_data.dart';
import 'package:equatable/equatable.dart';

part 'rating_state.dart';

class RatingCubit extends Cubit<RatingState> {
  RatingCubit() : super(RatingInitial());

  var networkController = NetworkController();

  void getRating(String prchOrdrId) async {
    emit(RatingLoading());
    try {
      var res = await networkController.ratingVendorList(prchOrdrId);
      emit(RatingLoaded(res));
    } catch (e) {
      emit(RatingError(e.toString()));
    }
  }

  void rateVendor(FeedbackData feedbackData) async {
    emit(RatingLoading());
    try {
      var res = await networkController.postFeedback(feedbackData);
      emit(const RatingSuccess("Rating submitted successfully."));
    } catch (e) {
      emit(RatingError(e.toString()));
    }
  }
}
