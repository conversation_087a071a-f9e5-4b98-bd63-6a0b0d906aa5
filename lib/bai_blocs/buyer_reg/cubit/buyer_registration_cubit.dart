import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/designation_res.dart';
import 'package:connectone/bai_models/districts_res.dart';
import 'package:connectone/bai_models/nature_of_business_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_models/roles_res.dart';
import 'package:connectone/bai_models/vendors_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'buyer_registration_state.dart';

class BuyerRegistrationCubit extends Cubit<BuyerRegistrationState> {
  BuyerRegistrationCubit() : super(BuyerRegistrationInitial());
  var api = NetworkController();

  void loadData() async {
    emit(BuyerRegistrationLoading());
    try {
      var districts = await api.getBaiCentres();
      var designations = await api.getDesignations();
      var natures = await api.getNatureOfBusinessBuyer();

      emit(BuyerRegistrationLoaded(
        districts: districts,
        designations: designations,
        natures: natures,
        vendors: null,
      ));
    } catch (e) {
      emit(BuyerRegistrationError(message: e.toString()));
    }
  }

  Future<void> getVendors(String id) async {
    var previousState = state as BuyerRegistrationLoaded;
    emit(BuyerRegistrationLoading());
    try {
      var vendors = await api.getVendors1(id, '', 'BUYR');
      emit(previousState.copyWith(vendors: vendors));
    } catch (e) {
      print(e.toString());
      emit(previousState);
    }
  }

  Future<void> getRoles(String id, String type) async {
    var previousState = state as BuyerRegistrationLoaded;
    emit(BuyerRegistrationLoading());
    try {
      var roles = await api.getRoles(id, type);
      emit(previousState.copyWith(roles: roles));
    } catch (e) {
      print(e.toString());
      emit(previousState);
    }
  }

  registerBuyer(RegisterReq req) async {
    var previousState = state as BuyerRegistrationLoaded;
    emit(BuyerRegistrationLoading());
    try {
      var res = await api.registerUser(req);
      emit(BuyerRegistrationSuccess());
      emit(previousState);
    } catch (e) {
      emit(BuyerRegistrationError(message: e.toString()));
      emit(previousState);
    }
  }
}
