part of 'buyer_registration_cubit.dart';

class BuyerRegistrationState extends Equatable {
  const BuyerRegistrationState();

  @override
  List<Object> get props => [];
}

class BuyerRegistrationInitial extends BuyerRegistrationState {}

class BuyerRegistrationLoading extends BuyerRegistrationState {}

class BuyerRegistrationError extends BuyerRegistrationState {
  final String message;
  const BuyerRegistrationError({required this.message});
}

class BuyerRegistrationSuccess extends BuyerRegistrationState {}

class BuyerRegistrationLoaded extends BuyerRegistrationState {
  final DistrictsRes? districts;
  final DesignationsRes? designations;
  final NatureOfBusinessRes? natures;
  final VendorsRes? vendors;
  final List<RolesRes>? roles;

  const BuyerRegistrationLoaded({
    this.districts,
    this.designations,
    this.natures,
    this.vendors,
    this.roles,
  });

  BuyerRegistrationLoaded copyWith({
    DistrictsRes? districts,
    DesignationsRes? designations,
    NatureOfBusinessRes? natures,
    VendorsRes? vendors,
    List<RolesRes>? roles,
  }) {
    return BuyerRegistrationLoaded(
      districts: districts ?? this.districts,
      designations: designations ?? this.designations,
      natures: natures ?? this.natures,
      vendors: vendors ?? this.vendors,
      roles: roles ?? this.roles,
    );
  }
}
