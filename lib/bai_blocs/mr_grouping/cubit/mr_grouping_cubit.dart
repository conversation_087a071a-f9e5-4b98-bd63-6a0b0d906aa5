import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/discount_get_req.dart';
import 'package:connectone/bai_models/discount_transport_res.dart';
import 'package:connectone/bai_models/discount_tranpsortation_req.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'mr_grouping_state.dart';

class MrGroupingCubit extends Cubit<MrGroupingState> {
  MrGroupingCubit() : super(MRGroupingLoading());
  var api = NetworkController();

  Future<void> getGrouping(String orderGroupId) async {
    emit(MRGroupingLoading());
    try {
      var res = await api.getMrGrouping(orderGroupId);
      emit(MRGroupingLoaded(groupRes: res));
    } catch (e) {}
  }

  Future<void> postDiscount(DiscountReq req) async {
    try {
      var res = await api.postdiscountTransportation(req);
    } catch (e) {}
  }

  Future<void> getdiscountTransport(DiscountGetReq discountTransportReq) async {
    emit(DiscountLoading());
    try {
      var res = await api.getdiscountTransportation(discountTransportReq);
      emit(DiscountLoaded(discountTransportRes: res));
    } catch (e) {
      emit(DiscountError(message: e.toString()));
    }
  }
}
