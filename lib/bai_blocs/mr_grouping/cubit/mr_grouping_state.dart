part of 'mr_grouping_cubit.dart';

class MrGroupingState extends Equatable {
  const MrGroupingState();

  @override
  List<Object> get props => [];
}

class MRGroupingLoading extends MrGroupingState {}

class MRGroupingLoaded extends MrGroupingState {
  final List<Content> groupRes;
  const MRGroupingLoaded({required this.groupRes});
}

class MRGroupingError extends MrGroupingState {}

class DiscountLoading extends MrGroupingState {}

class DiscountLoaded extends MrGroupingState {
  final DiscountTransportRes discountTransportRes;
  const DiscountLoaded({required this.discountTransportRes});
}

class DiscountError extends MrGroupingState {
  final String message;
  const DiscountError({required this.message});
}
