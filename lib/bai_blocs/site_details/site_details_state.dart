part of 'site_details_cubit.dart';

class SiteDetailsState extends Equatable {
  const SiteDetailsState();

  @override
  List<Object> get props => [];
}

class SiteDetailsInitial extends SiteDetailsState {}

class SiteDetailsLoading extends SiteDetailsState {}

class SiteDetailsLoaded extends SiteDetailsState {
  final SiteDetailsRes siteDetailsRes;
  final List<MemberListItem>? employeeList;

  const SiteDetailsLoaded({
    required this.siteDetailsRes,
    required this.employeeList,
  });

  SiteDetailsLoaded copyWith({
    SiteDetailsRes? siteDetailsRes,
    List<MemberListItem>? employeeList,
  }) {
    return SiteDetailsLoaded(
      siteDetailsRes: siteDetailsRes ?? this.siteDetailsRes,
      employeeList: employeeList ?? employeeList,
    );
  }
}

class EmployeeListLoaded extends SiteDetailsState {
  final List<MemberListItem>? employeeList;

  const EmployeeListLoaded({required this.employeeList});
}

class SiteDetailsError extends SiteDetailsState {
  final String message;
  const SiteDetailsError(this.message);
}

// employee added
class EmployeeAdded extends SiteDetailsState {
  final String message;
  const EmployeeAdded({required this.message});
}

// employee status changed
class EmployeeStatusChanged extends SiteDetailsState {
  final String message;
  const EmployeeStatusChanged({required this.message});
}

// project status changed
class ProjectStatusChanged extends SiteDetailsState {
  final String message;
  const ProjectStatusChanged({required this.message});
}
