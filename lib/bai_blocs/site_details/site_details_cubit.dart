import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/add_employee_req.dart';
import 'package:connectone/bai_models/member_list_res.dart';
import 'package:connectone/bai_models/site_details_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'site_details_state.dart';

class SiteDetailsCubit extends Cubit<SiteDetailsState> {
  SiteDetailsCubit() : super(SiteDetailsInitial());

  var networkController = NetworkController();

  Future<void> getSiteDetails(String projectId) async {
    emit(SiteDetailsLoading());
    try {
      var response = await networkController.siteDetails(projectId);
      emit(SiteDetailsLoaded(
        siteDetailsRes: response,
        employeeList: null,
      ));
    } catch (e) {
      emit(SiteDetailsError(e.toString()));
    }
  }

  // add employee
  Future<void> addEmployee(AddEmployeeReq req) async {
    var previousState = state as SiteDetailsLoaded;
    emit(SiteDetailsLoading());
    try {
      var res = await networkController.addEmployee(req);
      emit(EmployeeAdded(message: res.statusDescription));
    } catch (e) {
      emit(SiteDetailsError(e.toString()));
      emit(previousState);
    }
  }

  // change employee status
  Future<void> changeEmployeeStatus(
    String projectId,
    String employeeId,
    String status,
  ) async {
    var previousState = state as SiteDetailsLoaded;
    emit(SiteDetailsLoading());
    try {
      var res = await networkController.changeEmployeeStatus(
        projectId,
        employeeId,
        status,
      );
      emit(EmployeeStatusChanged(message: res.statusDescription));
    } catch (e) {
      emit(SiteDetailsError(e.toString()));
      emit(previousState);
    }
  }

  // change project status
  Future<void> changeProjectStatus(
    String projectId,
    String status,
  ) async {
    var previousState = state as SiteDetailsLoaded;
    emit(SiteDetailsLoading());
    try {
      var res = await networkController.changeProjectStatus(
        projectId,
        status,
      );
      emit(ProjectStatusChanged(message: res.statusDescription));
    } catch (e) {
      emit(SiteDetailsError(e.toString()));
      emit(previousState);
    }
  }

  // get employee list
  Future<void> getEmployeeList(String projectId) async {
    var previousState = state as SiteDetailsLoaded;
    emit(SiteDetailsLoading());
    try {
      // var res = await networkController.getEmployeeList(projectId);
      var res = await networkController.getMemberList();
      emit(previousState.copyWith(employeeList: res));
    } catch (e) {
      emit(SiteDetailsError(e.toString()));
    }
  }
}
