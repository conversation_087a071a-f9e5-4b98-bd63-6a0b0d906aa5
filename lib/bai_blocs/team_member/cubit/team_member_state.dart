part of 'team_member_cubit.dart';

class TeamMemberState extends Equatable {
  const TeamMemberState();

  @override
  List<Object> get props => [];
}

class TeamMemberInitial extends TeamMemberState {}

class TeamMemberLoading extends TeamMemberState {}

class TeamMemberLoaded extends TeamMemberState {
  final List<TeamMember> teamMembers;
  final List<MemberListItem>? memberList;

  const TeamMemberLoaded({
    required this.teamMembers,
    required this.memberList,
  });

  TeamMemberLoaded copyWith({
    List<TeamMember>? teamMembers,
    List<MemberListItem>? memberList,
  }) {
    return TeamMemberLoaded(
      teamMembers: teamMembers ?? this.teamMembers,
      memberList: memberList ?? this.memberList,
    );
  }

  @override
  List<Object> get props => [teamMembers];
}

class TeamMemberError extends TeamMemberState {}

class TeamMemberApproved extends TeamMemberState {}

class TeamMemberRejected extends TeamMemberState {}

class NewTeamMemberAssigned extends TeamMemberState {}

class TeamM<PERSON>berAdded extends TeamMemberState {}
