import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/add_seller_req.dart';
import 'package:connectone/bai_models/member_list_res.dart';
import 'package:connectone/bai_models/team_member.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';

part 'team_member_state.dart';

class TeamMemberCubit extends Cubit<TeamMemberState> {
  TeamMemberCubit() : super(TeamMemberInitial());
  var api = NetworkController();

  getTeamMembers() async {
    emit(TeamMemberLoading());
    try {
      var res = await api.getTeamMembers();
      emit(TeamMemberLoaded(
        teamMembers: res,
        memberList: null,
      ));
    } catch (e) {
      emit(TeamMemberError());
    }
  }

  approveTeamMember(String id) async {
    emit(TeamMemberLoading());
    try {
      var res = await api.approveTeamMembers(id);
      emit(TeamMemberApproved());
    } catch (e) {
      alert(e.toString());
      emit(TeamMemberError());
    }
  }

  rejectTeamMember(String id) async {
    emit(TeamMemberLoading());
    try {
      var res = await api.rejectTeamMembers(id);
      emit(TeamMemberRejected());
    } catch (e) {
      alert(e.toString());
      emit(TeamMemberError());
    }
  }

  getMemberList() async {
    var previousState = state as TeamMemberLoaded;
    emit(TeamMemberLoading());
    try {
      var res = await api.getMemberList();
      emit(previousState.copyWith(memberList: res));
    } catch (e) {
      emit(TeamMemberError());
    }
  }

  assignToNewMember(
    String deletedCustomerId,
    String newCustomerId,
  ) async {
    emit(TeamMemberLoading());
    try {
      var res1 = await api.deleteMember(deletedCustomerId);
      var res2 = await api.assignToNewMember(
        deletedCustomerId,
        newCustomerId,
      );
      emit(NewTeamMemberAssigned());
    } catch (e) {
      alert(e.toString());
      emit(TeamMemberError());
    }
  }

  addNewSeller(AddSellerReq req) async {
    emit(TeamMemberLoading());
    try {
      var res = await api.addSellerOrParticipant(req);
      emit(TeamMemberAdded());
    } catch (e) {
      alert(e.toString());
      emit(TeamMemberError());
    }
  }
}
