import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/cat_sub_cat_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'add_purchase_order_state.dart';

class AddPurchaseOrderCubit extends Cubit<AddPurchaseOrderState> {
  AddPurchaseOrderCubit() : super(AddPurchaseOrderInitial());
  var api = NetworkController();

  getCatSubCat(bool isMr) async {
    emit(AddPurchaseOrderLoading());
    try {
      var res = await api.getCatSubCat(isMr);
      emit(AddPurchaseOrderLoaded(catSubCatRes: res));
    } catch (e) {
      emit(AddPurchaseOrderError());
    }
  }
}
