part of 'add_purchase_order_cubit.dart';

class AddPurchaseOrderState extends Equatable {
  const AddPurchaseOrderState();

  @override
  List<Object> get props => [];
}

class AddPurchaseOrderInitial extends AddPurchaseOrderState {}

class AddPurchaseOrderLoading extends AddPurchaseOrderState {}

class AddPurchaseOrderLoaded extends AddPurchaseOrderState {
  final List<CatSubCatRes> catSubCatRes;
  const AddPurchaseOrderLoaded({required this.catSubCatRes});
}

class AddPurchaseOrderError extends AddPurchaseOrderState {}
