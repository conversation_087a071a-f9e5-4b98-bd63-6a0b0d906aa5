// add_product_state.dart
part of 'add_product_cubit.dart';

abstract class AddEditProductState extends Equatable {
  const AddEditProductState();

  @override
  List<Object> get props => [];
}

class AddProductInitial extends AddEditProductState {}

class AddProductLoading extends AddEditProductState {}

class AddProductLoaded extends AddEditProductState {
  final ItemOfferingRes? response;
  // final Map<int, List<String>> selectedMultiSelectValues;
  // final Map<int, String> selectedSingleSelectValues;

  const AddProductLoaded({
    this.response,
    // this.selectedMultiSelectValues = const {},
    // this.selectedSingleSelectValues = const {},
  });

  @override
  List<Object> get props => [response ?? []];
}

class EditProductInitial extends AddEditProductState {}

class EditProductLoading extends AddEditProductState {}

class EditProductLoaded extends AddEditProductState {
  final ItemOfferingRes? itemOfferingRes;
  final MrRes? mrRes;
  final SearchCategoryRes? categoryRes;
  // final Map<int, List<String>> selectedMultiSelectValues;
  // final Map<int, String> selectedSingleSelectValues;

  const EditProductLoaded({
    this.itemOfferingRes,
    this.mrRes,
    this.categoryRes,
    // this.selectedMultiSelectValues = const {},
    // this.selectedSingleSelectValues = const {},
  });

  @override
  List<Object> get props => [itemOfferingRes ?? []];
}

class EditProductSuccessful extends AddEditProductState {
  final String message;

  const EditProductSuccessful({required this.message});
}

class EditProductFailed extends AddEditProductState {
  final String message;

  const EditProductFailed({required this.message});
}

class DeleteProductSuccessful extends AddEditProductState {
  final String message;

  const DeleteProductSuccessful({required this.message});
}

class DeleteProductFailed extends AddEditProductState {
  final String message;

  const DeleteProductFailed({required this.message});
}
