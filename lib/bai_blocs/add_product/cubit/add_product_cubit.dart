// add_product_cubit.dart

import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/item_offering_res.dart';
import 'package:connectone/bai_models/mr_req.dart' as mrReq;
import 'package:connectone/bai_models/mr_res.dart';
import 'package:connectone/bai_models/search_categories_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'add_product_state.dart';

class AddEditProductCubit extends Cubit<AddEditProductState> {
  AddEditProductCubit() : super(AddProductInitial());
  var api = NetworkController();

  Future<void> getItemOffering(String? categoryId) async {
    emit(AddProductLoading());
    try {
      var response = await api.getItemOffering(categoryId);
      emit(AddProductLoaded(response: response));
    } catch (e) {
      emit(const AddProductLoaded());
    }
  }

  Future<void> loadDataForEdit(String id) async {
    emit(EditProductLoading());
    try {
      var mrRes = await api.getMrData(id);
      var categoryId = mrRes.itemOffering?.id ?? -3;
      var itemOfferingRes = await api.getItemOffering(categoryId.toString());
      var categoriesRes = await api.searchCategories(query: "");
      emit(EditProductLoaded(
        mrRes: mrRes,
        itemOfferingRes: itemOfferingRes,
        categoryRes: categoriesRes,
      ));
    } catch (e) {
      alert(e.toString());
      emit(EditProductFailed(message: e.toString()));
    }
  }

  Future<void> update(
    mrReq.MrReq req,
    List<String> paths,
    String id,
    List<Uint8List> bytes,
  ) async {
    var previousState = state as EditProductLoaded;
    emit(EditProductLoading());

    try {
      if (kIsWeb) {
        for (int i = 0; i < bytes.length; i++) {
          for (var path in bytes) {
            var response1 = await api.uploadMediaWeb(path);
            var url = response1.fileUrl;
            req.medias?.add(mrReq.Media(url: url));
          }
        }
      } else {
        for (int i = 0; i < paths.length; i++) {
          for (var path in paths) {
            var response1 = await api.uploadMedia(path);
            var url = response1.fileUrl;
            req.medias?.add(mrReq.Media(url: url));
          }
        }
      }

      if (req.medias is List) {
        for (var index = 0; index < (req.medias?.length ?? 0); index++) {
          req.medias?[index].sequence = index + 1;
        }
      }
      // throw Exception("Unable to upload media. Please try again later.");
      var res = await api.editMrData(id, req);
      emit(EditProductSuccessful(message: res.statusDescription));
    } catch (e) {
      emit(EditProductFailed(message: e.toString()));
      emit(previousState);
    }
  }

  Future<void> delete(
    mrReq.MrReq req,
    String id,
  ) async {
    emit(EditProductLoading());
    try {
      var res = await api.editMrData(id, req);
      emit(DeleteProductSuccessful(message: res.statusDescription));
    } catch (e) {
      emit(DeleteProductFailed(message: e.toString()));
    }
  }
}
