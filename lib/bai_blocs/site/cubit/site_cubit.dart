import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/assign_project_req.dart';
import 'package:connectone/bai_models/site_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';
part 'site_state.dart';

class SiteCubit extends Cubit<SiteState> {
  SiteCubit() : super(SiteInitial());
  var api = NetworkController();

  getSite() async {
    emit(SiteLoading());
    try {
      var res = await api.getSiteList();
      emit(SiteLoaded(siteList: res));
    } catch (e) {
      emit(SiteError());
    }
  }

  assignProject(AssignProjectReq assignProjectReq) async {
    emit(SiteLoading());
    try {
      var res = await api.assignProjectToUser(assignProjectReq);
      emit(SiteApproved());
    } catch (e) {
      alert(e.toString());
      emit(SiteError());
    }
  }

  updateProjectStatus(int projectId, String status) async {
    emit(SiteLoading());
    try {
      var res = await api.updateProjectStatus(projectId, status);
      emit(SiteStatusChanged());
    } catch (e) {
      emit(SiteError());
    }
  }

  getCustomersList() async {
    emit(SiteLoading());
    try {
      var res = await api.getCustomersList();
      emit(SiteLoaded(siteList: res));
    } catch (e) {
      emit(SiteError());
    }
  }
}
