part of 'site_cubit.dart';

class SiteState extends Equatable {
  const SiteState();

  @override
  List<Object> get props => [];
}

class SiteInitial extends SiteState {}

class SiteLoading extends SiteState {}

class SiteLoaded extends SiteState {
  final SiteRes siteList;

  const SiteLoaded({required this.siteList});
}

class SiteError extends SiteState {}

class SiteApproved extends SiteState {}

class SiteRejected extends SiteState {}

class SiteStatusChanged extends SiteState {}
