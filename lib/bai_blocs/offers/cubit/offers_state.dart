part of 'offers_cubit.dart';

class OffersState extends Equatable {
  const OffersState();

  @override
  List<Object> get props => [];
}

class OffersInitial extends OffersState {}

class BuyerOffersLoading extends OffersState {}

class BuyerOffersLoaded extends OffersState {
  final SellerOffers offers;
  final OffersFilterRes filter;
  final List<SummaryResponse> summary;
  final StatusListModel? nextStatus;

  const BuyerOffersLoaded({
    required this.offers,
    required this.filter,
    required this.summary,
    this.nextStatus,
  });
}

class BuyerOffersSuccess extends OffersState {}

class BuyerOffersAccepted extends OffersState {
  final String message;
  const BuyerOffersAccepted({required this.message});
}

class BuyerOffersNegotiated extends OffersState {
  final String message;
  const BuyerOffersNegotiated({required this.message});
}

class NegotiationAccepted extends OffersState {
  final String message;
  const NegotiationAccepted({required this.message});
}

class BuyerOffersFailed extends OffersState {
  final String message;

  const BuyerOffersFailed({required this.message});
}

class AcceptNegotiateFailed extends OffersState {
  final String message;

  const AcceptNegotiateFailed({required this.message});
}

class SellerOffersLoading extends OffersState {}

class SellerOffersLoaded extends OffersState {
  final BuyerOffersRes offers;
  final List<SummaryResponse> summary;
  final OffersFilterRes filter;
  final StatusListModel? nextStatus;
  final bool? showInviteButton;

  const SellerOffersLoaded({
    required this.showInviteButton,
    required this.offers,
    required this.summary,
    required this.filter,
    this.nextStatus,
  });
}

class SellerOffersSuccess extends OffersState {}

class SellerOffersFailed extends OffersState {}

class QuoteSummaryLoading extends OffersState {}

class QuotesOnlyLoading extends OffersState {}

class QuoteSummaryError extends OffersState {
  final String message;
  const QuoteSummaryError({required this.message});
}

class QuotesOnlyError extends OffersState {
  final String message;
  const QuotesOnlyError({required this.message});
}

class QuoteSummaryLoaded extends OffersState {
  final List<SummaryResponse> summary;

  const QuoteSummaryLoaded({required this.summary});
}

class QuotesOnlyLoaded extends OffersState {
  final BuyerOffersRes buyerOffers;

  const QuotesOnlyLoaded({required this.buyerOffers});
}

class DiscardSuccess extends OffersState {
  final String message;
  const DiscardSuccess(this.message);
}

class RefreshBuyerPage extends OffersState {
  const RefreshBuyerPage();
}

class RefreshSellerPage extends OffersState {
  const RefreshSellerPage();
}

class NotInterestedSuccess extends OffersState {
  const NotInterestedSuccess();
}

class NotifyBuyerSuccess extends OffersState {
  final String message;
  const NotifyBuyerSuccess(this.message);
}

class NotifyBuyerFailed extends OffersState {
  final String message;
  const NotifyBuyerFailed(this.message);
}
