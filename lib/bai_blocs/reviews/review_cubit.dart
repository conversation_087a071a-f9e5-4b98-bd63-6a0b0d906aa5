import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/customer_reviews_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'review_state.dart';

class ReviewCubit extends Cubit<ReviewState> {
  ReviewCubit() : super(ReviewInitial());
  var api = NetworkController();
  Future<void> loadReview(String id) async {
    emit(ReviewLoading());
    try {
      var res = await api.getUserReviews(id);
      emit(ReviewLoaded(reviewsRes: res));
    } catch (e) {
      emit(ReviewError());
    }
  }
}
