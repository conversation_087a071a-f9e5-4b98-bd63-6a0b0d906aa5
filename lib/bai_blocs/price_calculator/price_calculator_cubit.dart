import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'price_calculator_state.dart';

class PriceCalculatorCubit extends Cubit<PriceCalculatorState> {
  PriceCalculatorCubit() : super(PriceCalculatorLoaded());

  void calculatePrice() {}
}

class CalculatedPrice {
  num subTotal;
  num transportationCharge;
  num discount;
  num gst;
  num total;

  CalculatedPrice({
    required this.subTotal,
    required this.transportationCharge,
    required this.discount,
    required this.gst,
    required this.total,
  });
}
