part of 'insert_stock_cubit.dart';

class InsertStockState extends Equatable {
  const InsertStockState();

  @override
  List<Object> get props => [];
}

class InsertStockInitial extends InsertStockState {}

class InsertStockLoading extends InsertStockState {}

class InsertStockSuccess extends InsertStockState {
  final res.InsertStockRes response;
  const InsertStockSuccess({required this.response});
}

class InsertStockFailed extends InsertStockState {}
