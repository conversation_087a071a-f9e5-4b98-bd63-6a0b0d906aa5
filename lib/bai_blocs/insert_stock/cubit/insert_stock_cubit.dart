import 'package:bloc/bloc.dart';
import 'package:connectone/bai_cart/bai_cart.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/insert_stock_res.dart' as res;
import 'package:connectone/bai_models/upload_media_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'insert_stock_state.dart';

class InsertStockCubit extends Cubit<InsertStockState> {
  InsertStockCubit() : super(InsertStockInitial());
  var api = NetworkController();

  Future<void> insertStocks(InsertStockReq req) async {
    emit(InsertStockLoading());
    try {
      for (int i = 0; i < BaiCart.cartItems.length; i++) {
        var item = BaiCart.cartItems[i];
        List<String> paths = [
          ...?item.images,
          ...?item.audios,
          ...?item.files,
        ];
        List<Uint8List> bytesWeb = [
          ...?item.imageBytes,
          ...?item.fileBytes,
        ];
        if (!kIsWeb) {
          for (var path in paths) {
            UploadMediaRes uploadResponse;

            uploadResponse = await api.uploadMedia(path);

            var url = uploadResponse.fileUrl;
            if (req.poStockItems?[i].medias == null) {
              req.poStockItems?[i].medias = [];
            }
            req.poStockItems?[i].medias
                ?.add(Media(url: url, sequence: paths.indexOf(path) + 1));
          }
        } else {
          for (var item in bytesWeb) {
            UploadMediaRes uploadResponse;

            uploadResponse = await api.uploadMediaWeb(item);

            var url = uploadResponse.fileUrl;
            if (req.poStockItems?[i].medias == null) {
              req.poStockItems?[i].medias = [];
            }
            req.poStockItems?[i].medias
                ?.add(Media(url: url, sequence: bytesWeb.indexOf(item) + 1));
          }
        }
      }
      var res = await api.insertStock(req: req);
       if (res.orders!=null) {
            await api.notifyAction(
              prchOrdrId: res.orders?.first.id??0,
              statusCd: 'NEWW',
              offerId: res.orders?.first.id??0, // TODO: Replace with actual offer ID from postOffers response
            );

       
        }



      emit(InsertStockSuccess(response: res));
    } catch (e) {
      print(e);
      alert(e.toString());
      emit(InsertStockFailed());
    }
  }

  void reset() {
    emit(InsertStockInitial());
  }
}
