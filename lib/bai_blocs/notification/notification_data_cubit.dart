import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'notification_data_state.dart';

class NotificationDataCubit extends Cubit<NotificationDataState> {
  NotificationDataCubit() : super(NotificationDataInitial());

  var api = NetworkController();

  loadNotificationData(String prchId, {String? mvtPrchOrdrHistoryId}) async {
    emit(NotificationDataLoading());
    try {
      BaiProductsRes res;
      if (mvtPrchOrdrHistoryId != null) {
        Content res = await api.getBaiProductHistory(
          mvtPrchOrdrHistoryId: mvtPrchOrdrHistoryId,
        );
        emit(NotificationDataLoaded(
            notification: BaiProductsRes(content: [res])));
      } else {
        res = await api.getBaiProductListforNotification(
          queryString: prchId,
          page: 0,
          code: '',
          sortBy: '',
        );
        emit(NotificationDataLoaded(notification: res));
      }
    } catch (e) {
      emit(NotificationDataFailed());
    }
  }

  //  loadNotificationData(String prchId) async {
  //   emit(NotificationDataLoading());
  //   try {
  //     var res = await api.getMrData(prchId);
  //     emit(NotificationDataLoaded(notification: res));
  //   } catch (e) {
  //     emit(NotificationDataFailed());
  //   }
  // }
}
