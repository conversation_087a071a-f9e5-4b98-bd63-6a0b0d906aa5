part of 'notification_data_cubit.dart';

class NotificationDataState extends Equatable {
  const NotificationDataState();

  @override
  List<Object> get props => [];
}

class NotificationDataInitial extends NotificationDataState {}

class NotificationInitial extends NotificationDataState {}

class NotificationDataLoaded extends NotificationDataState {
  final BaiProductsRes notification;
  const NotificationDataLoaded({required this.notification});
}

class NotificationDataFailed extends NotificationDataState {}

class NotificationDataLoading extends NotificationDataState {}
