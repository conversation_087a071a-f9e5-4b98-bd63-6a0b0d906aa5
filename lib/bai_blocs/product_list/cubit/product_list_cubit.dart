import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/product_item.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:equatable/equatable.dart';

part 'product_list_state.dart';

class ProductListCubit extends Cubit<ProductListState> {
  ProductListCubit() : super(ProductListInitial());
  var api = NetworkController();
  Future<void> loadProducts(String id) async {
    try {
      emit(ProductListLoading());
      var res = await api.getProductBySubCat(id);
      emit(ProductListLoaded(products: res));
    } catch (e) {
      alert(e.toString());
      emit(ProductListError());
    }
  }
}
