part of 'product_list_cubit.dart';

class ProductListState extends Equatable {
  const ProductListState();

  @override
  List<Object> get props => [];
}

class ProductListInitial extends ProductListState {}

class ProductListLoaded extends ProductListState {
  final List<ProductItem> products;
  const ProductListLoaded({required this.products});
}

class ProductListError extends ProductListState {}

class ProductListLoading extends ProductListState {}
