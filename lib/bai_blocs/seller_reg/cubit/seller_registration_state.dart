part of 'seller_registration_cubit.dart';

class SellerRegistrationState extends Equatable {
  const SellerRegistrationState();

  @override
  List<Object> get props => [];
}

class SellerRegistrationInitial extends SellerRegistrationState {}

class SellerRegistrationLoading extends SellerRegistrationState {}

class SellerRegistrationSuccess extends SellerRegistrationState {
  final String orderId;
  const SellerRegistrationSuccess({required this.orderId});
}

class CategoriesAdded extends SellerRegistrationState {
  const CategoriesAdded();
}

class CategoriesAddError extends SellerRegistrationState {
  final String message;
  const CategoriesAddError({
    required this.message,
  });
}

class SellerRegistrationError extends SellerRegistrationState {
  final String message;

  const SellerRegistrationError({required this.message});
}

class SellerRegistrationError2 extends SellerRegistrationState {
  final String message;

  const SellerRegistrationError2({required this.message});
}

class SellerRegistrationLoaded extends SellerRegistrationState {
  final DistrictsRes? baiCentres;
  final DistrictsRes? baiDistricts;
  final DesignationsRes? designations;
  final NatureOfBusinessRes? natures;
  final VendorsRes? vendors;
  final AreaOfBusinessRes? areas;
  final PurchaseOrderCategoriesRes? fees;
  final List<PricingRes>? pricing;
  final List<RolesRes>? roles;

  const SellerRegistrationLoaded({
    this.roles,
    this.baiCentres,
    this.baiDistricts,
    this.designations,
    this.natures,
    this.vendors,
    this.areas,
    this.fees,
    this.pricing,
  });

  SellerRegistrationLoaded copyWith({
    DistrictsRes? districts,
    DistrictsRes? centers,
    DesignationsRes? designations,
    NatureOfBusinessRes? natures,
    VendorsRes? vendors,
    AreaOfBusinessRes? areas,
    PurchaseOrderCategoriesRes? fees,
    List<PricingRes>? pricing,
    List<RolesRes>? roles,
  }) {
    return SellerRegistrationLoaded(
      baiCentres: centers ?? baiCentres,
      baiDistricts: districts ?? baiDistricts,
      designations: designations ?? this.designations,
      natures: natures ?? this.natures,
      vendors: vendors ?? this.vendors,
      areas: areas ?? this.areas,
      fees: fees ?? this.fees,
      pricing: pricing ?? this.pricing,
      roles: roles ?? this.roles,
    );
  }
}
