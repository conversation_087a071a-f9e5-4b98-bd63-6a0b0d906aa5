import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/area_of_business_res.dart';
import 'package:connectone/bai_models/designation_res.dart';
import 'package:connectone/bai_models/districts_res.dart';
import 'package:connectone/bai_models/nature_of_business_res.dart';
import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/purchase_order_categories_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_models/vendors_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

import '../../../bai_models/add_category_req.dart';
import '../../../bai_models/roles_res.dart';

part 'seller_registration_state.dart';

class SellerRegistrationCubit extends Cubit<SellerRegistrationState> {
  SellerRegistrationCubit() : super(SellerRegistrationInitial());
  var api = NetworkController();
  Future<void> loadData() async {
    emit(SellerRegistrationLoading());
    try {
      var centres = await api.getBaiCentres();
      var districts = await api.getDistricts();
      var areas = await api.getareaOfBusiness();
      // var designations = await api.getDesignations();
      var natures = await api.getNatureOfBusinessSeller();
      var fees = await api.getPurchaseOrderCategories();
      var pricing = await api.getPricing();
      emit(SellerRegistrationLoaded(
        baiCentres: centres,
        baiDistricts: districts,
        areas: areas,
        natures: natures,
        fees: fees,
        pricing: pricing,
        vendors: null,
      ));
    } catch (e) {
      emit(SellerRegistrationError(message: e.toString()));
    }
  }

  Future<void> getVendors(String id) async {
    var previousState = state as SellerRegistrationLoaded;
    emit(SellerRegistrationLoading());
    try {
      var vendors = await api.getVendors1(id, '', 'BUYR');
      emit(previousState.copyWith(vendors: vendors));
    } catch (e) {
      print(e.toString());
      emit(previousState);
    }
  }

  Future<void> getRoles(String id, String type) async {
    print('ventor-------------');
    var previousState = state as SellerRegistrationLoaded;
    emit(SellerRegistrationLoading());
    try {
      var roles = await api.getRoles(id, type);
      print('response-------$roles');
      emit(previousState.copyWith(roles: roles));
    } catch (e) {
      print(e.toString());
      emit(previousState);
    }
  }

  getPricing() async {
    var previousState = state as SellerRegistrationLoaded;
    emit(SellerRegistrationLoading());
    try {
      var pricing = await api.getPricing();
    } catch (e) {
      print(e.toString());
      emit(previousState);
    }
  }

  registerSeller(RegisterReq req) async {
    var previousState = state as SellerRegistrationLoaded;
    emit(SellerRegistrationLoading());
    try {
      await api.registerUser(req);
      emit(const SellerRegistrationSuccess(orderId: "N/A"));
      emit(previousState);
    } catch (e) {
      emit(SellerRegistrationError(message: e.toString()));
      emit(previousState);
    }
  }

  addCategories(AddCategoryReq req) async {
    emit(SellerRegistrationLoading());
    try {
      await api.addCategory(req);
      emit(const CategoriesAdded());
    } catch (e) {
      emit(CategoriesAddError(message: e.toString()));
    }
  }
}
