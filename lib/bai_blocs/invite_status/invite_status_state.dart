part of 'invite_status_cubit.dart';

class InviteStatusState extends Equatable {
  const InviteStatusState();

  @override
  List<Object> get props => [];
}

class InviteStatusInitial extends InviteStatusState {}

class InviteStatusLoading extends InviteStatusState {}

class InviteStatusLoaded extends InviteStatusState {
  final InviteStatusRes inviteList;

  const InviteStatusLoaded({required this.inviteList});
}

class InviteStatusError extends InviteStatusState {
  final String error;

  const InviteStatusError(this.error);
}

class RemindSuccess extends InviteStatusState {}
