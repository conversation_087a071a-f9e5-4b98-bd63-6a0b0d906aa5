import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/invite_status_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'invite_status_state.dart';

class InviteStatusCubit extends Cubit<InviteStatusState> {
  InviteStatusCubit() : super(InviteStatusInitial());

  var networkController = NetworkController();

  Future<void> getInviteStatus(String prchOrdrId) async {
    try {
      emit(InviteStatusLoading());
      var res = await networkController.getInviteStatusList(prchOrdrId);
      emit(InviteStatusLoaded(inviteList: res));
    } catch (e) {
      emit(InviteStatusError(e.toString()));
    }
  }

  Future<void> remind(
    int prchOrdrId,
    int vendorId,
  ) async {
    var previousState = state as InviteStatusLoaded;
    try {
      emit(InviteStatusLoading());
      var res = await networkController.remindInvite(
        prchOrdrId: prchOrdrId,
        vendorId: vendorId,
      );
      emit(RemindSuccess());
      emit(previousState);
    } catch (e) {
      emit(InviteStatusError(e.toString()));
      emit(previousState);
    }
  }
}
