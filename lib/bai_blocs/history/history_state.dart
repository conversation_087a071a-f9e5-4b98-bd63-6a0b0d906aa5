part of 'history_cubit.dart';

class HistoryState extends Equatable {
  const HistoryState();

  @override
  List<Object> get props => [];
}

class HistoryInitial extends HistoryState {}

class HistoryLoading extends HistoryState {}

class HistoryError extends HistoryState {
  final String message;
  const HistoryError({required this.message});
}

class HistoryLoaded extends HistoryState {
  final List<HistoryRes> historyRes;
  const HistoryLoaded({required this.historyRes});
}
