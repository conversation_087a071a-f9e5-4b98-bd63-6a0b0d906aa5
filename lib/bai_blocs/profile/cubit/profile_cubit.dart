import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/user_profile_v2_res.dart';
import 'package:connectone/bai_models/user_v2_res.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:equatable/equatable.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(ProfileInitial());
  var api = NetworkController();
  // Future<void> loadProfile() async {
  //   emit(ProfileLoading());
  //   try {
  //     var res = await api.getUserV2();
  //     emit(ProfileLoaded(userV2Res: res));
  //   } catch (e) {
  //     emit(ProfileError());
  //   }
  // }

  // Future<void> loadUserProfile(num? vendorID) async {
  Future<void> loadUserProfile(dynamic vendorID) async {
    emit(ProfileLoading());
    try {
      var res = await api.getUserProfileV2(vendorID);
      emit(ProfileLoaded(userProfileV2Res: res));
    } catch (e) {
      emit(ProfileError());
    }
  }
}
