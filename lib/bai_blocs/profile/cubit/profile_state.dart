part of 'profile_cubit.dart';

class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object> get props => [];
}

class ProfileInitial extends ProfileState {}

class ProfileLoading extends ProfileState {}

class ProfileError extends ProfileState {}

class ProfileLoaded extends ProfileState {
  final bool isEditable;
  final String firstName;
  final String location;
  final String phoneNumber;
  final String email;
  final String website;
  final UserV2Res? userV2Res;
  final UserProfileV2Res? userProfileV2Res;
  const ProfileLoaded(
      {this.userV2Res,
      this.userProfileV2Res,
      this.isEditable = false,
      this.firstName = "",
      this.location = "",
      this.phoneNumber = "",
      this.email = "",
      this.website = ""});
  @override
  List<Object> get props =>
      [isEditable, firstName, location, phoneNumber, email, website];
}
