import 'package:equatable/equatable.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();
}

class InitializeProfile extends ProfileEvent {
  final bool isEditable;

  const InitializeProfile({this.isEditable = false});

  @override
  List<Object> get props => [isEditable];
}

class EditProfile extends ProfileEvent {
  final bool isEditable;
  final String addressLine1;
  final String firstName;
  final String homeNumber;

  const EditProfile(
      {required this.isEditable,
      required this.addressLine1,
      required this.firstName,
      required this.homeNumber});

  @override
  List<Object> get props => [isEditable, addressLine1, firstName, homeNumber];
}

class DeleteProfile extends ProfileEvent {
  const DeleteProfile();

  @override
  List<Object> get props => [];
}
