// import 'package:bloc/bloc.dart';
// import 'package:connectone/bai_blocs/profile/cubit/profile_cubit.dart';
// import 'package:connectone/bai_blocs/profile/profile_event.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';

// import '../../core/network/network_controller.dart';
// import '../../core/utils/constants.dart';
// import '../../core/utils/safe_print.dart';
// import '../../core/utils/tools.dart';

// class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
//   final networkController = NetworkController();

//   ProfileBloc() : super(ProfileInitial()) {
//     on<InitializeProfile>((event, emit) async {
//       try {
//         final accountData = await networkController.getAccount();
//         emit(
//           ProfileLoaded(
//             userProfileV2Res: accountData,
//             tec2: TextEditingController(),
//             tec3: TextEditingController(),
//             tec1: TextEditingController(),
//           ),
//         );
//       } catch (e) {
//         safePrint(e);
//         alert(sSomethingWentWrong);
//       }
//     });
//     on<EditProfile>((event, emit) async {
//       if (state is ProfileLoaded) {
//         final state = this.state as ProfileLoaded;
//         if (state.isEditable == true) {
//           emit(ProfileInitial());
//           try {
//             var response = await networkController.updateAccount(
//                 homeNumber: event.homeNumber,
//                 firstName: event.firstName,
//                 addressLine1: event.addressLine1);
//             if (response.status == 200) {
//               final accountData = await networkController.getAccount();
//               emit(
//                 ProfileLoaded(
//                   isEditable: !state.isEditable,
//                   account: accountData,
//                   tec2: TextEditingController(),
//                   tec3: TextEditingController(),
//                   tec1: TextEditingController(),
//                 ),
//               );
//             }
//           } catch (e) {
//             alert(e.toString());
//             emit(ProfileLoaded(
//                 account: state.account,
//                 isEditable: !state.isEditable,
//                 tec2: state.tec2,
//                 tec3: state.tec3,
//                 tec1: state.tec1));
//           }
//         } else {
//           emit(ProfileLoaded(
//               account: state.account,
//               isEditable: !state.isEditable,
//               tec2: state.tec2,
//               tec3: state.tec3,
//               tec1: state.tec1));
//         }
//       }
//     });
//     on<DeleteProfile>((event, emit) async {
//       try {
//         var response = await networkController.deleteAccountApi();
//         emit(const Message(message: 'Success'));
//       } catch (e) {
//         alert("Couldn't delete account, please try again!");
//         emit(const Message(message: 'Failed'));
//       }
//     });
//   }
// }
