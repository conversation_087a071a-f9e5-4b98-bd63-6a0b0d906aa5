part of 'quotes_only_cubit.dart';

class QuotesOnlyState extends Equatable {
  const QuotesOnlyState();

  @override
  List<Object> get props => [];
}

class QuotesOnlyInitial extends QuotesOnlyState {}

class BuyerQuotesLoading extends QuotesOnlyState {}

class BuyerQuotesError extends QuotesOnlyState {
  final String message;
  const BuyerQuotesError({required this.message});
}

class BuyerQuotesLoaded extends QuotesOnlyState {
  final BuyerOffersRes buyerOffers;
  final BaiProductsRes bpr;
  final OffersFilterRes filter;

  const BuyerQuotesLoaded({
    required this.buyerOffers,
    required this.bpr,
    required this.filter,
  });
}

class SellerQuotesLoading extends QuotesOnlyState {}

class SellerQuotesSuccess extends QuotesOnlyState {}

class SellerQuotesFailed extends QuotesOnlyState {
  final String message;
  const SellerQuotesFailed({required this.message});
}

class SellerQuotesLoaded extends QuotesOnlyState {
  final SellerOffers offers;
  final BaiProductsRes itemsContent;
  final OffersFilterRes filter;

  const SellerQuotesLoaded({
    required this.offers,
    required this.itemsContent,
    required this.filter,
  });
}
