import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_req.dart' as request;
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:equatable/equatable.dart';

import '../../../bai_models/seller_offers_res.dart';
import '../../../old_models/post_response_model.dart';

part 'quotes_only_state.dart';

class QuotesOnlyCubit extends Cubit<QuotesOnlyState> {
  QuotesOnlyCubit() : super(QuotesOnlyInitial());

  var api = NetworkController();

  loadQuotesBuyer(
    int prchOrdrId,
    ViewOfferReq req, {
    String? brand = "",
    int? offerId, 

  }) async {
    emit(BuyerQuotesLoading());
    try {
      print('val----1');
      
      var bpr = await api.getBaiProductListforNotification(
        queryString: prchOrdrId.toString(),
        page: 0,
        code: '',
        sortBy: '',
      );
      var filters = await api.getOffersFilter(prchOrdrId.toString());
      BuyerOffersRes res;
      if (offerId != null) {
      print('val----12');

        res = await api.getBuyerOffersWithOfferId(
          offerId,
          req,
          brandName: brand,
        );
      } else {
      print('val----123');

        res = await api.getBuyerOffers(
          bpr.content?[0].prchOrdrId?.toString() ?? "0",
          req,
          brandName: brand,
        );
      }
      emit(BuyerQuotesLoaded(
        buyerOffers: res,
        bpr: bpr,
        filter: filters,
      ));
    } catch (e) {
      emit(BuyerQuotesError(message: e.toString()));
    }
  }

  loadQuotesSeller(
    String prchId,
    ViewOfferReq req,
    int ordrGrpId,
  ) async {
    emit(SellerQuotesLoading());
    try {
      var res = await api.getOffersSeller(prchId, req);
      var bpr = await api.getBaiProductListforNotification(
        queryString: prchId.toString(),
        page: 0,
        code: '',
        sortBy: '',
      );
      var filters = await api.getOffersFilter(prchId);
      emit(SellerQuotesLoaded(
        offers: res,
        itemsContent: bpr,
        filter: filters,
      ));
    } catch (e) {
      emit(SellerQuotesFailed(message: e.toString()));
    }
  }

  postSellerOffers(
    List<request.OffersReq> offers,
    String productId,
    String gst,
    bool isEnableGst,
     String? statusCd

  ) async {
    emit(SellerQuotesLoading());
    int? offrId;
    int? status;
    try {
      if (isEnableGst) {
        await api.postGst(productId, gst);
      }

      for (var req in offers) {
        var images = req.paths;
        req.gst = double.tryParse(gst);
        if (images != null && images.isNotEmpty) {
          req.medias ??= [];

          for (var image in images) {
            var res = await api.uploadMedia(image);
            req.medias?.add(request.Media(url: res.fileUrl));
          }
        }
        PostResponse postRes = await api.postOffers(req);
        offrId = postRes.data?.offerId?.first;
        status = postRes.status;

        print('token---------${postRes.data?.offerId?.first}');

      }
        // Call notify-action API after successful offer creation
        // Note: The postOffers API currently returns CommonResponse without offer ID
        // This is a limitation that may need to be addressed by the backend team
        // For now, using a placeholder offer ID (0) - this should be replaced with actual offer ID
        if (status == 200 ) {
            await api.notifyAction(
              prchOrdrId: offers.first.prchOrdrId?.toInt() ?? 0,
              statusCd: 'QUOT',
              offerId: offrId??0, // TODO: Replace with actual offer ID from postOffers response
            );

       
        }
      
      emit(SellerQuotesSuccess());
    } catch (e) {
        print('token122---------${e.toString()}');

      emit(SellerQuotesFailed(message: e.toString()));
    }
  }
}
