part of 'status_dropdown_cubit.dart';

class StatusDropdownState extends Equatable {
  const StatusDropdownState();

  @override
  List<Object> get props => [];
}

class StatusDropdownInitial extends StatusDropdownState {}

class StatusDropdownLoading extends StatusDropdownState {}

class StatusDropdownError extends StatusDropdownState {}

class StatusDropdownLoaded extends StatusDropdownState {
  final StatusListModel? statusRes;

  const StatusDropdownLoaded({this.statusRes});
}

class StatusChangeSuccess extends StatusDropdownState {
  final String message;

  const StatusChangeSuccess({required this.message});
}

class StatusChangeFailed extends StatusDropdownState {
  final String message;

  const StatusChangeFailed({required this.message});
}
