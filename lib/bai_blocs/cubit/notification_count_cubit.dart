import 'package:bloc/bloc.dart';
import 'package:connectone/old_models/notification_count_res.dart';
import 'package:equatable/equatable.dart';

import '../../core/network/network_controller.dart';

part 'notification_count_state.dart';

class NotificationCountCubit extends Cubit<NotificationCountState> {
  NotificationCountCubit() : super(NotificationCountInitial());

  var api = NetworkController();
  Future<void> notificationsCount() async {
    emit(NotificationCountInitial());
    try {
      var res = await api.getNotificationCount();
      emit(NotificationCountLoaded(notificationsCountRes: res));
    } catch (e) {
      emit(NotificationCountError());
    }
  }
}
