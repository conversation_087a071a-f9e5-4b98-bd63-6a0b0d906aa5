part of 'notification_count_cubit.dart';

class NotificationCountState extends Equatable {
  const NotificationCountState();

  @override
  List<Object> get props => [];
}

class NotificationCountInitial extends NotificationCountState {}

class NotificationCountError extends NotificationCountState {}

class NotificationCountLoaded extends NotificationCountState {
  final NotificationsCountRes? notificationsCountRes;

  const NotificationCountLoaded({this.notificationsCountRes});
}
