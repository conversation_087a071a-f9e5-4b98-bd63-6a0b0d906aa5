import 'package:bloc/bloc.dart';
import 'package:connectone/bai_models/change_status_req.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import '../../bai_models/upload_media_res.dart';
import '../../core/network/network_controller.dart';
import '../../old_models/status_list_model.dart';

part 'status_dropdown_state.dart';

class StatusDropdownCubit extends Cubit<StatusDropdownState> {
  StatusDropdownCubit() : super(StatusDropdownInitial());

  var api = NetworkController();

  // Future<void> loadStatuses(
  //   String status,
  //   String ordrGrpId,
  // ) async {
  //   emit(StatusDropdownLoading());
  //   try {
  //     var res = await api.getStatusDropdown(
  //       status,
  //     );
  //     emit(StatusDropdownLoaded(statusRes: res));
  //   } catch (e) {
  //     emit(StatusDropdownError());
  //   }
  // }

  Future<void> changeStatusOfMR(
    ChangeStatusReq req,
    List<String> paths,
    List<Uint8List> bytes,
  ) async {
    emit(StatusDropdownLoading());
    try {
      req.medias = [];
      if (kIsWeb) {
        for (var item in bytes) {
          UploadMediaRes uploadResponse;
          uploadResponse = await api.uploadMediaWeb(item);
          var url = uploadResponse.fileUrl;
          req.medias?.add(Media(url: url));
        }
      } else {
        for (var path in paths) {
          var response1 = await api.uploadMedia(path);
          var url = response1.fileUrl;
          req.medias?.add(Media(url: url));
        }
      }
      var res = await api.changeStatusOfMR(req: req);
      emit(StatusChangeSuccess(message: res.statusDescription));
    } catch (e) {
      emit(StatusChangeFailed(message: e.toString()));
    }
  }
}
