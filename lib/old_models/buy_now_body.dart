// To parse this JSON data, do
//
//     final buyNowBody = buyNowBodyFromJson(jsonString);

import 'dart:convert';

String buyNowBodyToJson(BuyNowBody data) => json.encode(data.toJson());

class BuyNowBody {
  BuyNowBody({
    required this.url,
    required this.customerId,
    required this.stockId,
    required this.quantity,
    required this.amount,
    required this.bidDeskNo,
    required this.orderTypeCd,
  });

  String url;
  String customerId;
  int stockId;
  int quantity;
  int amount;
  String bidDeskNo;
  String orderTypeCd;

  Map<String, dynamic> toJson() => {
        "url": url,
        "customerId": customerId,
        "stockId": stockId,
        "quantity": quantity,
        "amount": amount,
        "bid_desk_no": bidDeskNo,
        "order_type_cd": orderTypeCd,
      };
}
