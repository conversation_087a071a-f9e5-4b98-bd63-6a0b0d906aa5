// To parse this JSON data, do
//
//     final myStocks = myStocksFromJson(jsonString?);

import 'dart:convert';

MyStocks myStocksFromJson(String str) => MyStocks.fromJson(json.decode(str));

class MyStocks {
  MyStocks({
    required this.content,
    required this.pageable,
    required this.totalPages,
    required this.totalElements,
    required this.last,
    required this.first,
    required this.numberOfElements,
    required this.sort,
    required this.size,
    required this.number,
    required this.empty,
  });

  List<Content> content;
  Pageable pageable;
  num? totalPages;
  num? totalElements;
  bool last;
  bool first;
  num? numberOfElements;
  Sort sort;
  num? size;
  num? number;
  bool empty;

  factory MyStocks.fromJson(Map<String?, dynamic> json) => MyStocks(
        content:
            List<Content>.from(json["content"].map((x) => Content.fromJson(x))),
        pageable: Pageable.fromJson(json["pageable"]),
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        last: json["last"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );
}

class Content {
  Content({
    required this.id,
    required this.auctionTypeCd,
    required this.pdctCd,
    required this.methodOfCatch,
    required this.freshness,
    required this.lotNo,
    required this.serviceId,
    this.product,
    required this.itemId,
    required this.priceRange,
    required this.startingPrice,
    required this.grade,
    required this.amount,
    required this.deliveryDate,
    required this.quantity,
    required this.unitId,
    required this.unitName,
    required this.unitShortName,
    required this.customerId,
    required this.buyerCustomerId,
    required this.grade8MmSickdsplit,
    required this.grade8MmFruit,
    required this.grade8MmClean,
    required this.grade8MmTotalPercentage,
    required this.grade7T8MmClean,
    required this.grade7T8MmSickdsplit,
    required this.grade7T8MmFruit,
    required this.grade7T8MmTotalPercentage,
    required this.grade17MmClean,
    required this.grade17MmSickdsplit,
    required this.grade17MmFruit,
    required this.grade17MmTotalPercentage,
    required this.totalSickdsplit,
    required this.totalFruit,
    required this.totalPercentage,
    required this.size,
    required this.literWeight,
    required this.moisture,
    required this.colour,
    required this.totalWeight,
    required this.totalClean,
    required this.image1Thumbnail,
    required this.image1,
    this.image2Thumbnail,
    this.image2,
    this.image3Thumbnail,
    this.image3,
    this.image4Thumbnail,
    this.image4,
    this.image5Thumbnail,
    this.image5,
    required this.pickupAddressLine1,
    required this.pickupAddressLine2,
    required this.pickupCity,
    required this.pickupState,
    required this.pickupCountry,
    required this.pickupPincode,
    required this.pickupLat,
    required this.pickupLng,
    required this.vendorId,
    required this.vendorName,
    required this.roomId,
    required this.stockStatusName,
    required this.stockStatusCd,
    required this.auctionCloseTs,
    required this.favouriteYn,
    required this.averageRating,
    required this.rateCount,
    required this.uniqueViews,
    required this.totalViews,
    required this.highestBidAmount,
    required this.rejectionsClean,
    required this.rejectionsSickdsplit,
    required this.rejectionsFruit,
    required this.rejectionsTotalpercentage,
    required this.expectedPrice,
  });

  num? id;
  String? auctionTypeCd;
  String? pdctCd;
  dynamic methodOfCatch;
  dynamic freshness;
  num? lotNo;
  num? serviceId;
  String? product;
  num? itemId;
  String? priceRange;
  num? startingPrice;
  String? grade;
  num? amount;
  DateTime deliveryDate;
  num? quantity;
  num? unitId;
  String? unitName;
  String? unitShortName;
  num? customerId;
  num? buyerCustomerId;
  num? grade8MmClean;
  num? grade8MmSickdsplit;
  num? grade8MmFruit;
  num? grade8MmTotalPercentage;
  num? grade7T8MmClean;
  num? grade7T8MmSickdsplit;
  num? grade7T8MmFruit;
  num? grade7T8MmTotalPercentage;
  num? grade17MmClean;
  num? grade17MmSickdsplit;
  num? grade17MmFruit;
  num? grade17MmTotalPercentage;
  num? totalSickdsplit;
  num? totalFruit;
  num? totalPercentage;
  String? size;
  String? literWeight;
  String? moisture;
  String? colour;
  String? totalWeight;
  num? totalClean;
  String? image1Thumbnail;
  String? image1;
  dynamic image2Thumbnail;
  dynamic image2;
  dynamic image3Thumbnail;
  dynamic image3;
  dynamic image4Thumbnail;
  dynamic image4;
  dynamic image5Thumbnail;
  dynamic image5;
  String? pickupAddressLine1;
  String? pickupAddressLine2;
  String? pickupCity;
  dynamic pickupState;
  String? pickupCountry;
  String? pickupPincode;
  String? pickupLat;
  String? pickupLng;
  num? vendorId;
  String? vendorName;
  dynamic roomId;
  String? stockStatusName;
  String? stockStatusCd;
  dynamic auctionCloseTs;
  String? favouriteYn;
  String? averageRating;
  String? rateCount;
  String? uniqueViews;
  String? totalViews;
  num? highestBidAmount;
  num? rejectionsClean;
  num? rejectionsSickdsplit;
  num? rejectionsFruit;
  num? rejectionsTotalpercentage;
  num? expectedPrice;

  factory Content.fromJson(Map<String?, dynamic> json) => Content(
        id: json["id"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        methodOfCatch: json["methodOfCatch"],
        freshness: json["freshness"],
        lotNo: json["lotNo"],
        serviceId: json["serviceId"],
        product: json["product"],
        itemId: json["itemId"],
        priceRange: json["priceRange"],
        startingPrice: json["startingPrice"],
        grade: json["grade"],
        amount: json["amount"],
        deliveryDate: DateTime.parse(json["deliveryDate"]),
        quantity: json["quantity"],
        unitId: json["unitId"],
        unitName: json["unitName"],
        unitShortName: json["unitShortName"],
        customerId: json["customerId"],
        buyerCustomerId: json["buyerCustomerId"],
        grade8MmClean: json["grade8mmClean"],
        grade8MmSickdsplit: json["grade8mmSickdsplit"],
        grade8MmFruit: json["grade8mmFruit"],
        grade8MmTotalPercentage: json["grade8mmTotalPercentage"],
        grade7T8MmClean: json["grade7t8mmClean"],
        grade7T8MmSickdsplit: json["grade7t8mmSickdsplit"],
        grade7T8MmFruit: json["grade7t8mmFruit"],
        grade7T8MmTotalPercentage: json["grade7t8mmTotalPercentage"],
        grade17MmClean: json["grade17mmClean"],
        grade17MmSickdsplit: json["grade17mmSickdsplit"],
        grade17MmFruit: json["grade17mmFruit"],
        grade17MmTotalPercentage: json["grade17mmTotalPercentage"],
        totalSickdsplit: json["totalSickdsplit"],
        totalFruit: json["totalFruit"],
        totalPercentage: json["totalPercentage"],
        size: json["size"],
        literWeight: json["literWeight"],
        moisture: json["moisture"],
        colour: json["colour"],
        totalWeight: json["totalWeight"],
        totalClean: json["totalClean"],
        image1Thumbnail: json["image1Thumbnail"],
        image1: json["image1"],
        image2Thumbnail: json["image2Thumbnail"],
        image2: json["image2"],
        image3Thumbnail: json["image3Thumbnail"],
        image3: json["image3"],
        image4Thumbnail: json["image4Thumbnail"],
        image4: json["image4"],
        image5Thumbnail: json["image5Thumbnail"],
        image5: json["image5"],
        pickupAddressLine1: json["pickupAddressLine1"],
        pickupAddressLine2: json["pickupAddressLine2"],
        pickupCity: json["pickupCity"],
        pickupState: json["pickupState"],
        pickupCountry: json["pickupCountry"],
        pickupPincode: json["pickupPincode"],
        pickupLat: json["pickupLat"],
        pickupLng: json["pickupLng"],
        vendorId: json["vendorId"],
        vendorName: json["vendorName"],
        roomId: json["roomId"],
        stockStatusName: json["stockStatusName"],
        stockStatusCd: json["stockStatusCd"],
        auctionCloseTs: json["auctionCloseTs"],
        favouriteYn: json["favourite_YN"],
        averageRating: json["averageRating"],
        rateCount: json["rateCount"],
        uniqueViews: json["uniqueViews"],
        totalViews: json["totalViews"],
        highestBidAmount: json["highestBidAmount"],
        rejectionsClean: json["rejectionsClean"],
        rejectionsSickdsplit: json["rejectionsSickdsplit"],
        rejectionsFruit: json["rejectionsFruit"],
        rejectionsTotalpercentage: json["rejectionsTotalpercentage"],
        expectedPrice: json["expectedPrice"],
      );
}

class Pageable {
  Pageable({
    required this.sort,
    required this.pageNumber,
    required this.pageSize,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  Sort sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool paged;
  bool unpaged;

  factory Pageable.fromJson(Map<String?, dynamic> json) => Pageable(
        sort: Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );
}

class Sort {
  Sort({
    required this.unsorted,
    required this.sorted,
    required this.empty,
  });

  bool unsorted;
  bool sorted;
  bool empty;

  factory Sort.fromJson(Map<String?, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );
}
