// To parse this JSON data, do
//
//     final myStocks = myStocksFromJson(jsonString);

import 'dart:convert';

SoldOut myStocksFromJson(String str) => SoldOut.fromJson(json.decode(str));

String myStocksToJson(SoldOut data) => json.encode(data.toJson());

class SoldOut {
  SoldOut({
    required this.content,
    required this.pageable,
    required this.totalPages,
    required this.totalElements,
    required this.last,
    required this.first,
    required this.numberOfElements,
    required this.sort,
    required this.size,
    required this.number,
    required this.empty,
  });

  List<Content> content;
  Pageable pageable;
  int totalPages;
  int totalElements;
  bool last;
  bool first;
  int numberOfElements;
  Sort sort;
  int size;
  int number;
  bool empty;

  factory SoldOut.fromJson(Map<String, dynamic> json) => SoldOut(
        content:
            List<Content>.from(json["content"].map((x) => Content.fromJson(x))),
        pageable: Pageable.fromJson(json["pageable"]),
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        last: json["last"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": List<dynamic>.from(content.map((x) => x.toJson())),
        "pageable": pageable.toJson(),
        "totalPages": totalPages,
        "totalElements": totalElements,
        "last": last,
        "first": first,
        "numberOfElements": numberOfElements,
        "sort": sort.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  Content({
    required this.id,
    // required this.auctionTypeCd,
    // required this.methodOfCatch,
    // required this.freshness,
    required this.lotNo,
    // required this.serviceId,
    this.product,
    // required this.itemId,
    // required this.priceRange,
    // required this.startingPrice,
    required this.grade,
    required this.amount,
    required this.deliveryDate,
    required this.quantity,
    // required this.unitId,
    // required this.unitName,
    // required this.unitShortName,
    required this.customerId,
    //required this.buyerCustomerId,
    required this.grade8MmClean,
    required this.grade8MmSickdsplit,
    required this.grade8MmFruit,
    required this.grade8MmTotalPercentage,
    required this.grade7T8MmClean,
    required this.grade7T8MmSickdsplit,
    required this.grade7T8MmFruit,
    required this.grade7T8MmTotalPercentage,
    required this.grade17MmClean,
    required this.grade17MmSickdsplit,
    required this.grade17MmFruit,
    required this.grade17MmTotalPercentage,
    required this.totalSickdsplit,
    required this.totalFruit,
    required this.totalPercentage,
    required this.size,
    required this.literWeight,
    required this.moisture,
    required this.colour,
    required this.totalWeight,
    required this.totalClean,
    // this.image1Thumbnail,
    this.image1,
    // this.image2Thumbnail,
    // this.image2,
    // this.image3Thumbnail,
    // this.image3,
    // this.image4Thumbnail,
    // this.image4,
    // this.image5Thumbnail,
    // this.image5,
    // required this.pickupAddressLine1,
    // required this.pickupAddressLine2,
    required this.pickupCity,
    // required this.pickupState,
    // required this.pickupCountry,
    // required this.pickupPincode,
    // required this.pickupLat,
    // required this.pickupLng,
    required this.vendorId,
    required this.vendorName,
    // required this.roomId,
    // required this.stockStatusName,
    // required this.stockStatusCd,
    // required this.auctionCloseTs,
    // required this.favouriteYn,
    required this.averageRating,
    // required this.rateCount,
    // required this.uniqueViews,
    // required this.totalViews,
    required this.highestBidAmount,
    required this.rejectionsClean,
    required this.rejectionsSickdsplit,
    required this.rejectionsFruit,
    required this.rejectionsTotalpercentage,
  });

  int id;

  // late String auctionTypeCd;
  // late dynamic methodOfCatch;
  // late dynamic freshness;
  int? lotNo;

  // late int serviceId;
  String? product;

  // late int itemId;
  // late String priceRange;
  // late int startingPrice;
  String? grade;
  double? amount;
  String? deliveryDate;
  double quantity;

  // int unitId;
  // String unitName;
  // String unitShortName;
  int customerId;

  // int buyerCustomerId;
  double? grade8MmClean;
  double? grade8MmSickdsplit;
  double? grade8MmFruit;
  double? grade8MmTotalPercentage;
  double? grade7T8MmClean;
  double? grade7T8MmSickdsplit;
  double? grade7T8MmFruit;
  double? grade7T8MmTotalPercentage;
  double? grade17MmClean;
  double? grade17MmSickdsplit;
  double? grade17MmFruit;
  double? grade17MmTotalPercentage;
  double? totalSickdsplit;
  double? totalFruit;
  double? totalPercentage;
  String? size;
  String? literWeight;
  String? moisture;
  String? colour;
  String? totalWeight;
  double? totalClean;

  // dynamic image1Thumbnail;
  String? image1;

  // dynamic image2Thumbnail;
  // dynamic image2;
  // dynamic image3Thumbnail;
  // dynamic image3;
  // dynamic image4Thumbnail;
  // dynamic image4;
  // dynamic image5Thumbnail;
  // dynamic image5;
  // String pickupAddressLine1;
  // String pickupAddressLine2;
  late String? pickupCity;

  // late dynamic pickupState;
  // String pickupCountry;
  // String pickupPincode;
  // String pickupLat;
  // String pickupLng;
  int vendorId;
  String? vendorName;

  // dynamic roomId;
  // String stockStatusName;
  // String stockStatusCd;
  // dynamic auctionCloseTs;
  // String favouriteYn;
  String? averageRating;

  // String rateCount;
  // String uniqueViews;
  // String totalViews;
  double? highestBidAmount;
  double? rejectionsClean;
  double? rejectionsSickdsplit;
  double? rejectionsFruit;
  double? rejectionsTotalpercentage;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        // auctionTypeCd: json["auctionTypeCd"],
        // methodOfCatch: json["methodOfCatch"],
        // freshness: json["freshness"],
        lotNo: json["lotNo"],
        // serviceId: json["serviceId"],
        product: json["product"],
        // itemId: json["itemId"],
        // priceRange: json["priceRange"],
        // startingPrice: json["startingPrice"],
        grade: json["grade"],
        amount: json["amount"],
        deliveryDate: json["deliveryDate"],
        quantity: json["quantity"].toDouble(),
        // unitId: json["unitId"],
        // unitName: json["unitName"],
        // unitShortName: json["unitShortName"],
        customerId: json["customerId"],
        // buyerCustomerId: json["buyerCustomerId"],
        grade8MmClean: json["grade8mmClean"],
        grade8MmSickdsplit: json["grade8mmSickdsplit"],
        grade8MmFruit: json["grade8mmFruit"],
        grade8MmTotalPercentage: json["grade8mmTotalPercentage"],
        grade7T8MmClean: json["grade7t8mmClean"],
        grade7T8MmSickdsplit: json["grade7t8mmSickdsplit"],
        grade7T8MmFruit: json["grade7t8mmFruit"],
        grade7T8MmTotalPercentage: json["grade7t8mmTotalPercentage"],
        grade17MmClean: json["grade17mmClean"],
        grade17MmSickdsplit: json["grade17mmSickdsplit"],
        grade17MmFruit: json["grade17mmFruit"],
        grade17MmTotalPercentage: json["grade17mmTotalPercentage"],
        totalSickdsplit: json["totalSickdsplit"],
        totalFruit: json["totalFruit"],
        totalPercentage: json["totalPercentage"],
        size: json["size"],
        literWeight: json["literWeight"],
        moisture: json["moisture"],
        colour: json["colour"],
        totalWeight: json["totalWeight"],
        totalClean: json["totalClean"],
        // image1Thumbnail: json["image1Thumbnail"],
        image1: json["image1"],
        // image2Thumbnail: json["image2Thumbnail"],
        // image2: json["image2"],
        // image3Thumbnail: json["image3Thumbnail"],
        // image3: json["image3"],
        // image4Thumbnail: json["image4Thumbnail"],
        // image4: json["image4"],
        // image5Thumbnail: json["image5Thumbnail"],
        // image5: json["image5"],
        // pickupAddressLine1: json["pickupAddressLine1"],
        // pickupAddressLine2: json["pickupAddressLine2"],
        pickupCity: json["pickupCity"],
        // pickupState: json["pickupState"],
        // pickupCountry: json["pickupCountry"],
        // pickupPincode: json["pickupPincode"],
        // pickupLat: json["pickupLat"],
        // pickupLng: json["pickupLng"],
        vendorId: json["vendorId"],
        vendorName: json["vendorName"],
        // roomId: json["roomId"],
        // stockStatusName: json["stockStatusName"],
        // stockStatusCd: json["stockStatusCd"],
        // auctionCloseTs: json["auctionCloseTs"],
        // favouriteYn: json["favourite_YN"],
        averageRating: json["averageRating"],
        // rateCount: json["rateCount"],
        // uniqueViews: json["uniqueViews"],
        // totalViews: json["totalViews"],
        highestBidAmount: json["highestBidAmount"],
        rejectionsClean: json["rejectionsClean"],
        rejectionsSickdsplit: json["rejectionsSickdsplit"],
        rejectionsFruit: json["rejectionsFruit"],
        rejectionsTotalpercentage: json["rejectionsTotalpercentage"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        // "auctionTypeCd": auctionTypeCd,
        // "methodOfCatch": methodOfCatch,
        // "freshness": freshness,
        "lotNo": lotNo,
        // "serviceId": serviceId,
        // "product": product,
        // "itemId": itemId,
        // "priceRange": priceRange,
        // "startingPrice": startingPrice,
        "grade": grade,
        "amount": amount,
        "deliveryDate": deliveryDate,
        "quantity": quantity,
        // "unitId": unitId,
        // "unitName": unitName,
        // "unitShortName": unitShortName,
        // "customerId": customerId,
        // "buyerCustomerId": buyerCustomerId,
        "grade8mmClean": grade8MmClean,
        "grade8mmSickdsplit": grade8MmSickdsplit,
        "grade8mmFruit": grade8MmFruit,
        "grade8mmTotalPercentage": grade8MmTotalPercentage,
        "grade7t8mmClean": grade7T8MmClean,
        "grade7t8mmSickdsplit": grade7T8MmSickdsplit,
        "grade7t8mmFruit": grade7T8MmFruit,
        "grade7t8mmTotalPercentage": grade7T8MmTotalPercentage,
        "grade17mmClean": grade17MmClean,
        "grade17mmSickdsplit": grade17MmSickdsplit,
        "grade17mmFruit": grade17MmFruit,
        "grade17mmTotalPercentage": grade17MmTotalPercentage,
        "totalSickdsplit": totalSickdsplit,
        "totalFruit": totalFruit,
        "totalPercentage": totalPercentage,
        "size": size,
        "literWeight": literWeight,
        "moisture": moisture,
        "colour": colour,
        "totalWeight": totalWeight,
        "totalClean": totalClean,
        // "image1Thumbnail": image1Thumbnail,
        // "image1": image1,
        // "image2Thumbnail": image2Thumbnail,
        // "image2": image2,
        // "image3Thumbnail": image3Thumbnail,
        // "image3": image3,
        // "image4Thumbnail": image4Thumbnail,
        // "image4": image4,
        // "image5Thumbnail": image5Thumbnail,
        // "image5": image5,
        // "pickupAddressLine1": pickupAddressLine1,
        // "pickupAddressLine2": pickupAddressLine2,
        "pickupCity": pickupCity,
        // "pickupState": pickupState,
        // "pickupCountry": pickupCountry,
        // "pickupPincode": pickupPincode,
        // "pickupLat": pickupLat,
        // "pickupLng": pickupLng,
        "vendorId": vendorId,
        "vendorName": vendorName,
        // "roomId": roomId,
        // "stockStatusName": stockStatusName,
        // "stockStatusCd": stockStatusCd,
        // "auctionCloseTs": auctionCloseTs,
        // "favourite_YN": favouriteYn,
        // "averageRating": averageRating,
        // "rateCount": rateCount,
        // "uniqueViews": uniqueViews,
        // "totalViews": totalViews,
        "highestBidAmount": highestBidAmount,
        "rejectionsClean": rejectionsClean,
        "rejectionsSickdsplit": rejectionsSickdsplit,
        "rejectionsFruit": rejectionsFruit,
        "rejectionsTotalpercentage": rejectionsTotalpercentage,
      };
}

class Pageable {
  Pageable({
    required this.sort,
    required this.pageNumber,
    required this.pageSize,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  Sort sort;
  int pageNumber;
  int pageSize;
  int offset;
  bool paged;
  bool unpaged;

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort.toJson(),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  Sort({
    required this.unsorted,
    required this.sorted,
    required this.empty,
  });

  bool unsorted;
  bool sorted;
  bool empty;

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
