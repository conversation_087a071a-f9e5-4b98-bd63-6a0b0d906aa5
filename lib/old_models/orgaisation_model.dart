class OrganisationModel {
  OrganisationModel({
    this.data,
    this.checkinStatus,
    this.status,
    this.statusDescription,
  });

  OrganisationData? data;
  int? checkinStatus;
  int? status;
  String? statusDescription;

  OrganisationModel.fromJson(Map<String, dynamic> json) {
    data =
        json['data'] != null ? OrganisationData.fromJson(json['data']) : null;
    checkinStatus = json['checkin_status'];
    status = json['status'];
    statusDescription = json['status_description'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['data'] = this.data?.toJson();
    data['checkin_status'] = checkinStatus;
    data['status'] = status;
    data['status_description'] = statusDescription;
    return data;
  }
}

class OrganisationData {
  OrganisationData({
    this.organizationName,
    this.tenantId,
    this.currencyName,
    this.currencyShortCode,
    this.privacyPolicy,
    this.locationVerificationFlag,
    this.customerAppProductDisplay,
    this.feedbackYn,
    this.reservationYn,
    this.waiterCallingYn,
    this.customerRegEmail,
    this.customerRegAddress,
    this.customerRegName,
    this.customerRegPassword,
    this.legalDocs,
    this.adminPortalUrl,
    this.addressOptional,
    this.recurringOrder,
    this.roomVerificationFlag,
    this.currencySymbol,
    this.gotoCartTxt,
    this.databaseKey,
    this.goShoppingTxt,
    this.organizationId,
    this.registrationButtonColor,
    this.cappAuctionCalendarPage,
    this.userPoolId,
    this.cognitoRegion,
    this.appClientWeb,
    this.appClientAndroid,
    this.appClientAdmin,
    this.appClientIos,
    this.showOfflineCalendarYn,
    this.liveAuctionVisibleYn,
    this.offlineAuctionVisibleYn,
    this.liveAuctionName,
    this.offlineAuctionName,
    this.buyerRegistrationUrl,
    this.sellerRegistrationUrl,
    this.customerLoginUrl,
    this.myStocksUrl,
    this.addStocksUrl,
    this.retailYn,
    this.showAddStocksYn,
    this.showViewStocksYn,
    this.showRetailYn,
    this.offerYn,
    this.marginYn,
    this.orderingAppUrl,
  });

  String? organizationName;
  String? tenantId;
  String? currencyName;
  String? currencyShortCode;
  String? privacyPolicy;
  String? locationVerificationFlag;
  String? customerAppProductDisplay;
  String? feedbackYn;
  String? reservationYn;
  String? waiterCallingYn;
  String? customerRegEmail;
  String? customerRegAddress;
  String? customerRegName;
  String? customerRegPassword;
  String? legalDocs;
  String? adminPortalUrl;
  String? addressOptional;
  bool? recurringOrder;
  String? roomVerificationFlag;
  String? currencySymbol;
  String? gotoCartTxt;
  String? databaseKey;
  String? goShoppingTxt;
  int? organizationId;
  String? registrationButtonColor;
  String? cappAuctionCalendarPage;
  String? userPoolId;
  String? cognitoRegion;
  String? appClientWeb;
  String? appClientAndroid;
  String? appClientAdmin;
  String? appClientIos;
  String? showOfflineCalendarYn;
  String? liveAuctionVisibleYn;
  String? offlineAuctionVisibleYn;
  String? liveAuctionName;
  String? offlineAuctionName;
  String? buyerRegistrationUrl;
  String? sellerRegistrationUrl;
  String? customerLoginUrl;
  String? myStocksUrl;
  String? addStocksUrl;
  String? retailYn;
  String? showAddStocksYn;
  String? showViewStocksYn;
  String? showRetailYn;
  String? offerYn;
  String? marginYn;
  String? orderingAppUrl;

  OrganisationData.fromJson(Map<String, dynamic> json) {
    organizationName = json['organization_name'];
    tenantId = json['tenant_id'];
    currencyName = json['currency_name'];
    currencyShortCode = json['currency_short_code'];
    privacyPolicy = json['privacy_policy'];
    locationVerificationFlag = json['location_verification_flag'];
    customerAppProductDisplay = json['customer_app_product_display'];
    feedbackYn = json['feedback_yn'];
    reservationYn = json['reservation_yn'];
    waiterCallingYn = json['waiter_calling_yn'];
    customerRegEmail = json['customer_reg_email'];
    customerRegAddress = json['customer_reg_address'];
    customerRegName = json['customer_reg_name'];
    customerRegPassword = json['customer_reg_password'];
    legalDocs = json['legal_docs'];
    adminPortalUrl = json['admin_portal_url'];
    addressOptional = json['address_optional'];
    recurringOrder = json['recurring_order'];
    roomVerificationFlag = json['room_verification_flag'];
    currencySymbol = json['currency_symbol'];
    gotoCartTxt = json['goto_cart_txt'];
    databaseKey = json['database_key'];
    goShoppingTxt = json['go_shopping_txt'];
    organizationId = json['organization_id'];
    registrationButtonColor = json['registration_button_color'];
    cappAuctionCalendarPage = json['capp_auction_calendar_page'];
    userPoolId = json['user_pool_id'];
    cognitoRegion = json['cognito_region'];
    appClientWeb = json['app_client_web'];
    appClientAndroid = json['app_client_android'];
    appClientAdmin = json['app_client_admin'];
    appClientIos = json['app_client_ios'];
    showOfflineCalendarYn = json['show_offline_calendar_yn'];
    liveAuctionVisibleYn = json['live_auction_visible_yn'];
    offlineAuctionVisibleYn = json['offline_auction_visible_yn'];
    liveAuctionName = json['live_auction_name'];
    offlineAuctionName = json['offline_auction_name'];
    buyerRegistrationUrl = json['buyer_registration_url'];
    sellerRegistrationUrl = json['seller_registration_url'];
    customerLoginUrl = json['customer_login_url'];
    myStocksUrl = json['my_stocks_url'];
    addStocksUrl = json['add_stocks_url'];
    retailYn = json['retail_yn'];
    showAddStocksYn = json['show_add_stocks_yn'];
    showViewStocksYn = json['show_view_stocks_yn'];
    showRetailYn = json['show_retail_yn'];
    offerYn = json['offer_yn'];
    marginYn = json['margin_yn'];
    orderingAppUrl = json['ordering_app_url'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['organization_name'] = organizationName;
    data['tenant_id'] = tenantId;
    data['currency_name'] = currencyName;
    data['currency_short_code'] = currencyShortCode;
    data['privacy_policy'] = privacyPolicy;
    data['location_verification_flag'] = locationVerificationFlag;
    data['customer_app_product_display'] = customerAppProductDisplay;
    data['feedback_yn'] = feedbackYn;
    data['reservation_yn'] = reservationYn;
    data['waiter_calling_yn'] = waiterCallingYn;
    data['customer_reg_email'] = customerRegEmail;
    data['customer_reg_address'] = customerRegAddress;
    data['customer_reg_name'] = customerRegName;
    data['customer_reg_password'] = customerRegPassword;
    data['legal_docs'] = legalDocs;
    data['admin_portal_url'] = adminPortalUrl;
    data['address_optional'] = addressOptional;
    data['recurring_order'] = recurringOrder;
    data['room_verification_flag'] = roomVerificationFlag;
    data['currency_symbol'] = currencySymbol;
    data['goto_cart_txt'] = gotoCartTxt;
    data['database_key'] = databaseKey;
    data['go_shopping_txt'] = goShoppingTxt;
    data['organization_id'] = organizationId;
    data['registration_button_color'] = registrationButtonColor;
    data['capp_auction_calendar_page'] = cappAuctionCalendarPage;
    data['user_pool_id'] = userPoolId;
    data['cognito_region'] = cognitoRegion;
    data['app_client_web'] = appClientWeb;
    data['app_client_android'] = appClientAndroid;
    data['app_client_admin'] = appClientAdmin;
    data['app_client_ios'] = appClientIos;
    data['show_offline_calendar_yn'] = showOfflineCalendarYn;
    data['live_auction_visible_yn'] = liveAuctionVisibleYn;
    data['offline_auction_visible_yn'] = offlineAuctionVisibleYn;
    data['live_auction_name'] = liveAuctionName;
    data['offline_auction_name'] = offlineAuctionName;
    data['buyer_registration_url'] = buyerRegistrationUrl;
    data['customer_login_url'] = customerLoginUrl;
    data['my_stocks_url'] = myStocksUrl;
    data['add_stocks_url'] = addStocksUrl;
    data['retail_yn'] = retailYn;
    data['show_add_stocks_yn'] = showAddStocksYn;
    data['show_view_stocks_yn'] = showViewStocksYn;
    data['show_retail_yn'] = showRetailYn;
    data['offer_yn'] = offerYn;
    data['ordering_app_url'] = orderingAppUrl;
    return data;
  }
}
