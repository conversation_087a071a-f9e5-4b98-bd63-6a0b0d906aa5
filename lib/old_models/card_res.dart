class CardRes {
  final Data? data;
  final num? status;
  final String? statusDescription;

  CardRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  CardRes.fromJson(Map<String, dynamic> json)
      : data = (json['data'] as Map<String, dynamic>?) != null
            ? Data.fromJson(json['data'] as Map<String, dynamic>)
            : null,
        status = json['status'] as num?,
        statusDescription = json['status_description'] as String?;

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
        'status': status,
        'status_description': statusDescription
      };
}

class Data {
  final num? id;
  final num? orderId;
  final num? grade8mmClean;
  final num? grade8mmSickdsplit;
  final num? grade8mmFruit;
  final num? grade8mmTotalpercentage;
  final num? grade7t8mmClean;
  final num? grade7t8mmSickdsplit;
  final num? grade7t8mmFruit;
  final num? grade7t8mmTotalpercentage;
  final num? grade17mmClean;
  final num? grade17mmSickdsplit;
  final num? grade17mmFruit;
  final num? grade17mmTotalpercentage;
  final num? totalClean;
  final num? totalSickdsplit;
  final num? totalFruit;
  final num? totalPercentage;
  final String? size;
  final num? ltrWt;
  final num? moisture;
  final String? colour;
  final String? totalWeight;
  final String? noOfBags;
  final String? category;
  final String? misc;
  final String? grade;
  final String? createdAt;
  final String? modifiedAt;
  final String? rejectionsClean;
  final String? rejectionsSickdsplit;
  final String? rejectionsFruit;
  final String? rejectionsTotalpercentage;

  Data({
    this.id,
    this.orderId,
    this.grade8mmClean,
    this.grade8mmSickdsplit,
    this.grade8mmFruit,
    this.grade8mmTotalpercentage,
    this.grade7t8mmClean,
    this.grade7t8mmSickdsplit,
    this.grade7t8mmFruit,
    this.grade7t8mmTotalpercentage,
    this.grade17mmClean,
    this.grade17mmSickdsplit,
    this.grade17mmFruit,
    this.grade17mmTotalpercentage,
    this.totalClean,
    this.totalSickdsplit,
    this.totalFruit,
    this.totalPercentage,
    this.size,
    this.ltrWt,
    this.moisture,
    this.colour,
    this.totalWeight,
    this.noOfBags,
    this.category,
    this.misc,
    this.grade,
    this.createdAt,
    this.modifiedAt,
    this.rejectionsClean,
    this.rejectionsSickdsplit,
    this.rejectionsFruit,
    this.rejectionsTotalpercentage,
  });

  Data.fromJson(Map<String, dynamic> json)
      : id = json['id'] as num?,
        orderId = json['order_id'] as num?,
        grade8mmClean = json['grade8mm_clean'] as num?,
        grade8mmSickdsplit = json['grade8mm_sickdsplit'] as num?,
        grade8mmFruit = json['grade8mm_fruit'] as num?,
        grade8mmTotalpercentage = json['grade8mm_totalpercentage'] as num?,
        grade7t8mmClean = json['grade7t8mm_clean'] as num?,
        grade7t8mmSickdsplit = json['grade7t8mm_sickdsplit'] as num?,
        grade7t8mmFruit = json['grade7t8mm_fruit'] as num?,
        grade7t8mmTotalpercentage = json['grade7t8mm_totalpercentage'] as num?,
        grade17mmClean = json['grade17mm_clean'] as num?,
        grade17mmSickdsplit = json['grade17mm_sickdsplit'] as num?,
        grade17mmFruit = json['grade17mm_fruit'] as num?,
        grade17mmTotalpercentage = json['grade17mm_totalpercentage'] as num?,
        totalClean = json['total_clean'] as num?,
        totalSickdsplit = json['total_sickdsplit'] as num?,
        totalFruit = json['total_fruit'] as num?,
        totalPercentage = json['total_percentage'] as num?,
        size = json['size'] as String?,
        ltrWt = json['ltr_wt'] as num?,
        moisture = json['moisture'] as num?,
        colour = json['colour'] as String?,
        totalWeight = json['total_weight'] as String?,
        noOfBags = json['no_of_bags'] as String?,
        category = json['category'] as String?,
        misc = json['misc'] as String?,
        grade = json['grade'] as String?,
        createdAt = json['created_at'] as String?,
        modifiedAt = json['modified_at'] as String?,
        rejectionsClean = json['rejections_clean'] as String?,
        rejectionsSickdsplit = json['rejections_sickdsplit'] as String?,
        rejectionsFruit = json['rejections_fruit'] as String?,
        rejectionsTotalpercentage =
            json['rejections_totalpercentage'] as String?;

  Map<String, dynamic> toJson() => {
        'id': id,
        'order_id': orderId,
        'grade8mm_clean': grade8mmClean,
        'grade8mm_sickdsplit': grade8mmSickdsplit,
        'grade8mm_fruit': grade8mmFruit,
        'grade8mm_totalpercentage': grade8mmTotalpercentage,
        'grade7t8mm_clean': grade7t8mmClean,
        'grade7t8mm_sickdsplit': grade7t8mmSickdsplit,
        'grade7t8mm_fruit': grade7t8mmFruit,
        'grade7t8mm_totalpercentage': grade7t8mmTotalpercentage,
        'grade17mm_clean': grade17mmClean,
        'grade17mm_sickdsplit': grade17mmSickdsplit,
        'grade17mm_fruit': grade17mmFruit,
        'grade17mm_totalpercentage': grade17mmTotalpercentage,
        'total_clean': totalClean,
        'total_sickdsplit': totalSickdsplit,
        'total_fruit': totalFruit,
        'total_percentage': totalPercentage,
        'size': size,
        'ltr_wt': ltrWt,
        'moisture': moisture,
        'colour': colour,
        'total_weight': totalWeight,
        'no_of_bags': noOfBags,
        'category': category,
        'misc': misc,
        'grade': grade,
        'created_at': createdAt,
        'modified_at': modifiedAt,
        'rejections_clean': rejectionsClean,
        'rejections_sickdsplit': rejectionsSickdsplit,
        'rejections_fruit': rejectionsFruit,
        'rejections_totalpercentage': rejectionsTotalpercentage
      };
}
