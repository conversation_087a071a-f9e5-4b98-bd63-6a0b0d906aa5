// To parse this JSON data, do
//
//     final autobidBody = autobidBodyFromJson(jsonString);

import 'dart:convert';

String autobidBodyToJson(AutobidBody data) => json.encode(data.toJson());

class AutobidBody {
  AutobidBody({
    required this.url,
    required this.customerId,
    required this.stockId,
    required this.quantity,
    required this.limit,
    required this.increment,
    required this.bidDeskNo,
    required this.orderTypeCd,
  });

  String url;
  String customerId;
  int stockId;
  int quantity;
  int limit;
  String increment;
  String bidDeskNo;
  String orderTypeCd;

  Map<String, dynamic> toJson() => {
        "url": url,
        "customerId": customerId,
        "stockId": stockId,
        "quantity": quantity,
        "limit": limit,
        "increment": increment,
        "bid_desk_no": bidDeskNo,
        "order_type_cd": orderTypeCd,
      };
}
