// To parse this JSON data, do
//
//     final acceptBidBody = acceptBidBodyFromJson(jsonString);

import 'dart:convert';

AcceptBidBody acceptBidBodyFromJson(String str) =>
    AcceptBidBody.fromJson(json.decode(str));

String acceptBidBodyToJson(AcceptBidBody data) => json.encode(data.toJson());

class AcceptBidBody {
  AcceptBidBody({
    required this.bidId,
    required this.stockId,
  });

  int bidId;
  int stockId;

  factory AcceptBidBody.fromJson(Map<String, dynamic> json) => AcceptBidBody(
        bidId: json["bidId"],
        stockId: json["stockId"],
      );

  Map<String, dynamic> toJson() => {
        "bidId": bidId,
        "stockId": stockId,
      };
}
