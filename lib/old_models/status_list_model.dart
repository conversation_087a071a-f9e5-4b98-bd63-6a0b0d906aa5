// To parse this JSON data, do
//
//     final statusListModel = statusListModelFromJson(jsonString);

import 'dart:convert';

StatusListModel statusListModelFromJson(String str) =>
    StatusListModel.fromJson(json.decode(str));

String statusListModelToJson(StatusListModel data) =>
    json.encode(data.toJson());

class StatusListModel {
  List<Datum>? data;
  int? status;
  String? statusDescription;

  StatusListModel({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory StatusListModel.fromJson(Map<String, dynamic> json) =>
      StatusListModel(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  int? id;
  String? statusCode;
  String? statusName;
  String? buttonText;
  bool? enableLocation;
  bool? enablePictures;
  bool? enableAudio;
  bool? enableComments;
  bool? assignVendorButton;
  bool? negotiationButton;
  bool? enablePoSummary;
  bool? isNegotiated;
  bool? isOffer;

  Datum({
    this.id,
    this.statusCode,
    this.statusName,
    this.buttonText,
    this.enableLocation,
    this.enablePictures,
    this.enableAudio,
    this.enableComments,
    this.assignVendorButton,
    this.negotiationButton,
    this.enablePoSummary,
    this.isNegotiated,
    this.isOffer,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        statusCode: json["status_code"],
        statusName: json["status_name"],
        buttonText: json["button_text"],
        enableLocation: json["enable_location"],
        enablePictures: json["enable_pictures"],
        enableAudio: json["enable_audio"],
        enableComments: json["enable_comments"],
        assignVendorButton: json["assign_vendor_button"],
        negotiationButton: json["negotiation_button"],
        enablePoSummary: json["enable_po_summary"],
        isNegotiated: json["is_negotiated"],
        isOffer: json["is_offer"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "status_code": statusCode,
        "status_name": statusName,
        "button_text": buttonText,
        "enable_location": enableLocation,
        "enable_pictures": enablePictures,
        "enable_audio": enableAudio,
        "enable_comments": enableComments,
        "assign_vendor_button": assignVendorButton,
        "negotiation_button": negotiationButton,
        "enable_po_summary": enablePoSummary,
        "is_negotiated": isNegotiated,
        "is_offer": isOffer,
      };
}
