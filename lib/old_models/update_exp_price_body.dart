// To parse this JSON data, do
//
//     final updatedExpPriceBody = updatedExpPriceBodyFromJson(jsonString);

import 'dart:convert';

UpdatedExpPriceBody updatedExpPriceBodyFromJson(String str) =>
    UpdatedExpPriceBody.fromJson(json.decode(str));

String updatedExpPriceBodyToJson(UpdatedExpPriceBody data) =>
    json.encode(data.toJson());

class UpdatedExpPriceBody {
  UpdatedExpPriceBody({
    required this.expectedPrice,
    required this.stockId,
  });

  int expectedPrice;
  int stockId;

  factory UpdatedExpPriceBody.fromJson(Map<String, dynamic> json) =>
      UpdatedExpPriceBody(
        expectedPrice: json["expectedPrice"],
        stockId: json["stockId"],
      );

  Map<String, dynamic> toJson() => {
        "expectedPrice": expectedPrice,
        "stockId": stockId,
      };
}
