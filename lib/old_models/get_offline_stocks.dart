// To parse this JSON data, do
//
//     final offlineStocks = offlineStocksFromJson(jsonString);

import 'dart:convert';

OfflineStocks offlineStocksFromJson(String str) =>
    OfflineStocks.fromJson(json.decode(str));

String offlineStocksToJson(OfflineStocks data) => json.encode(data.toJson());

class OfflineStocks {
  OfflineStocks({
    this.content,
    this.pageable,
    this.totalPages,
    this.totalElements,
    this.last,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  List<Content>? content;
  Pageable? pageable;
  num? totalPages;
  num? totalElements;
  bool? last;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  factory OfflineStocks.fromJson(Map<String, dynamic> json) => OfflineStocks(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        totalPages: json["total_pages"],
        totalElements: json["total_elements"],
        last: json["last"],
        first: json["first"],
        numberOfElements: json["number_of_elements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "total_pages": totalPages,
        "total_elements": totalElements,
        "last": last,
        "first": first,
        "number_of_elements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  Content({
    this.id,
    this.product,
    this.grade,
    this.quantity,
    this.size,
    this.moisture,
    this.colour,
    this.auctionTypeCd,
    this.pdctCd,
    this.bidIncrementValue,
    this.lotNo,
    this.serviceId,
    this.itemId,
    this.priceRange,
    this.startingPrice,
    this.deliveryDate,
    this.unitId,
    this.unitName,
    this.unitShortName,
    this.customerId,
    this.grade8MmClean,
    this.grade8MmSickdsplit,
    this.grade8MmFruit,
    this.grade8MmTotalPercentage,
    this.grade7T8MmClean,
    this.grade7T8MmSickdsplit,
    this.grade7T8MmFruit,
    this.grade7T8MmTotalPercentage,
    this.grade17MmClean,
    this.grade17MmSickdsplit,
    this.grade17MmFruit,
    this.grade17MmTotalPercentage,
    this.rejectionsClean,
    this.rejectionsSickdsplit,
    this.rejectionsFruit,
    this.rejectionsTotalpercentage,
    this.totalSickdsplit,
    this.totalFruit,
    this.totalPercentage,
    this.literWeight,
    this.totalWeight,
    this.totalClean,
    this.pickupAddressLine1,
    this.pickupAddressLine2,
    this.pickupCity,
    this.pickupCountry,
    this.pickupPincode,
    this.pickupLat,
    this.pickupLng,
    this.vendorId,
    this.vendorName,
    this.stockStatusName,
    this.stockStatusCd,
    this.favouriteYn,
    this.averageRating,
    this.rateCount,
    this.uniqueViews,
    this.totalViews,
    this.expectedPrice,
    this.highestBidAmount,
    this.buyBidPrice,
    this.image1,
    this.prmySellCd,
    this.auctYn,
  });

  num? id;
  String? product;
  String? grade;
  num? quantity;
  String? size;
  String? moisture;
  String? colour;
  String? auctionTypeCd;
  String? pdctCd;
  String? bidIncrementValue;
  num? lotNo;
  num? serviceId;
  num? itemId;
  String? priceRange;
  num? startingPrice;
  DateTime? deliveryDate;
  num? unitId;
  String? unitName;
  String? unitShortName;
  num? customerId;
  num? grade8MmClean;
  num? grade8MmSickdsplit;
  num? grade8MmFruit;
  num? grade8MmTotalPercentage;
  num? grade7T8MmClean;
  num? grade7T8MmSickdsplit;
  num? grade7T8MmFruit;
  num? grade7T8MmTotalPercentage;
  num? grade17MmClean;
  num? grade17MmSickdsplit;
  num? grade17MmFruit;
  num? grade17MmTotalPercentage;
  num? rejectionsClean;
  num? rejectionsSickdsplit;
  num? rejectionsFruit;
  num? rejectionsTotalpercentage;
  num? totalSickdsplit;
  num? totalFruit;
  num? totalPercentage;
  String? literWeight;
  String? totalWeight;
  num? totalClean;
  String? pickupAddressLine1;
  String? pickupAddressLine2;
  String? pickupCity;
  String? pickupCountry;
  String? pickupPincode;
  String? pickupLat;
  String? pickupLng;
  num? vendorId;
  String? vendorName;
  String? stockStatusName;
  String? stockStatusCd;
  String? favouriteYn;
  String? averageRating;
  String? rateCount;
  String? uniqueViews;
  String? totalViews;
  num? expectedPrice;
  num? highestBidAmount;
  num? buyBidPrice;
  String? image1;
  String? auctYn;
  String? prmySellCd;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        product: json["product"],
        grade: json["grade"],
        quantity: json["quantity"]?.toDouble(),
        size: json["size"],
        moisture: json["moisture"],
        colour: json["colour"],
        auctionTypeCd: json["auctionTypeCd"],
        pdctCd: json["pdctCd"],
        bidIncrementValue: json["bidIncrementValue"],
        lotNo: json["lotNo"],
        serviceId: json["serviceId"],
        itemId: json["itemId"],
        priceRange: json["priceRange"],
        startingPrice: json["startingPrice"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        unitId: json["unitId"],
        unitName: json["unitName"],
        unitShortName: json["unitShortName"],
        customerId: json["customerId"],
        grade8MmClean: json["grade8mmClean"],
        grade8MmSickdsplit: json["grade8mmSickdsplit"],
        grade8MmFruit: json["grade8mmFruit"],
        grade8MmTotalPercentage: json["grade8mmTotalPercentage"],
        grade7T8MmClean: json["grade7t8mmClean"],
        grade7T8MmSickdsplit: json["grade7t8mmSickdsplit"],
        grade7T8MmFruit: json["grade7t8mmFruit"],
        grade7T8MmTotalPercentage: json["grade7t8mmTotalPercentage"],
        grade17MmClean: json["grade17mmClean"],
        grade17MmSickdsplit: json["grade17mmSickdsplit"],
        grade17MmFruit: json["grade17mmFruit"],
        grade17MmTotalPercentage: json["grade17mmTotalPercentage"],
        rejectionsClean: json["rejectionsClean"],
        rejectionsSickdsplit: json["rejectionsSickdsplit"],
        rejectionsFruit: json["rejectionsFruit"],
        rejectionsTotalpercentage: json["rejectionsTotalpercentage"],
        totalSickdsplit: json["totalSickdsplit"],
        totalFruit: json["totalFruit"],
        totalPercentage: json["totalPercentage"],
        literWeight: json["literWeight"],
        totalWeight: json["totalWeight"],
        totalClean: json["totalClean"],
        pickupAddressLine1: json["pickupAddressLine1"],
        pickupAddressLine2: json["pickupAddressLine2"],
        pickupCity: json["pickupCity"],
        pickupCountry: json["pickupCountry"],
        pickupPincode: json["pickupPincode"],
        pickupLat: json["pickupLat"],
        pickupLng: json["pickupLng"],
        vendorId: json["vendorId"],
        vendorName: json["vendorName"],
        stockStatusName: json["stockStatusName"],
        stockStatusCd: json["stockStatusCd"],
        favouriteYn: json["favourite_YN"],
        averageRating: json["averageRating"],
        rateCount: json["rateCount"],
        uniqueViews: json["uniqueViews"],
        totalViews: json["totalViews"],
        expectedPrice: json["expectedPrice"],
        highestBidAmount: json["highestBidAmount"],
        buyBidPrice: json["buyBidPrice"],
        image1: json["image1"],
        prmySellCd: json["prmySellCd"],
        auctYn: json["auctYn"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "product": product,
        "grade": grade,
        "quantity": quantity,
        "size": size,
        "moisture": moisture,
        "colour": colour,
        "auctionTypeCd": auctionTypeCd,
        "pdctCd": pdctCd,
        "bidIncrementValue": bidIncrementValue,
        "lotNo": lotNo,
        "serviceId": serviceId,
        "itemId": itemId,
        "priceRange": priceRange,
        "startingPrice": startingPrice,
        "deliveryDate":
            "${deliveryDate!.year.toString().padLeft(4, '0')}-${deliveryDate!.month.toString().padLeft(2, '0')}-${deliveryDate!.day.toString().padLeft(2, '0')}",
        "unitId": unitId,
        "unitName": unitName,
        "unitShortName": unitShortName,
        "customerId": customerId,
        "grade8mmClean": grade8MmClean,
        "grade8mmSickdsplit": grade8MmSickdsplit,
        "grade8mmFruit": grade8MmFruit,
        "grade8mmTotalPercentage": grade8MmTotalPercentage,
        "grade7t8mmClean": grade7T8MmClean,
        "grade7t8mmSickdsplit": grade7T8MmSickdsplit,
        "grade7t8mmFruit": grade7T8MmFruit,
        "grade7t8mmTotalPercentage": grade7T8MmTotalPercentage,
        "grade17mmClean": grade17MmClean,
        "grade17mmSickdsplit": grade17MmSickdsplit,
        "grade17mmFruit": grade17MmFruit,
        "grade17mmTotalPercentage": grade17MmTotalPercentage,
        "rejectionsClean": rejectionsClean,
        "rejectionsSickdsplit": rejectionsSickdsplit,
        "rejectionsFruit": rejectionsFruit,
        "rejectionsTotalpercentage": rejectionsTotalpercentage,
        "totalSickdsplit": totalSickdsplit,
        "totalFruit": totalFruit,
        "totalPercentage": totalPercentage,
        "literWeight": literWeight,
        "totalWeight": totalWeight,
        "totalClean": totalClean,
        "pickupAddressLine1": pickupAddressLine1,
        "pickupAddressLine2": pickupAddressLine2,
        "pickupCity": pickupCity,
        "pickupCountry": pickupCountry,
        "pickupPincode": pickupPincode,
        "pickupLat": pickupLat,
        "pickupLng": pickupLng,
        "vendorId": vendorId,
        "vendorName": vendorName,
        "stockStatusName": stockStatusName,
        "stockStatusCd": stockStatusCd,
        "favourite_YN": favouriteYn,
        "averageRating": averageRating,
        "rateCount": rateCount,
        "uniqueViews": uniqueViews,
        "totalViews": totalViews,
        "expectedPrice": expectedPrice,
        "highestBidAmount": highestBidAmount,
        "buyBidPrice": buyBidPrice,
      };
}

class Pageable {
  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["page_number"],
        pageSize: json["page_size"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "page_number": pageNumber,
        "page_size": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  bool? unsorted;
  bool? sorted;
  bool? empty;

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
