// To parse this JSON data, do
//
//     final notificationsListRes = notificationsListResFromJson(jsonString);

import 'dart:convert';

NotificationsListRes notificationsListResFromJson(String str) =>
    NotificationsListRes.fromJson(json.decode(str));

String notificationsListResToJson(NotificationsListRes data) =>
    json.encode(data.toJson());

class NotificationsListRes {
  List<Datum>? data;
  int? status;
  String? statusDescription;

  NotificationsListRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  factory NotificationsListRes.fromJson(Map<String, dynamic> json) =>
      NotificationsListRes(
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  int? id;
  int? prchOrdrId;
  int? customerId;
  String? mrNo;
  dynamic messageTitle;
  String? message;
  String? readYn;
  DateTime? createdAt;
  String? notificationCode;
  String? status;
  int? failedCount;
  int? successCount;

  Datum({
    this.id,
    this.prchOrdrId,
    this.customerId,
    this.mrNo,
    this.messageTitle,
    this.message,
    this.readYn,
    this.createdAt,
    this.notificationCode,
    this.status,
    this.failedCount,
    this.successCount,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        prchOrdrId: json["prch_ordr_id"],
        customerId: json["customer_id"],
        mrNo: json["mr_no"],
        messageTitle: json["message_title"],
        message: json["message"],
        readYn: json["read_YN"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        notificationCode: json["notification_code"],
        status: json["status"],
        failedCount: json["failed_count"],
        successCount: json["success_count"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "prch_ordr_id": prchOrdrId,
        "customer_id": customerId,
        "mr_no": mrNo,
        "message_title": messageTitle,
        "message": message,
        "read_YN": readYn,
        "created_at": createdAt?.toIso8601String(),
        "notification_code": notificationCode,
        "status": status,
        "failed_count": failedCount,
        "success_count": successCount,
      };
}

enum ReadYn { N, Y }

final readYnValues = EnumValues({"N": ReadYn.N, "Y": ReadYn.Y});

class EnumValues<T> {
  late Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap;
    return reverseMap;
  }
}
