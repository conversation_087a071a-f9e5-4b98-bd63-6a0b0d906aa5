import 'dart:convert';

FeedbackData feedbackDataFromJson(String str) =>
    FeedbackData.fromJson(json.decode(str));

String feedbackDataToJson(FeedbackData data) => json.encode(data.toJson());

class FeedbackData {
  FeedbackData(
      {this.customerId,
      this.rating,
      this.vendorId,
      this.comment,
      this.prchOrdrId,
      this.stockId});

  final int? customerId;
  final double? rating;
  final int? vendorId;
  final String? comment;
  final int? prchOrdrId;
  final int? stockId;

  factory FeedbackData.fromJson(Map<String, dynamic> json) => FeedbackData(
        customerId: json["customerId"],
        rating: json["rating"],
        vendorId: json["vendorId"],
        comment: json["comment"],
        prchOrdrId: json["prchOrdrId"],
        stockId: json["stockId"],
      );

  Map<String, dynamic> toJson() => {
        "customerId": customerId,
        "rating": rating,
        "vendorId": vendorId,
        "comment": comment,
        "prchOrdrId": prchOrdrId,
        "stockId": stockId,
      };
}
