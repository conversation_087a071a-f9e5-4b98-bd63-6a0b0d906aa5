// To parse this JSON data, do
//
//     final makeOfflineBidBody = makeOfflineBidBodyFromJson(jsonString);

import 'dart:convert';

String makeOfflineBidBodyToJson(MakeOfflineBidBody data) =>
    json.encode(data.toJson());

class MakeOfflineBidBody {
  MakeOfflineBidBody({
    required this.amount,
    required this.auctionId,
    required this.bidDate,
    required this.customerId,
    required this.orderTypeCd,
    required this.quantity,
    required this.roomId,
    required this.stockId,
    required this.url,
    required this.vendorId,
  });

  double amount;
  int auctionId;
  String bidDate;
  String customerId;
  String orderTypeCd;
  int quantity;
  String roomId;
  int stockId;
  String url;
  int vendorId;

  Map<String, dynamic> toJson() => {
        "amount": amount,
        "auction_id": auctionId,
        "bidDate": bidDate,
        "customerId": customerId,
        "order_type_cd": orderTypeCd,
        "quantity": quantity,
        "room_id": roomId,
        "stockId": stockId,
        "url": url,
        "vendor_id": vendorId,
      };
}
