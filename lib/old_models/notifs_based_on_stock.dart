// To parse this JSON data, do
//
//     final notifsBasedOnStock = notifsBasedOnStockFromJson(jsonString);

import 'dart:convert';

NotifsBasedOnStock notifsBasedOnStockFromJson(String str) =>
    NotifsBasedOnStock.fromJson(json.decode(str));

class NotifsBasedOnStock {
  NotifsBasedOnStock({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum> data;
  int status;
  String statusDescription;

  factory NotifsBasedOnStock.fromJson(Map<String, dynamic> json) =>
      NotifsBasedOnStock(
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );
}

class Datum {
  Datum({
    required this.id,
    required this.stockId,
    required this.customerId,
    required this.lotNo,
    required this.messageTitle,
    required this.message,
    required this.readYn,
    required this.createdAt,
    required this.notificationCode,
  });

  int id;
  int stockId;
  int customerId;
  int? lotNo;
  String messageTitle;
  String message;
  String readYn;
  DateTime createdAt;
  String? notificationCode;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        stockId: json["stockId"],
        customerId: json["customerId"],
        lotNo: json["lotNo"],
        messageTitle: json["messageTitle"],
        message: json["message"],
        readYn: json["readYN"],
        createdAt: DateTime.parse(json["createdAt"]),
        notificationCode: json["notification_code"],
      );
}
