class MarginHistory {
  final List<Data>? data;
  final double? availableMargin;
  final int? biddingLimitRemaining;
  final int? status;
  final String? statusDescription;

  MarginHistory(
    MarginHistory marginHistory, {
    this.data,
    this.availableMargin,
    this.biddingLimitRemaining,
    this.status,
    this.statusDescription,
  });

  MarginHistory.fromJson(Map<String, dynamic> json)
      : data = (json['data'] as List?)
            ?.map((dynamic e) => Data.fromJson(e as Map<String, dynamic>))
            .toList(),
        availableMargin = json['availableMargin'] as double?,
        biddingLimitRemaining = json['biddingLimitRemaining'] as int?,
        status = json['status'] as int?,
        statusDescription = json['status_description'] as String?;

  Map<String, dynamic> toJson() => {
        'data': data?.map((e) => e.toJson()).toList(),
        'availableMargin': availableMargin,
        'biddingLimitRemaining': biddingLimitRemaining,
        'status': status,
        'status_description': statusDescription
      };
}

class Data {
  final int? id;
  final String? type;
  final double? amount;
  final String? createdAt;
  final String? updatedAt;
  final String? attachedEntity;
  final int? attachedEntityId;
  final int? customerId;
  final double? balance;
  final String? creditOrDebit;

  Data({
    this.id,
    this.type,
    this.amount,
    this.createdAt,
    this.updatedAt,
    this.attachedEntity,
    this.attachedEntityId,
    this.customerId,
    this.balance,
    this.creditOrDebit,
  });

  Data.fromJson(Map<String, dynamic> json)
      : id = json['id'] as int?,
        type = json['type'] as String?,
        amount = json['amount'] as double?,
        createdAt = json['createdAt'] as String?,
        updatedAt = json['updatedAt'] as String?,
        attachedEntity = json['attachedEntity'] as String?,
        attachedEntityId = json['attachedEntityId'] as int?,
        customerId = json['customerId'] as int?,
        balance = json['balance'] as double?,
        creditOrDebit = json['creditOrDebit'] as String?;

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type,
        'amount': amount,
        'createdAt': createdAt,
        'updatedAt': updatedAt,
        'attachedEntity': attachedEntity,
        'attachedEntityId': attachedEntityId,
        'customerId': customerId,
        'balance': balance,
        'creditOrDebit': creditOrDebit
      };
}
