import 'dart:convert';

TotalStocks1 totalstocks1FromJson(String str) =>
    TotalStocks1.fromJson(json.decode(str));

String totalstocks1ToJson(TotalStocks1 data) => json.encode(data.toJson());

class TotalStocks1 {
  TotalStocks1({
    this.noOfStocksSold,
    this.totalAmountOfSoldStocks,
    this.totalQuantitySold,
  });

  final int? noOfStocksSold;
  final num? totalAmountOfSoldStocks;
  final num? totalQuantitySold;

  factory TotalStocks1.fromJson(Map<String, dynamic> json) => TotalStocks1(
        noOfStocksSold: json["no_of_stocks_sold"],
        totalAmountOfSoldStocks: json["total_amount_of_sold_stocks"],
        totalQuantitySold: json["total_quantity_sold"],
      );

  Map<String, dynamic> toJson() => {
        "no_of stocks sold": noOfStocksSold,
        "total_amount_of_sold_stocks": totalAmountOfSoldStocks,
        "total_quantity_sold": totalQuantitySold,
      };
}
