// To parse this JSON data, do
//
//     final liveBidBody = liveBidBodyFromJson(jsonString);

import 'dart:convert';

LiveBidBody liveBidBodyFromJson(String str) =>
    LiveBidBody.fromJson(json.decode(str));

String liveBidBodyToJson(LiveBidBody data) => json.encode(data.toJson());

class LiveBidBody {
  LiveBidBody({
    required this.amount,
    //required this.auctionId,
    required this.bidDate,
    required this.bidDeskNo,
    required this.customerId,
    this.orderTypeCd,
    required this.quantity,
    required this.roomId,
    required this.stockId,
    required this.url,
    required this.vendorId,
  });

  double amount;

  //int auctionId;
  String bidDate;
  String bidDeskNo;
  String customerId;
  String? orderTypeCd;
  double quantity;
  String roomId;
  int stockId;
  String url;
  int vendorId;

  factory LiveBidBody.fromJson(Map<String, dynamic> json) => LiveBidBody(
        amount: json["amount"],
        //auctionId: json["auction_id"],
        bidDate: json["bidDate"],
        bidDeskNo: json["bid_desk_no"],
        customerId: json["customerId"],
        orderTypeCd: json["order_type_cd"],
        quantity: json["quantity"].toDouble(),
        roomId: json["room_id"],
        stockId: json["stockId"],
        url: json["url"],
        vendorId: json["vendor_id"],
      );

  Map<String, dynamic> toJson() => {
        "amount": amount,
        //"auction_id": auctionId,
        "bidDate": bidDate,
        "bid_desk_no": bidDeskNo,
        "customerId": customerId,
        "order_type_cd": orderTypeCd,
        "quantity": quantity,
        "room_id": roomId,
        "stockId": stockId,
        "url": url,
        "vendor_id": vendorId,
      };
}

LiveBidBodyNew liveBidBodyNewFromJson(String str) =>
    LiveBidBodyNew.fromJson(json.decode(str));

String liveBidBodyNewToJson(LiveBidBodyNew data) => json.encode(data.toJson());

class LiveBidBodyNew {
  String? customerId;
  double? amount;
  int? stockId;
  double? quantity;
  String? bidDeskNo;
  DateTime? timeOfBid;

  LiveBidBodyNew({
    this.customerId,
    this.amount,
    this.stockId,
    this.quantity,
    this.bidDeskNo,
    this.timeOfBid,
  });

  factory LiveBidBodyNew.fromJson(Map<String, dynamic> json) => LiveBidBodyNew(
        customerId: json["customerId"],
        amount: json["amount"]?.toDouble(),
        stockId: json["stockId"],
        quantity: json["quantity"],
        bidDeskNo: json["bid_desk_no"],
        timeOfBid: json["timeOfBid"] == null
            ? null
            : DateTime.parse(json["timeOfBid"]),
      );

  Map<String, dynamic> toJson() => {
        "customerId": customerId,
        "amount": amount,
        "stockId": stockId,
        "quantity": quantity,
        "bid_desk_no": bidDeskNo,
        "timeOfBid": timeOfBid?.toIso8601String(),
      };
}
