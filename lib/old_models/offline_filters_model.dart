import '../old_screens/offline_filters/offline_filters_bloc.dart';

// ignore: must_be_immutable
class OfflineFilterRes extends OfflineFilterState {
  OfflineFilterRes({
    required this.filters,
    required this.calender,
    required this.summary,
    required this.status,
    required this.statusDescription,
  });

  late final List<Filters>? filters;
  late final List<Calender>? calender;
  late final List<Summary>? summary;
  late final int? status;
  late final String? statusDescription;

  @override
  List<Object> get props => [refreshProd];
  int refreshProd = DateTime.now().millisecondsSinceEpoch;

  OfflineFilterRes.fromJson(Map<String, dynamic> json) {
    filters =
        List.from(json['filters']).map((e) => Filters.fromJson(e)).toList();
    calender =
        List.from(json['calender']).map((e) => Calender.fromJson(e)).toList();
    summary =
        List.from(json['summary']).map((e) => Summary.fromJson(e)).toList();
    status = json['status'];
    statusDescription = json['status_description'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['filters'] = filters?.map((e) => e.toJson()).toList();
    data['calender'] = calender?.map((e) => e.toJson()).toList();
    data['summary'] = summary?.map((e) => e.toJson()).toList();
    data['status'] = status;
    data['status_description'] = statusDescription;
    return data;
  }

  OfflineFilterRes copyWith({
    List<Filters>? filters,
    List<Calender>? calender,
    List<Summary>? summary,
    int? status,
    String? statusDescription,
  }) =>
      OfflineFilterRes(
        filters: filters ?? this.filters,
        calender: calender ?? this.calender,
        summary: summary ?? this.summary,
        status: status ?? this.status,
        statusDescription: statusDescription ?? this.statusDescription,
      );
}

class Filters {
  Filters({
    required this.product,
    required this.grade,
    required this.range,
    required this.location,
    required this.deliveryDate,
  });

  late final List<Product>? product;
  late final List<Product>? grade;
  late final List<Range>? range;
  late final List<Product>? location;
  late final List<Product>? deliveryDate;

  Filters.fromJson(Map<String, dynamic> json) {
    product =
        List.from(json['product']).map((e) => Product.fromJson(e)).toList();
    grade = List.from(json['grade']).map((e) => Product.fromJson(e)).toList();
    range = List.from(json['range']).map((e) => Range.fromJson(e)).toList();
    location =
        List.from(json['location']).map((e) => Product.fromJson(e)).toList();
    deliveryDate = List.from(json['DeliveryDate'])
        .map((e) => Product.fromJson(e))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['product'] = product?.map((e) => e.toJson()).toList();
    data['grade'] = grade?.map((e) => e.toJson()).toList();
    data['range'] = range?.map((e) => e.toJson()).toList();
    data['location'] = location?.map((e) => e.toJson()).toList();
    data['DeliveryDate'] = deliveryDate?.map((e) => e.toJson()).toList();
    return data;
  }
}

class Product {
  Product({
    required this.name,
    required this.stockCount,
  });

  late final String? name;
  late final int? stockCount;

  Product.fromJson(Map<String, dynamic> json) {
    name = json['name'] ?? json['grade'] ?? "";
    stockCount = json['stockCount'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['stockCount'] = stockCount;
    return data;
  }
}

class Range {
  Range({
    required this.priceRangeMin,
    required this.buyBidPriceMax,
    required this.quantityRangeMin,
    required this.quantityRangeMax,
    required this.buyBidPriceRangeMin,
    required this.priceRangeMax,
  });

  late final double? priceRangeMin;
  late final double? buyBidPriceMax;
  late final double? quantityRangeMin;
  late final double? quantityRangeMax;
  late final double? buyBidPriceRangeMin;
  late final double? priceRangeMax;

  Range.fromJson(Map<String, dynamic> json) {
    priceRangeMin = json['priceRangeMin'];
    buyBidPriceMax = json['buyBidPriceMax'];
    quantityRangeMin = json['quantityRangeMin'];
    quantityRangeMax = json['quantityRangeMax'];
    buyBidPriceRangeMin = json['buyBidPriceRangeMin'];
    priceRangeMax = json['priceRangeMax'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['priceRangeMin'] = priceRangeMin;
    data['buyBidPriceMax'] = buyBidPriceMax;
    data['quantityRangeMin'] = quantityRangeMin;
    data['quantityRangeMax'] = quantityRangeMax;
    data['buyBidPriceRangeMin'] = buyBidPriceRangeMin;
    data['priceRangeMax'] = priceRangeMax;
    return data;
  }
}

class Calender {
  Calender({
    required this.unit,
    required this.quantity,
    required this.deliveryDate,
    required this.activeYn,
    required this.stockCount,
  });

  late final String? unit;
  late final double? quantity;
  late final String? deliveryDate;
  late final String? activeYn;
  late final int? stockCount;

  Calender.fromJson(Map<String, dynamic> json) {
    unit = json['unit'];
    quantity = json['quantity'];
    deliveryDate = json['deliveryDate'];
    activeYn = json['active_yn'];
    stockCount = json['stockCount'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['unit'] = unit;
    data['quantity'] = quantity;
    data['deliveryDate'] = deliveryDate;
    data['active_yn'] = activeYn;
    data['stockCount'] = stockCount;
    return data;
  }
}

class Summary {
  Summary({
    required this.product,
    required this.unit,
    required this.quantity,
    required this.deliveryDate,
    required this.activeYn,
    required this.stockCount,
  });

  late final String? product;
  late final String? unit;
  late final double? quantity;
  late final String? deliveryDate;
  late final String? activeYn;
  late final int? stockCount;

  Summary.fromJson(Map<String, dynamic> json) {
    product = json['product'];
    unit = json['unit'];
    quantity = json['quantity'];
    deliveryDate = json['deliveryDate'];
    activeYn = json['active_yn'];
    stockCount = json['stockCount'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['product'] = product;
    data['unit'] = unit;
    data['quantity'] = quantity;
    data['deliveryDate'] = deliveryDate;
    data['active_yn'] = activeYn;
    data['stockCount'] = stockCount;
    return data;
  }
}
