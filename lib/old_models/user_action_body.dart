// To parse this JSON data, do
//
//     final userActionReq = userActionReqFromJson(jsonString);

import 'dart:convert';

UserActionReq userActionReqFromJson(String str) =>
    UserActionReq.fromJson(json.decode(str));

String userActionReqToJson(UserActionReq data) => json.encode(data.toJson());

class UserActionReq {
  int? customerId;
  DateTime? actionTime;
  String? action;
  int? stockId;
  String? deviceType;
  String? screenName;
  String? vendorId;

  UserActionReq({
    this.customerId,
    this.actionTime,
    this.action,
    this.stockId,
    this.deviceType,
    this.screenName,
    this.vendorId,
  });

  factory UserActionReq.fromJson(Map<String, dynamic> json) => UserActionReq(
        customerId: json["customer_id"],
        actionTime: json["action_time"] == null
            ? null
            : DateTime.parse(json["action_time"]),
        action: json["action"],
        stockId: json["stock_id"],
        deviceType: json["device_type"],
        screenName: json["screen_name"],
        vendorId: json["vendor_id"],
      );

  Map<String, dynamic> toJson() => {
        "customer_id": customerId,
        "action_time": actionTime?.toSaveUserActionTime(),
        "action": action,
        "stock_id": stockId,
        "device_type": deviceType,
        "screen_name": screenName,
        "vendor_id": vendorId,
      };
}

extension DateTimeFormatting on DateTime {
  String toSaveUserActionTime() {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    String year = this.year.toString();
    String month = twoDigits(this.month);
    String day = twoDigits(this.day);
    String hour = twoDigits(this.hour);
    String minute = twoDigits(this.minute);
    String second = twoDigits(this.second);

    return '$year-$month-$day $hour:$minute:$second';
  }
}
