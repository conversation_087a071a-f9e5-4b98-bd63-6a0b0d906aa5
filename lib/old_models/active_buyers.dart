// To parse this JSON data, do
//
//     final activeBuyers = activeBuyersFromJson(jsonString);

import 'dart:convert';

ActiveBuyers? activeBuyersFromJson(String str) =>
    ActiveBuyers.fromJson(json.decode(str));

String activeBuyersToJson(ActiveBuyers? data) => json.encode(data!.toJson());

class ActiveBuyers {
  ActiveBuyers({
    this.data,
    this.status,
    this.statusDescription,
  });

  Data? data;
  int? status;
  String? statusDescription;

  factory ActiveBuyers.fromJson(Map<String, dynamic> json) => ActiveBuyers(
        data: Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data!.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  Data({
    this.noOfBuyers,
  });

  String? noOfBuyers;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        noOfBuyers: json["noOfBuyers"],
      );

  Map<String, dynamic> toJson() => {
        "noOfBuyers": noOfBuyers,
      };
}
