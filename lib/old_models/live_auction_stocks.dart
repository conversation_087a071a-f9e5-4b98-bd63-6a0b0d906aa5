// To parse this JSON data, do
//
//     final liveAuctionStocks = liveAuctionStocksFromJson(jsonString);

import 'dart:convert';

LiveAuctionStocks liveAuctionStocksFromJson(String str) =>
    LiveAuctionStocks.fromJson(json.decode(str));

String liveAuctionStocksToJson(LiveAuctionStocks data) =>
    json.encode(data.toJson());

class LiveAuctionStocks {
  LiveAuctionStocks({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  Data data;
  int status;
  String statusDescription;

  factory LiveAuctionStocks.fromJson(Map<String, dynamic> json) =>
      LiveAuctionStocks(
        data: Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  Data({
    required this.days,
    required this.orders,
  });

  List<Day> days;
  List<Order> orders;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        days: List<Day>.from(json["days"].map((x) => Day.fromJson(x))),
        orders: List<Order>.from(json["orders"].map((x) => Order.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "days": List<dynamic>.from(days.map((x) => x.toJson())),
        "orders": List<dynamic>.from(orders.map((x) => x.toJson())),
      };
}

class Day {
  Day({
    required this.deliveryDate,
    required this.itemId,
    required this.itemName,
    required this.noOfStocks,
    required this.totalStocks,
    required this.unitName,
    required this.unitShortName,
  });

  DateTime deliveryDate;
  int itemId;
  String itemName;
  int noOfStocks;
  double totalStocks;
  String unitName;
  String unitShortName;

  factory Day.fromJson(Map<String, dynamic> json) => Day(
        deliveryDate: DateTime.parse(json["delivery_date"]),
        itemId: json["item_id"],
        itemName: json["item_name"],
        noOfStocks: json["no_of_stocks"],
        totalStocks: json["total_stocks"],
        unitName: json["unit_name"],
        unitShortName: json["unit_short_name"],
      );

  Map<String, dynamic> toJson() => {
        "delivery_date":
            "${deliveryDate.year.toString().padLeft(4, '0')}-${deliveryDate.month.toString().padLeft(2, '0')}-${deliveryDate.day.toString().padLeft(2, '0')}",
        "item_id": itemId,
        "item_name": itemName,
        "no_of_stocks": noOfStocks,
        "total_stocks": totalStocks,
        "unit_name": unitName,
        "unit_short_name": unitShortName,
      };
}

class Order {
  Order({
    required this.deliveryDate,
    required this.itemId,
    required this.itemName,
    required this.orderId,
    required this.lotNo,
    required this.vendorName,
    required this.quantity,
    required this.unitId,
    required this.unitName,
    required this.unitShortName,
    required this.sellerVendorId,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.images,
    required this.orderSpecifications,
  });

  DateTime deliveryDate;
  int itemId;
  String itemName;
  int orderId;
  int lotNo;
  String vendorName;
  double quantity;
  int unitId;
  String unitName;
  String unitShortName;
  int sellerVendorId;
  String location;
  String latitude;
  String longitude;
  List<String> images;
  List<OrderSpecification> orderSpecifications;

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        deliveryDate: DateTime.parse(json["delivery_date"]),
        itemId: json["item_id"],
        itemName: json["item_name"],
        orderId: json["order_id"],
        lotNo: json["lot_no"],
        vendorName: json["vendor_name"],
        quantity: json["quantity"],
        unitId: json["unit_id"],
        unitName: json["unit_name"],
        unitShortName: json["unit_short_name"],
        sellerVendorId: json["seller_vendor_id"],
        location: json["location"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        images: List<String>.from(json["images"].map((x) => x)),
        orderSpecifications: List<OrderSpecification>.from(
            json["order_specifications"]
                .map((x) => OrderSpecification.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "delivery_date":
            "${deliveryDate.year.toString().padLeft(4, '0')}-${deliveryDate.month.toString().padLeft(2, '0')}-${deliveryDate.day.toString().padLeft(2, '0')}",
        "item_id": itemId,
        "item_name": itemName,
        "order_id": orderId,
        "lot_no": lotNo,
        "vendor_name": vendorName,
        "quantity": quantity,
        "unit_id": unitId,
        "unit_name": unitName,
        "unit_short_name": unitShortName,
        "seller_vendor_id": sellerVendorId,
        "location": location,
        "latitude": latitude,
        "longitude": longitude,
        "images": List<dynamic>.from(images.map((x) => x)),
        "order_specifications":
            List<dynamic>.from(orderSpecifications.map((x) => x.toJson())),
      };
}

class OrderSpecification {
  OrderSpecification({
    required this.noOfBags,
    required this.grade7T8MmClean,
    required this.rejectionsSickdsplit,
    required this.grade7T8MmSickdsplit,
    required this.rejectionsTotal,
    required this.totalFruit,
    required this.grade7T8MmTotalpercentage,
    required this.grade17MmClean,
    required this.grade8MmClean,
    required this.rejectionsClean,
    required this.totalPercentage,
    required this.totalWeight,
    required this.grade8MmSickdsplit,
    required this.rejectionsFruit,
    required this.grade7T8MmFruit,
    required this.grade8MmFruit,
    required this.grade17MmFruit,
    required this.grade17MmTotalpercentage,
    required this.moisture,
    required this.grade17MmSickdsplit,
    required this.ltrWt,
    required this.colour,
    required this.totalSickdsplit,
    required this.size,
    required this.grade,
    required this.grade8MmTotalpercentage,
    required this.category,
    required this.totalClean,
  });

  String noOfBags;
  double grade7T8MmClean;
  double rejectionsSickdsplit;
  double grade7T8MmSickdsplit;
  double rejectionsTotal;
  double totalFruit;
  double grade7T8MmTotalpercentage;
  double grade17MmClean;
  double grade8MmClean;
  double rejectionsClean;
  double totalPercentage;
  double totalWeight;
  double grade8MmSickdsplit;
  double rejectionsFruit;
  double grade7T8MmFruit;
  double grade8MmFruit;
  double grade17MmFruit;
  double grade17MmTotalpercentage;
  String moisture;
  double grade17MmSickdsplit;
  String ltrWt;
  String colour;
  double totalSickdsplit;
  String size;
  String grade;
  double grade8MmTotalpercentage;
  String category;
  double totalClean;

  factory OrderSpecification.fromJson(Map<String, dynamic> json) =>
      OrderSpecification(
        noOfBags: json["no_of_bags"],
        grade7T8MmClean: json["grade_7t8mm_clean"],
        rejectionsSickdsplit: json["rejections_sickdsplit"],
        grade7T8MmSickdsplit: json["grade_7t8mm_sickdsplit"],
        rejectionsTotal: json["rejections_total"],
        totalFruit: json["total_fruit"],
        grade7T8MmTotalpercentage: json["grade_7t8mm_totalpercentage"],
        grade17MmClean: json["grade_17mm_clean"],
        grade8MmClean: json["grade_8mm_clean"],
        rejectionsClean: json["rejections_clean"],
        totalPercentage: json["total_percentage"],
        totalWeight: json["total_weight"],
        grade8MmSickdsplit: json["grade_8mm_sickdsplit"],
        rejectionsFruit: json["rejections_fruit"],
        grade7T8MmFruit: json["grade_7t8mm_fruit"],
        grade8MmFruit: json["grade_8mm_fruit"],
        grade17MmFruit: json["grade_17mm_fruit"],
        grade17MmTotalpercentage: json["grade_17mm_totalpercentage"],
        moisture: json["moisture"],
        grade17MmSickdsplit: json["grade_17mm_sickdsplit"],
        ltrWt: json["ltr_wt"],
        colour: json["colour"],
        totalSickdsplit: json["total_sickdsplit"],
        size: json["size"],
        grade: json["grade"],
        grade8MmTotalpercentage: json["grade_8mm_totalpercentage"],
        category: json["category"],
        totalClean: json["total_clean"],
      );

  Map<String, dynamic> toJson() => {
        "no_of_bags": noOfBags,
        "grade_7t8mm_clean": grade7T8MmClean,
        "rejections_sickdsplit": rejectionsSickdsplit,
        "grade_7t8mm_sickdsplit": grade7T8MmSickdsplit,
        "rejections_total": rejectionsTotal,
        "total_fruit": totalFruit,
        "grade_7t8mm_totalpercentage": grade7T8MmTotalpercentage,
        "grade_17mm_clean": grade17MmClean,
        "grade_8mm_clean": grade8MmClean,
        "rejections_clean": rejectionsClean,
        "total_percentage": totalPercentage,
        "total_weight": totalWeight,
        "grade_8mm_sickdsplit": grade8MmSickdsplit,
        "rejections_fruit": rejectionsFruit,
        "grade_7t8mm_fruit": grade7T8MmFruit,
        "grade_8mm_fruit": grade8MmFruit,
        "grade_17mm_fruit": grade17MmFruit,
        "grade_17mm_totalpercentage": grade17MmTotalpercentage,
        "moisture": moisture,
        "grade_17mm_sickdsplit": grade17MmSickdsplit,
        "ltr_wt": ltrWt,
        "colour": colour,
        "total_sickdsplit": totalSickdsplit,
        "size": size,
        "grade": grade,
        "grade_8mm_totalpercentage": grade8MmTotalpercentage,
        "category": category,
        "total_clean": totalClean,
      };
}
