// To parse this JSON data, do
//
//     final autobidResult = autobidResultFromJson(jsonString);

import 'dart:convert';

CommonResponse autobidResponseFromJson(String str) =>
    CommonResponse.fromJson(json.decode(str));

String autobidResponseToJson(CommonResponse data) => json.encode(data.toJson());

class CommonResponse {
  CommonResponse({
    required this.status,
    required this.statusDescription,
  });

  int status;
  String statusDescription;

  factory CommonResponse.fromJson(Map<String, dynamic> json) => CommonResponse(
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "status_description": statusDescription,
      };
}

class CommonResponse1 {
  CommonResponse1({
    required this.status,
    required this.statusDescription,
  });

  int status;
  String statusDescription;

  factory CommonResponse1.fromJson(Map<String, dynamic> json) =>
      CommonResponse1(
        status: json["status"],
        statusDescription: json["statusDescription"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "statusDescription": statusDescription,
      };
}
