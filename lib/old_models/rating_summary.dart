class RatingSummary {
  RatingSummary({
    this.totalFeedbacks,
    this.avgRating,
    this.ratingPercentage,
  });

  final int? totalFeedbacks;
  final double? avgRating;
  final List<RatingPercentage>? ratingPercentage;

  factory RatingSummary.fromJson(Map<String, dynamic> json) => RatingSummary(
        totalFeedbacks: json["totalFeedbacks"],
        avgRating: json["avgRating"]?.toDouble(),
        ratingPercentage: json["ratingPercentage"] == null
            ? []
            : List<RatingPercentage>.from(json["ratingPercentage"]!
                .map((x) => RatingPercentage.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "totalFeedbacks": totalFeedbacks,
        "avgRating": avgRating,
        "ratingPercentage": ratingPercentage == null
            ? []
            : List<dynamic>.from(ratingPercentage!.map((x) => x.toJson())),
      };
}

class RatingPercentage {
  RatingPercentage({
    this.percentage,
    this.rating,
    this.count,
  });

  final double? percentage;
  final int? rating;
  final int? count;

  factory RatingPercentage.fromJson(Map<String, dynamic> json) =>
      RatingPercentage(
        percentage: json["percentage"]?.toDouble(),
        rating: json["rating"],
        count: json["count"],
      );

  Map<String, dynamic> toJson() => {
        "percentage": percentage,
        "rating": rating,
        "count": count,
      };
}
