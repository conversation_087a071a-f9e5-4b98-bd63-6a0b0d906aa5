// To parse this JSON data, do
//
//     final profile = profileFrom<PERSON>son(jsonString);

import 'dart:convert';

Profile profileFromJson(String str) => Profile.fromJson(json.decode(str));

String profileToJson(Profile data) => json.encode(data.toJson());

class Profile {
  Profile({
    required this.status,
    required this.statusDescription,
    required this.data,
  });

  int? status;
  String? statusDescription;
  List<Datum> data;

  factory Profile.fromJson(Map<String, dynamic> json) => Profile(
        status: json["status"],
        statusDescription: json["status_description"],
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "status_description": statusDescription,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class Datum {
  Datum({
    this.comments,
    this.vip,
    required this.firstName,
    this.lastName,
    this.phone,
    this.apartment,
    this.city,
    this.state,
    this.country,
    this.zip,
    this.latitude,
    this.longitude,
    this.username,
    this.email,
    this.officeNumber,
    this.landlineNumber,
    this.addressLine1,
    this.addressLine2,
    this.homeNumber,
    this.isSuperadmin,
    this.phoneCountryCode,
    this.officeNumberCc,
    this.homeNumberCc,
    this.landlineNumberCc,
    this.gstNumber,
    this.shippingAddress,
    this.customerTypeCd,
    this.bidMaxAmount,
  });

  dynamic comments;
  String? vip;
  String? firstName;
  dynamic lastName;
  String? phone;
  dynamic apartment;
  dynamic city;
  String? state;
  dynamic country;
  String? zip;
  dynamic latitude;
  dynamic longitude;
  dynamic username;
  String? email;
  dynamic officeNumber;
  dynamic landlineNumber;
  String? addressLine1;
  String? addressLine2;
  dynamic homeNumber;
  dynamic isSuperadmin;
  String? phoneCountryCode;
  dynamic officeNumberCc;
  dynamic homeNumberCc;
  dynamic landlineNumberCc;
  String? gstNumber;
  dynamic shippingAddress;
  String? customerTypeCd;
  int? bidMaxAmount;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        comments: json["comments"],
        vip: json["vip"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        phone: json["phone"],
        apartment: json["apartment"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        zip: json["zip"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        username: json["username"],
        email: json["email"],
        officeNumber: json["office_number"],
        landlineNumber: json["landline_number"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        homeNumber: json["home_number"],
        isSuperadmin: json["is_superadmin"],
        phoneCountryCode: json["phone_country_code"],
        officeNumberCc: json["office_number_cc"],
        homeNumberCc: json["home_number_cc"],
        landlineNumberCc: json["landline_number_cc"],
        gstNumber: json["gst_number"],
        shippingAddress: json["shipping_address"],
        customerTypeCd: json["customer_type_cd"],
        bidMaxAmount: json["bid_max_amount"],
      );

  Map<String, dynamic> toJson() => {
        "comments": comments,
        "vip": vip,
        "first_name": firstName,
        "last_name": lastName,
        "phone": phone,
        "apartment": apartment,
        "city": city,
        "state": state,
        "country": country,
        "zip": zip,
        "latitude": latitude,
        "longitude": longitude,
        "username": username,
        "email": email,
        "office_number": officeNumber,
        "landline_number": landlineNumber,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "home_number": homeNumber,
        "is_superadmin": isSuperadmin,
        "phone_country_code": phoneCountryCode,
        "office_number_cc": officeNumberCc,
        "home_number_cc": homeNumberCc,
        "landline_number_cc": landlineNumberCc,
        "gst_number": gstNumber,
        "shipping_address": shippingAddress,
        "customer_type_cd": customerTypeCd,
        "bid_max_amount": bidMaxAmount,
      };
}
