// To parse this JSON data, do
//
//     final notificationsListRes = notificationsListResFromJson(jsonString);

import 'dart:convert';

NotificationsCountRes notificationsListResFromJson(String str) =>
    NotificationsCountRes.fromJson(json.decode(str));

String notificationsListResToJson(NotificationsCountRes data) =>
    json.encode(data.toJson());

class NotificationsCountRes {
  int? unreadCount;

  NotificationsCountRes({
    this.unreadCount,
  });

  factory NotificationsCountRes.fromJson(Map<String, dynamic> json) =>
      NotificationsCountRes(
        unreadCount: json["unread_count"],
      );

  Map<String, dynamic> toJson() => {
        "unread_count": unreadCount,
      };
}
