// To parse this JSON data, do
//
//     final cancelAutobidBody = cancelAutobidBodyFromJson(jsonString);

import 'dart:convert';

CancelAutobidBody cancelAutobidBodyFromJson(String str) =>
    CancelAutobidBody.fromJson(json.decode(str));

String cancelAutobidBodyToJson(CancelAutobidBody data) =>
    json.encode(data.toJson());

class CancelAutobidBody {
  CancelAutobidBody({
    required this.stockId,
  });

  int stockId;

  factory CancelAutobidBody.fromJson(Map<String, dynamic> json) =>
      CancelAutobidBody(
        stockId: json["stockId"],
      );

  Map<String, dynamic> toJson() => {
        "stockId": stockId,
      };
}
