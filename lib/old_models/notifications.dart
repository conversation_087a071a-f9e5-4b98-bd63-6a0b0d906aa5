// // To parse this JSON data, do
// //
// //     final notifications = notificationsFrom<PERSON><PERSON>(jsonString);
//
// // ignore_for_file: constant_identifier_names
//
// import 'dart:convert';
//
// Notifications notificationsFromJson(String str) =>
//     Notifications.fromJson(json.decode(str));
//
// String notificationsToJson(Notifications data) => json.encode(data.toJson());
//
// class Notifications {
//   Notifications({
//     required this.data,
//     required this.status,
//     required this.statusDescription,
//   });
//
//   List<Datum> data;
//   int status;
//   String statusDescription;
//
//   factory Notifications.fromJson(Map<String, dynamic> json) => Notifications(
//         data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
//         status: json["status"],
//         statusDescription: json["status_description"],
//       );
//
//   Map<String, dynamic> to<PERSON><PERSON>() => {
//         "data": List<dynamic>.from(data.map((x) => x.toJson())),
//         "status": status,
//         "status_description": statusDescription,
//       };
// }
//
// class Datum {
//   Datum({
//     this.id,
//     this.stockId,
//     this.customerId,
//     this.lotNo,
//     this.messageTitle,
//     this.message,
//     this.readYn,
//     this.createdAt,
//   });
//
//   int? id;
//   int? stockId;
//   int? customerId;
//   int? lotNo;
//   String? messageTitle;
//   Message? message;
//   ReadYn? readYn;
//   DateTime? createdAt;
//
//   factory Datum.fromJson(Map<String, dynamic> json) => Datum(
//         id: json["id"],
//         stockId: json["stockId"],
//         customerId: json["customerId"],
//         lotNo: json["lotNo"],
//         messageTitle: json["messageTitle"],
//         message: messageValues.map[json["message"]]!,
//         readYn: readYnValues.map[json["readYN"]]!,
//         createdAt: DateTime.parse(json["createdAt"]),
//       );
//
//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "stockId": stockId,
//         "customerId": customerId,
//         "lotNo": lotNo,
//         "messageTitle": messageTitle,
//         "message": messageValues.reverse![message],
//         "readYN": readYnValues.reverse![readYn],
//         "createdAt": createdAt?.toIso8601String(),
//       };
// }
//
// enum Message {
//   YOUR_BID_IS_UNDER_BID_NOW,
//   MATCHING_SEARCH_FOUND_FOR_STOCK,
//   MORE_STOCKS_ARE_AVAILABLE_FOR_YOUR_SAVED_SEARCH,
//   YOUR_BID_IS_ACCEPTED
// }
//
// final messageValues = EnumValues({
//   "Matching search found for stock": Message.MATCHING_SEARCH_FOUND_FOR_STOCK,
//   "More stocks are available for your saved search":
//       Message.MORE_STOCKS_ARE_AVAILABLE_FOR_YOUR_SAVED_SEARCH,
//   "Your bid is accepted": Message.YOUR_BID_IS_ACCEPTED,
//   "Your bid is under bid now": Message.YOUR_BID_IS_UNDER_BID_NOW
// });
//
// enum ReadYn { N }
//
// final readYnValues = EnumValues({"N": ReadYn.N});
//
// class EnumValues<T> {
//   Map<String, T> map;
//   Map<T, String>? reverseMap;
//
//   EnumValues(this.map);
//
//   Map<T, String>? get reverse {
//     reverseMap ??= map.map((k, v) => MapEntry(v, k));
//     return reverseMap;
//   }
// }

import 'dart:convert';

Notifications notificationsFromJson(String str) =>
    Notifications.fromJson(json.decode(str));

class Notifications {
  Notifications({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum>? data;
  int status;
  String statusDescription;

  factory Notifications.fromJson(Map<String, dynamic> json) => Notifications(
        data: json["data"] == null
            ? null
            : List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );
}

class Datum {
  Datum({
    required this.id,
    required this.stockId,
    required this.customerId,
    required this.lotNo,
    required this.messageTitle,
    required this.message,
    required this.readYn,
    required this.createdAt,
    required this.notificationCode,
  });

  int? id;
  int? stockId;
  int? customerId;
  int? lotNo;
  String? messageTitle;
  String? message;
  ReadYn? readYn;
  DateTime? createdAt;
  String? notificationCode;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        stockId: json["stockId"],
        customerId: json["customerId"],
        lotNo: json["lotNo"],
        messageTitle: json["messageTitle"],
        message: json["message"],
        notificationCode: json["notification_code"],
        readYn:
            json["readYN"] == null ? null : readYnValues.map[json["readYN"]],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );
}

enum ReadYn { N, Y }

final readYnValues = EnumValues({"N": ReadYn.N, "Y": ReadYn.Y});

class EnumValues<T> {
  late Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap;
    return reverseMap;
  }
}
