// To parse this JSON data, do
//
//     final favouritesResponse = favouritesResponseFromJson(jsonString);

import 'dart:convert';

FavouritesResponse favouritesResponseFromJson(String str) =>
    FavouritesResponse.fromJson(json.decode(str));

class FavouritesResponse {
  FavouritesResponse({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum1>? data;
  int status;
  String statusDescription;

  factory FavouritesResponse.fromJson(Map<String, dynamic> json) =>
      FavouritesResponse(
        data: json["data"] == null
            ? null
            : List<Datum1>.from(json["data"].map((x) => Datum1.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );
}

class Datum1 {
  Datum1({
    required this.stockId,
    required this.autoBidStatus,
    required this.autoBidAmount,
    required this.optionStatus,
    required this.buyBidPrice,
    required this.autoBidCreatedAt,
  });

  int stockId;
  AutoBidStatus? autoBidStatus;
  double autoBidAmount;
  OptionStatus? optionStatus;
  double buyBidPrice;
  DateTime? autoBidCreatedAt;

  factory Datum1.fromJson(Map<String, dynamic> json) => Datum1(
        stockId: json["stockId"],
        autoBidStatus: json["autoBidStatus"] == null
            ? null
            : autoBidStatusValues.map[json["autoBidStatus"]],
        autoBidAmount: json["autoBidAmount"],
        optionStatus: json["optionStatus"] == null
            ? null
            : optionStatusValues.map[json["optionStatus"]],
        buyBidPrice: json["buyBidPrice"].toDouble(),
        autoBidCreatedAt: json["autoBidCreatedAt"] == null
            ? null
            : DateTime.parse(json["autoBidCreatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "stockId": stockId,
        "autoBidStatus": autoBidStatus == null
            ? null
            : autoBidStatusValues.reverse![autoBidStatus],
        "autoBidAmount": autoBidAmount,
        "optionStatus": optionStatus == null
            ? null
            : optionStatusValues.reverse![optionStatus],
        "buyBidPrice": buyBidPrice,
        "autoBidCreatedAt": autoBidCreatedAt?.toIso8601String(),
      };
}

enum AutoBidStatus { actv, cncl, iact }

final autoBidStatusValues = EnumValues({
  "ACTV": AutoBidStatus.actv,
  "CNCL": AutoBidStatus.cncl,
  "IACT": AutoBidStatus.iact
});

enum OptionStatus { none }

final optionStatusValues = EnumValues({"NONE": OptionStatus.none});

class EnumValues<T> {
  Map<String, T> map;
  Map<T, String>? reverseMap;

  EnumValues(this.map);

  Map<T, String>? get reverse {
    reverseMap ??= map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
