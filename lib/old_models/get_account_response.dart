// To parse this JSON data, do
//
//     final account = accountFrom<PERSON>son(jsonString);

import 'dart:convert';

Account accountFromJson(String str) => Account.fromJson(json.decode(str));

class Account {
  Account({
    required this.status,
    required this.statusDescription,
    required this.data,
  });

  int status;
  String statusDescription;
  List<Datum> data;

  factory Account.fromJson(Map<String, dynamic> json) => Account(
        status: json["status"],
        statusDescription: json["status_description"],
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
      );
}

class Datum {
  Datum({
    required this.comments,
    required this.vip,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.apartment,
    required this.city,
    required this.state,
    required this.country,
    required this.zip,
    required this.latitude,
    required this.longitude,
    required this.username,
    required this.email,
    required this.officeNumber,
    required this.landlineNumber,
    required this.addressLine1,
    required this.addressLine2,
    required this.homeNumber,
    required this.isSuperadmin,
    required this.phoneCountryCode,
    required this.officeNumberCc,
    required this.homeNumberCc,
    required this.landlineNumberCc,
    required this.gstNumber,
    required this.shippingAddress,
    required this.customerTypeCd,
    required this.bidMaxAmount,
  });

  dynamic comments;
  String? vip;
  String? firstName;
  dynamic lastName;
  String? phone;
  dynamic apartment;
  dynamic city;
  String? state;
  dynamic country;
  String? zip;
  dynamic latitude;
  dynamic longitude;
  dynamic username;
  String? email;
  dynamic officeNumber;
  dynamic landlineNumber;
  String? addressLine1;
  String? addressLine2;
  dynamic homeNumber;
  dynamic isSuperadmin;
  String? phoneCountryCode;
  dynamic officeNumberCc;
  dynamic homeNumberCc;
  dynamic landlineNumberCc;
  String? gstNumber;
  dynamic shippingAddress;
  String? customerTypeCd;
  int? bidMaxAmount;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        comments: json["comments"],
        vip: json["vip"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        phone: json["phone"],
        apartment: json["apartment"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        zip: json["zip"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        username: json["username"],
        email: json["email"],
        officeNumber: json["office_number"],
        landlineNumber: json["landline_number"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        homeNumber: json["home_number"],
        isSuperadmin: json["is_superadmin"],
        phoneCountryCode: json["phone_country_code"],
        officeNumberCc: json["office_number_cc"],
        homeNumberCc: json["home_number_cc"],
        landlineNumberCc: json["landline_number_cc"],
        gstNumber: json["gst_number"],
        shippingAddress: json["shipping_address"],
        customerTypeCd: json["customer_type_cd"],
        bidMaxAmount: json["bid_max_amount"],
      );
}
