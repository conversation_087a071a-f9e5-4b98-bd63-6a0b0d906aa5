// To parse this JSON data, do
//
//     final offlineSearchResult = offlineSearchResultFromJson(jsonString);

import 'dart:convert';

OfflineSearchResult offlineSearchResultFromJson(String str) =>
    OfflineSearchResult.fromJson(json.decode(str));

class OfflineSearchResult {
  OfflineSearchResult({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum>? data;
  int status;
  String statusDescription;

  factory OfflineSearchResult.fromJson(Map<String, dynamic> json) =>
      OfflineSearchResult(
        data: json["data"] == null
            ? null
            : List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );
}

class Datum {
  Datum({
    required this.id,
    required this.product,
    required this.grade,
    required this.quantity,
    required this.size,
    required this.moisture,
    required this.colour,
    required this.auctionTypeCd,
    required this.bidIncrementValue,
    required this.lotNo,
    required this.serviceId,
    required this.itemId,
    required this.priceRange,
    required this.startingPrice,
    required this.deliveryDate,
    required this.unitId,
    required this.unitName,
    required this.unitShortName,
    required this.customerId,
    required this.grade8MmClean,
    required this.grade8MmSickdsplit,
    required this.grade8MmFruit,
    required this.grade8MmTotalPercentage,
    required this.grade7T8MmClean,
    required this.grade7T8MmSickdsplit,
    required this.grade7T8MmFruit,
    required this.grade7T8MmTotalPercentage,
    required this.grade17MmClean,
    required this.grade17MmSickdsplit,
    required this.grade17MmFruit,
    required this.grade17MmTotalPercentage,
    required this.rejectionsClean,
    required this.rejectionsSickdsplit,
    required this.rejectionsFruit,
    required this.rejectionsTotalpercentage,
    required this.totalSickdsplit,
    required this.totalFruit,
    required this.totalPercentage,
    required this.literWeight,
    required this.totalWeight,
    required this.totalClean,
    required this.pickupAddressLine1,
    required this.pickupAddressLine2,
    required this.pickupCity,
    required this.pickupCountry,
    required this.pickupPincode,
    required this.pickupLat,
    required this.pickupLng,
    required this.vendorId,
    required this.vendorName,
    required this.stockStatusName,
    required this.stockStatusCd,
    required this.favouriteYn,
    required this.averageRating,
    required this.rateCount,
    required this.uniqueViews,
    required this.totalViews,
    required this.expectedPrice,
    required this.highestBidAmount,
    required this.buyBidPrice,
  });

  int id;
  String product;
  String grade;
  double quantity;
  String size;
  String moisture;
  String colour;
  String auctionTypeCd;
  String bidIncrementValue;
  int lotNo;
  int serviceId;
  int itemId;
  String priceRange;
  int startingPrice;
  DateTime? deliveryDate;
  int unitId;
  String unitName;
  String unitShortName;
  int customerId;
  int grade8MmClean;
  int grade8MmSickdsplit;
  int grade8MmFruit;
  int grade8MmTotalPercentage;
  int grade7T8MmClean;
  int grade7T8MmSickdsplit;
  int grade7T8MmFruit;
  int grade7T8MmTotalPercentage;
  int grade17MmClean;
  int grade17MmSickdsplit;
  int grade17MmFruit;
  int grade17MmTotalPercentage;
  int rejectionsClean;
  int rejectionsSickdsplit;
  int rejectionsFruit;
  int rejectionsTotalpercentage;
  int totalSickdsplit;
  int totalFruit;
  int totalPercentage;
  String literWeight;
  String totalWeight;
  int totalClean;
  String pickupAddressLine1;
  String pickupAddressLine2;
  String pickupCity;
  String pickupCountry;
  String pickupPincode;
  String pickupLat;
  String pickupLng;
  int vendorId;
  String vendorName;
  String stockStatusName;
  String stockStatusCd;
  String favouriteYn;
  String averageRating;
  String rateCount;
  String uniqueViews;
  String totalViews;
  int expectedPrice;
  int highestBidAmount;
  int buyBidPrice;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        product: json["product"],
        grade: json["grade"],
        quantity: json["quantity"].toDouble(),
        size: json["size"],
        moisture: json["moisture"],
        colour: json["colour"],
        auctionTypeCd: json["auctionTypeCd"],
        bidIncrementValue: json["bidIncrementValue"],
        lotNo: json["lotNo"],
        serviceId: json["serviceId"],
        itemId: json["itemId"],
        priceRange: json["priceRange"],
        startingPrice: json["startingPrice"],
        deliveryDate: json["deliveryDate"] == null
            ? null
            : DateTime.parse(json["deliveryDate"]),
        unitId: json["unitId"],
        unitName: json["unitName"],
        unitShortName: json["unitShortName"],
        customerId: json["customerId"],
        grade8MmClean: json["grade8mmClean"],
        grade8MmSickdsplit: json["grade8mmSickdsplit"],
        grade8MmFruit: json["grade8mmFruit"],
        grade8MmTotalPercentage: json["grade8mmTotalPercentage"],
        grade7T8MmClean: json["grade7t8mmClean"],
        grade7T8MmSickdsplit: json["grade7t8mmSickdsplit"],
        grade7T8MmFruit: json["grade7t8mmFruit"],
        grade7T8MmTotalPercentage: json["grade7t8mmTotalPercentage"],
        grade17MmClean: json["grade17mmClean"],
        grade17MmSickdsplit: json["grade17mmSickdsplit"],
        grade17MmFruit: json["grade17mmFruit"],
        grade17MmTotalPercentage: json["grade17mmTotalPercentage"],
        rejectionsClean: json["rejectionsClean"],
        rejectionsSickdsplit: json["rejectionsSickdsplit"],
        rejectionsFruit: json["rejectionsFruit"],
        rejectionsTotalpercentage: json["rejectionsTotalpercentage"],
        totalSickdsplit: json["totalSickdsplit"],
        totalFruit: json["totalFruit"],
        totalPercentage: json["totalPercentage"],
        literWeight: json["literWeight"],
        totalWeight: json["totalWeight"],
        totalClean: json["totalClean"],
        pickupAddressLine1: json["pickupAddressLine1"],
        pickupAddressLine2: json["pickupAddressLine2"],
        pickupCity: json["pickupCity"],
        pickupCountry: json["pickupCountry"],
        pickupPincode: json["pickupPincode"],
        pickupLat: json["pickupLat"],
        pickupLng: json["pickupLng"],
        vendorId: json["vendorId"],
        vendorName: json["vendorName"],
        stockStatusName: json["stockStatusName"],
        stockStatusCd: json["stockStatusCd"],
        favouriteYn: json["favourite_YN"],
        averageRating: json["averageRating"],
        rateCount: json["rateCount"],
        uniqueViews: json["uniqueViews"],
        totalViews: json["totalViews"],
        expectedPrice: json["expectedPrice"],
        highestBidAmount: json["highestBidAmount"],
        buyBidPrice: json["buyBidPrice"],
      );
}
