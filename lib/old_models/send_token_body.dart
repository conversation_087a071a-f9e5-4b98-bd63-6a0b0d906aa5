// To parse this JSON data, do
//
//     final sendTokenBody = sendTokenBodyFromJson(jsonString);

import 'dart:convert';

SendTokenBody sendTokenBodyFromJson(String str) =>
    SendTokenBody.fromJson(json.decode(str));

String sendTokenBodyToJson(SendTokenBody data) => json.encode(data.toJson());

class SendTokenBody {
  String? token;
  String? deviceId;
  String? osVersion;
  String? clientName;
  String? appVersion;

  SendTokenBody({
    this.token,
    this.deviceId,
    this.osVersion,
    this.clientName,
    this.appVersion,
  });

  factory SendTokenBody.fromJson(Map<String, dynamic> json) => SendTokenBody(
        token: json["token"],
        deviceId: json["device_id"],
        osVersion: json["os_version"],
        clientName: json["client_name"],
        appVersion: json["app_version"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "device_id": deviceId,
        "os_version": osVersion,
        "client_name": clientName,
        "app_version": appVersion,
      };
}
