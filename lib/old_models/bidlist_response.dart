// To parse this JSON data, do
//
//     final bidlist = bidlistFrom<PERSON><PERSON>(jsonString);

import 'dart:convert';

Bidlist bidlistFromJson(String str) => Bidlist.fromJson(json.decode(str));

String bidlistToJson(Bidlist data) => json.encode(data.toJson());

class Bidlist {
  Bidlist({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum> data;
  int status;
  String statusDescription;

  factory Bidlist.fromJson(Map<String, dynamic> json) => Bidlist(
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  Datum({
    required this.auctionId,
    required this.price,
    required this.totalPrice,
    required this.biddingDate,
    required this.lotNo,
    required this.bidderName,
    required this.action,
    required this.statusCd,
    required this.bidSubmissionDate,
    required this.bidReceivedDate,
    required this.stockId,
    required this.approvedYn,
    required this.buyerId,
    required this.phone,
    required this.time,
  });

  int? auctionId;
  double? price;
  double? totalPrice;
  DateTime? biddingDate;
  int? lotNo;
  String? bidderName;
  String? action;
  String? statusCd;
  String? bidSubmissionDate;
  DateTime? bidReceivedDate;
  int? stockId;
  String? approvedYn;
  dynamic buyerId;
  String? phone;
  String? time;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        auctionId: json["auctionId"],
        price: json["price"],
        totalPrice: json["totalPrice"],
        biddingDate: DateTime.parse(json["biddingDate"]),
        lotNo: json["lotNo"],
        bidderName: json["bidderName"],
        action: json["action"],
        statusCd: json["statusCd"],
        bidSubmissionDate: json["bidSubmissionDate"],
        bidReceivedDate: DateTime.parse(json["bidReceivedDate"]),
        stockId: json["stockId"],
        approvedYn: json["approvedYN"],
        buyerId: json["buyerId"],
        phone: json["phone"],
        time: json["time"],
      );

  Map<String, dynamic> toJson() => {
        "auctionId": auctionId,
        "price": price,
        "totalPrice": totalPrice,
        "biddingDate":
            "${biddingDate?.year.toString().padLeft(4, '0')}-${biddingDate?.month.toString().padLeft(2, '0')}-${biddingDate?.day.toString().padLeft(2, '0')}",
        "lotNo": lotNo,
        "bidderName": bidderName,
        "action": action,
        "statusCd": statusCd,
        "bidSubmissionDate": bidSubmissionDate,
        "bidReceivedDate":
            "${bidReceivedDate?.year.toString().padLeft(4, '0')}-${bidReceivedDate?.month.toString().padLeft(2, '0')}-${bidReceivedDate?.day.toString().padLeft(2, '0')}",
        "stockId": stockId,
        "approvedYN": approvedYn,
        "buyerId": buyerId,
        "phone": phone,
        "time": time,
      };
}
