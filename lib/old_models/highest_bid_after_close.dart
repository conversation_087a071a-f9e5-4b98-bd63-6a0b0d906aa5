// To parse this JSON data, do
//
//     final highestBidAfterClose = highestBidAfterCloseFromJson(jsonString);

import 'dart:convert';

HighestBidAfterClose highestBidAfterCloseFromJson(String str) =>
    HighestBidAfterClose.fromJson(json.decode(str));

String highestBidAfterCloseToJson(HighestBidAfterClose data) =>
    json.encode(data.toJson());

class HighestBidAfterClose {
  HighestBidAfterClose({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  Data data;
  int status;
  String statusDescription;

  factory HighestBidAfterClose.fromJson(Map<String, dynamic> json) =>
      HighestBidAfterClose(
        data: Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  Data({
    required this.bidId,
    required this.stockId,
    required this.highestBidAmount,
    required this.startingPrice,
    required this.expectedPrice,
    this.withDrawActiveYN,
  });

  int bidId;
  int stockId;
  double highestBidAmount;
  double startingPrice;
  double expectedPrice;
  String? withDrawActiveYN;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        bidId: json["bidId"],
        stockId: json["stockId"],
        highestBidAmount: json["highestBidAmount"],
        startingPrice: json["startingPrice"],
        expectedPrice: json["expectedPrice"],
        withDrawActiveYN: json["withdrawActiveYN"],
      );

  Map<String, dynamic> toJson() => {
        "bidId": bidId,
        "stockId": stockId,
        "highestBidAmount": highestBidAmount,
        "startingPrice": startingPrice,
        "expectedPrice": expectedPrice,
      };
}
