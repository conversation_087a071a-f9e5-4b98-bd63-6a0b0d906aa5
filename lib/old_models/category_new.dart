// class CategoryNew {
//   String? id;
//   String? parentId;
//   String? name;
//   String? hasChild;
//   String? image;
//   bool? isAvailable;
//   int? sequence;
//   String? categoryType;
//   String? primaryPhone;
//   String? avgRating;
//   String? totalRating;
//   int? reservationTypeId;
//   String? categoryFilter;
//   List<CategoryNew>? children;
//   String? shortServiceName;
//   String? timeText;
//   String? hcType;

//   CategoryNew({
//     this.id,
//     this.parentId,
//     this.name,
//     this.hasChild,
//     this.image,
//     this.isAvailable,
//     this.sequence,
//     this.categoryType,
//     this.primaryPhone,
//     this.avgRating,
//     this.totalRating,
//     this.reservationTypeId,
//     this.categoryFilter,
//     this.children,
//     this.shortServiceName,
//     this.timeText,
//     this.hcType,
//   });

//   factory CategoryNew.fromJson(Map<String, dynamic> json) {
//     return CategoryNew(
//       id: json['id'],
//       parentId: json['parent_id'],
//       name: json['name'],
//       hasChild: json['has_child'],
//       image: json['image'],
//       isAvailable: json['is_available'],
//       sequence: json['sequence'],
//       categoryType: json['category_type'],
//       primaryPhone: json['primary_phone'],
//       avgRating: json['avg_rating'],
//       totalRating: json['total_rating'],
//       reservationTypeId: json['reservation_type_id'],
//       categoryFilter: json['category_filter'],
//       children: json['children'] == null ? null : (json['children'] as List).map((i) => CategoryNew.fromJson(i)).toList(),
//       shortServiceName: json['short_service_name'],
//       timeText: json['time_text'],
//       hcType: json['hc_type'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'parent_id': parentId,
//       'name': name,
//       'has_child': hasChild,
//       'image': image,
//       'is_available': isAvailable,
//       'sequence': sequence,
//       'category_type': categoryType,
//       'primary_phone': primaryPhone,
//       'avg_rating': avgRating,
//       'total_rating': totalRating,
//       'reservation_type_id': reservationTypeId,
//       'category_filter': categoryFilter,
//       'children': children?.map((i) => i.toJson()).toList(),
//       'short_service_name': shortServiceName,
//       'time_text': timeText,
//       'hc_type': hcType,
//     };
//   }
// }

class CategoryNew {
  final num? customerId;
  final String? categoryId;
  final String? parentId;
  final String? name;
  final String? link;
  final String? shortServiceName;
  final String? code;
  final num? hasChild;
  final String? image;
  final String? sequence;
  final String? categoryType;
  final String? shortDescription;
  final String? longDescription;
  final String? primaryPhone;
  final num? isAvailable;
  final String? publicInsertPageLink;
  final String? icon;
  final String? timeText;
  final String? avgRating;
  final String? totalRating;
  final num? reservationTypeId;
  final String? headingTextColor;
  final String? homePageYn;
  final String? hcType;
  final String? categoryData;
  final String? categoryFilter;
  final num? internalCategoryId;
  final String? group;
  final num? dealerRate1;
  final num? manufacturerRate1;
  final num? memberDealerRate1;
  final num? memberManufacturerRate1;
  final num? dealerRate2;
  final num? manufacturerRate2;
  final num? memberDealerRate2;
  final num? memberManufacturerRate2;
  final bool? allowInRegistration;
  final bool? removeInCategories;
  final bool? removeInRegistration;
  final String? tileCode;
  List<CategoryNew>? children;

  CategoryNew({
    this.customerId,
    this.categoryId,
    this.parentId,
    this.name,
    this.link,
    this.shortServiceName,
    this.code,
    this.hasChild,
    this.image,
    this.sequence,
    this.categoryType,
    this.shortDescription,
    this.longDescription,
    this.primaryPhone,
    this.isAvailable,
    this.publicInsertPageLink,
    this.icon,
    this.timeText,
    this.avgRating,
    this.totalRating,
    this.reservationTypeId,
    this.headingTextColor,
    this.homePageYn,
    this.hcType,
    this.categoryData,
    this.categoryFilter,
    this.internalCategoryId,
    this.group,
    this.dealerRate1,
    this.manufacturerRate1,
    this.memberDealerRate1,
    this.memberManufacturerRate1,
    this.dealerRate2,
    this.manufacturerRate2,
    this.memberDealerRate2,
    this.memberManufacturerRate2,
    this.allowInRegistration,
    this.removeInCategories,
    this.removeInRegistration,
    this.children,
    this.tileCode,
  });

  factory CategoryNew.fromJson(Map<String, dynamic> json) {
    var childrenFromJson = json['children'] as List?;
    List<CategoryNew>? childrenList =
        childrenFromJson?.map((child) => CategoryNew.fromJson(child)).toList();

    return CategoryNew(
      customerId: json['customerId'],
      categoryId: json['categoryId'],
      parentId: json['parentId'],
      name: json['name'],
      link: json['link'],
      shortServiceName: json['shortServiceName'],
      code: json['code'],
      hasChild: json['hasChild'],
      image: json['image'],
      sequence: json['sequence'],
      categoryType: json['categoryType'],
      shortDescription: json['shortDescription'],
      longDescription: json['longDescription'],
      primaryPhone: json['primaryPhone'],
      isAvailable: json['isAvailable'],
      publicInsertPageLink: json['publicInsertPageLink'],
      icon: json['icon'],
      timeText: json['timeText'],
      avgRating: json['avgRating'],
      totalRating: json['totalRating'],
      reservationTypeId: json['reservationTypeId'],
      headingTextColor: json['headingTextColor'],
      homePageYn: json['homePageYn'],
      hcType: json['hcType'],
      categoryData: json['categoryData'],
      categoryFilter: json['categoryFilter'],
      internalCategoryId: json['internalCategoryId'],
      group: json['group'],
      dealerRate1: json['dealerRate1'],
      manufacturerRate1: json['manufacturerRate1'],
      memberDealerRate1: json['memberDealerRate1'],
      memberManufacturerRate1: json['memberManufacturerRate1'],
      dealerRate2: json['dealerRate2'],
      manufacturerRate2: json['manufacturerRate2'],
      memberDealerRate2: json['memberDealerRate2'],
      memberManufacturerRate2: json['memberManufacturerRate2'],
      allowInRegistration: json['allowInRegistration'],
      removeInCategories: json['removeInCategories'],
      removeInRegistration: json['removeInRegistration'],
      tileCode: json['tileCode'],
      children: childrenList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'categoryId': categoryId,
      'parentId': parentId,
      'name': name,
      'link': link,
      'shortServiceName': shortServiceName,
      'code': code,
      'hasChild': hasChild,
      'image': image,
      'sequence': sequence,
      'categoryType': categoryType,
      'shortDescription': shortDescription,
      'longDescription': longDescription,
      'primaryPhone': primaryPhone,
      'isAvailable': isAvailable,
      'publicInsertPageLink': publicInsertPageLink,
      'icon': icon,
      'timeText': timeText,
      'avgRating': avgRating,
      'totalRating': totalRating,
      'reservationTypeId': reservationTypeId,
      'headingTextColor': headingTextColor,
      'homePageYn': homePageYn,
      'hcType': hcType,
      'categoryData': categoryData,
      'categoryFilter': categoryFilter,
      'internalCategoryId': internalCategoryId,
      'group': group,
      'dealerRate1': dealerRate1,
      'manufacturerRate1': manufacturerRate1,
      'memberDealerRate1': memberDealerRate1,
      'memberManufacturerRate1': memberManufacturerRate1,
      'dealerRate2': dealerRate2,
      'manufacturerRate2': manufacturerRate2,
      'memberDealerRate2': memberDealerRate2,
      'memberManufacturerRate2': memberManufacturerRate2,
      'allowInRegistration': allowInRegistration,
      'removeInCategories': removeInCategories,
      'removeInRegistration': removeInRegistration,
      'tileCode': tileCode,
      'children': children?.map((child) => child.toJson()).toList(),
    };
  }
}
