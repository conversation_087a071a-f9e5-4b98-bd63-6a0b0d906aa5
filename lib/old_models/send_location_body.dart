// To parse this JSON data, do
//
//     final sendLocationBody = sendLocationBodyFromJson(jsonString);

import 'dart:convert';

String sendLocationBodyToJson(SendLocationBody data) =>
    json.encode(data.toJson());

class SendLocationBody {
  SendLocationBody({
    required this.latitude,
    required this.longitude,
  });

  double? latitude;
  double? longitude;

  Map<String, dynamic> toJson() => {
        "latitude": latitude,
        "longitude": longitude,
      };
}
