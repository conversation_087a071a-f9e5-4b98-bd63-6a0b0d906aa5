class SoldList {
  SoldList({
    this.content,
    this.pageable,
    this.totalElements,
    this.totalPages,
    this.last,
    this.first,
    this.numberOfElements,
    this.sort,
    this.size,
    this.number,
    this.empty,
  });

  List<Content>? content;
  Pageable? pageable;
  num? totalElements;
  num? totalPages;
  bool? last;
  bool? first;
  num? numberOfElements;
  Sort? sort;
  num? size;
  num? number;
  bool? empty;

  factory SoldList.fromJson(Map<String, dynamic> json) => SoldList(
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        last: json["last"],
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        size: json["size"],
        number: json["number"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "totalElements": totalElements,
        "totalPages": totalPages,
        "last": last,
        "first": first,
        "numberOfElements": numberOfElements,
        "sort": sort?.toJson(),
        "size": size,
        "number": number,
        "empty": empty,
      };
}

class Content {
  Content({
    this.stockId,
    this.sellerId,
    this.buyerId,
    this.createdAt,
    this.updatedAt,
    this.totalPurchaseAmount,
    this.purchaseTax,
    this.purchaseTaxPercentage,
    this.purchaseAmount,
    this.paidUpAmount,
    this.remainingPurchaseAmount,
    this.amountPaidToSeller,
    this.totalCommision,
    this.netCommision,
    this.commisionTax,
    this.commisionTaxPercentage,
    this.amountPendingToSeller,
    this.amountWithheldFromSeller,
    this.amountWithheldAt,
    this.quantity,
    this.sellerName,
    this.sellerVendorName,
    this.buyerName,
    this.buyerVendorName,
    this.sellerCurrentMargin,
    this.buyerCurrentMargin,
    this.lotSoldAt,
  });

  num? stockId;
  num? sellerId;
  num? buyerId;
  DateTime? createdAt;
  DateTime? updatedAt;
  num? totalPurchaseAmount;
  num? purchaseTax;
  num? purchaseTaxPercentage;
  num? purchaseAmount;
  num? paidUpAmount;
  num? remainingPurchaseAmount;
  dynamic amountPaidToSeller;
  dynamic totalCommision;
  dynamic netCommision;
  dynamic commisionTax;
  dynamic commisionTaxPercentage;
  dynamic amountPendingToSeller;
  dynamic amountWithheldFromSeller;
  dynamic amountWithheldAt;
  num? quantity;
  String? sellerName;
  String? sellerVendorName;
  String? buyerName;
  String? buyerVendorName;
  dynamic sellerCurrentMargin;
  num? buyerCurrentMargin;
  DateTime? lotSoldAt;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        stockId: json["stock_id"],
        sellerId: json["seller_id"],
        buyerId: json["buyer_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        totalPurchaseAmount: json["total_purchase_amount"]?.toDouble(),
        purchaseTax: json["purchase_tax"]?.toDouble(),
        purchaseTaxPercentage: json["purchase_tax_percentage"],
        purchaseAmount: json["purchase_amount"]?.toDouble(),
        paidUpAmount: json["paid_up_amount"],
        remainingPurchaseAmount: json["remaining_purchase_amount"]?.toDouble(),
        amountPaidToSeller: json["amount_paid_to_seller"],
        totalCommision: json["total_commision"],
        netCommision: json["net_commision"],
        commisionTax: json["commision_tax"],
        commisionTaxPercentage: json["commision_tax_percentage"],
        amountPendingToSeller: json["amount_pending_to_seller"],
        amountWithheldFromSeller: json["amount_withheld_from_seller"],
        amountWithheldAt: json["amount_withheld_at"],
        quantity: json["quantity"]?.toDouble(),
        sellerName: json["seller_name"],
        sellerVendorName: json["seller_vendor_name"],
        buyerName: json["buyer_name"],
        buyerVendorName: json["buyer_vendor_name"],
        sellerCurrentMargin: json["seller_current_margin"],
        buyerCurrentMargin: json["buyer_current_margin"]?.toDouble(),
        lotSoldAt: json["lot_sold_at"] == null
            ? null
            : DateTime.parse(json["lot_sold_at"]),
      );

  Map<String, dynamic> toJson() => {
        "stock_id": stockId,
        "seller_id": sellerId,
        "buyer_id": buyerId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "total_purchase_amount": totalPurchaseAmount,
        "purchase_tax": purchaseTax,
        "purchase_tax_percentage": purchaseTaxPercentage,
        "purchase_amount": purchaseAmount,
        "paid_up_amount": paidUpAmount,
        "remaining_purchase_amount": remainingPurchaseAmount,
        "amount_paid_to_seller": amountPaidToSeller,
        "total_commision": totalCommision,
        "net_commision": netCommision,
        "commision_tax": commisionTax,
        "commision_tax_percentage": commisionTaxPercentage,
        "amount_pending_to_seller": amountPendingToSeller,
        "amount_withheld_from_seller": amountWithheldFromSeller,
        "amount_withheld_at": amountWithheldAt,
        "quantity": quantity,
        "seller_name": sellerName,
        "seller_vendor_name": sellerVendorName,
        "buyer_name": buyerName,
        "buyer_vendor_name": buyerVendorName,
        "seller_current_margin": sellerCurrentMargin,
        "buyer_current_margin": buyerCurrentMargin,
        "lot_sold_at": lotSoldAt?.toIso8601String(),
      };
}

class Pageable {
  Pageable({
    this.sort,
    this.pageNumber,
    this.pageSize,
    this.offset,
    this.paged,
    this.unpaged,
  });

  Sort? sort;
  num? pageNumber;
  num? pageSize;
  num? offset;
  bool? paged;
  bool? unpaged;

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        offset: json["offset"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "offset": offset,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  Sort({
    this.unsorted,
    this.sorted,
    this.empty,
  });

  bool? unsorted;
  bool? sorted;
  bool? empty;

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        unsorted: json["unsorted"],
        sorted: json["sorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "unsorted": unsorted,
        "sorted": sorted,
        "empty": empty,
      };
}
