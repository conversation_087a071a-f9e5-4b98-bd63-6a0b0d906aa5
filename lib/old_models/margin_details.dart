class MarginDetails {
  final Data? data;
  final int? status;
  final String? statusDescription;

  MarginDetails({
    this.data,
    this.status,
    this.statusDescription,
  });

  MarginDetails.fromJson(Map<String, dynamic> json)
      : data = (json['data'] as Map<String, dynamic>?) != null
            ? Data.fromJson(json['data'] as Map<String, dynamic>)
            : null,
        status = json['status'] as int?,
        statusDescription = json['status_description'] as String?;

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
        'status': status,
        'status_description': statusDescription
      };
}

class Data {
  final double? biddingLimitRemaining;
  final double? availableMargin;

  Data({
    this.biddingLimitRemaining,
    this.availableMargin,
  });

  Data.fromJson(Map<String, dynamic> json)
      : biddingLimitRemaining = json['biddingLimitRemaining'] as double?,
        availableMargin = json['availableMargin'] as double?;

  Map<String, dynamic> toJson() => {
        'biddingLimitRemaining': biddingLimitRemaining,
        'availableMargin': availableMargin
      };
}
