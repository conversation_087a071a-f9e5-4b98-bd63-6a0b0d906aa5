import 'dart:convert';

TotalStockSold totalStockFromJson(String str) =>
    TotalStockSold.fromJson(json.decode(str));

String totalStockToJson(TotalStockSold data) => json.encode(data.toJson());

class TotalStockSold {
  TotalStockSold({
    this.noOfStocksSold,
    this.totalAmountOfSoldStocks,
    this.totalQuantitySold,
  });

  final int? noOfStocksSold;
  final num? totalAmountOfSoldStocks;
  final num? totalQuantitySold;

  factory TotalStockSold.fromJson(Map<String, dynamic> json) => TotalStockSold(
        noOfStocksSold: json["no_of_stocks_sold"],
        totalAmountOfSoldStocks: json["total_amount_of_sold_stocks"],
        totalQuantitySold: json["total_quantity_sold"],
      );

  Map<String, dynamic> toJson() => {
        "no_of_stocks_sold": noOfStocksSold,
        "total_amount_of_sold_stocks": totalAmountOfSoldStocks,
        "total_quantity_sold": totalQuantitySold,
      };
}

class TotalStockBought {
  TotalStockBought({
    this.noOfStocksBought,
    this.totalAmountOfBoughtStocks,
    this.totalQuantityBought,
  });

  final int? noOfStocksBought;
  final num? totalAmountOfBoughtStocks;
  final num? totalQuantityBought;

  factory TotalStockBought.fromJson(Map<String, dynamic> json) =>
      TotalStockBought(
        noOfStocksBought: json["no_of_stocks_bought"],
        totalAmountOfBoughtStocks: json["total_amount_of_bought_stocks"],
        totalQuantityBought: json["total_quantity_bought"],
      );

  Map<String, dynamic> toJson() => {
        "no_of_stocks_sold": noOfStocksBought,
        "total_amount_of_sold_stocks": totalAmountOfBoughtStocks,
        "total_quantity_sold": totalQuantityBought,
      };
}
