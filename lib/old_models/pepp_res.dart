class PeppRes {
  final Data? data;
  final int? status;
  final String? statusDescription;

  PeppRes({
    this.data,
    this.status,
    this.statusDescription,
  });

  PeppRes.fromJson(Map<String, dynamic> json)
      : data = (json['data'] as Map<String, dynamic>?) != null
            ? Data.fromJson(json['data'] as Map<String, dynamic>)
            : null,
        status = json['status'] as int?,
        statusDescription = json['status_description'] as String?;

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
        'status': status,
        'status_description': statusDescription
      };
}

class Data {
  final int? id;
  final String? createdAt;
  final String? modifiedAt;
  final int? stockId;
  final String? pdctCd;
  final String? bulkDensity;
  final String? moisture;
  final String? lightBerries;
  final String? extraneousMatter;
  final String? bold5mm;
  final String? bold425mm;
  final String? bold475mm;
  final String? mineralOil;
  final String? mould;

  Data({
    this.id,
    this.createdAt,
    this.modifiedAt,
    this.stockId,
    this.pdctCd,
    this.bulkDensity,
    this.moisture,
    this.lightBerries,
    this.extraneousMatter,
    this.bold5mm,
    this.bold425mm,
    this.bold475mm,
    this.mineralOil,
    this.mould,
  });

  Data.fromJson(Map<String, dynamic> json)
      : id = json['id'] as int?,
        createdAt = json['created_at'] as String?,
        modifiedAt = json['modified_at'] as String?,
        stockId = json['stock_id'] as int?,
        pdctCd = json['pdct_cd'] as String?,
        bulkDensity = json['bulk_density'] as String?,
        moisture = json['moisture'] as String?,
        lightBerries = json['light_berries'] as String?,
        extraneousMatter = json['extraneous_matter'] as String?,
        bold5mm = json['bold5mm'] as String?,
        bold425mm = json['bold425mm'] as String?,
        bold475mm = json['bold475mm'] as String?,
        mineralOil = json['mineral_oil'] as String?,
        mould = json['mould'] as String?;

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt,
        'modified_at': modifiedAt,
        'stock_id': stockId,
        'pdct_cd': pdctCd,
        'bulk_density': bulkDensity,
        'moisture': moisture,
        'light_berries': lightBerries,
        'extraneous_matter': extraneousMatter,
        'bold5mm': bold5mm,
        'bold425mm': bold425mm,
        'bold475mm': bold475mm,
        'mineral_oil': mineralOil,
        'mould': mould
      };
}
