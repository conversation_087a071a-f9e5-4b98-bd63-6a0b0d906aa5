// To parse this JSON data, do
//
//     final updateAccountBody = updateAccountBodyFromJson(jsonString);

import 'dart:convert';

UpdateAccountBody updateAccountBodyFromJson(String str) =>
    UpdateAccountBody.fromJson(json.decode(str));

String updateAccountBodyToJson(UpdateAccountBody data) =>
    json.encode(data.toJson());

class UpdateAccountBody {
  UpdateAccountBody({
    required this.url,
    required this.homeNumber,
    required this.firstName,
    required this.addressLine1,
    this.customerId,
  });

  String url;
  String homeNumber;
  String firstName;
  String addressLine1;
  String? customerId;

  factory UpdateAccountBody.fromJson(Map<String, dynamic> json) =>
      UpdateAccountBody(
        url: json["url"],
        homeNumber: json["home_number"],
        firstName: json["first_name"],
        addressLine1: json["address_line_1"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "home_number": homeNumber,
        "first_name": firstName,
        "address_line_1": addressLine1,
        "customerId": customerId,
      };
}
