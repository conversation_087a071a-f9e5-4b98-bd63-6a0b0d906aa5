class PostResponse {
  final Data data;
  final int status;
  final String statusDescription;

  PostResponse({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  factory PostResponse.fromJson(Map<String, dynamic> json) {
    return PostResponse(
      data: Data.fromJson(json['data']),
      status: json['status'],
      statusDescription: json['status_description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
      'status': status,
      'status_description': statusDescription,
    };
  }
}

class Data {
  final List<int> offerId;

  Data({required this.offerId});

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      offerId: List<int>.from(json['offerId']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offerId': offerId,
    };
  }
}
