class GetComments {
  GetComments({
    this.totalPages,
    this.currentPage,
    this.content,
    this.totalElements,
  });

  final int? totalPages;
  final int? currentPage;
  final List<Content>? content;
  final int? totalElements;

  factory GetComments.fromJson(Map<String, dynamic> json) => GetComments(
        totalPages: json["totalPages"],
        currentPage: json["currentPage"],
        content: json["content"] == null
            ? []
            : List<Content>.from(
                json["content"]!.map((x) => Content.fromJson(x))),
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "totalPages": totalPages,
        "currentPage": currentPage,
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "totalElements": totalElements,
      };
}

class Content {
  Content({
    this.id,
    this.stockId,
    this.helpfulCount,
    this.verifiedPurchase,
    this.comment,
    this.rating,
    this.createdAt,
    this.customerName,
    this.customersCompanyName,
    this.reported,
  });

  final int? id;
  final int? stockId;
  final int? helpfulCount;
  final bool? verifiedPurchase;
  final String? comment;
  final int? rating;
  final DateTime? createdAt;
  final String? customerName;
  final String? customersCompanyName;
  final bool? reported;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        stockId: json["stockId"],
        helpfulCount: json["helpfulCount"],
        verifiedPurchase: json["verifiedPurchase"],
        comment: json["comment"],
        rating: json["rating"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        customerName: json["customerName"]!,
        customersCompanyName: json["customersCompanyName"]!,
        reported: json["reported"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "stockId": stockId,
        "helpfulCount": helpfulCount,
        "verifiedPurchase": verifiedPurchase,
        "comment": comment,
        "rating": rating,
        "createdAt": createdAt?.toIso8601String(),
        "customerName": customerNameValues.reverse[customerName],
        "customersCompanyName":
            customersCompanyNameValues.reverse[customersCompanyName],
        "reported": reported,
      };
}

enum CustomerName { USER }

final customerNameValues = EnumValues({"User": CustomerName.USER});

enum CustomersCompanyName { CONNECTONE }

final customersCompanyNameValues =
    EnumValues({"Connectone": CustomersCompanyName.CONNECTONE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
