// To parse this JSON data, do
//
//     final category = categoryFromJson(jsonString);

import 'dart:convert';

Category categoryFromJson(String str) => Category.fromJson(json.decode(str));

String categoryToJson(Category data) => json.encode(data.toJson());

class Category {
  Category({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum> data;
  int status;
  String statusDescription;

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  Datum(
      {required this.id,
      required this.parentId,
      required this.name,
      required this.shortServiceName,
      required this.hasChild,
      required this.image,
      required this.isAvailable,
      required this.sequence,
      required this.categoryType,
      required this.primaryPhone,
      required this.avgRating,
      required this.totalRating,
      required this.reservationTypeId,
      required this.hcType,
      required this.categoryFilter,
      this.categoryData});

  String id;
  String parentId;
  String name;
  String shortServiceName;
  String hasChild;
  String image;
  bool isAvailable;
  int sequence;
  String categoryType;
  String primaryPhone;
  String avgRating;
  String totalRating;
  int reservationTypeId;
  String hcType;
  String categoryFilter;
  String? categoryData;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        parentId: json["parent_id"],
        name: json["name"],
        shortServiceName: json["short_service_name"],
        hasChild: json["has_child"],
        image: json["image"],
        isAvailable: json["is_available"],
        sequence: json["sequence"],
        categoryType: json["category_type"],
        primaryPhone: json["primary_phone"],
        avgRating: json["avg_rating"],
        totalRating: json["total_rating"],
        reservationTypeId: json["reservation_type_id"],
        hcType: json["hc_type"],
        categoryFilter: json["category_filter"],
        categoryData: json["category_data"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "name": name,
        "short_service_name": shortServiceName,
        "has_child": hasChild,
        "image": image,
        "is_available": isAvailable,
        "sequence": sequence,
        "category_type": categoryType,
        "primary_phone": primaryPhone,
        "avg_rating": avgRating,
        "total_rating": totalRating,
        "reservation_type_id": reservationTypeId,
        "hc_type": hcType,
        "category_filter": categoryFilter,
        "category_data": categoryData
      };
}
