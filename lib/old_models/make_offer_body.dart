// To parse this JSON data, do
//
//     final makeOfferBody = makeOfferBodyFromJson(jsonString);

import 'dart:convert';

String makeOfferBodyToJson(MakeOfferBody data) => json.encode(data.toJson());

class MakeOfferBody {
  MakeOfferBody({
    required this.url,
    required this.customerId,
    required this.stockId,
    required this.quantity,
    required this.amount,
    required this.bidDeskNo,
    required this.orderTypeCd,
  });

  String url;
  String customerId;
  int stockId;
  int quantity;
  int amount;
  String bidDeskNo;
  String orderTypeCd;

  Map<String, dynamic> toJson() => {
        "url": url,
        "customerId": customerId,
        "stockId": stockId,
        "quantity": quantity,
        "amount": amount,
        "bid_desk_no": bidDeskNo,
        "order_type_cd": orderTypeCd,
      };
}
