// To parse this JSON data, do
//
//     final configuration = configurationFromJson(jsonString);

import 'dart:convert';

List<Configuration?>? configurationFromJson(String str) =>
    json.decode(str) == null
        ? []
        : List<Configuration?>.from(
            json.decode(str)!.map((x) => Configuration.fromJson(x)));

String configurationToJson(List<Configuration?>? data) => json.encode(
    data == null ? [] : List<dynamic>.from(data.map((x) => x!.toJson())));

class Configuration {
  Configuration({
    this.id,
    this.keyName1,
    this.valueName1,
    this.iosMobEnabled,
    this.androidMobEnabled,
    this.adminWebEnabled,
    this.customerWebEnabled,
    this.editable,
    this.readable,
    this.createdByAdmin,
    // this.createdAt,
    // this.modifiedAt,
  });

  int? id;
  String? keyName1;
  String? valueName1;
  bool? iosMobEnabled;
  bool? androidMobEnabled;
  bool? adminWebEnabled;
  bool? customerWebEnabled;
  bool? editable;
  bool? readable;
  bool? createdByAdmin;

  // DateTime? createdAt;
  // DateTime? modifiedAt;

  factory Configuration.fromJson(Map<String, dynamic> json) => Configuration(
        id: json["id"],
        keyName1: json["key_name1"],
        valueName1: json["value_name1"],
        iosMobEnabled: json["ios_mob_enabled"],
        androidMobEnabled: json["android_mob_enabled"],
        adminWebEnabled: json["admin_web_enabled"],
        customerWebEnabled: json["customer_web_enabled"],
        editable: json["editable"],
        readable: json["readable"],
        createdByAdmin: json["created_by_admin"],
        // createdAt: DateTime.parse(json["created_at"]),
        // modifiedAt: json["modified_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "key_name1": keyName1,
        "value_name1": valueName1,
        "ios_mob_enabled": iosMobEnabled,
        "android_mob_enabled": androidMobEnabled,
        "admin_web_enabled": adminWebEnabled,
        "customer_web_enabled": customerWebEnabled,
        "editable": editable,
        "readable": readable,
        "created_by_admin": createdByAdmin,
        // "created_at": createdAt?.toIso8601String(),
        // "modified_at": modifiedAt,
      };
}
