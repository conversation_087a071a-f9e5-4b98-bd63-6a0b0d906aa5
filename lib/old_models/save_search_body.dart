// To parse this JSON data, do
//
//     final saveSearchBody = saveSearchBodyFromJson(jsonString);

import 'dart:convert';

String saveSearchBodyToJson(SaveSearchBody data) => json.encode(data.toJson());

class SaveSearchBody {
  SaveSearchBody({
    this.buyBidPriceFrom,
    this.buyBidPriceTo,
    this.customerId,
    this.days,
    this.isWeb,
    this.location,
    this.priceFrom,
    this.priceTo,
    this.product,
    this.quantityFrom,
    this.quantityTo,
    this.recurrence,
    this.searchName,
  });

  int? buyBidPriceFrom;
  int? buyBidPriceTo;
  int? customerId;
  List<String>? days;
  String? isWeb;
  String? location;
  int? priceFrom;
  int? priceTo;
  String? product;
  int? quantityFrom;
  int? quantityTo;
  String? recurrence;
  String? searchName;

  Map<String, dynamic> toJson() => {
        "buyBidPriceFrom": buyBidPriceFrom,
        "buyBidPriceTo": buyBidPriceTo,
        "customerId": customerId,
        "days": days != null ? List<dynamic>.from(days!.map((x) => x)) : [],
        "isWeb": isWeb,
        "location": location,
        "priceFrom": priceFrom,
        "priceTo": priceTo,
        "product": product,
        "quantityFrom": quantityFrom,
        "quantityTo": quantityTo,
        "recurrence": recurrence,
        "searchName": searchName,
      };
}
