// // To parse this JSON data, do
// //
// //     final getFilters = getFiltersFromJson(jsonString);
//
// import 'package:meta/meta.dart';
// import 'dart:convert';
//
// List<GetFilters> getFiltersFromJson(String str) => List<GetFilters>.from(json.decode(str).map((x) => GetFilters.fromJson(x)));
//
// String getFiltersToJson(List<GetFilters> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));
//
// class GetFilters {
//   GetFilters({
//     required this.id,
//     required this.customerId,
//     required this.product,
//     required this.location,
//     required this.grade,
//     required this.priceFrom,
//     required this.priceTo,
//     required this.quantityFrom,
//     required this.quantityTo,
//     required this.buyBidPriceFrom,
//     required this.buyBidPriceTo,
//     required this.deliveryDate,
//     required this.searchName,
//     required this.isWeb,
//     required this.days,
//     required this.recurrence,
//   });
//
//   int id;
//   int customerId;
//   Product product;
//   String location;
//   dynamic grade;
//   int priceFrom;
//   int priceTo;
//   int quantityFrom;
//   int quantityTo;
//   int buyBidPriceFrom;
//   int buyBidPriceTo;
//   dynamic deliveryDate;
//   String searchName;
//   IsWeb isWeb;
//   List<String> days;
//   String recurrence;
//
//   factory GetFilters.fromJson(Map<String, dynamic> json) => GetFilters(
//     id: json["id"] == null ? null : json["id"],
//     customerId: json["customerId"] == null ? null : json["customerId"],
//     product: json["product"] == null ? null : productValues.map[json["product"]],
//     location: json["location"] == null ? null : json["location"],
//     grade: json["grade"],
//     priceFrom: json["priceFrom"] == null ? null : json["priceFrom"],
//     priceTo: json["priceTo"] == null ? null : json["priceTo"],
//     quantityFrom: json["quantityFrom"] == null ? null : json["quantityFrom"],
//     quantityTo: json["quantityTo"] == null ? null : json["quantityTo"],
//     buyBidPriceFrom: json["buyBidPriceFrom"] == null ? null : json["buyBidPriceFrom"],
//     buyBidPriceTo: json["buyBidPriceTo"] == null ? null : json["buyBidPriceTo"],
//     deliveryDate: json["deliveryDate"],
//     searchName: json["searchName"] == null ? null : json["searchName"],
//     isWeb: json["isWeb"] == null ? null : isWebValues.map[json["isWeb"]],
//     days: json["days"] ==  List<String>.from(json["days"].map((x) => x)),
//     recurrence: json["recurrence"] == null ? null : json["recurrence"],
//   );
//
//   Map<String, dynamic> toJson() => {
//     "id": id == null ? null : id,
//     "customerId": customerId == null ? null : customerId,
//     "product": product == null ? null : productValues.reverse[product],
//     "location": location == null ? null : location,
//     "grade": grade,
//     "priceFrom": priceFrom == null ? null : priceFrom,
//     "priceTo": priceTo == null ? null : priceTo,
//     "quantityFrom": quantityFrom == null ? null : quantityFrom,
//     "quantityTo": quantityTo == null ? null : quantityTo,
//     "buyBidPriceFrom": buyBidPriceFrom == null ? null : buyBidPriceFrom,
//     "buyBidPriceTo": buyBidPriceTo == null ? null : buyBidPriceTo,
//     "deliveryDate": deliveryDate,
//     "searchName": searchName == null ? null : searchName,
//     "isWeb": isWeb == null ? null : isWebValues.reverse[isWeb],
//     "days": days == null ? null : List<dynamic>.from(days.map((x) => x)),
//     "recurrence": recurrence == null ? null : recurrence,
//   };
// }
//
// enum IsWeb { FALSE, TRUE }
//
// final isWebValues = EnumValues({
//   "False": IsWeb.FALSE,
//   "True": IsWeb.TRUE
// });
//
// enum Product { CHOW_CHOW, TOMATO }
//
// final productValues = EnumValues({
//   "Chow Chow": Product.CHOW_CHOW,
//   "Tomato": Product.TOMATO
// });
//
// class EnumValues<T> {
//   Map<String, T> map;
//   late Map<T, String> reverseMap;
//
//   EnumValues(this.map);
//
//   Map<T, String> get reverse {
//     if (reverseMap == null) {
//       reverseMap = map.map((k, v) => new MapEntry(v, k));
//     }
//     return reverseMap;
//   }
// }
