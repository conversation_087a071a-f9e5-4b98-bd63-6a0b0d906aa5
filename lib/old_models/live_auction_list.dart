// To parse this JSON data, do
//
//     final liveAuctionList = liveAuctionListFromJson(jsonString);

import 'dart:convert';

LiveAuctionList liveAuctionListFromJson(String str) =>
    LiveAuctionList.fromJson(json.decode(str));

String liveAuctionListToJson(LiveAuctionList data) =>
    json.encode(data.toJson());

class LiveAuctionList {
  LiveAuctionList({
    required this.data,
    required this.status,
    required this.statusDescription,
  });

  List<Datum> data;
  int status;
  String statusDescription;

  factory LiveAuctionList.fromJson(Map<String, dynamic> json) =>
      LiveAuctionList(
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "status": status,
        "status_description": statusDescription,
      };
}

class Datum {
  Datum({
    required this.auctionNo,
    required this.date,
    required this.product,
    required this.stocks,
    required this.quantity,
    required this.avgPrice,
    required this.status,
    required this.image,
  });

  String auctionNo;
  DateTime date;
  String? product;
  int? stocks;
  double? quantity;
  double? avgPrice;
  String status;
  String image;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        auctionNo: json["auction_no"],
        date: DateTime.parse(json["date"]),
        product: json["product"],
        stocks: json["stocks"],
        quantity: json["quantity"],
        avgPrice: json["avg_price"],
        status: json["status"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "auction_no": auctionNo,
        "date": date.toIso8601String(),
        "product": productValues.reverse![product],
        "stocks": stocks,
        "quantity": quantity,
        "avg_price": avgPrice,
        "status": status,
        "image": image,
      };
}

enum Product { cardamom }

final productValues = EnumValues({"CARDAMOM": Product.cardamom});

enum Status { closed, scheduled, inProgress }

class EnumValues<T> {
  Map<String, T> map;
  Map<T, String>? reverseMap;

  EnumValues(this.map);

  Map<T, String>? get reverse {
    reverseMap ??= map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
