import 'package:firebase_database/firebase_database.dart';

class FirebaseResponseOffline {
  late String stockAuctionStatus;
  late String bidUpdateTimestamp;
  late String? stockStatusDescription;
  late num buyNowPrice;
  late int highestBidCustomerId;
  late String auctionEndTs;
  late num highestBid = 0;

  FirebaseResponseOffline(
      {required this.highestBid,
      required this.auctionEndTs,
      required this.bidUpdateTimestamp,
      required this.buyNowPrice,
      required this.highestBidCustomerId,
      required this.stockAuctionStatus,
      required this.stockStatusDescription});

  FirebaseResponseOffline.fromSnapshot(DataSnapshot snapshot) {
    if (snapshot.exists) {
      var a = snapshot.value as Map;
      stockAuctionStatus = a['stock_auction_status'];
      bidUpdateTimestamp = a['bid_update_timestamp'];
      stockStatusDescription = a['stock_status_description'];
      buyNowPrice = a['buy_now_price'];
      highestBidCustomerId = a['highest_bid_customer_id'];
      auctionEndTs = a['auction_end_ts'];
      highestBid = a['highest_bid'];
    }
  }
}

class FirebaseResponseSellerNotifs {
  String? bid_insert_timestamp;
  num? price;
  num? bid_id;

  FirebaseResponseSellerNotifs({
    required this.bid_insert_timestamp,
    required this.price,
    required this.bid_id,
  });

  FirebaseResponseSellerNotifs.fromSnapshot(DataSnapshot snapshot) {
    if (snapshot.exists) {
      var a = snapshot.value as Map;
      bid_insert_timestamp = a['bid_insert_timestamp'];
      price = a['price'];
      bid_id = a['bid_id'];
    }
  }
}

class FirebaseResponseLive {
  late Map highest_bids;
  late int bid_id;
  late int customer_id;
  late int highest_bid;
  late int livebidding_stock_lot_no;
  late int auction_no;
  late Map finalbid;
  late int seconds;
  late DateTime auction_date;
  late int live_auction_status;
  late Map soldout_stats;
  late int averageSalesAmount;
  late int remainingLots;
  late int minimumSalesAmount;
  late int totalLots;
  late int totalStocks;
  late int maximumSalesAmount;
  late int soldOutLots;
  late int livebidding_seller_watching_bool;
  late String livebidding_whatsapp_chat;
  late DateTime restart_time;
  late int livebidding_increment_value;
  late int admin_transaction_id;
  late int livebidding;
  late int livebidding_seller_sold_bool;
  late String auction_status_cd;
  late int livebidding_stockid;
  late int auction_name;
  late String livebidding_meeting;
  late DateTime auction_time;
  late int livebidding_seller_id;
  late int auction_description;

  FirebaseResponseLive({
    required this.bid_id,
    required this.highest_bid,
    required this.admin_transaction_id,
    required this.auction_date,
    required this.auction_description,
    required this.auction_name,
    required this.auction_no,
    required this.auction_status_cd,
    required this.auction_time,
    required this.averageSalesAmount,
    required this.customer_id,
    required this.finalbid,
    required this.highest_bids,
    required this.live_auction_status,
    required this.livebidding,
    required this.livebidding_increment_value,
    required this.livebidding_meeting,
    required this.livebidding_seller_id,
    required this.livebidding_seller_sold_bool,
    required this.livebidding_seller_watching_bool,
    required this.livebidding_stock_lot_no,
    required this.livebidding_stockid,
    required this.livebidding_whatsapp_chat,
    required this.maximumSalesAmount,
    required this.minimumSalesAmount,
    required this.remainingLots,
    required this.restart_time,
    required this.seconds,
    required this.soldout_stats,
    required this.soldOutLots,
    required this.totalLots,
    required this.totalStocks,
  });

  FirebaseResponseLive.fromSnapshot(DataSnapshot snapshot) {
    if (snapshot.exists) {
      var a = snapshot.value as Map;
      var highestBids = a['highest_bids'] as Map;
      bid_id = a['bid_id'];
    }
  }
}
