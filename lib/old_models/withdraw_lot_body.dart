// To parse this JSON data, do
//
//     final withdrawLotBody = withdrawLotBodyFromJson(jsonString);

import 'dart:convert';

WithdrawLotBody withdrawLotBodyFromJson(String str) =>
    WithdrawLotBody.fromJson(json.decode(str));

String withdrawLotBodyToJson(WithdrawLotBody data) =>
    json.encode(data.toJson());

class WithdrawLotBody {
  WithdrawLotBody({
    required this.customerId,
    required this.stockId,
    required this.url,
  });

  int customerId;
  int stockId;
  String url;

  factory WithdrawLotBody.fromJson(Map<String, dynamic> json) =>
      WithdrawLotBody(
        customerId: json["customerId"],
        stockId: json["stockId"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "customerId": customerId,
        "stockId": stockId,
        "url": url,
      };
}
