// To parse this JSON data, do
//
//     final sellerInfo = sellerInfoFromJson(jsonString);

import 'dart:convert';

SellerInfo sellerInfoFromJson(String str) =>
    SellerInfo.fromJson(json.decode(str));

String sellerInfoToJson(SellerInfo data) => json.encode(data.toJson());

class SellerInfo {
  SellerInfo({
    this.data,
    this.status,
    this.statusDescription,
  });

  Data? data;
  int? status;
  String? statusDescription;

  factory SellerInfo.fromJson(Map<String, dynamic> json) => SellerInfo(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        status: json["status"],
        statusDescription: json["status_description"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "status": status,
        "status_description": statusDescription,
      };
}

class Data {
  Data({
    this.customerId,
    this.name,
    this.phone,
    this.phoneCountryCode,
    this.rating,
    this.location,
    this.email,
    this.address,
    this.latitude,
    this.longitude,
  });

  int? customerId;
  String? name;
  String? phone;
  String? phoneCountryCode;
  String? rating;
  String? location;
  String? email;
  Address? address;
  double? latitude;
  double? longitude;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        customerId: json["customer_id"],
        name: json["name"],
        phone: json["phone"],
        phoneCountryCode: json["phoneCountryCode"],
        rating: json["rating"],
        location: json["location"],
        email: json["email"],
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        latitude: json["latitude"],
        longitude: json["longitude"],
      );

  Map<String, dynamic> toJson() => {
        "customer_id": customerId,
        "name": name,
        "phone": phone,
        "phoneCountryCode": phoneCountryCode,
        "rating": rating,
        "location": location,
        "email": email,
        "address": address?.toJson(),
        "latitude": latitude,
        "longitude": longitude,
      };
}

class Address {
  Address({
    this.id,
    this.sellingAddressLine1,
    this.sellingAddressLine2,
    this.city,
    this.country,
    this.state,
    this.pincode,
    this.latitude,
    this.longitude,
    this.cityLan,
    this.cityLong,
    this.createdAt,
  });

  int? id;
  String? sellingAddressLine1;
  String? sellingAddressLine2;
  String? city;
  String? country;
  dynamic state;
  String? pincode;
  double? latitude;
  double? longitude;
  double? cityLan;
  double? cityLong;
  DateTime? createdAt;

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        id: json["id"],
        sellingAddressLine1: json["sellingAddressLine1"],
        sellingAddressLine2: json["sellingAddressLine2"],
        city: json["city"],
        country: json["country"],
        state: json["state"],
        pincode: json["pincode"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        cityLan: json["cityLan"]?.toDouble(),
        cityLong: json["cityLong"]?.toDouble(),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "sellingAddressLine1": sellingAddressLine1,
        "sellingAddressLine2": sellingAddressLine2,
        "city": city,
        "country": country,
        "state": state,
        "pincode": pincode,
        "latitude": latitude,
        "longitude": longitude,
        "cityLan": cityLan,
        "cityLong": cityLong,
        "createdAt": createdAt?.toIso8601String(),
      };
}
