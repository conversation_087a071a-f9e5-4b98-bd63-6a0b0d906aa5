class SearchModel {
  int? id;
  int? customerId;
  String? product;
  String? location;
  String? grade;
  double? priceFrom;
  double? priceTo;
  double? quantityFrom;
  double? quantityTo;
  double? buyBidPriceFrom;
  double? buyBidPriceTo;
  String? deliveryDate;
  String? searchName;
  String? isWeb;
  List<String>? days;
  String? recurrence;

  SearchModel(
      {this.id,
      this.customerId,
      this.product,
      this.location,
      this.grade,
      this.priceFrom,
      this.priceTo,
      this.quantityFrom,
      this.quantityTo,
      this.buyBidPriceFrom,
      this.buyBidPriceTo,
      this.deliveryDate,
      this.searchName,
      this.isWeb,
      this.days,
      this.recurrence});

  SearchModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customerId'];
    product = json['product'];
    location = json['location'];
    grade = json['grade'];
    priceFrom = json['priceFrom'];
    priceTo = json['priceTo'];
    quantityFrom = json['quantityFrom'];
    quantityTo = json['quantityTo'];
    buyBidPriceFrom = json['buyBidPriceFrom'];
    buyBidPriceTo = json['buyBidPriceTo'];
    deliveryDate = json['deliveryDate'];
    searchName = json['searchName'];
    isWeb = json['isWeb'];
    days = json['days'].cast<String>();
    recurrence = json['recurrence'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['customerId'] = customerId;
    data['product'] = product;
    data['location'] = location;
    data['grade'] = grade;
    data['priceFrom'] = priceFrom;
    data['priceTo'] = priceTo;
    data['quantityFrom'] = quantityFrom;
    data['quantityTo'] = quantityTo;
    data['buyBidPriceFrom'] = buyBidPriceFrom;
    data['buyBidPriceTo'] = buyBidPriceTo;
    data['deliveryDate'] = deliveryDate;
    data['searchName'] = searchName;
    data['isWeb'] = isWeb;
    data['days'] = days;
    data['recurrence'] = recurrence;
    return data;
  }
}
