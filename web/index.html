<!DOCTYPE html>
<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta content="A new Flutter project." name="description">

    <!-- iOS meta tags & icons -->
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="connectone_flutter" name="apple-mobile-web-app-title">
    <link href="icons/Icon-192.png" rel="apple-touch-icon">

    <!-- Favicon -->
    <link href="favicon.png" rel="icon" type="image/png">

  <title>BAI Store</title>
    <link href="manifest.json" rel="manifest">

    <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;




    </script>
    <!-- This script adds the flutter initialization JS code -->
    <script defer="" src="flutter.js"></script>
    <link href="splash/style.css" rel="stylesheet" type="text/css">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
          name="viewport">
    <script src="splash/splash.js"></script>
</head>
<body>
<picture id="splash">
    <source media="(prefers-color-scheme: light)"
            srcset="splash/img/light-1x.png 1x, splash/img/light-2x.png 2x, splash/img/light-3x.png 3x, splash/img/light-4x.png 4x">
    <source media="(prefers-color-scheme: dark)"
            srcset="splash/img/dark-1x.png 1x, splash/img/dark-2x.png 2x, splash/img/dark-3x.png 3x, splash/img/dark-4x.png 4x">
    <img alt="" aria-hidden="true" class="center" src="splash/img/light-1x.png">
</picture>
<script>
<!--    window.addEventListener('load', function(ev) {-->
<!--      // Download main.dart.js-->
<!--      _flutter.loader.loadEntrypoint({-->
<!--        serviceWorker: {-->
<!--          serviceWorkerVersion: serviceWorkerVersion,-->
<!--        },-->
<!--        onEntrypointLoaded: function(engineInitializer) {-->
<!--          engineInitializer.initializeEngine().then(function(appRunner) {-->
<!--            appRunner.runApp();-->
<!--          });-->
<!--        }-->
<!--      });-->
<!--    });-->


<!--  if ('serviceWorker' in navigator) {-->
<!--  window.addEventListener("load", function () {-->
<!--    navigator.serviceWorker.register("/firebase-messaging-sw.js");-->
<!--  });-->
<!--  window.addEventListener('flutter-first-frame', function () {-->
<!--    navigator.serviceWorker.register('/flutter_service_worker.js');-->
<!--  });-->
<!--}-->


</script>
<script src="main.dart.js" type="application/javascript"></script>
<script>
       if ('serviceWorker' in navigator) {
          // Service workers are supported. Use them.
          window.addEventListener('load', function () {
            // ADD THIS LINE
            navigator.serviceWorker.register('/firebase-messaging-sw.js');

            // Wait for registration to finish before dropping the <script> tag.
            // Otherwise, the browser will load the script multiple times,
            // potentially different versions.
            var serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;

            //  ...
          });
      }


</script>


</body>
</html>