html {
  height: 100%
}

body {
  margin: 0;
  min-height: 100%;
  background-color: #ffffff;
  background-size: 100% 100%;
}

.center {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.contain {
  display:block;
  width:100%; height:100%;
  object-fit: contain;
}

.stretch {
  display:block;
  width:100%; height:100%;
}

.cover {
  display:block;
  width:100%; height:100%;
  object-fit: cover;
}

.bottom {
  position: absolute;
  bottom: 0;
  left: 50%;
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
}

.bottomLeft {
  position: absolute;
  bottom: 0;
  left: 0;
}

.bottomRight {
  position: absolute;
  bottom: 0;
  right: 0;
}

@media (prefers-color-scheme: dark) {
  body {
    background-color: #ffffff;
  }
}
