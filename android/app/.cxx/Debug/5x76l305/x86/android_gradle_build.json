{"buildFiles": ["/home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Downloads/projects/bai-store/android/app/.cxx/Debug/5x76l305/x86", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Downloads/projects/bai-store/android/app/.cxx/Debug/5x76l305/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang.lld", "cppCompilerExecutable": "/home/<USER>/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}