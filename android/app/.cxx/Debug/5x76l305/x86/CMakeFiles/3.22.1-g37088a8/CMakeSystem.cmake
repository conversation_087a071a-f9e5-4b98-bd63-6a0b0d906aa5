set(CMAKE_HOST_SYSTEM "Linux-6.8.0-60-generic")
set(CMAKE_HOST_SYSTEM_NAME "Linux")
set(CMAKE_HOST_SYSTEM_VERSION "6.8.0-60-generic")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/home/<USER>/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake")

set(CMAKE_SYSTEM "Android-1")
set(CMAKE_SYSTEM_NAME "Android")
set(CMAKE_SYSTEM_VERSION "1")
set(CMAKE_SYSTEM_PROCESSOR "i686")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
