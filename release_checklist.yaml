#change package name, run command - q
#change base url & fb url
#change isPhoneNumber variable in constants
#change fb configuration
#change app name
#change app icon by running command
#change icon in side drawer
#run command to change app name - flutter pub run flutter_app_name
#login page icon change
#change Runner.entitlements & manifest.xml urls for app link
#run splash screen command flutter pub run flutter_native_splash:create

////////////////
Use rename package to change name and bundle id (then add closing tag in manifest if there are build errors)
Use icons_launcher package to change icons
Use flutter_native_splash to change splash screens
Delete existing firebase configurations in both android and ios folders and then run flutterfire cli command
flutter pub global run rename --bundleId ipsta.pepper.etrade
flutter pub global run rename --appname "IPSTA Pepper Trade"
#flutter pub run flutter_native_splash:create
flutter pub run icons_launcher:create
flutterfire configure --project=connectone-prod
