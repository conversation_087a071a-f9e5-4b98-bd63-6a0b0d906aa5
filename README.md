# Bai Store

A Flutter-based mobile application for managing and facilitating commerce operations. The app provides a platform for buyers and sellers to interact, manage inventory, and handle transactions efficiently.

## Features

- **User Authentication**: Secure login system with role-based access (buyer/seller)
- **Inventory Management**: Track and manage product stock
- **Order Management**: Handle purchase orders and transactions
- **Rating System**: User feedback and rating functionality
- **Push Notifications**: Real-time updates using Firebase Cloud Messaging
- **Theme Customization**: Dynamic color schemes based on user role

## Technical Stack

- **Frontend**: Flutter
- **Backend**: REST APIs
- **Authentication**: Firebase Authentication
- **Push Notifications**: Firebase Cloud Messaging
- **Analytics**: Firebase Analytics
- **Crash Reporting**: Firebase Crashlytics
- **State Management**: BLoC Pattern

## Getting Started

### Prerequisites

- Flutter SDK
- Android Studio / Xcode
- Firebase project setup
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ConnectOne-Club/bai-store.git
cd bai-store
```

2. Install dependencies:
```bash
flutter pub get
```

3. Configure Firebase:
```bash
flutterfire configure --project=connectone-prod
```

4. Run the app:
```bash
flutter run
```

## Configuration

The app requires several configuration parameters that can be found in `lib/core/utils/constants.dart`:

- Base URLs for API endpoints
- Firebase configuration
- App version information
- Bundle IDs

## Development

### Project Structure

- `lib/bai_screens/`: Main application screens
- `lib/core/`: Core utilities and network handling
- `lib/old_screens/`: Legacy screens (to be migrated/removed)
- `lib/main.dart`: Application entry point

### Key Components

- BLoC pattern for state management
- Firebase services integration
- Custom theme management
- Network handling with dio

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is proprietary software. All rights reserved.

## Support

For support, please contact the development team or raise an issue in the GitLab repository.
